import React from 'react';
import { initialize, mswLoader } from 'msw-storybook-addon';
import { Preview } from '@storybook/react';

import { AppProviders } from '../src/containers/App';
import { StorybookLogger } from '../src/services/logger';
import { ApplicationThemeProvider } from '../src/theme';
import { UserThemeProvider } from '../src/theme/UserThemeContext';

import {
  searchDataMockHandlers,
  epgMockHandlers,
  epgExtendedMockHandlers,
  vodAssetMockHandlers,
  vodPurchasesAssetMockHandlers,
  channelsAllMockHandlers,
  regionalListMockHandlers,
  householdMockHandlers,
  householdTveServiceMockHandlers,
  getDevicesMaxMockHandlers,
  appConfigsMockHandlers,
} from '../src/services/api/mock/mocksIndex';
import { MemoryRouter } from 'react-router-dom';

initialize();

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
  msw: {
    handlers: [
      ...searchDataMockHandlers,
      ...epgMockHandlers,
      ...epgExtendedMockHandlers,
      ...vodAssetMockHandlers,
      ...vodPurchasesAssetMockHandlers,
      ...channelsAllMockHandlers,
      ...regionalListMockHandlers,
      ...householdMockHandlers,
      ...householdTveServiceMockHandlers,
      ...getDevicesMaxMockHandlers,
      ...appConfigsMockHandlers,
    ],
  },
};

const preview: Preview = {
  decorators: [
    (Story) => {
      return (
        <UserThemeProvider>
          <ApplicationThemeProvider>
            <MemoryRouter>
              <AppProviders logger={StorybookLogger}>
                <Story />
              </AppProviders>
            </MemoryRouter>
          </ApplicationThemeProvider>
        </UserThemeProvider>
      );
    },
  ],
  loaders: [mswLoader],
};

export default preview;
