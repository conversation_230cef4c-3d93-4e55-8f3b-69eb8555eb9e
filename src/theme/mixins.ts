import { css } from 'styled-components';

import { devices } from './media';

export interface BaseTextWrapProps {
  $withWrap?: boolean;
}
export interface BaseTextColorProps {
  $primary?: boolean;
  $secondary?: boolean;
  $tertiaryLight?: boolean;
  $highlight?: boolean;
  $black?: boolean;
  $red?: boolean;
}

export interface BaseTextSizeProps {
  $sizeXXSmall?: boolean;
  $sizeXSmall?: boolean;
  $sizeSmall?: boolean;
  $sizeMedium?: boolean;
  $sizeXMedium?: boolean;
  $sizeXXMedium?: boolean;
  $sizeLarge?: boolean;
  $sizeXLarge?: boolean;
}

export interface VisibilityProps {
  $isVisible?: boolean;
}

export type DrawLayerType =
  | 'appBase'
  | 'appOverlay'
  | 'regionalTvBoard'
  | 'appInteractiveLow'
  | 'appInteractiveBase'
  | 'appInteractiveHigh'
  | 'player'
  | 'playerGlass'
  | 'playerErrorScreen'
  | 'playerOverlay'
  | 'playerControls'
  | 'detailsView'
  | 'background'
  | 'modal'
  | 'loader'
  | 'header';

export const baseTextWrap = css<BaseTextWrapProps>`
  ${({ $withWrap }) =>
    $withWrap
      ? 'text-overflow: ellipsis; white-space: nowrap; overflow: hidden;'
      : ''}
`;

export const baseTextColor = css<BaseTextColorProps>`
  color: ${({
    $highlight,
    $primary,
    $secondary,
    $tertiaryLight,
    $black,
    $red,
    theme,
  }) => {
    if ($primary) {
      return theme.colors.alabaster;
    }
    if ($secondary) {
      return theme.colors.white80;
    }
    if ($tertiaryLight) {
      return theme.colors.white50;
    }
    if ($highlight) {
      return theme.colors.primary;
    }
    if ($black) {
      return theme.colors.black;
    }
    if ($red) {
      return theme.colors.red;
    }
    return theme.colors.alabaster;
  }};
`;

export const baseTextSize = css<BaseTextSizeProps>`
  font-size: ${({
    $sizeXXSmall,
    $sizeXSmall,
    $sizeSmall,
    $sizeMedium,
    $sizeXMedium,
    $sizeXXMedium,
    $sizeLarge,
    $sizeXLarge,
    theme,
  }) => {
    const fontSizeKey =
      ($sizeXXSmall && 'xxSmall') ||
      ($sizeXSmall && 'xSmall') ||
      ($sizeSmall && 'small') ||
      ($sizeMedium && 'medium') ||
      ($sizeXMedium && 'xMedium') ||
      ($sizeXXMedium && 'xxMedium') ||
      ($sizeLarge && 'large') ||
      ($sizeXLarge && 'xLarge') ||
      'medium';

    return theme.fontSizes?.[fontSizeKey] || theme.fontSizes.medium;
  }};
`;

export const visibility = css<VisibilityProps>`
  opacity: ${({ $isVisible }) => ($isVisible ? 1 : 0)};
  transition: opacity 0.3s linear;
`;

export const setSliderRecordingCardSizesVertical = (
  marginRightRem: string,
) => css`
  width: calc(100% / 2.2 - ${marginRightRem});
  flex-shrink: 0;

  @media ${devices.mobileM} {
    width: calc(100% / 3.2 - ${marginRightRem});
  }

  @media ${devices.mobileL} {
    width: calc(100% / 4.4 - ${marginRightRem});
  }

  @media ${devices.tabletS} {
    width: calc(100% / 5.2 - ${marginRightRem});
  }

  @media ${devices.desktopS} {
    width: calc(100% / 6.2 - ${marginRightRem});
  }

  @media ${devices.desktopM} {
    width: calc(100% / 8.2 - ${marginRightRem});
  }
`;

export const setSliderRecordingCardSizes = (marginRightRem: string) => css`
  width: calc(100% / 1.2 - ${marginRightRem});
  flex-shrink: 0;

  @media ${devices.mobileM} {
    width: calc(100% / 1.7 - ${marginRightRem});
  }

  @media ${devices.mobileL} {
    width: calc(100% / 2.2 - ${marginRightRem});
  }

  @media ${devices.tabletS} {
    width: calc(100% / 2.7 - ${marginRightRem});
  }

  @media ${devices.desktopS} {
    width: calc(100% / 3.7 - ${marginRightRem});
  }

  @media ${devices.desktopM} {
    width: calc(100% / 5.2 - ${marginRightRem});
  }
`;

export const setSliderRecordingCardSizesBig = (marginRightRem: string) => css`
  width: 100%;
  flex-shrink: 0;

  @media ${devices.mobileM} {
    width: calc(100% / 1.25 - ${marginRightRem});
  }

  @media ${devices.mobileL} {
    width: calc(100% / 1.75 - ${marginRightRem});
  }

  @media ${devices.tabletS} {
    width: calc(100% / 1.25 - ${marginRightRem});
  }

  @media ${devices.desktopS} {
    width: calc(100% / 1.75 - ${marginRightRem});
  }
`;

export const GridImages = () => css`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 1fr;
  grid-gap: 3.2rem 1.2rem;

  @media ${devices.mobileM} {
    grid-template-columns: repeat(3, 1fr);
  }

  @media ${devices.tabletS} {
    grid-template-columns: repeat(4, 1fr);
  }

  @media ${devices.desktopS} {
    grid-template-columns: repeat(5, 1fr);
  }

  @media ${devices.desktopM} {
    grid-template-columns: repeat(7, 1fr);
  }
`;

export const GridImagesBig = () => css`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 1fr;
  grid-gap: 3.2rem 1.2rem;

  @media ${devices.mobileM} {
    grid-template-columns: repeat(2, 1fr);
  }

  @media ${devices.tabletS} {
    grid-template-columns: repeat(3, 1fr);
  }

  @media ${devices.desktopS} {
    grid-template-columns: repeat(4, 1fr);
  }

  @media ${devices.desktopM} {
    grid-template-columns: repeat(5, 1fr);
  }
`;

export const RenderLayer = (layer: DrawLayerType) => css`
  z-index: var(--${layer});
`;
