import { audioLanguagesArray } from 'utils/languages/languages';
import { getCountryNameByCode } from 'utils/countries/countries';
import { CountyCodesArray } from 'features/Player/Interface/SettingsMenu/Options/components/types';

import { PICTOGRAMS_PATTERN, SPLIT_PATTERN } from './constants';

const splitArray = (array: Array<string>) => {
  return array.join(',').split(SPLIT_PATTERN);
};

export const getPictograms = (genres: Array<string> | undefined) => {
  if (!Array.isArray(genres) || genres.length === 0) {
    return [];
  }
  const genresSplitted = splitArray(genres);
  return genresSplitted.filter((genre) => PICTOGRAMS_PATTERN.test(genre));
};

export const filterGenres = (
  genresUnfiltered: Array<string> | undefined,
  pictograms: Array<string> | undefined,
) => {
  if (!Array.isArray(genresUnfiltered) || !Array.isArray(pictograms)) {
    return '';
  }
  return splitArray(genresUnfiltered)
    .filter((genre) => !pictograms.includes(genre))
    .join(', ');
};

export const extendCountryCode = (
  countryCodes: Array<string>,
  countryCodesMapArray: CountyCodesArray,
) => {
  const countryCodesSet = new Set(countryCodes);
  const labels = countryCodesMapArray
    .filter((countryCode) => {
      const countryCodeValue = countryCode.value;
      return Array.isArray(countryCodeValue)
        ? countryCodeValue.some((countryCodeItem) =>
            countryCodesSet.has(countryCodeItem),
          )
        : countryCodesSet.has(countryCodeValue);
    })
    .map((countryCode) => countryCode.label);

  return labels;
};

export const getLanguagesName = (countryCodes: Array<string> | undefined) => {
  if (!Array.isArray(countryCodes) || countryCodes.length === 0) {
    return '';
  }
  const extendedCountryCodes = extendCountryCode(
    countryCodes,
    audioLanguagesArray,
  ).map((language) => language.toLowerCase());

  return extendedCountryCodes.join(', ');
};

export const getCountriesName = (countryCodes: Array<string>) => {
  if (!Array.isArray(countryCodes) || countryCodes.length === 0) {
    return '';
  }
  const listOfCountries = countryCodes.map((countryCode) =>
    getCountryNameByCode(countryCode),
  );
  const uniqueListOfCountries = [...new Set(listOfCountries)];
  return uniqueListOfCountries
    .filter((countryName) => countryName !== '')
    .join(', ');
};
