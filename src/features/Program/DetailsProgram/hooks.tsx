import { useEffect, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { millisecondsToSeconds } from 'date-fns';

import { DetailsViewType } from 'services/detailsView/types';
import { useChannelsAllQuery } from 'services/api/newApi/live/channels';
import { initialDataDetailsView } from 'services/detailsView/constants';
import { useShortInfo } from 'hooks/useShortInfo/useShortInfo';
import {
  MediaProgram,
  useTvguideMediaQuery,
  useTvguideProgramQuery,
  useTvguideSeriesQuery,
} from 'services/api/newApi/core/tvguide';
import { useConfig } from 'services/config';
import { getDuration, getLiveStatus } from 'utils/dateUtils';
import { getPublicAssetUrl } from 'utils/url';
import { checkIfIsCatchupAvailable } from 'utils/program';
import { useHandleProgramButtons } from 'hooks/useHandleProgramButtons';
import { checkIfIsNowOrFutureProgram } from 'hooks/useProgramButtonsVisibility';

import { EpisodeWithChannelDetails, UseDetailsProgramProps } from './types';

export const useDetailsProgram = ({
  dataDetailsView,
  setBackgroundImage,
  setData,
  detailsWrapperRef,
}: UseDetailsProgramProps) => {
  const { formatMessage } = useIntl();
  const { withVisibleNpvrFeatures } = useConfig().getTechConfig();

  const { data: channelsData } = useChannelsAllQuery();

  const programId = dataDetailsView.id as string;

  const { data: program, isFetching: isFetchingProgram } =
    useTvguideProgramQuery(programId);

  const { data: seriesPrograms, isFetching: isFetchingSeries } =
    useTvguideSeriesQuery(program?.seriesId || '');

  const { data: mediaPrograms, isFetching: isFetchingMedia } =
    useTvguideMediaQuery(program?.mediaId || '');
  const combinedEpisodes = useMemo<EpisodeWithChannelDetails[]>(() => {
    const episodesMap = new Map<string, EpisodeWithChannelDetails>();

    const addEpisodeToMap = (episode: EpisodeWithChannelDetails) => {
      const uniqueKey = `${episode.programExtId}_${episode.startTimeUtc}_${episode.channelExtId}`;
      if (!episodesMap.has(uniqueKey)) {
        episodesMap.set(uniqueKey, episode);
      }
    };

    const findEpisodeChannel = (channelExtId: string) =>
      channelsData?.find((channel) => channel.channelExtId === channelExtId);

    if (seriesPrograms && 'series' in seriesPrograms) {
      seriesPrograms.series
        .filter((episode) => {
          const episodeChannel = findEpisodeChannel(episode.channelExtId);
          if (!episodeChannel) return false;

          const isCatchupAvailable = checkIfIsCatchupAvailable({
            endDate: episode.endTimeUtc,
            catchupDuration: episodeChannel?.catchupDuration,
            isCatchupDisabled: episode.properties?.catchUpDisabled,
            playFeatures: episodeChannel?.playFeatures,
            withVisibleNpvrFeatures,
          });

          return (
            checkIfIsNowOrFutureProgram(episode.endTimeUtc) ||
            isCatchupAvailable
          );
        })
        .forEach((episode) => {
          const episodeChannel = findEpisodeChannel(episode.channelExtId);
          const channelLogoSignatureForEpisode = episodeChannel?.logoSignature;

          addEpisodeToMap({
            ...episode,
            logoSignature: channelLogoSignatureForEpisode || '',
            isChannelRecordingAllowed:
              episodeChannel?.isRecordingAllowed || false,
            isSubscribedChannel: episodeChannel?.isSubscribed || false,
          });
        });
    }

    if (mediaPrograms && Array.isArray(mediaPrograms.media)) {
      mediaPrograms.media
        .filter((media: MediaProgram) => {
          const episodeChannel = findEpisodeChannel(media.channelExtId);
          if (!episodeChannel) return false;

          const isCatchupAvailable = checkIfIsCatchupAvailable({
            endDate: media.endTimeUtc,
            catchupDuration: episodeChannel?.catchupDuration,
            isCatchupDisabled: media.properties?.catchUpDisabled ?? false,
            playFeatures: episodeChannel?.playFeatures,
            withVisibleNpvrFeatures,
          });

          return (
            checkIfIsNowOrFutureProgram(media.endTimeUtc) || isCatchupAvailable
          );
        })
        .forEach((media: MediaProgram) => {
          const episodeChannel = findEpisodeChannel(media.channelExtId);
          const channelLogoSignatureForEpisode = episodeChannel?.logoSignature;

          addEpisodeToMap({
            ...media,
            logoSignature: channelLogoSignatureForEpisode || '',
            isChannelRecordingAllowed:
              episodeChannel?.isRecordingAllowed || false,
            isSubscribedChannel: episodeChannel?.isSubscribed || false,
            properties: {
              audioDescription: media.properties?.audioDescription ?? false,
              catchUpDisabled: media.properties?.catchUpDisabled ?? false,
              hohSubtitles: media.properties?.hohSubtitles ?? false,
              live: media.properties?.live ?? false,
              recordingDisabled: media.properties?.recordingDisabled ?? false,
              signLanguage: media.properties?.signLanguage ?? false,
              startOverDisabled: media.properties?.startOverDisabled ?? false,
              subtitles: media.properties?.subtitles ?? false,
              tveStreamDisabled: media.properties?.tveStreamDisabled ?? false,
            },
          });
        });
    }

    const areNoRelatedEpisodes =
      program && !program.mediaId && !program.seriesId;

    if (areNoRelatedEpisodes) {
      const addCurrentProgramAsEpisode = () => {
        const episodeChannel = findEpisodeChannel(program.channelExtId);
        addEpisodeToMap({
          ...program,
          logoSignature: episodeChannel?.logoSignature || '',
          isSubscribedChannel: episodeChannel?.isSubscribed || false,
          isChannelRecordingAllowed:
            episodeChannel?.isRecordingAllowed || false,
        });
      };
      addCurrentProgramAsEpisode();
    }

    const filteredEpisodes = Array.from(episodesMap.values()).filter(
      (episode) =>
        episode.series?.seasonNumber === program?.series?.seasonNumber,
    );

    return filteredEpisodes.sort((a, b) => a.startTimeUtc - b.startTimeUtc);
  }, [
    seriesPrograms,
    mediaPrograms,
    channelsData,
    withVisibleNpvrFeatures,
    program,
  ]);

  const scrollProgramDetailsToTop = () => {
    if (detailsWrapperRef.current) {
      detailsWrapperRef.current.scrollTop = 0;
    }
  };

  const handleOpenProgramDetails = (
    programExtId: string,
    isSubscribedChannel: boolean,
    isChannelRecordingAllowed: boolean,
  ) => {
    scrollProgramDetailsToTop();
    setData({
      type: DetailsViewType.Program,
      id: programExtId,
      isSubscribedChannel,
      isChannelRecordingAllowed,
    });
  };

  const programChannel = useMemo(() => {
    return channelsData?.find(
      (channel) => channel.channelExtId === program?.channelExtId,
    );
  }, [channelsData, program?.channelExtId]);

  const {
    handlePlayCatchupProgram,
    handlePlayProgram,
    handlePlayStartoverProgram,
  } = useHandleProgramButtons(
    dataDetailsView.isSubscribedChannel,
    programChannel?.isRegionalTv,
  );

  const time = program
    ? getDuration(program.startTimeUtc, program.endTimeUtc)
    : '';
  const countries = program?.countries || [];
  const director = program?.cast?.find((member) => member.role === 'director');

  const shortInfo = useShortInfo({
    directors: director?.name,
    countries,
    year: program?.year,
    dubbing: undefined,
  });

  const actors = useMemo(() => {
    return program?.cast
      ?.filter((person) => person.role === 'actor' || person.role === 'host')
      .map((actor) => ({
        name: actor.name,
        img:
          actor?.image && program?.imagePaths?.standard
            ? getPublicAssetUrl(program?.imagePaths?.standard, actor?.image)
            : undefined,
      }));
  }, [program?.cast, program?.imagePaths?.standard]);

  const playProgram = () => {
    if (program && dataDetailsView.id) {
      const { channelExtId } = program;
      const isLive = getLiveStatus(
        program.startTimeUtc * 1000,
        program.endTimeUtc * 1000,
      );
      setData(initialDataDetailsView);

      if (isLive) {
        return handlePlayProgram(
          channelExtId,
          dataDetailsView.id,
          program.prLevel,
        );
      }
      return handlePlayCatchupProgram(
        channelExtId,
        dataDetailsView.id,
        program.endTimeUtc,
        program.prLevel,
      );
    }
  };

  const playStartoverProgram = () => {
    if (program && dataDetailsView.id) {
      const { channelExtId } = program;

      handlePlayStartoverProgram(
        channelExtId,
        dataDetailsView.id,
        program.prLevel,
      );
      return setData(initialDataDetailsView);
    }
  };

  const indexOfEpisodeClosestToCurrentTime = useMemo(() => {
    let closestEpisodeIndex = 0;
    let shortestDistanceInSec = Infinity;
    const currentTime = millisecondsToSeconds(Date.now());
    combinedEpisodes.forEach(({ startTimeUtc }, index) => {
      const episodeDistanceToCurrentTime = Math.abs(currentTime - startTimeUtc);
      if (episodeDistanceToCurrentTime < shortestDistanceInSec) {
        shortestDistanceInSec = episodeDistanceToCurrentTime;
        closestEpisodeIndex = index;
      }
    });
    return closestEpisodeIndex;
  }, [combinedEpisodes]);

  useEffect(() => {
    if (program?.imagePaths?.standard && program?.image) {
      const backgroundImage = getPublicAssetUrl(
        program?.imagePaths?.standard,
        program?.image,
      );

      setBackgroundImage(backgroundImage);
    } else {
      setBackgroundImage('');
    }
  }, [program, setBackgroundImage, setData]);

  const isNpvr = useMemo(() => {
    return programChannel?.playFeatures.otg.isNpvr;
  }, [programChannel?.playFeatures.otg.isNpvr]);

  return {
    isFetchingProgram,
    isFetchingSeries,
    isFetchingMedia,
    time,
    shortInfo,
    actors,
    playProgram,
    formatMessage,
    program,
    programChannel,
    isNpvr,
    playStartoverProgram,
    otherEpisodesOfProgram: combinedEpisodes,
    handleOpenProgramDetails,
    indexOfEpisodeClosestToCurrentTime,
  };
};
