import styled from 'styled-components';

import { Text } from 'components/Typography';

export const ActionsWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const CheckboxWrapper = styled.div`
  display: flex;
  margin-left: 2rem;

  transition: color 0.2s ease-out;

  & > div {
    flex-direction: row-reverse;
    margin-right: 1rem;

    label {
      margin-right: 1.2rem;

      &:hover {
        cursor: pointer;
        color: ${({ theme }) => theme.colors.primary};
      }
    }

    &:first-child > div {
      margin-right: 2rem;
    }
  }
`;

export const Grid = styled.div`
  display: grid;
  grid-template-columns: minmax(min-content, 22.5rem) auto;
  row-gap: 7.2rem;
  align-items: center;
  padding-left: 2.4rem;
  padding-top: 4.8rem;
  max-width: 78rem;

  button {
    width: 22rem;
    padding: 1rem 2rem;
  }
`;

export const OutsideGridElementWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 7.4rem 0 0 2.4rem;
`;

export const InfoTextWrapper = styled.div`
  max-width: 1270px;
  padding-top: 3.2rem;
`;

export const Version = styled(Text)`
  margin: 7rem 0;
  font-size: 2.4rem;
`;

ActionsWrapper.displayName = 'ServiceActionsWrapper';
Grid.displayName = 'ServiceGrid';
OutsideGridElementWrapper.displayName = 'OutsideGridElementWrapper';
