import { useIntl } from 'react-intl';

import { Section } from 'components/Section';
import { Text } from 'components/Typography';
import { SingleDevice } from 'components/SingleDevice';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { ModalConfirmation } from 'components/ModalConfirmation';
import { Terminal } from 'services/api/newApi/core/device/types';
import { globalConfig } from 'services/config/config';

import { messages } from './messages';
import { useDevice } from './hook';
import * as S from './styles';

export const Devices = () => {
  const { formatMessage } = useIntl();
  const {
    device: { terminalsMaxOTTLimit },
  } = globalConfig;

  const {
    isDeleteModalOpen,
    currentSerialNumber,
    disabledAddButton,
    setCurrentSerialNumber,
    setIsDeleteModalOpen,
    deleteDevice,
    registerTerminal,
    devices,
  } = useDevice();

  return (
    <Section title={formatMessage(messages.title)}>
      <ModalConfirmation
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        modalTitle={formatMessage(messages.modalTitle)}
        modalDescription={formatMessage(messages.modalDesc)}
        buttonSubmitText={formatMessage(messages.modalButtonConfirm)}
        buttonDeniedText={formatMessage(messages.modalButtonDeny)}
        onSubmit={() => deleteDevice(currentSerialNumber)}
      />
      <S.InfoTextWrapper>
        <Text>
          {formatMessage(messages.infoText, {
            terminalsMaxOttLimit: terminalsMaxOTTLimit,
          })}
        </Text>
      </S.InfoTextWrapper>
      <PrimaryButton
        type='button'
        variant='orange'
        disabled={disabledAddButton}
        onClick={registerTerminal}
      >
        <Text>{formatMessage(messages.addDevice)}</Text>
      </PrimaryButton>
      <S.DeviceGrid data-testid='DeviceGrid'>
        {devices?.list &&
          devices.list.map((device: Terminal) => (
            <SingleDevice
              key={device.serialNumber}
              device={device}
              onDelete={() => {
                setCurrentSerialNumber(device.serialNumber);
                setIsDeleteModalOpen(true);
              }}
              isSmall
            />
          ))}
      </S.DeviceGrid>
    </Section>
  );
};
