import styled from 'styled-components';
export const Grid = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;

  gap: 2.4rem;
  align-items: center;
  padding: 4.8rem 0 4.8rem 2.4rem;

  button {
    width: 22rem;
    padding: 1rem 2rem;
  }
`;

export const InfoTextWrapper = styled.div`
  max-width: 1270px;
  padding: 3.6rem 0;
`;

export const ConnectionStatusDot = styled.div<{ $isActive: boolean }>`
  background-color: ${(isActive) => (isActive ? 'green' : 'red')};
  width: 30px;
  height: 30px;
  border-radius: 50%;
`;

export const ListOfFailedCdns = styled.div`
  display: grid;
  grid-column: 1/-1;
  grid-template-columns: auto 1fr;
  gap: 1.2rem;
  padding-left: 2.8rem;
  > *:nth-child(2n) {
    margin-left: 2.8rem;
  }
`;

export const HomeZoneWrapper = styled.div`
  display: flex;
  gap: 0.2rem;
`;
