import { useIntl } from 'react-intl';
import { useEffect } from 'react';

import { Section } from 'components/Section';
import { Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { Status, StatusIndicator } from 'components/StatusIndicator';
import { ModalConfirmation } from 'components/ModalConfirmation';
import { useTerminal } from 'features/Player/Hook';
import {
  IconCheckInCircle,
  IconCrossInCircle,
  IconHomeArea,
  IconHomeAreaError,
} from 'components/Icons';
import { Tooltip } from 'components/Tooltip';
import { useConfig } from 'services/config';

import { messages } from './messages';
import * as S from './styles';
import { useDiagnosticsCenter, usePmsReport } from './hooks';
import { TOOLTIP_WIDTH } from './constants';
import { DiagnosticCenterState } from './types';

export const DiagnosticsCenter = () => {
  const { formatMessage } = useIntl();
  const {
    diagnostics: { isHomeZoneConnectionCheckVisible },
  } = useConfig().getTechConfig();
  const {
    config: {
      appConfig: { diagnosticsConfig },
    },
  } = useConfig();

  const { state, setState, diagnosticDetails, diagnoseApp } =
    useDiagnosticsCenter();
  const { sendDiagnosticReportToPms } = usePmsReport({ setState });
  const { registerTerminal, addNewDevice } = useTerminal();

  useEffect(() => {
    if (
      state === DiagnosticCenterState.DIAGNOSED ||
      state === DiagnosticCenterState.ADD_TERMINAL_SUCCESS
    ) {
      sendDiagnosticReportToPms(diagnosticDetails);
    }
  }, [sendDiagnosticReportToPms, state, diagnosticDetails]);

  const handleRegisterNewTerminal = async () => {
    await registerTerminal({
      onSuccessTerminalDeleteCallback: async () => {
        await addNewDevice();
        setState(DiagnosticCenterState.ADD_TERMINAL_SUCCESS);
      },
    });
    await sendDiagnosticReportToPms(diagnosticDetails);
  };

  return (
    <Section title={formatMessage(messages.title)}>
      {state === DiagnosticCenterState.ADD_TERMINAL ? (
        <ModalConfirmation
          isOpen={true}
          onClose={() => {
            setState(DiagnosticCenterState.DIAGNOSTIC_ENDED);
          }}
          modalTitle={formatMessage(messages.terminalNotExistModal.title)}
          modalDescription={formatMessage(
            messages.terminalNotExistModal.description,
          )}
          buttonSubmitText={formatMessage(
            messages.terminalNotExistModal.submitButton,
          )}
          buttonDeniedText={formatMessage(
            messages.terminalNotExistModal.deniedButton,
          )}
          onSubmit={() => {
            handleRegisterNewTerminal();
          }}
          withSubmitClose={false}
        />
      ) : (
        <></>
      )}
      <S.InfoTextWrapper>
        <Text>{formatMessage(messages.description)}</Text>
      </S.InfoTextWrapper>
      <PrimaryButton
        variant='orange'
        disabled={
          !(state === DiagnosticCenterState.NOT_DIAGNOSED) || !diagnosticsConfig
        }
        onClick={() => diagnoseApp(isHomeZoneConnectionCheckVisible)}
      >
        <Text>{formatMessage(messages.diagnoseButton)}</Text>
      </PrimaryButton>
      <S.Grid>
        <Text>{formatMessage(messages.serviceId)}</Text>
        <Text>{diagnosticDetails.householdExtId}</Text>
        <Text>{formatMessage(messages.appVersion)}</Text>
        <Text>{diagnosticDetails.appVersion}</Text>
        <Text>{formatMessage(messages.browserVersion)}</Text>
        <Text>{diagnosticDetails.browserVersion}</Text>
        <Text>{formatMessage(messages.osType)}</Text>
        <Text>{diagnosticDetails.osType}</Text>
        <Text>{formatMessage(messages.timezone)}</Text>
        <Text>{diagnosticDetails.timeZone}</Text>
        <Text>{formatMessage(messages.onLine)}</Text>
        {diagnosticDetails.isOnline ? (
          <StatusIndicator
            status={Status.SUCCESS}
            IconSuccess={IconCheckInCircle}
            IconError={IconCrossInCircle}
          />
        ) : (
          <StatusIndicator
            status={Status.ERROR}
            IconSuccess={IconCheckInCircle}
            IconError={IconCrossInCircle}
          />
        )}
        {isHomeZoneConnectionCheckVisible && (
          <>
            <S.HomeZoneWrapper>
              <Text>{formatMessage(messages.homeZoneAccess)}</Text>
              <Tooltip
                content={formatMessage(messages.homeZoneAccessTooltip)}
                width={TOOLTIP_WIDTH}
              />
              <Text>:</Text>
            </S.HomeZoneWrapper>
            <StatusIndicator
              status={diagnosticDetails.homeZoneConnectionStatus}
              IconSuccess={IconHomeArea}
              IconError={IconHomeAreaError}
            />
          </>
        )}
        <Text>{formatMessage(messages.dnsConnectionStatus)}</Text>
        <StatusIndicator
          status={diagnosticDetails.dnsConnectionStatus}
          IconSuccess={IconCheckInCircle}
          IconError={IconCrossInCircle}
        />

        <Text>{formatMessage(messages.cdn)}</Text>
        <StatusIndicator
          status={diagnosticDetails.cdnsConnectionStatus}
          IconSuccess={IconCheckInCircle}
          IconError={IconCrossInCircle}
        />
        <S.ListOfFailedCdns>
          {diagnosticDetails.failedCdns.map((failedCnd) => (
            <>
              <Text>{failedCnd.name}</Text>
              <Text $red>{formatMessage(messages.connectionError)}</Text>
            </>
          ))}
        </S.ListOfFailedCdns>
      </S.Grid>
    </Section>
  );
};
