import { useIntl } from 'react-intl';

import { Section } from 'components/Section';
import { Text } from 'components/Typography';
import { Image } from 'components/Image';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { useQRCodeQuery } from 'services/api/newApi/auth';

import { messages } from './messages';
import * as S from './styles';

export const QRLogin = () => {
  const { formatMessage } = useIntl();
  const { data: qrCodeImage, refetch } = useQRCodeQuery();

  return (
    <Section title={formatMessage(messages.title)}>
      <S.InfoTextWrapper>
        <Text>{formatMessage(messages.infoText)}</Text>
      </S.InfoTextWrapper>
      <S.InnerContainer>
        {qrCodeImage && <Image src={qrCodeImage} />}
        <PrimaryButton variant='orange' onClick={refetch}>
          <Text>{formatMessage(messages.getNewQRCodeBtn)}</Text>
        </PrimaryButton>
      </S.InnerContainer>
    </Section>
  );
};
