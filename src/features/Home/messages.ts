export const messages = {
  channels: {
    id: 'home.channels',
    defaultMessage: 'Kanały',
  },
  continueWatching: {
    id: 'home.continueWatching',
    defaultMessage: 'Kontynuuj oglądanie',
  },

  rentedMovies: {
    id: 'home.rentedMovies',
    defaultMessage: 'Wypożyczone filmy',
  },

  recentlyWatchedChannels: {
    id: 'recentlyWatchedChannels.header',
    defaultMessage: 'Ostatnio oglądane kanały',
  },

  favorites: { id: 'home.favorites', defaultMessage: 'Moje ulubione' },

  modalError: {
    modalTitle: {
      id: 'recentlyWatchedChannels.modalTitle',
      defaultMessage:
        'W<PERSON>tą<PERSON>ł błąd podczas pobierania danych, odświe<PERSON> stronę przeglądarki lub spróbuj ponownie.',
    },
    modalConfirmation: {
      id: 'recentlyWatchedChannels.modalConfirmation',
      defaultMessage: 'Odświe<PERSON>',
    },
    modalDenied: {
      id: 'recentlyWatchedChannels.modalDenied',
      defaultMessage: 'Zamknij',
    },
  },
  noRecentlyWatchedChannels: {
    id: 'recentlyWatchedChannels.noRecentlyWatchedChannels',
    defaultMessage: 'Tu znajdą się oglądane przez Ciebie kanały',
  },
  favoritesChannels: {
    id: 'home.favoritesChannels',
    defaultMessage: 'Ulubione kanały',
  },
  noFavoritesChannels: {
    id: 'home.noFavoritesChannels',
    defaultMessage:
      'Dodawaj kanały do ulubionych {starIcon}, aby mieć do nich szybki dostęp.',
  },
};
