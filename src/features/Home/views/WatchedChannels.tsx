import { useIntl } from 'react-intl';

import { ChannelsGrid } from 'components/ChannelsGrid';
import { Loader } from 'components/Loader';
import { messages } from 'features/Home/messages';
import { useConfig } from 'services/config';

import { useWatchedChannels } from './hooks';

export const WatchedChannels = () => {
  const { formatMessage } = useIntl();
  const { withRecentlyWatchedChannels } = useConfig().getTechConfig().myZone;

  const { watchedChannels, isFetching, isError } = useWatchedChannels();

  if (isFetching) {
    return <Loader />;
  }

  if (isError) {
    return null;
  }

  if (withRecentlyWatchedChannels) {
    return (
      <ChannelsGrid
        channels={watchedChannels}
        isFetching={isFetching}
        title={formatMessage(messages.recentlyWatchedChannels)}
        isSlider={true}
        amountLines={1}
        noDataMessage={formatMessage(messages.noRecentlyWatchedChannels)}
      />
    );
  }

  return null;
};
