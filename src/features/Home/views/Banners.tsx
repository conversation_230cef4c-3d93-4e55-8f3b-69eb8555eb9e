import { useMemo } from 'react';

import { Loader } from 'components/Loader';
import { PromoSlider } from 'features/Home/components/PromoSlider';
import { useConfig } from 'services/config';
import { shuffleArray } from 'utils/array';
import { useBannersQuery } from 'services/api/newApi/optional/banners/queries';
import { LocationType } from 'services/api/newApi/optional/banners/types';

export const Banners = () => {
  const { data, isFetching } = useBannersQuery({
    view: 'home',
    type: LocationType.TOP_LINE,
  });

  const { getTechConfig } = useConfig();
  const {
    myZone: { withPromoContent },
  } = getTechConfig();

  const topBanners = useMemo(() => {
    return data?.bannerList || [];
  }, [data]);

  if (isFetching) {
    return <Loader />;
  }
  const shouldShowBanners = withPromoContent && topBanners.length > 0;

  if (shouldShowBanners) {
    return <PromoSlider banners={shuffleArray(topBanners)} />;
  }

  return null;
};
