import { millisecondsToSeconds } from 'date-fns';

import { useChannels } from 'routes/Channels/hooks';
import {
  Channel,
  useChannelsAllQuery,
} from 'services/api/newApi/live/channels';
import {
  RecentlyWatchedProgram,
  RecentlyWatchedRecording,
  useMyListQuery,
} from 'services/api/oldApi/myzone';
import { useRecentlyWatchedChannels } from 'services/localApi/RecentlyWatchedChannelsApiContext';

export const useWatchedChannels = () => {
  const {
    recentlyWatchedChannels = [],
    isFetching: isFetchingRecentlyWatched,
    isError: isRecentlyWatchedError,
  } = useRecentlyWatchedChannels();

  const {
    data: liveChannels = [],
    isFetching: isFetchingLiveChannels,
    isError: isLiveChannelsError,
  } = useChannelsAllQuery();

  const isFetching = isFetchingRecentlyWatched || isFetchingLiveChannels;
  const isError = isLiveChannelsError || isRecentlyWatchedError;

  const extendedRecentlyWatchedChannels = recentlyWatchedChannels.map(
    (recentlyWatchedChannel) => {
      const extendedChannel = liveChannels?.find(
        (liveChannel) =>
          liveChannel.channelExtId === recentlyWatchedChannel.channelExtId,
      );
      return extendedChannel as Channel;
    },
  );

  return {
    watchedChannels: extendedRecentlyWatchedChannels,
    live: liveChannels,
    isFetching,
    isError,
  };
};

export const useFavoritesChannels = () => {
  const { data: myList, isFetching: isMyListFetching } = useMyListQuery();
  const { data: liveChannels = [], isFetching: isFetchingLiveChannels } =
    useChannelsAllQuery();

  const isDataFetching = isMyListFetching || isFetchingLiveChannels;

  const favoritesChannels = myList?.channelList ?? [];

  const favoritesChannelsWithDetails = liveChannels.filter(
    (liveChannel) =>
      favoritesChannels?.some(
        (favoriteChannel) =>
          favoriteChannel.channelExternalId === liveChannel.channelExtId,
      ),
  );

  return { favoritesChannelsWithDetails, isDataFetching };
};

export const useProgramsForOtg = (programs: Array<RecentlyWatchedProgram>) => {
  const { allChannels } = useChannels();
  const programsAvailableForOtg = programs.filter((program) => {
    const currentTime = millisecondsToSeconds(Date.now());

    const programChannelId = program.channelExternalId;
    const programChannel = allChannels.find(
      (channel) => channel.channelExtId === programChannelId,
    );

    const isCatchupProgram =
      program.isCatchup && currentTime > program.endTimestamp;

    if (isCatchupProgram) {
      return programChannel?.playFeatures.otg.isCatchUp || false;
    }

    const isStartoverProgram =
      program.isStartover && currentTime <= program.endTimestamp;

    if (isStartoverProgram) {
      return programChannel?.playFeatures.otg.isStartOver || false;
    }

    return false;
  });

  return { programsAvailableForOtg };
};

export const useRecordingsForOtg = (
  recordings: Array<RecentlyWatchedRecording>,
) => {
  const { allChannels } = useChannels();
  const recordingsAvailableForOtg = recordings.filter((recording) => {
    const recordingChannel = allChannels.find(
      (channel) => channel.channelExtId === recording.channelExternalId,
    );
    return recordingChannel?.playFeatures.otg.isNpvr;
  });

  return { recordingsAvailableForOtg };
};
