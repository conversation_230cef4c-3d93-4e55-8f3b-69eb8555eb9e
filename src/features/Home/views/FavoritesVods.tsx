import { PosterPreviewSlider } from 'components/PosterPreviewSlider';
import { DetailsViewType, useDetailsView } from 'services/detailsView';
import { useUserProfile } from 'services/user';
import { getPublicAssetUrl } from 'utils/url';
import { useMyListQuery } from 'services/api/oldApi/myzone/queries';
import { useConfig } from 'services/config';
import { VodPosterPreview } from 'components/VodPosterPreview';

import * as S from './styles';

import { messages } from '../messages';

export const FavoritesVods = () => {
  const { setData: setDataDetailsView } = useDetailsView();

  const { data: myList, isFetching } = useMyListQuery();

  const { withFavoriteVods } = useConfig().getTechConfig().myZone;

  const favoritesVods = myList?.vodList ?? [];

  const areFavoritesVodsVisible = withFavoriteVods && favoritesVods.length > 0;

  if (!areFavoritesVodsVisible) {
    return null;
  }

  return (
    <PosterPreviewSlider isFetching={isFetching} title={messages.favorites}>
      {favoritesVods.map((vod) => (
        <S.VodPosterContainer
          key={vod.assetExternalId}
          onClick={() =>
            setDataDetailsView({
              type: DetailsViewType.VOD,
              id: vod.assetExternalId,
            })
          }
        >
          <VodPosterPreview
            title={vod.vodName}
            src={getPublicAssetUrl(vod.coverImageVerticalPath)}
            id={vod.assetExternalId}
          />
        </S.VodPosterContainer>
      ))}
    </PosterPreviewSlider>
  );
};
