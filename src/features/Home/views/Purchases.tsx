import { PosterPreviewSlider } from 'components/PosterPreviewSlider';
import { useUserPurchasesQuery } from 'services/api/oldApi/vod';
import { DetailsViewType, useDetailsView } from 'services/detailsView';
import { useUserProfile } from 'services/user';
import { getPublicAssetUrl } from 'utils/url';
import { getTimeRemainingInHours } from 'utils/dateUtils';
import { VodPosterPreview } from 'components/VodPosterPreview';

import * as S from './styles';

import { messages } from '../messages';

export const Purchases = () => {
  const { setData: setDataDetailsView } = useDetailsView();

  const { data, isFetching } = useUserPurchasesQuery();
  const purchases = data?.assetPurchasedList ?? [];

  const areVodPurchasesVisible = purchases.length > 0;

  if (areVodPurchasesVisible) {
    return (
      <PosterPreviewSlider
        isFetching={isFetching}
        title={messages.rentedMovies}
      >
        {purchases.map((asset) => (
          <S.VodPosterContainer
            key={asset?.vodAsset?.assetExternalId}
            onClick={() =>
              setDataDetailsView({
                type: DetailsViewType.VOD,
                id: asset.vodAsset.assetExternalId,
              })
            }
          >
            <VodPosterPreview
              title={asset.vodAsset.name}
              src={getPublicAssetUrl(
                asset?.vodAsset?.vodCovers?.verticalImagePath,
              )}
              remainingTime={getTimeRemainingInHours(
                asset.versions[0].purchasedUntil,
              )}
              isTicketActive={asset.versions[0].isTicketActive}
              id={asset?.vodAsset?.assetExternalId}
            />
          </S.VodPosterContainer>
        ))}
      </PosterPreviewSlider>
    );
  }

  return null;
};
