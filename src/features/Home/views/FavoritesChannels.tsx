import { useIntl } from 'react-intl';

import { ChannelsGrid } from 'components/ChannelsGrid';

import * as S from './styles';
import { useFavoritesChannels } from './hooks';

import { messages } from '../messages';

export const FavoritesChannels = () => {
  const { formatMessage } = useIntl();
  const { favoritesChannelsWithDetails, isDataFetching } =
    useFavoritesChannels();

  return (
    <ChannelsGrid
      channels={favoritesChannelsWithDetails}
      isFetching={isDataFetching}
      title={formatMessage(messages.favoritesChannels)}
      isSlider={true}
      amountLines={1}
      noDataMessage={formatMessage(messages.noFavoritesChannels, {
        starIcon: <S.StyledIconStar isEmpty={false} width={22} />,
      })}
    />
  );
};
