import styled from 'styled-components';

import { devices, RenderLayer } from 'theme';
import { CategoryButton } from 'components/Buttons/CategoryButton';
import { Text } from 'components/Typography';
import { convertHexToRgbaString } from 'theme/colors';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 2.4rem;
`;

export const ControlsContainer = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  position: absolute;
  top: 0;
  right: 16px;

  ${RenderLayer('appBase')};

  & > button {
    margin-right: 1.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.4rem;
  }

  @media ${devices.tabletS} {
    flex-direction: row;
  }
`;

export const Header = styled.div`
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-bottom: 2.4rem;
  line-height: normal;
`;

export const CategoryButtonStyles = styled(CategoryButton).attrs((props) => ({
  $category: props.$category,
}))`
  min-width: 86px;
  padding: 1.6rem;
`;

export const NoDataInfo = styled(Text)`
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${({ theme }) => convertHexToRgbaString(theme.colors.white, 0.8)};
  min-height: 11rem;
`;

Container.displayName = 'RecentlyWatchedContainer';
ControlsContainer.displayName = 'RecentlyWatchedControlsContainer';
Header.displayName = 'RecentlyWatchedHeader';
