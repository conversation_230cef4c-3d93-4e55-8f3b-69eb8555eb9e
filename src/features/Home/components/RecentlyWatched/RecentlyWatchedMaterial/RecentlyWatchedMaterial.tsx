import { useIntl } from 'react-intl';

import { PosterPreview } from 'components/PosterPreview';
import { getVideoProgress } from 'utils/math';
import { IconBin, IconPlay } from 'components/Icons';
import { DescribedIconButton } from 'components/Buttons/DescribedIconButton';
import { useTheme } from 'theme';
import { createProgramName } from 'utils/misc/createProgramName';
import { getPublicAssetUrl } from 'utils/url';

import * as S from './styles';
import { RecentlyWatchedMaterialProps } from './types';
import { useRecentlyWatchedMaterial } from './hooks';
import { messages } from './messages';

export const RecentlyWatchedMaterial = ({
  program,
  handlePlay,
}: RecentlyWatchedMaterialProps) => {
  const appTheme = useTheme();
  const { formatMessage } = useIntl();

  const {
    setIsHover,
    isHover,
    handleDelete,
    isBeforeDelete,
    handleDeleteButton,
    beforeDeleteOverlay,
  } = useRecentlyWatchedMaterial(program);

  const shouldShowBin =
    (program.type === 'vod' && !program.persistent) || program.type !== 'vod';

  const programTitle =
    program.type !== 'vod'
      ? createProgramName(program.title, program.episodeNumber)
      : program.title;

  const ProgramOverlayOnHover = (
    <S.ImageOverlay>
      <DescribedIconButton
        text={formatMessage(messages.play)}
        onClick={() => handlePlay(program.key)}
      >
        <S.PlayIconWrapper>
          <IconPlay color={appTheme.colors.black} />
        </S.PlayIconWrapper>
      </DescribedIconButton>
      {shouldShowBin && (
        <DescribedIconButton
          text={formatMessage(messages.delete)}
          onClick={handleDeleteButton}
        >
          <IconBin color={appTheme.colors.black} />
        </DescribedIconButton>
      )}
    </S.ImageOverlay>
  );

  const BeforeDeleteOverlay = (
    <S.ImageOverlay ref={beforeDeleteOverlay}>
      <S.ConfirmDeleteWrapper>
        <S.StyledText $sizeXXSmall>
          {formatMessage(messages.deleteConfirmation)}
        </S.StyledText>
        <DescribedIconButton
          text={formatMessage(messages.delete)}
          onClick={handleDelete}
          iconColorHover={appTheme.colors.white}
          circleColorHover={appTheme.colors.red}
          circleColor={appTheme.colors.white}
        >
          <IconBin color={appTheme.colors.red} />
        </DescribedIconButton>
      </S.ConfirmDeleteWrapper>
    </S.ImageOverlay>
  );

  return (
    <S.RecentlyWatchedMaterial
      key={program.key}
      $withBorder={!!program.isVertical}
      onMouseEnter={() => {
        setIsHover(true);
      }}
      onMouseLeave={() => {
        setIsHover(false);
      }}
    >
      <S.ImageWrapper>
        {isHover && !isBeforeDelete && ProgramOverlayOnHover}
        {isBeforeDelete && BeforeDeleteOverlay}
        {program.videoDuration && program.resumePosition && (
          <S.ProgressBar
            aria-label='Progress of video'
            value={getVideoProgress(
              program.videoDuration,
              program.resumePosition,
            )}
            max='100'
          />
        )}
        <PosterPreview
          src={getPublicAssetUrl(program.src)}
          title={programTitle}
          scaleWidth={true}
        />
      </S.ImageWrapper>
    </S.RecentlyWatchedMaterial>
  );
};
