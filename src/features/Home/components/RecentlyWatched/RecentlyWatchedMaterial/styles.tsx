import styled, { css } from 'styled-components';

import { convertHexToRgbaString, RenderLayer } from 'theme';
import { Text } from 'components/Typography';

export const ImageOverlay = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 4.8rem;
  position: absolute;
  width: 100%;
  height: calc(100% - 3.5rem);
  background-color: ${({ theme }) =>
    convertHexToRgbaString(theme.colors.black, 0.8)};
  border: 0.4rem solid ${({ theme }) => theme.colors.primary};
  ${RenderLayer('appBase')};
`;

export const RecentlyWatchedMaterial = styled.div<{
  $withBorder: boolean;
}>`
  position: relative;
  margin-right: 1.6rem;

  & > div > div > img {
    ${({ $withBorder, theme }) =>
      $withBorder &&
      css`
        border: 1px solid ${theme.colors.white80};
      `};
  }
  min-width: 35.5rem;
  max-width: 35.5rem;
`;

export const ProgressBar = styled.progress`
  position: absolute;
  width: 100%;
  height: 0.6rem;
  bottom: 3.5rem;
  left: 0;
  right: 0;
  appearance: none;
  border: none;
  background: transparent;

  ${RenderLayer('appBase')};

  &::-moz-progress-bar {
    background: ${({ theme }) => theme.colors.primary};
  }
  &::-webkit-progress-value {
    background: ${({ theme }) => theme.colors.primary};
  }
  &::-webkit-progress-bar {
    background: transparent;
  }
`;

export const PlayIconWrapper = styled.div`
  margin-left: 0.8rem;
`;

export const ImageWrapper = styled.div`
  position: relative;
  height: 100%;
  max-height: 25rem;
`;

export const TitleContainer = styled.div`
  margin-top: 1.2rem;
  padding-left: 0.4rem;
`;

export const ConfirmDeleteWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const StyledText = styled(Text)`
  margin-bottom: 2rem;
`;

ProgressBar.displayName = 'RecentlyWatchedProgressBar';
RecentlyWatchedMaterial.displayName = 'RecentlyWatchedRecentlyWatchedMaterial';
