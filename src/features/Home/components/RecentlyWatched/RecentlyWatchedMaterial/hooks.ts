import { MouseEvent, useCallback, useRef, useState } from 'react';

import {
  useDeleteRecentlyWatchedChannel,
  useDeleteRecentlyWatchedRecording,
  useDeleteRecentlyWatchedVod,
} from 'services/api/oldApi/myzone';
import { useOutsideClick } from 'hooks/useOutsideClick';
import { SlideCategory } from 'features/Home/views/types';

import { Slide } from '../types';

export const useRecentlyWatchedMaterial = (program: Slide) => {
  const beforeDeleteOverlay = useRef<HTMLDivElement>(null);
  const [isHover, setIsHover] = useState(false);
  const [isBeforeDelete, setIsBeforeDelete] = useState(false);

  const { mutateAsync: deleteProgramFromRecentlyWatched } =
    useDeleteRecentlyWatchedChannel();
  const { mutateAsync: deleteRecordingFromRecentlyWatched } =
    useDeleteRecentlyWatchedRecording();
  const { mutateAsync: deleteVodFromRecentlyWatched } =
    useDeleteRecentlyWatchedVod();

  useOutsideClick(beforeDeleteOverlay, () => {
    setIsBeforeDelete(false);
  });

  const handleDeleteButton = useCallback((event: MouseEvent) => {
    event.stopPropagation();
    setIsBeforeDelete(true);
  }, []);

  const handleDelete = () => {
    const programType = program.type;

    switch (programType) {
      case SlideCategory.Program:
        const { channelExtId, programExtId } = program;
        return deleteProgramFromRecentlyWatched({
          channelExtId,
          programExtId,
        });

      case SlideCategory.Recording:
        const { recordingExtId } = program;
        return deleteRecordingFromRecentlyWatched({
          otfRecordingId: recordingExtId,
        });

      case SlideCategory.Vod:
        const { videoExternalId } = program;
        return deleteVodFromRecentlyWatched({ videoExternalId });

      default:
        const exhaustiveCheck: never = programType;
        throw new Error(`Invalid program type: ${exhaustiveCheck}`);
    }
  };

  return {
    setIsHover,
    isHover,
    isBeforeDelete,
    setIsBeforeDelete,
    handleDelete,
    handleDeleteButton,
    beforeDeleteOverlay,
  };
};
