import { SlideCategory } from 'features/Home/views/types';
import {
  AssetId,
  ChannelId,
  ProgramId,
  RecordingId,
  TimeInSeconds,
  Timestamp,
  VideoId,
} from 'services/api/common/types';
import {
  RecentlyWatchedProgram,
  RecentlyWatchedRecording,
  RecentlyWatchedVod,
} from 'services/api/oldApi/myzone';

export interface RecentlyWatchedProps {
  programs: Array<RecentlyWatchedProgram>;
  recordings: Array<RecentlyWatchedRecording>;
  vod: Array<RecentlyWatchedVod>;
  activeCategory: SlideCategory;
  handleCategoryChange: React.Dispatch<React.SetStateAction<SlideCategory>>;
}

type BaseSlide = {
  key: string;
  src: string;
  title: string;
  isVertical?: boolean;
  videoDuration: number;
  resumePosition: TimeInSeconds;
  audioLang: string;
  subsLang: string;
};

export type ProgramSlide = BaseSlide & {
  type: SlideCategory.Program;
  channelExtId: ChannelId;
  programExtId: ProgramId;
  startTimestamp: Timestamp;
  endTimestamp: Timestamp;
  isStartOver: boolean;
  isCatchup: boolean;
  episodeNumber: string;
};

export type RecordingSlide = BaseSlide & {
  type: SlideCategory.Recording;
  channelExtId: ChannelId;
  recordingExtId: RecordingId;
  startTimestamp: Timestamp;
  episodeNumber: string;
};

export type VodSlide = BaseSlide & {
  type: SlideCategory.Vod;
  assetExternalId: AssetId;
  videoExternalId: VideoId;
  persistent: boolean;
};

export type Slide = ProgramSlide | RecordingSlide | VodSlide;

export interface ButtonControl {
  category: SlideCategory;
  isVisible: boolean;
  text: string;
}
