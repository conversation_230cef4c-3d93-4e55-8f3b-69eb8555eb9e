import { useIntl } from 'react-intl';

import { Slider } from 'components/Slider';
import { H2, Text } from 'components/Typography';
import { SelectContext } from 'components/Slider/hooks';

import { useRecentlyWatched } from './hooks';
import { messages } from './messages';
import * as S from './styles';
import { RecentlyWatchedProps } from './types';
import { RecentlyWatchedMaterial } from './RecentlyWatchedMaterial';

export const RecentlyWatched = ({
  programs,
  recordings,
  vod,
  activeCategory,
  handleCategoryChange,
}: RecentlyWatchedProps) => {
  const { formatMessage } = useIntl();
  const {
    slides,
    buttonsControl,
    setIsControlVisibleOnMobile,
    handleClickSingleSlide,
  } = useRecentlyWatched(programs, recordings, vod, activeCategory);

  const slidesRender = (
    <SelectContext.Consumer>
      {({ handleSlideClick }) =>
        slides.map((program) => (
          <RecentlyWatchedMaterial
            key={program.key}
            program={program}
            handlePlay={handleSlideClick}
          />
        ))
      }
    </SelectContext.Consumer>
  );

  if (slides.length === 0) {
    return (
      <S.Container data-testid='recentlyWatched'>
        <H2>{formatMessage(messages.header)}</H2>
        <S.NoDataInfo>
          {formatMessage(messages.noRecentlyWatchedPrograms)}
        </S.NoDataInfo>
      </S.Container>
    );
  }

  const buttonsRender = buttonsControl
    .filter((button) => button.isVisible)
    .map((button) => {
      const { category } = button;

      return (
        <S.CategoryButtonStyles
          key={category}
          $category={category}
          active={activeCategory === category}
          onClick={() => {
            handleCategoryChange(category);
          }}
        >
          <Text>{button.text}</Text>
        </S.CategoryButtonStyles>
      );
    });

  return (
    <S.Container>
      <S.Header>
        <H2>{formatMessage(messages.header)}</H2>
        <S.ControlsContainer
          onMouseEnter={() => setIsControlVisibleOnMobile(true)}
          onMouseLeave={() => setIsControlVisibleOnMobile(false)}
        >
          {buttonsRender}
        </S.ControlsContainer>
      </S.Header>
      <Slider
        onSlideClick={handleClickSingleSlide}
        reset={activeCategory}
        withButtons
      >
        {slidesRender}
      </Slider>
    </S.Container>
  );
};
