import { millisecondsToSeconds } from 'date-fns';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import { usePlayContent } from 'features/Player/Hook';
import useWindowSize from 'hooks/useWindowSize/useWindowSize';
import {
  RecentlyWatchedProgram,
  RecentlyWatchedRecording,
  RecentlyWatchedVod,
} from 'services/api/oldApi/myzone';
import { breakpointsNumbers } from 'theme';
import { getPublicAssetUrl } from 'utils/url';
import { SlideCategory } from 'features/Home/views/types';
import { OnSlideClick } from 'components/Slider/types';

import { messages } from './messages';
import { ButtonControl, Slide } from './types';

export const useRecentlyWatched = (
  programData: Array<RecentlyWatchedProgram>,
  recordingData: Array<RecentlyWatchedRecording>,
  vodData: Array<RecentlyWatchedVod>,
  activeCategory: SlideCategory,
) => {
  const intl = useIntl();

  const [width] = useWindowSize();
  const { playChannel, playRecording, playCatchup, playStartover, playVod } =
    usePlayContent();
  const programSlideCount = useMemo(
    () => programData?.length | 0,
    [programData],
  );
  const recordingSlideCount = useMemo(
    () => recordingData?.length | 0,
    [recordingData],
  );
  const vodSlideCount = useMemo(() => vodData?.length | 0, [vodData]);

  const availableButtons: Array<ButtonControl> = useMemo(
    () => [
      {
        category: SlideCategory.Program,
        isVisible: Boolean(programSlideCount),
        text: intl.formatMessage(
          width < breakpointsNumbers.large
            ? messages.category.programMobile
            : messages.category.program,
        ),
      },
      {
        category: SlideCategory.Recording,
        isVisible: Boolean(recordingSlideCount),
        text: intl.formatMessage(
          width < breakpointsNumbers.large
            ? messages.category.recordingMobile
            : messages.category.recording,
        ),
      },
      {
        category: SlideCategory.Vod,
        isVisible: Boolean(vodSlideCount),
        text: intl.formatMessage(messages.category.vod),
      },
    ],
    [intl, programSlideCount, recordingSlideCount, vodSlideCount, width],
  );

  const [slides, setSlides] = useState<Array<Slide>>([]);
  const [buttonsControl, setButtonControl] =
    useState<ButtonControl[]>(availableButtons);
  const [isControlsVisibleOnMobile, setIsControlVisibleOnMobile] =
    useState(false);

  const handleClickSingleSlide: OnSlideClick = useCallback(
    (param) => {
      if (typeof param !== 'string') {
        throw new Error(
          'Invalid param for recently watched slide - expected string',
        );
      }
      const id = param;
      const clickedSlide = slides.find((slide) => slide.key === id);

      if (clickedSlide) {
        switch (clickedSlide.type) {
          case SlideCategory.Vod: {
            const { videoExternalId, assetExternalId } = clickedSlide;

            return playVod({
              assetExternalId: assetExternalId,
              versionExternalId: videoExternalId,
            });
          }
          case SlideCategory.Recording: {
            const {
              channelExtId,
              recordingExtId,
              resumePosition: startPosition,
            } = clickedSlide;

            return playRecording({
              recordingExtId,
              channelExtId,
              startPosition,
            });
          }
          case SlideCategory.Program: {
            const {
              channelExtId,
              programExtId,
              endTimestamp,
              resumePosition: startPosition,
              isCatchup,
              isStartOver,
            } = clickedSlide;

            const currentTime = millisecondsToSeconds(Date.now());

            switch (true) {
              case isCatchup && currentTime > endTimestamp:
                return playCatchup({
                  channelExtId,
                  programExtId,
                  startPosition,
                  programEndDate: endTimestamp,
                });

              case isStartOver:
                return playStartover({
                  channelExtId,
                  programExtId,
                  startPosition,
                });

              default:
                return playChannel({
                  channelExtId,
                  programExtId,
                  isFromRecentlyWatched: true,
                });
            }
          }
        }
      }
    },
    [playCatchup, playChannel, playRecording, playStartover, playVod, slides],
  );

  useEffect(() => {
    switch (activeCategory) {
      case 'program':
        setSlides(
          programData?.map((program) => ({
            type: activeCategory,
            key: program.programRefId,
            src: getPublicAssetUrl(program.programImgPath),
            title: program.programName,
            episodeNumber: program.episodeNumber,
            videoDuration: program.endTimestamp - program.startTimestamp,
            resumePosition: program.resumePosition,
            channelExtId: program.channelExternalId,
            startTimestamp: program.startTimestamp,
            endTimestamp: program.endTimestamp,
            programExtId: program.programRefId,
            isStartOver: program.isStartover,
            isCatchup: program.isCatchup,
            audioLang: program.audioLang,
            subsLang: program.subsLang,
          })),
        );
        break;
      case 'recording':
        setSlides(
          recordingData?.map((recording) => ({
            type: activeCategory,
            key: recording.otfRecordingId,
            src: getPublicAssetUrl(recording.programImg),
            title: recording.programName,
            videoDuration: recording.endTimestamp - recording.startTimestamp,
            resumePosition: recording.resumePosition,
            channelExtId: recording.channelExternalId,
            recordingExtId: recording.otfRecordingId,
            startTimestamp: recording.startTimestamp,
            audioLang: recording.audioLang,
            subsLang: recording.subsLang,
            episodeNumber: recording.episodeNumber,
          })),
        );
        break;
      case 'vod':
        setSlides(
          vodData?.map((vod) => ({
            type: activeCategory,
            key: vod.assetExternalId,
            src: getPublicAssetUrl(
              vod.bannerImagePath
                ? vod.bannerImagePath
                : vod.coverImageVerticalPath,
            ),
            title: vod.title,
            isVertical: !vod.bannerImagePath,
            videoDuration: vod.msDuration,
            resumePosition: vod.msPosition,
            assetExternalId: vod.assetExternalId,
            videoExternalId: vod.videoExternalId,
            audioLang: vod.audioLang,
            subsLang: vod.subsLang,
            persistent: vod.persistent,
          })),
        );
        break;
    }
  }, [activeCategory, programData, recordingData, vodData]);

  useEffect(() => {
    if (width < breakpointsNumbers.large && !isControlsVisibleOnMobile) {
      const newButtons = availableButtons.filter(
        (button) => button.category === activeCategory,
      );
      setButtonControl(newButtons);
      return;
    }

    setButtonControl(availableButtons);
  }, [isControlsVisibleOnMobile, activeCategory, availableButtons, width]);

  return {
    slides,
    buttonsControl,
    setIsControlVisibleOnMobile,
    handleClickSingleSlide,
  };
};

export const useRecentlyWatchedCategory = (
  programs: Array<RecentlyWatchedProgram>,
  recordings: Array<RecentlyWatchedRecording>,
  vod: Array<RecentlyWatchedVod>,
) => {
  const [activeCategory, setActiveCategory] = useState<SlideCategory>(
    SlideCategory.Program,
  );
  const dataSets = useMemo(
    () => [
      { category: SlideCategory.Program, data: programs },
      { category: SlideCategory.Recording, data: recordings },
      { category: SlideCategory.Vod, data: vod },
    ],
    [programs, recordings, vod],
  );

  useEffect(() => {
    const emptyDataSet = dataSets.find((dataSet) => dataSet.data.length === 0);
    const filledDataSet = dataSets.find((dataSet) => dataSet.data.length !== 0)
      ?.category;

    if (emptyDataSet && filledDataSet) {
      setActiveCategory(filledDataSet);
    }
  }, [dataSets]);

  return { activeCategory, setActiveCategory };
};
