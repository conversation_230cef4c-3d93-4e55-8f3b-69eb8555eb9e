import { Story, Meta } from '@storybook/react';

import { PromoSlider } from './PromoSlider';
import { PromoSliderProps } from './types';

export default {
  title: 'Components/PromoSlider',
  component: PromoSlider,
} as Meta;

const data: PromoSliderProps['banners'] = [
  {
    title: 'title 1',
    imagePath: '/mnapi-dev/promo/73cd802c-4497-4689-8c59-f91b61012175',
    action: {
      type: 'vod-details',
      value: '20210226CZYSCIEC2',
    },
    visibleSinceTimestamp: 0,
    visibleUntilTimestamp: 0,
  },
  {
    title: 'title 2',
    imagePath: '/mnapi-dev/promo/73cd802c-4497-4689-8c59-f91b61012175',
    action: {
      type: 'vod-details',
      value: '20210226CZYSCIEC2',
    },
    visibleSinceTimestamp: 0,
    visibleUntilTimestamp: 0,
  },
  {
    title: 'title 3',
    imagePath: '/mnapi-dev/promo/73cd802c-4497-4689-8c59-f91b61012175',
    action: {
      type: 'vod-details',
      value: '20210226CZYSCIEC2',
    },
    visibleSinceTimestamp: 0,
    visibleUntilTimestamp: 0,
  },
];

const Template: Story = ({ dataArr }) => <PromoSlider banners={dataArr} />;

export const Default = Template.bind({});
Default.args = {
  isFetching: false,
  dataArr: data,
};
