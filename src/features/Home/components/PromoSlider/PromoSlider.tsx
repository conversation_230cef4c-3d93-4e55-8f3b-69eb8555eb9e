import { useIntl } from 'react-intl';

import { useBannerAction } from 'hooks/useBannerAction';
import { Image } from 'components/Image';
import { Slider } from 'components/Slider';
import { SelectContext } from 'components/Slider/hooks';
import { H2 } from 'components/Typography';

import { messages } from './messages';
import * as S from './styles';
import { PromoSliderProps } from './types';

export const PromoSlider = (props: PromoSliderProps) => {
  const { banners, ...rest } = props;

  const intl = useIntl();
  const { onSlideClick } = useBannerAction({ banners });

  return (
    <S.Container>
      <H2>{intl.formatMessage(messages.title)}</H2>
      <Slider
        {...rest}
        onSlideClick={onSlideClick}
        slidesToScroll={1}
        autoScrollTime={10000}
        withButtons
        loop
      >
        <SelectContext.Consumer>
          {({ handleSlideClick }) =>
            banners.map((banner, index) => (
              <S.PromoContainer
                key={banner.imageId}
                onClick={() => handleSlideClick(banner.action)}
              >
                <Image
                  src={banner.image}
                  alt={
                    intl.formatMessage(messages.bannerDescription) +
                    `${index + 1}`
                  }
                  photoRatio={9 / 21}
                  data-testid='PromoSlider-PromoImage'
                />
              </S.PromoContainer>
            ))
          }
        </SelectContext.Consumer>
      </Slider>
    </S.Container>
  );
};
