import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import {
  Banner,
  BannerActionType,
  LocationType,
} from 'services/api/newApi/optional/banners/types';

import { PromoSlider } from '../PromoSlider';

const dummyImageData: Banner = {
  type: LocationType.TOP_LINE,
  view: 'home',
  image: '/mnapi/btm-007/46f2226992e6633d019bb3b41306ac58',
  imageId: '46f2226992e6633d019bb3b41306ac58',
  startTimestamp: 1739794500,
  endTimestamp: 1766185259,
  action: {
    type: BannerActionType.LIVE,
    lcn: 1,
  },
};

describe('Components:PromoSlider', () => {
  it('should render PromoSlider component with title', () => {
    render(<PromoSlider banners={[dummyImageData]} />);
    expect(screen.getByText('Nowości')).toBeInTheDocument();
  });

  it('should render slides', () => {
    render(<PromoSlider banners={[dummyImageData]} />);
    const sliderTile = screen.getByAltText('Promo banner 1');
    expect(sliderTile).toBeInTheDocument();
  });
});
