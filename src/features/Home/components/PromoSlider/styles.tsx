import styled from 'styled-components';

import { devices } from 'theme';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: -1.4rem;

  @media ${devices.tabletS} {
    margin-left: 0;
  }
`;

export const PromoContainer = styled.div`
  cursor: pointer;
  flex-grow: 0;
  flex-shrink: 0;
  padding-right: 1.6rem;
  width: 100%;

  @media ${devices.mobileM} {
    width: 100%;
  }
  @media ${devices.mobileL} {
    width: 50%;
  }
`;

Container.displayName = 'PromoSlider';
PromoContainer.displayName = 'PromoContainer';
