import { FC } from 'react';
import { useIntl } from 'react-intl';

import { Image } from 'components/Image';
import { Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { DeviceType } from 'hooks/useMobileDetector';
import { redirectToPage } from 'utils/redirectToPage';
import { getStaticImageUrl } from 'utils/url/asset';
import { ANDROID_APP_URL, IOS_APP_URL } from 'services/config/config';

import * as S from './styles';
import { MobileDisabledViewProps } from './types';
import {
  OTG_APPS_1X_IMAGE_NAME,
  OTG_APPS_2X_IMAGE_NAME,
  OTG_APPS_3X_IMAGE_NAME,
  OTG_LOGO_IMAGE_NAME,
} from './constatns';
import { messages } from './messages';

export const MobileDisabledView: FC<MobileDisabledViewProps> = ({
  deviceType,
}) => {
  const { formatMessage } = useIntl();

  const SelectedLink = {
    [DeviceType.android]: ANDROID_APP_URL,
    [DeviceType.iOS]: IOS_APP_URL,
  };

  return (
    <S.Container>
      <S.ContentWrapper>
        <S.TitleWrapper>
          <S.LogoWrapper>
            <Image src={getStaticImageUrl(OTG_LOGO_IMAGE_NAME)} />
          </S.LogoWrapper>
          <Text>{formatMessage(messages.title)}</Text>
        </S.TitleWrapper>
        <S.SubtitleWrapper>
          <Text $sizeSmall>{formatMessage(messages.subtitle)}</Text>
        </S.SubtitleWrapper>
        <S.DescriptionWrapper>
          <Text $sizeXXSmall>{formatMessage(messages.description)}</Text>
        </S.DescriptionWrapper>
        <S.ButtonWrapper>
          <PrimaryButton
            variant='orange'
            onClick={() => redirectToPage(SelectedLink[deviceType], false)}
          >
            <Text $sizeXXSmall>{formatMessage(messages.downloadButton)}</Text>
          </PrimaryButton>
        </S.ButtonWrapper>
      </S.ContentWrapper>
      <S.OtgPromoImageWrapper>
        <Image
          srcSet={`${getStaticImageUrl(
            OTG_APPS_1X_IMAGE_NAME,
          )}, ${getStaticImageUrl(
            OTG_APPS_2X_IMAGE_NAME,
          )} 2x, ${getStaticImageUrl(OTG_APPS_3X_IMAGE_NAME)} 3x`}
          src={getStaticImageUrl(OTG_APPS_3X_IMAGE_NAME)}
        />
      </S.OtgPromoImageWrapper>
    </S.Container>
  );
};
