import styled from 'styled-components';

import { devices } from 'theme/media';

export const Container = styled.div`
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 32px;
  @media ${devices.mobileM} {
    padding: 48px;
  }
  @media (orientation: landscape) {
    flex-direction: row;
    padding: 0;
  }
`;

export const TitleWrapper = styled.div`
  display: flex;

  align-items: center;
  width: 100%;
  transform: translateX(-12px);
  margin-bottom: 24px;

  span {
    white-space: nowrap;
    font-weight: bold;
  }
  @media ${devices.mobileM} {
    margin-bottom: 32px;
    span {
      font-size: 40px;
    }
  }
`;

export const LogoWrapper = styled.div`
  width: 64px;
  height: 64px;
  @media ${devices.mobileM} {
    width: 96px;
    height: 96px;
  }
`;

export const SubtitleWrapper = styled.div`
  width: 100%;
  margin-bottom: 24px;
  span {
    font-weight: bold;
  }
  @media ${devices.mobileM} {
    margin-bottom: 32px;
    span {
      font-size: 34px;
    }
  }
`;

export const DescriptionWrapper = styled.div`
  width: 100%;
  margin-bottom: 24px;
  span {
    @media ${devices.mobileM} {
      font-size: 18px;
    }
  }
`;

export const ButtonWrapper = styled.div`
  width: 100%;
  margin-bottom: 32px;
  button {
    width: 100%;
  }
  @media ${devices.mobileM} {
    margin-bottom: 88px;
    button {
      width: 296px;
      height: 50px;
    }
  }
`;

export const OtgPromoImageWrapper = styled.div`
  img {
    object-fit: contain;
  }
  @media ${devices.mobileM} {
    width: 100%;
    @media (orientation: landscape) {
      width: 700px;
      padding: 16px;
    }
  }
`;

export const ContentWrapper = styled.div`
  min-width: 256px;
  @media (orientation: landscape) {
    margin: auto 32px;
  }
`;
