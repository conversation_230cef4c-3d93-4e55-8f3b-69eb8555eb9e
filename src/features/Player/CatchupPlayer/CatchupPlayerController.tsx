import { FC, PropsWithChildren } from 'react';

import {
  useLiveContinueWatchingHandler,
  useProgramEndEvent,
  useRefreshLiveSession,
} from 'features/Player/Hook';

import { CatchupPlayerControllerProps } from './types';

import { useContinueEndedContent } from '../Hook/useContinueEndedProgram';
import { PlayerType } from '../types';

export const CatchupPlayerController: FC<
  PropsWithChildren<CatchupPlayerControllerProps>
> = ({ playback, children }) => {
  const { channelExtId } = playback;

  useProgramEndEvent({ timerType: 'seconds' });
  useContinueEndedContent(PlayerType.Catchup);
  useRefreshLiveSession({ channelExtId });
  useLiveContinueWatchingHandler();

  return <>{children}</>;
};
