import { CatchupPlayback } from 'features/Player/Context';
import { ChannelPlayInfoResponse } from 'services/api/newApi/live/channels';
import { ProgramDetails } from 'services/api/newApi/core/tvguide';

export interface CatchupPlayerProps {
  playback: CatchupPlayback;
}

export interface CatchupPlayerWrapperProps {
  playback: CatchupPlayback;
}

export interface CatchupPlayerInnerWrapperProps {
  playback: CatchupPlayback;
  playInfo: ChannelPlayInfoResponse;
  program: ProgramDetails;
  refetchToken: () => void;
}

export interface CatchupPlayerControllerProps {
  playback: CatchupPlayback;
}
