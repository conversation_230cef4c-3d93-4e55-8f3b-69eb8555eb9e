import React, { FC, PropsWithChildren } from 'react';

import { BasePlayer } from 'features/Player/BasePlayer';
import {
  PlayerFeaturesProvider,
  PlayerProgramProvider,
  usePlayerProgram,
} from 'features/Player/Context';
import { useHandlePlayInfoError, useProgramTimer } from 'features/Player/Hook';
import { useUserProfile } from 'services/user/UserProfileContext/UserProfileContext';
import { useChannelTimeShiftingPlayInfo } from 'services/api/newApi/live/channels';

import { CatchupPlayerController } from './CatchupPlayerController';
import {
  CatchupPlayerInnerWrapperProps,
  CatchupPlayerProps,
  CatchupPlayerWrapperProps,
} from './types';

export const CatchupPlayerInnerWrapper: FC<
  PropsWithChildren<CatchupPlayerInnerWrapperProps>
> = (props) => {
  const { playback, playInfo, program, refetchToken, children } = props;
  const { startPosition: playbackStartPosition, loop } = playback;
  const { startTimeUtc, endTimeUtc } = program;

  const { getResumePosition, startPosition } = useProgramTimer({
    startTimeUtc,
    endTimeUtc,
  });

  const resumePosition = playbackStartPosition
    ? getResumePosition(playbackStartPosition)
    : startPosition;

  return (
    <BasePlayer
      mediaUrl={playInfo.streamUrl}
      casToken={playInfo.casToken}
      startPosition={resumePosition}
      refetchToken={refetchToken}
      loop={loop}
    >
      <CatchupPlayerController playback={playback}>
        {children}
      </CatchupPlayerController>
    </BasePlayer>
  );
};

export const CatchupPlayerWrapper: FC<
  PropsWithChildren<CatchupPlayerWrapperProps>
> = (props) => {
  const { playback, children } = props;
  const { channelExtId, programExtId } = playback;

  const { handlePlayInfoError } = useHandlePlayInfoError();
  const { userHhTech } = useUserProfile();
  const { program, channel } = usePlayerProgram();

  const { data: playInfo, refetch: refetchPlayInfo } =
    useChannelTimeShiftingPlayInfo(
      {
        channelExtId,
        programExtId,
        timeShiftingService: 'catchup',
      },
      true,
      (error: unknown) => handlePlayInfoError(error, refetchPlayInfo),
    );

  if (!playInfo) {
    return <></>;
  }

  return (
    <PlayerFeaturesProvider
      channel={channel}
      program={program}
      withConfigLagTime
      withConfigLeadTime
    >
      <CatchupPlayerInnerWrapper
        playback={playback}
        playInfo={playInfo}
        program={program}
        refetchToken={refetchPlayInfo}
      >
        {children}
      </CatchupPlayerInnerWrapper>
    </PlayerFeaturesProvider>
  );
};

export const CatchupPlayer: FC<PropsWithChildren<CatchupPlayerProps>> = (
  props,
) => {
  const { playback, children } = props;
  const { channelExtId, programExtId } = playback;

  return (
    <PlayerProgramProvider channelId={channelExtId} programId={programExtId}>
      <CatchupPlayerWrapper playback={playback}>
        {children}
      </CatchupPlayerWrapper>
    </PlayerProgramProvider>
  );
};
