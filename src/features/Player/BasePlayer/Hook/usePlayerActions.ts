import { useCallback, useEffect } from 'react';
import { millisecondsToSeconds } from 'date-fns';

import {
  usePlayerInstance,
  usePlayerState,
  usePlayerTimeLimit,
} from 'features/Player/BasePlayer/Context';
import { usePlayerPlayback, usePlayerProgram } from 'features/Player/Context';
import { PlayerState } from 'features/Player/types';
import { useToggleFullScreen } from 'hooks/useToggleFullScreen';
import { TimeInSeconds } from 'services/api/common/types';
import { usePlayerSettingsValues } from 'features/Player/Interface/SettingsMenu';
import { usePlayerOverlay } from 'features/Player/PlayerOverlay/PlayerOverlayContext';
import { PlayerOverlayContent } from 'features/Player/PlayerOverlay/types';
import { DEFAULT_VOLUME_SETTINGS } from 'features/Player/Interface/SettingsMenu/constants';

import { usePlayerActionsValue } from './types';

export const usePlayerActions = (): usePlayerActionsValue => {
  const { player, containerRef } = usePlayerInstance();
  const { loadCurrentProgram } = usePlayerProgram();
  const { setState } = usePlayerState();
  const { toggle: toggleFullScreen } = useToggleFullScreen(
    containerRef?.current,
  );
  const { setPlayerOverlay } = usePlayerOverlay();
  const { volumeSettings, setVolumeSettings } = usePlayerSettingsValues();
  const { setPlayback } = usePlayerPlayback();
  const { resetTimer } = usePlayerTimeLimit();

  const playPause = useCallback(() => {
    if (player) {
      player.playing ? player.pause() : player.play();
      resetTimer();
    }
  }, [player, resetTimer]);

  const stop = useCallback(() => {
    if (player) {
      // Player emit for ended event not working here, so we need to set
      // player state manually also before pause because pause will override
      // player state
      setState(PlayerState.Ended);
      player.pause();
      resetTimer();
    }
  }, [player, resetTimer, setState]);

  const jumpToLive = useCallback(() => {
    if (player && player.isLive()) {
      player.currentTime = millisecondsToSeconds(Date.now());
      loadCurrentProgram();
      resetTimer();
    }
  }, [loadCurrentProgram, player, resetTimer]);

  const jumpTo = useCallback(
    (time: TimeInSeconds) => {
      if (player) {
        player.currentTime = time;
        resetTimer();
      }
    },
    [player, resetTimer],
  );

  const saveVolumeValue = useCallback(() => {
    if (player) {
      const volumeData = {
        isMuted: player.muted,
        volumeValue: player.volume,
      };
      setVolumeSettings(volumeData);
    }
  }, [player, setVolumeSettings]);

  const toggleMuted = useCallback(() => {
    if (player) {
      player.muted = !player.muted;
      saveVolumeValue();
      resetTimer();
    }
  }, [player, resetTimer, saveVolumeValue]);

  const toggleMutedOnKey = useCallback(() => {
    if (player) {
      player.muted = !player.muted;
      saveVolumeValue();
      resetTimer();
      setPlayerOverlay(PlayerOverlayContent.ChangeVolumeOnKeyInfo);
    }
  }, [player, resetTimer, saveVolumeValue, setPlayerOverlay]);

  const setVolume = useCallback(
    (value: number) => {
      if (player) {
        resetTimer();

        player.volume = value;
        player.muted = false;
        setVolumeSettings({
          isMuted: volumeSettings?.isMuted || DEFAULT_VOLUME_SETTINGS.isMuted,
          volumeValue: value,
        });
        saveVolumeValue();

        if (value === 0) {
          player.muted = true;
          setVolumeSettings({
            ...volumeSettings,
            isMuted: true,
            volumeValue:
              volumeSettings?.volumeValue ||
              DEFAULT_VOLUME_SETTINGS.volumeValue,
          });
        }
      }
    },
    [player, resetTimer, saveVolumeValue, setVolumeSettings, volumeSettings],
  );

  const changeVolumeValue = (change: number) => {
    if (volumeSettings?.volumeValue) {
      const newVolumeValue = Number(
        (volumeSettings?.volumeValue + change).toFixed(2),
      );
      if (newVolumeValue >= 0 && newVolumeValue <= 1) {
        setVolume(newVolumeValue);
        setPlayerOverlay(PlayerOverlayContent.ChangeVolumeOnKeyInfo);
      }
    }
  };

  useEffect(() => {
    const setupVolumeOnPlayer = () => {
      if (player && volumeSettings) {
        const { isMuted, volumeValue } = volumeSettings;
        player.muted = isMuted || DEFAULT_VOLUME_SETTINGS.isMuted;
        player.volume = volumeValue || DEFAULT_VOLUME_SETTINGS.volumeValue;
      }
    };
    setupVolumeOnPlayer();
  }, [player, setPlayback, volumeSettings]);

  return {
    toggleFullScreen,
    playPause,
    jumpToLive,
    jumpTo,
    stop,
    toggleMuted,
    toggleMutedOnKey,
    setVolume,
    changeVolumeValue,
    isMuted: volumeSettings?.isMuted,
    volumeValue: volumeSettings?.volumeValue,
  };
};
