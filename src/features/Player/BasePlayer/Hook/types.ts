import { Error } from 'voplayer-html5/lib/src/player/errors/Error';

import { TimeInSeconds } from 'services/api/common/types';

export interface usePlayerActionsValue {
  toggleFullScreen: () => void;
  playPause: () => void;
  jumpToLive: () => void;
  jumpTo: (time: TimeInSeconds) => void;
  stop: () => void;
  toggleMuted: () => void;
  toggleMutedOnKey: () => void;
  setVolume: (value: number) => void;
  changeVolumeValue: (newVolumeValue: number) => void;
  isMuted?: boolean;
  volumeValue?: number;
}

export type PlayerErrorType = {
  data: {
    status: number;
  };
} & Error;

export type ChannelTimer = {
  channelId: string;
  time: number;
};
