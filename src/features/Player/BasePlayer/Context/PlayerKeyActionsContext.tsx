import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';

import { useAppLayoutMode } from 'services/appLayoutMode';
import { PlayerMode } from 'features/Player/types';
import { exitIfFullScreen } from 'hooks/useToggleFullScreen';
import { usePlayerOverlay } from 'features/Player/PlayerOverlay/PlayerOverlayContext';
import { PlayerOverlayContent } from 'features/Player/PlayerOverlay/types';
import { globalConfig } from 'services/config/config';
import { usePlayerControlsContext } from 'features/Player/PlayerControls/context';
import { usePlayerPlayback } from 'features/Player/Context';

import {
  PlayerDownKeys,
  PlayerKeyActionsValue,
  PlayerKeyDownActions,
  PlayerKeyUpActions,
  PlayerUpKeys,
} from './types';
import {
  DECREASE_VOLUME_VALUE,
  HIDE_OVERLAY_TIME,
  INCREASE_VOLUME_VALUE,
} from './constants';
import { isSpecificKeyDownCode, isSpecificKeyUpCode } from './guards';

import { usePlayerActions } from '../Hook';

const PlayerKeyActionsContext = createContext<PlayerKeyActionsValue>(
  {} as PlayerKeyActionsValue,
);

export const PlayerKeyActionsProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const { mode, isSearchOpen } = useAppLayoutMode();
  const { reset } = usePlayerPlayback();
  const { setPlayerOverlay } = usePlayerOverlay();
  const { playPause, toggleFullScreen, toggleMutedOnKey, changeVolumeValue } =
    usePlayerActions();
  const { setArePlayerControlsVisible } = usePlayerControlsContext();
  const { playerControlsTimeHidden } = globalConfig.player;

  const overlayTimer = useRef<number | null>(null);
  const playerControlsTimer = useRef<number | null>(null);

  const canAddKeyListeners = useMemo(() => {
    return (
      (mode === PlayerMode.FullScreen || mode === PlayerMode.Expanded) &&
      !isSearchOpen
    );
  }, [isSearchOpen, mode]);

  const keyDownActions: PlayerKeyDownActions = useMemo(
    () => ({
      [PlayerDownKeys.Space]: playPause,
      [PlayerDownKeys.ShiftKeyX]: reset,
      [PlayerDownKeys.KeyF]: toggleFullScreen,
      [PlayerDownKeys.KeyM]: toggleMutedOnKey,
      [PlayerDownKeys.Escape]: exitIfFullScreen,
      [PlayerDownKeys.ArrowUp]: () => changeVolumeValue(INCREASE_VOLUME_VALUE),
      [PlayerDownKeys.ArrowDown]: () =>
        changeVolumeValue(DECREASE_VOLUME_VALUE),
      [PlayerDownKeys.ArrowRight]: () => {},
      [PlayerDownKeys.ArrowLeft]: () => {},
    }),
    [playPause, reset, toggleFullScreen, toggleMutedOnKey, changeVolumeValue],
  );

  const keyUpActions: PlayerKeyUpActions = useMemo(
    () => ({
      [PlayerUpKeys.ArrowLeft]: () => {},
      [PlayerUpKeys.ArrowRight]: () => {},
    }),
    [],
  );

  const setKeyDownAction = (key: PlayerDownKeys, action: () => void) => {
    keyDownActions[key] = action;
  };

  const setKeyUpAction = (key: PlayerUpKeys, action: () => void) => {
    keyUpActions[key] = action;
  };

  const clearTimeouts = () => {
    if (overlayTimer.current) {
      clearTimeout(overlayTimer.current);
    }

    if (playerControlsTimer.current) {
      clearTimeout(playerControlsTimer.current);
    }
  };

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      clearTimeouts();
      const eventCode = event.shiftKey ? `Shift${event.code}` : event.code;
      if (isSpecificKeyDownCode(eventCode)) {
        event.preventDefault();
        const action = keyDownActions[eventCode];
        setArePlayerControlsVisible(true);
        return action();
      }
    },
    [keyDownActions, setArePlayerControlsVisible],
  );

  const handleKeyUp = useCallback(
    (event: KeyboardEvent) => {
      overlayTimer.current = window.setTimeout(() => {
        setPlayerOverlay(PlayerOverlayContent.Default);
      }, HIDE_OVERLAY_TIME);

      playerControlsTimer.current = window.setTimeout(() => {
        setArePlayerControlsVisible(false);
      }, playerControlsTimeHidden * 1000);

      if (isSpecificKeyUpCode(event.code)) {
        event.preventDefault();
        const action = keyUpActions[event.code];
        return action();
      }
    },
    [
      keyUpActions,
      playerControlsTimeHidden,
      setArePlayerControlsVisible,
      setPlayerOverlay,
    ],
  );

  const removeKeyListeners = useCallback(() => {
    window.removeEventListener('keydown', handleKeyDown);
    window.removeEventListener('keyup', handleKeyUp);
  }, [handleKeyDown, handleKeyUp]);

  const addKeyListeners = useCallback(() => {
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
  }, [handleKeyDown, handleKeyUp]);

  useEffect(() => {
    if (canAddKeyListeners) {
      addKeyListeners();
    }

    return () => {
      removeKeyListeners();
    };
  }, [addKeyListeners, canAddKeyListeners, removeKeyListeners]);

  return (
    <PlayerKeyActionsContext.Provider
      value={{ setKeyDownAction, setKeyUpAction, removeKeyListeners }}
    >
      {children}
    </PlayerKeyActionsContext.Provider>
  );
};

export const usePlayerKeyActions = (): PlayerKeyActionsValue => {
  const context = useContext(PlayerKeyActionsContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerStateContext');
};
