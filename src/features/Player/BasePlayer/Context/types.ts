import React, { ReactNode } from 'react';
import { Player } from 'voplayer-html5';

import { TimeInSeconds } from 'services/api/common/types';
import { PlayerState } from 'features/Player/types';

export interface PlayerTimer {
  currentTime: TimeInSeconds;
  duration: TimeInSeconds;
}

export interface PlayerEventHandlerValue {
  type: PlayerState;
  [key: string]: string | number;
}

export interface PlayerInstanceProviderProps {
  player: Player | null;
  containerRef: React.MutableRefObject<HTMLDivElement | null>;
}

export interface PlayerInstanceContextValue {
  player: Player | null;
  containerRef: React.MutableRefObject<HTMLDivElement | null>;
}

export interface PlayerTimerContextValue {
  timer: PlayerTimer;
  isSyncedWithLive: boolean;
}

export interface PlayerStateContextValue {
  state: PlayerState;
  setState: (state: PlayerState) => void;
}

export type ChannelTimer = {
  channelId: string | null;
  time: number | null;
};
export interface PlayerTimeLimitContextValue {
  lastInteractionTime: number;
  resetTimer: () => void;
}

export interface PlayerTimeChannelLimitContextValue {
  updateChannelTimer: (id: string) => void;
  clearChannelTimer: () => void;
  channelTimer: ChannelTimer | undefined;
}
export interface PlayerReloadContextValue {
  setReloadPlayer: (reload: () => void) => void;
  reloadPlayer: () => void;
  reloadPlayerWithNewToken: () => void;
  setReloadPlayerWithNewToken: (reload: () => void) => void;
}

export enum PlayerDownKeys {
  ShiftKeyX = 'ShiftKeyX',
  KeyF = 'KeyF',
  KeyM = 'KeyM',
  Escape = 'Escape',
  Space = 'Space',
  ArrowLeft = 'ArrowLeft',
  ArrowRight = 'ArrowRight',
  ArrowUp = 'ArrowUp',
  ArrowDown = 'ArrowDown',
}

export enum PlayerUpKeys {
  ArrowLeft = 'ArrowLeft',
  ArrowRight = 'ArrowRight',
}
export interface PlayerKeyActionsValue {
  setKeyDownAction: (key: PlayerDownKeys, action: () => void) => void;
  setKeyUpAction: (key: PlayerUpKeys, action: () => void) => void;
  removeKeyListeners: () => void;
}

export type PlayerKeyDownActions = {
  [key in PlayerDownKeys]: () => void;
};

export type PlayerKeyUpActions = {
  [key in PlayerUpKeys]: () => void;
};
