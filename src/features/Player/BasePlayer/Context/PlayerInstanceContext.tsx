import { createContext, FC, PropsWithChildren, useContext } from 'react';

import {
  PlayerInstanceContextValue,
  PlayerInstanceProviderProps,
} from './types';

const PlayerInstanceContext = createContext<PlayerInstanceContextValue>(
  {} as PlayerInstanceContextValue,
);

export const PlayerInstanceProvider: FC<
  PropsWithChildren<PlayerInstanceProviderProps>
> = ({ children, player, containerRef }) => {
  return (
    <PlayerInstanceContext.Provider value={{ player, containerRef }}>
      {children}
    </PlayerInstanceContext.Provider>
  );
};

export const usePlayerInstance = (): PlayerInstanceContextValue => {
  const context = useContext(PlayerInstanceContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerInstanceContext');
};
