import { millisecondsToSeconds } from 'date-fns';
import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { usePlayerFeatures } from 'features/Player/Context';

import { usePlayerInstance } from './PlayerInstanceContext';
import { PlayerTimer, PlayerTimerContextValue } from './types';

const PlayerTimerContext = createContext<PlayerTimerContextValue>(
  {} as PlayerTimerContextValue,
);

export const PlayerTimerProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const {
    features: { lagTime },
  } = usePlayerFeatures();

  const [timer, setTimer] = useState<PlayerTimer>({
    currentTime: 0,
    duration: 0,
  });

  const { player } = usePlayerInstance();

  const onPlayerTimeUpdate = useCallback(() => {
    if (player) {
      setTimer({
        currentTime: player.currentTime | 0,
        duration: player.duration | 0,
      });
    }
  }, [player]);

  const isSyncedWithLive = useMemo(() => {
    return timer.currentTime + 30 >= millisecondsToSeconds(Date.now());
  }, [timer]);

  useEffect(() => {
    player?.on('timeupdate', onPlayerTimeUpdate);

    return () => {
      player?.off('timeupdate', onPlayerTimeUpdate);
    };
  }, [player, onPlayerTimeUpdate]);

  return (
    <PlayerTimerContext.Provider value={{ timer, isSyncedWithLive }}>
      {children}
    </PlayerTimerContext.Provider>
  );
};

export const usePlayerTimer = (): PlayerTimerContextValue => {
  const context = useContext(PlayerTimerContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerTimerContext');
};
