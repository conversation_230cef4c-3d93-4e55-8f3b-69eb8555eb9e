import { PlayerDownKeys, PlayerUpKeys } from './types';

export const isSpecificKeyDownCode = (
  keyCode: string,
): keyCode is PlayerDownKeys => {
  if (Object.values(PlayerDownKeys).includes(keyCode as PlayerDownKeys)) {
    return true;
  }
  return false;
};

export const isSpecificKeyUpCode = (
  keyCode: string,
): keyCode is PlayerUpKeys => {
  if (Object.values(PlayerUpKeys).includes(keyCode as PlayerUpKeys)) {
    return true;
  }
  return false;
};
