import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { PlayerState } from 'features/Player/types';

import { usePlayerInstance } from './PlayerInstanceContext';
import { PlayerEventHandlerValue, PlayerStateContextValue } from './types';

const PlayerStateContext = createContext<PlayerStateContextValue>(
  {} as PlayerStateContextValue,
);

export const PlayerStateProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const [state, setState] = useState<PlayerState>(PlayerState.Init);

  const { player } = usePlayerInstance();

  const eventHandler = useCallback((params: PlayerEventHandlerValue) => {
    setState(params.type);
  }, []);

  useEffect(() => {
    const PLAYER_EVENTS = Object.values(PlayerState).filter(
      (playerState) => playerState !== PlayerState.Init,
    );

    PLAYER_EVENTS.forEach((event) => {
      player?.on(event, eventHandler);
    });

    return () => {
      PLAYER_EVENTS.forEach((event) => {
        player?.off(event, eventHandler);
      });
    };
  }, [player, eventHandler]);

  return (
    <PlayerStateContext.Provider value={{ state, setState }}>
      {children}
    </PlayerStateContext.Provider>
  );
};

export const usePlayerState = (): PlayerStateContextValue => {
  const context = useContext(PlayerStateContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond PlayerStateContext');
};
