import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
} from 'react';

import { useSessionStorage } from 'services/storage';

import { CHANNEL_TIMER_KEY, INITIAL_CHANNEL_TIMER } from './constants';
import { ChannelTimer, PlayerTimeChannelLimitContextValue } from './types';

const PlayerTimeChannelLimitContext =
  createContext<PlayerTimeChannelLimitContextValue>(
    {} as PlayerTimeChannelLimitContextValue,
  );

export const PlayerTimeChannelLimitProvider: FC<PropsWithChildren> = ({
  children,
}) => {
  const [channelTimer, setChannelTimer] = useSessionStorage<ChannelTimer>(
    CHANNEL_TIMER_KEY,
    INITIAL_CHANNEL_TIMER,
  );

  const clearChannelTimer = useCallback(() => {
    window.sessionStorage.removeItem(CHANNEL_TIMER_KEY);
  }, []);

  const updateChannelTimer = useCallback(
    (id: string) => {
      setChannelTimer({
        channelId: id,
        time: new Date().getTime(),
      });
    },
    [setChannelTimer],
  );

  return (
    <PlayerTimeChannelLimitContext.Provider
      value={{
        updateChannelTimer,
        clearChannelTimer,
        channelTimer,
      }}
    >
      {children}
    </PlayerTimeChannelLimitContext.Provider>
  );
};

export const usePlayerTimeChannelLimit =
  (): PlayerTimeChannelLimitContextValue => {
    const context = useContext(PlayerTimeChannelLimitContext);

    if (context) {
      return context;
    }

    throw new Error('Component beyond PlayerTimeChannelLimitContextValue');
  };
