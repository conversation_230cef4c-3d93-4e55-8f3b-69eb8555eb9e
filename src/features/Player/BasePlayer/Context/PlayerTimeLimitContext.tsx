import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { usePlayerProgram } from 'features/Player/Context';
import { useDevSettings } from 'features/Settings/DevSettings/hooks';
import { PLAYER_TIME_LIMIT } from 'services/api/oldApi';
import { useGlobalLoaderContext } from 'services/loader';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { PlayerOverlayContent } from 'features/Player/PlayerOverlay/types';
import { usePlayerOverlay } from 'features/Player/PlayerOverlay/PlayerOverlayContext';

import { PlayerTimeLimitContextValue } from './types';
import { usePlayerTimeChannelLimit } from './PlayerTimeChannelLimitContext';
import { MILLISEC_TO_SEC, MODAL_EXTRA_TIME } from './constants';

const PlayerTimeLimitContext = createContext<PlayerTimeLimitContextValue>(
  {} as PlayerTimeLimitContextValue,
);

export const PlayerTimeLimitProvider: FC<PropsWithChildren> = ({
  children,
}) => {
  const modalTimerRef = useRef<number | null>(null);
  const timerRef = useRef<number | null>(null);
  const { playerAutoShutdownTimeout } = useDevSettings();
  const { program } = usePlayerProgram();
  const { isLoaderVisible } = useGlobalLoaderContext();
  const { mode } = useAppLayoutMode();
  const { channelTimer, updateChannelTimer } = usePlayerTimeChannelLimit();
  const { setPlayerOverlay } = usePlayerOverlay();

  const [lastInteractionTime, setLastInteractionTime] = useState(
    new Date().getTime(),
  );

  const { BASE_INACTIVE_TIME, PLAYER_TIME_LIMIT_ANIMATION_TIME: MODAL_TIME } =
    PLAYER_TIME_LIMIT;
  const INACTIVE_TIME = playerAutoShutdownTimeout
    ? Number(playerAutoShutdownTimeout) * MILLISEC_TO_SEC
    : BASE_INACTIVE_TIME;

  const isChannel = useMemo(() => Boolean(program), [program]);

  const resetTimer = useCallback(() => {
    clearInterval(timerRef.current as number);
    clearInterval(modalTimerRef.current as number);
    setLastInteractionTime(new Date().getTime());

    if (isChannel) {
      return updateChannelTimer(program?.channelExtId);
    }
  }, [isChannel, program?.channelExtId, updateChannelTimer]);

  const startModalTimer = useCallback(() => {
    setPlayerOverlay(PlayerOverlayContent.PlayerTimeLimitModal);
    const startModalTime = new Date().getTime();
    modalTimerRef.current = window.setInterval(() => {
      const now = new Date().getTime();
      const difference = now - startModalTime;
      const isTimeExpired = difference > MODAL_TIME + MODAL_EXTRA_TIME;
      if (isTimeExpired) {
        clearInterval(modalTimerRef.current as number);
      }
    }, MILLISEC_TO_SEC);
  }, [MODAL_TIME, setPlayerOverlay]);

  const handleModalTimer = useCallback(() => {
    clearInterval(timerRef.current as number);
    startModalTimer();
  }, [startModalTimer]);

  const startTimer = useCallback(() => {
    timerRef.current = window.setInterval(() => {
      const now = new Date().getTime();
      const difference = now - lastInteractionTime;
      const isChannelTimeExpired =
        channelTimer?.time && now - channelTimer.time > INACTIVE_TIME;
      if (isChannel && isChannelTimeExpired) {
        handleModalTimer();
        return;
      }
      if (difference > INACTIVE_TIME) {
        handleModalTimer();
        return;
      }
    }, MILLISEC_TO_SEC);
  }, [
    INACTIVE_TIME,
    channelTimer?.time,
    handleModalTimer,
    isChannel,
    lastInteractionTime,
  ]);

  useEffect(() => {
    if (isLoaderVisible) {
      return;
    }

    startTimer();

    return () => {
      clearInterval(timerRef.current as number);
      clearInterval(modalTimerRef.current as number);
    };
  }, [isLoaderVisible, startTimer]);

  useEffect(() => {
    resetTimer();
  }, [mode, resetTimer]);

  return (
    <PlayerTimeLimitContext.Provider
      value={{
        lastInteractionTime,
        resetTimer,
      }}
    >
      {children}
    </PlayerTimeLimitContext.Provider>
  );
};

export const usePlayerTimeLimit = (): PlayerTimeLimitContextValue => {
  const context = useContext(PlayerTimeLimitContext);

  if (context) {
    return context;
  }

  throw new Error('Component beyond PlayerTimeLimitContext');
};
