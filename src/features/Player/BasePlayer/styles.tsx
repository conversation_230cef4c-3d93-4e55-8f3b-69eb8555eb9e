import styled, { css } from 'styled-components';

import { PlayerMode } from 'features/Player/types';
import { convertHexToRgbaString, RenderLayer } from 'theme';

export const PlayerGlass = styled.div<{ mode: PlayerMode }>`
  position: relative;
  width: 100%;

  ${({ mode }) =>
    mode === PlayerMode.Background &&
    css`
      &:after {
        content: '';
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        height: 1000px;
        width: 100%;
        background-color: ${({ theme }) =>
          convertHexToRgbaString(theme.colors.black, 0.75)};
        ${RenderLayer('playerGlass')};
      }
    `}
`;

export const PlayerContainer = styled.div<{ mode: PlayerMode }>`
  position: fixed;
  transition: all 0.3s linear;
  box-sizing: content-box;
  margin-top: ${({ theme }) => theme.containerSizes.navigationHeight};
  background: ${({ theme }) => theme.colors.black};

  ${RenderLayer('player')};

  ${({ mode }) =>
    mode === PlayerMode.Background &&
    css`
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
    `}

  ${({ mode }) =>
    mode === PlayerMode.Mini &&
    css`
      right: 3rem;
      bottom: 3rem;
      width: ${({ theme }) => theme.containerSizes.miniPlayerWidth};
      height: ${({ theme }) => theme.containerSizes.miniPlayerHeight};
      aspect-ratio: auto;
      border: 3px solid ${({ theme }) => theme.colors.alabaster};
    `}

  ${({ mode }) =>
    mode === PlayerMode.Expanded &&
    css`
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      padding: 0;
      display: flex;
      align-items: center;
    `}

  ${({ mode }) =>
    mode === PlayerMode.FullScreen &&
    css`
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
    `}

  video {
    width: inherit;
    height: inherit;
  }
`;
