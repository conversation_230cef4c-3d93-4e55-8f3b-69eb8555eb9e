import { ChannelPlayback } from 'features/Player/Context';
import { ChannelPlayInfoResponse } from 'services/api/newApi/live/channels';
import { ProgramDetails } from 'services/api/newApi/core/tvguide';

export interface ChannelPlayerProps {
  playback: ChannelPlayback;
}

export interface ChannelPlayerWrapperProps {
  playback: ChannelPlayback;
}

export interface ChannelPlayerInnerWrapperProps {
  playback: ChannelPlayback;
  playInfo: ChannelPlayInfoResponse;
  program: ProgramDetails;
  refetchToken: () => void;
}

export interface ChannelPlayerControllerProps {
  playback: ChannelPlayback;
}
