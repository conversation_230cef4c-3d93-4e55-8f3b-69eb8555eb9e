import { useCallback, useEffect } from 'react';
import { secondsToMilliseconds } from 'date-fns';

import {
  usePlayerState,
  usePlayerTimer,
} from 'features/Player/BasePlayer/Context';
import { usePlayerProgram } from 'features/Player/Context';
import { PlayerState } from 'features/Player/types';
import {
  PlayerOverlayContent,
  usePlayerOverlay,
} from 'features/Player/PlayerOverlay';

export const useUpdateLiveProgram = () => {
  const { state } = usePlayerState();
  const { program, loadCurrentProgram, getNextProgram } = usePlayerProgram();
  const {
    timer: { currentTime: playerCurrentTime },
    isSyncedWithLive,
  } = usePlayerTimer();
  const { setPlayerOverlay } = usePlayerOverlay();

  const isPlaying = state === PlayerState.Play;
  const programEnded = program && playerCurrentTime >= program.endTimeUtc;
  const checkIfShouldBackToLive = useCallback(() => {
    if (isSyncedWithLive) {
      return;
    }

    const nextProgram = getNextProgram();
    if (!nextProgram) {
      return;
    }

    const startOverDisabled = nextProgram.properties?.startOverDisabled;
    const catchUpDisabled = nextProgram.properties?.catchUpDisabled;

    const currentTime = new Date().getTime();
    const nextProgramIsStartover =
      secondsToMilliseconds(nextProgram.endTimeUtc) > currentTime;
    const nextProgramIsCatchup =
      secondsToMilliseconds(nextProgram.endTimeUtc) <= currentTime;
    const shouldBackToLive =
      (nextProgramIsStartover && startOverDisabled) ||
      (nextProgramIsCatchup && catchUpDisabled);

    if (shouldBackToLive) {
      setPlayerOverlay(PlayerOverlayContent.BackToLiveModal);
    }
  }, [getNextProgram, isSyncedWithLive, setPlayerOverlay]);

  useEffect(() => {
    if (isPlaying && programEnded) {
      checkIfShouldBackToLive();
      loadCurrentProgram();
    }
  }, [loadCurrentProgram, isPlaying, programEnded, checkIfShouldBackToLive]);
};
