import { FC, PropsWithChildren } from 'react';

import {
  useLiveContinueWatchingHandler,
  useLivePauseWatcher,
  useRefreshLiveSession,
} from 'features/Player/Hook';

import { useUpdateLiveProgram } from './Hook';
import { ChannelPlayerControllerProps } from './types';

export const ChannelPlayerController: FC<
  PropsWithChildren<ChannelPlayerControllerProps>
> = ({ playback, children }) => {
  const { channelExtId } = playback;

  useUpdateLiveProgram();
  useLivePauseWatcher();
  useRefreshLiveSession({ channelExtId });
  useLiveContinueWatchingHandler();

  return <>{children}</>;
};
