import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { routes } from 'routes/routes-map';
import { useAppLayoutMode } from 'services/appLayoutMode';
import { PlayerMode } from 'features/Player/types';

/**
 * Navigate to EPG with channel ID in router state
 * Includes timestamp to force re-centering on each navigation
 **/

export function useGoToEpg() {
  const navigate = useNavigate();
  const { setMode } = useAppLayoutMode();

  return useCallback(
    (channelExtId: string) => {
      setMode(PlayerMode.Background);
      setTimeout(() => {
        navigate(routes.program, {
          state: {
            centerChannelExtId: channelExtId,
            centerTimestamp: Date.now(),
          },
        });
      }, 100);
    },
    [navigate, setMode],
  );
}
