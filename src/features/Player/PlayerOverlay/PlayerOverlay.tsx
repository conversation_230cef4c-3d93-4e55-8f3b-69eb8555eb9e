import { BackToLiveModal } from './overlays/BackToLiveModal';
import { PlayerTimeLimitModal } from './overlays/PlayerTimeLimitModal';
import { usePlayerOverlay } from './PlayerOverlayContext';
import { PlayerOverlayContent } from './types';
import * as S from './styles';
import { UnsupportedBrowserBoard } from './overlays/UnsupportedBrowserBoard';
import { ChangeVolumeOnKeyInfo } from './overlays/ChangeVolumeOnKeyInfo';

export const PlayerOverlay = () => {
  const { playerOverlay } = usePlayerOverlay();

  const OverlayContent = {
    [PlayerOverlayContent.Default]: <></>,
    [PlayerOverlayContent.PlayerTimeLimitModal]: <PlayerTimeLimitModal />,
    [PlayerOverlayContent.BackToLiveModal]: <BackToLiveModal />,
    [PlayerOverlayContent.BackToLiveWithParentalControl]: (
      <BackToLiveModal withParentalControl />
    ),
    [PlayerOverlayContent.UnsupportedBrowserBoard]: <UnsupportedBrowserBoard />,
    [PlayerOverlayContent.ChangeVolumeOnKeyInfo]: <ChangeVolumeOnKeyInfo />,
  };

  return (
    <S.PlayerOverlayContainer type={playerOverlay}>
      {OverlayContent[playerOverlay]}
    </S.PlayerOverlayContainer>
  );
};
