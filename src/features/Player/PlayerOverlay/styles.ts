import styled, { css } from 'styled-components';

import { RenderLayer } from 'theme';

import { PlayerOverlayContent } from './types';

export const PlayerOverlayContainer = styled.div<{
  type: PlayerOverlayContent;
}>`
  ${RenderLayer('playerOverlay')};
  position: fixed;

  ${({ type }) =>
    type === PlayerOverlayContent.UnsupportedBrowserBoard &&
    css`
      ${RenderLayer('playerErrorScreen')};
    `}
`;
