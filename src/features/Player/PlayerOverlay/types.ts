export enum PlayerOverlayContent {
  Default = 'default',
  PlayerTimeLimitModal = 'playerTimeLimit',
  BackToLiveModal = 'backToLive',
  UnsupportedBrowserBoard = 'unsupportedBrowserBoard',
  BackToLiveWithParentalControl = 'backToLiveWithParentalControl',
  ChangeVolumeOnKeyInfo = 'changeVolumeOnKeyInfo',
}

export type PlayerOverlayContextValue = {
  playerOverlay: PlayerOverlayContent;
  setPlayerOverlay: (newPlayerOverlay: PlayerOverlayContent) => void;
};

export type PlayerOverlayProviderProps = {};
