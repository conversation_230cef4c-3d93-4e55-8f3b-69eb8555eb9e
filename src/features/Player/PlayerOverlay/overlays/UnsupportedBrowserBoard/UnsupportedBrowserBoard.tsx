import { useIntl } from 'react-intl';
import { useMemo } from 'react';

import { useAppLayoutMode } from 'services/appLayoutMode';
import { IconWarning } from 'components/Icons';
import { PlayerMode } from 'features/Player/types';
import { ClosePlayerButton } from 'features/Player/Interface/Button';

import { messages } from './messages';
import * as S from './styles';

export const UnsupportedBrowserBoard = () => {
  const { formatMessage } = useIntl();
  const { mode } = useAppLayoutMode();

  const normalBoardDetails = useMemo(
    () => (
      <>
        <IconWarning />
        <S.StyledTitle $sizeLarge>
          {formatMessage(messages.unsupportedBrowserBoardTitle)}
        </S.StyledTitle>
        <S.StyledText $sizeMedium>
          {formatMessage(messages.unsupportedBrowserBoardInformation)}
        </S.StyledText>
      </>
    ),
    [formatMessage],
  );

  const miniPlayerBoardDetails = useMemo(
    () => (
      <>
        <IconWarning scale={0.7} />
        <S.StyledTitle $sizeXSmall>
          {formatMessage(messages.unsupportedBrowserBoardTitle)}
        </S.StyledTitle>
        <S.StyledText $sizeXSmall>
          {formatMessage(messages.unsupportedBrowserBoardInformation)}
        </S.StyledText>
      </>
    ),
    [formatMessage],
  );

  const boardDetails = useMemo(() => {
    if (mode === PlayerMode.Mini) return miniPlayerBoardDetails;

    return normalBoardDetails;
  }, [miniPlayerBoardDetails, mode, normalBoardDetails]);

  return (
    <S.BoardContainer mode={mode}>
      {mode === PlayerMode.Mini && (
        <S.CloseWrapper>
          <ClosePlayerButton />
        </S.CloseWrapper>
      )}
      {boardDetails}
    </S.BoardContainer>
  );
};
