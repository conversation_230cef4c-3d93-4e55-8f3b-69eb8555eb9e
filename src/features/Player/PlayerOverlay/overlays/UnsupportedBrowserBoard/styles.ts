import styled, { css } from 'styled-components';

import { Text } from 'components/Typography';
import { PlayerMode } from 'features/Player/types';
import { RenderLayer } from 'theme';

export const BoardContainer = styled.div<{ mode: PlayerMode }>`
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  bottom: 0;
  right: 0;
  top: 0;
  left: 0;
  background-color: ${({ theme }) => theme.colors.black};
  row-gap: 1.6rem;

  svg {
    margin-bottom: 1.6rem;
  }

  ${({ mode }) =>
    (mode === PlayerMode.FullScreen || mode === PlayerMode.Expanded) &&
    css`
      height: 100%;
      width: 100%;
    `}

  ${({ mode }) =>
    mode === PlayerMode.Mini &&
    css`
      right: 3.3rem;
      bottom: 3.3rem;
      width: ${({ theme }) => theme.containerSizes.miniPlayerWidth};
      height: ${({ theme }) => theme.containerSizes.miniPlayerHeight};
      overflow: hidden;
      top: unset;
      left: unset;
      row-gap: 0.2rem;

      svg {
        margin-bottom: 0.8rem;
      }
    `}
`;

export const StyledTitle = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-weight: bold;
`;

export const StyledText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  white-space: pre-line;
  text-align: center;
`;

export const BrowserLogosContainer = styled.div`
  display: flex;
`;

export const CloseWrapper = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  padding: 1.6rem;

  ${RenderLayer('playerControls')};
`;
