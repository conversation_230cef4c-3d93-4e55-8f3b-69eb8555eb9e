import React from 'react';
import { useIntl } from 'react-intl';

import { Text } from 'components/Typography';
import { useGoToEpg } from 'features/Player/PlayerControls/FullScreenPlayerControls/useGoToEpg';

import { StyledShowChannelProgramButton } from './styles';
import { ShowChannelProgramButtonProps } from './types';
import { messages } from './messages';

const noScaleVariants = {
  hover: { scale: 1 },
  tap: { scale: 1 },
};

export const ShowChannelProgramButton: React.FC<
  ShowChannelProgramButtonProps
> = ({ channelExtId, ...rest }) => {
  const { formatMessage } = useIntl();

  const goToEpg = useGoToEpg();

  const handleGoToEpg = () => {
    goToEpg(channelExtId);
  };

  return (
    <StyledShowChannelProgramButton
      variant='ghostWhite'
      onClick={handleGoToEpg}
      variants={noScaleVariants}
      {...rest}
    >
      <Text $sizeXXMedium>{formatMessage(messages.program)}</Text>
    </StyledShowChannelProgramButton>
  );
};
