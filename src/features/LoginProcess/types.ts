export type LoginProcessState =
  | { type: 'LOADING' }
  | { type: 'QUICK_LOGIN'; error: boolean }
  | {
      type: 'FULL_LOGIN';
      error: boolean;
    }
  | { type: 'PIN_ACTIVATION' }
  | {
      type: 'SUBSCRIPTION';
    }
  | { type: 'ACCEPT_CONSENTS' }
  | { type: 'LOGIN_SUCCESS' };

export type LoginProcessAction =
  | { type: 'SET_QUICK_LOGIN'; error: boolean }
  | { type: 'SET_FULL_LOGIN'; error: boolean }
  | { type: 'SET_PIN_ACTIVATION' }
  | { type: 'SET_ACCEPT_CONSENTS' };
