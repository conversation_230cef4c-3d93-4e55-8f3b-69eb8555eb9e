import { FC, useCallback, useEffect, useMemo, useReducer } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  getUserErrorType,
  isBadGatewayError,
  isForbiddenError,
  isHouseholdSuspendError,
  isQuickLoginInvalidPin,
  isUnauthorizedError,
  useErrorScreen,
} from 'services/error';
import { useAuthenticationStatus, useUserSession } from 'services/user';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { useGlobalLoaderContext } from 'services/loader';
import { ModalPinActivation } from 'services/user/ModalPinActivation';
import { useAnalyticsAgreement } from 'features/Settings/Service/AnalyticsAgreement';
import { useConfig } from 'services/config/ConfigContext';
import { generateEncodedPin } from 'utils/cryptography/pinEncodeAlgorithm';
import { routes } from 'routes/routes-map';

import { LoginProcessReducer } from './reducers/LoginProcessReducer';
import {
  ConsentsView,
  FullLoginView,
  LoadingView,
  QuickLoginView,
} from './views';
import { LoginProcessState } from './types';

export const LoginProcess: FC = () => {
  const { setIsLoaderVisible } = useGlobalLoaderContext();
  const { isAuthenticated } = useAuthenticationStatus();

  const { data: householdInfo, isFetching: isHouseholdInfoFetching } =
    useCoreHouseholdQuery();

  const { fullLogin, quickLogin, logout } = useUserSession();
  const { getTechConfig } = useConfig();

  const { usernameWithPinEncryptionRegex } = getTechConfig();

  const { showErrorModal } = useErrorScreen();
  const { analyticsAgreement, setAnalyticsAgreement } = useAnalyticsAgreement();

  const analyticsConsentsSaved = analyticsAgreement !== null;

  const hasSubscriptionStatus = useMemo(
    () => Boolean(householdInfo?.everywhere),
    [householdInfo?.everywhere],
  );

  const initialState: LoginProcessState = {
    type: 'FULL_LOGIN',
    error: false,
  };
  const navigate = useNavigate();

  const [state, dispatch] = useReducer(LoginProcessReducer, initialState);

  const chooseQuickLogin = async () => {
    dispatch({ type: 'SET_QUICK_LOGIN', error: false });
  };

  const chooseFullLogin = async () => {
    dispatch({ type: 'SET_FULL_LOGIN', error: false });
  };

  const handleSuccessLogin = useCallback(() => {
    navigate(routes.home);
  }, [navigate]);

  const setStateAfterLogin = useCallback(async () => {
    if (analyticsConsentsSaved) {
      return handleSuccessLogin();
    }

    return dispatch({
      type: 'SET_ACCEPT_CONSENTS',
    });
  }, [analyticsConsentsSaved, handleSuccessLogin]);

  const handleUserInfoLoaded = useCallback(() => {
    setIsLoaderVisible(false);
    const isPinDefined: boolean = householdInfo?.parentalPin === 'set';
    const loggedUserHaveActiveTVE = hasSubscriptionStatus;

    if (!loggedUserHaveActiveTVE) {
      return;
    }

    if (!isPinDefined) {
      return dispatch({ type: 'SET_PIN_ACTIVATION' });
    }

    setStateAfterLogin();
  }, [
    setIsLoaderVisible,
    householdInfo?.parentalPin,
    hasSubscriptionStatus,
    setStateAfterLogin,
  ]);

  const submitQuickLogin = async (code: string) => {
    if (state.type !== 'QUICK_LOGIN') {
      throw new Error(`Invalid State: ${state}`);
    }

    try {
      setIsLoaderVisible(true);
      dispatch({ type: 'SET_QUICK_LOGIN', error: false });
      await quickLogin(code);
    } catch (error: unknown) {
      dispatch({ type: 'SET_QUICK_LOGIN', error: true });
      setIsLoaderVisible(false);

      if (isQuickLoginInvalidPin(error)) {
        showErrorModal('QUICK_LOGIN_INVALID_PIN');
        return;
      }

      if (isUnauthorizedError(error) || isBadGatewayError(error)) {
        showErrorModal('AUTHORIZATION_QUICK_LOGIN');
        return;
      }

      if (isHouseholdSuspendError(error)) {
        showErrorModal('HOUSEHOLD_SUSPENDED_ON_LOGIN');
        return;
      }

      showErrorModal(getUserErrorType(error));
    }
    setIsLoaderVisible(false);
  };

  const submitFullLogin = async (username: string, code: string) => {
    if (state.type !== 'FULL_LOGIN') {
      throw new Error(`Invalid State: ${state.type}`);
    }
    try {
      setIsLoaderVisible(true);
      dispatch({ type: 'SET_FULL_LOGIN', error: false });

      const techWithPinEncryption = new RegExp(usernameWithPinEncryptionRegex);
      const pinCode = techWithPinEncryption.test(username)
        ? generateEncodedPin(code)
        : code;

      await fullLogin(username, pinCode);
    } catch (error: unknown) {
      dispatch({ type: 'SET_FULL_LOGIN', error: true });
      if (isForbiddenError(error)) {
        showErrorModal('LOGIN_NOT_ALLOWED');
        return;
      }

      if (isUnauthorizedError(error)) {
        showErrorModal('AUTHORIZATION');
        return;
      }

      if (isHouseholdSuspendError(error)) {
        showErrorModal('HOUSEHOLD_SUSPENDED_ON_LOGIN');
        return;
      }

      showErrorModal(getUserErrorType(error));
    } finally {
      setIsLoaderVisible(false);
    }
  };

  const handleSetPinCode = () => {
    if (state.type !== 'PIN_ACTIVATION') {
      throw new Error(`Invalid State: ${state}`);
    }

    setStateAfterLogin();
  };

  const submitConsents = async (accepted: boolean) => {
    if (state.type !== 'ACCEPT_CONSENTS') {
      throw new Error(`Invalid State: ${state}`);
    }
    setAnalyticsAgreement(accepted);
  };

  const handlePinActivationInterrupt = () => {
    dispatch({ type: 'SET_FULL_LOGIN', error: false });
    logout();
  };

  useEffect(() => {
    const isHouseholdInfoLoaded = !isHouseholdInfoFetching && householdInfo;

    if (isAuthenticated && isHouseholdInfoLoaded) {
      handleUserInfoLoaded();
    }
  }, [
    handleUserInfoLoaded,
    isHouseholdInfoFetching,
    householdInfo,
    isAuthenticated,
  ]);

  const renderCurrentStep = () => {
    switch (state.type) {
      case 'LOADING':
        return <LoadingView />;
      case 'QUICK_LOGIN':
        return (
          <QuickLoginView
            onLogin={submitQuickLogin}
            onFullLoginSelect={chooseFullLogin}
            onFocused={() =>
              dispatch({ type: 'SET_QUICK_LOGIN', error: false })
            }
            error={state.error}
          />
        );
      case 'FULL_LOGIN':
        return (
          <FullLoginView
            onLogin={submitFullLogin}
            onQuickLoginSelect={chooseQuickLogin}
            onFocused={() => dispatch({ type: 'SET_FULL_LOGIN', error: false })}
            error={state.error}
          />
        );
      case 'PIN_ACTIVATION':
        return (
          <ModalPinActivation
            onSubmit={handleSetPinCode}
            onClose={handlePinActivationInterrupt}
          />
        );
      case 'ACCEPT_CONSENTS':
        return <ConsentsView onAccept={submitConsents} />;
      default:
        return <></>;
    }
  };

  return renderCurrentStep();
};
