import { LoginProcessAction, LoginProcessState } from '../types';

export const LoginProcessReducer = (
  state: LoginProcessState,
  action: LoginProcessAction,
): LoginProcessState => {
  switch (action.type) {
    case 'SET_QUICK_LOGIN':
      return { ...state, type: 'QUICK_LOGIN', error: action.error };

    case 'SET_FULL_LOGIN':
      return { ...state, type: 'FULL_LOGIN', error: action.error };

    case 'SET_PIN_ACTIVATION':
      return { ...state, type: 'PIN_ACTIVATION' };

    case 'SET_ACCEPT_CONSENTS':
      return { ...state, type: 'ACCEPT_CONSENTS' };

    default:
      const _exhaustiveCheck: never = action;
      throw new Error(
        `Unhandled action type: ${
          (_exhaustiveCheck as LoginProcessAction).type
        }`,
      );
  }
};
