export const messages = {
  titleTooltip: {
    id: 'tooltipView.titleTooltip',
    defaultMessage: 'Dane do logowania',
  },
  titleTv: {
    id: 'tooltipView.titleTv',
    defaultMessage: 'Klienci Orange',
  },
  descriptionTv: {
    id: 'tooltipView.descriptionTv',
    defaultMessage:
      'Upewnij się, że Twoja Telewizja została zarejestrowana. <PERSON>ż<PERSON>z to zrobić w aplikacji Mój Orange, zakładka: {tvBreadcrumbs}. Jeśli opcja rejestracji konta jest dostępna, wykonaj ją. Następnie uruchom ponownie aplikację Orange TV Go.',
  },
  orangePortal: {
    id: 'tooltipView.orangePortal',
    defaultMessage: 'www.orange.pl',
  },
  securitySection: {
    id: 'tooltipView.securitySection',
    defaultMessage: 'Zabezpieczenia',
  },
  titleTvMobile: {
    id: 'tooltipView.titleTvMobile',
    defaultMessage: 'Telewizja mobilna Orange',
  },
  descriptionTvMobile: {
    id: 'tooltipView.descriptionTvMobile',
    defaultMessage:
      'Je<PERSON><PERSON> nie pamiętasz danych do logowania, znajdziesz je w aplikacji Mój Orange, zakładka {tvBreadcrumbs1} lub {tvBreadcrumbs2}',
  },
  titleNjuMobile: {
    id: 'tooltipView.titleNjuMobile',
    defaultMessage: 'Klienci nju mobile',
  },
  descriptionNjuMobile: {
    id: 'tooltipView.descriptionnjuMobile',
    defaultMessage:
      'Dane dostępowe do usługi TV możesz zmienić: {breakLine} - dla planu nju podstawowy (nju abonament) w aplikacji NJU mobile oraz na portalu {njuMobilePortal} w zakładce {njuBreadcrumbs} {breakLine} - dla planu nju komórkowy (nju subskrybcja) w aplikacji nju mobile na koncie {njuPortal} w sekcji {njuPortalLocation}',
  },
  njuMobilePortal: {
    id: 'tooltipView.njuMobilePortal',
    defaultMessage: 'www.njumobile.pl',
  },
  njuPortal: {
    id: 'tooltipView.njuPortal',
    defaultMessage: 'www.nju.pl',
  },
  njuPortalHelp: {
    id: 'tooltipView.njuPortalHelp',
    defaultMessage: 'telewizja mobilna',
  },
};
