import styled from 'styled-components';

import { Text } from 'components/Typography';
import { Image } from 'components/Image';

export const LoginHelperContainer = styled.div`
  min-width: 60rem;
  display: flex;
  flex-direction: column;
  row-gap: 3.2rem;
  margin-top: 4rem;
`;

export const CloseButtonContainer = styled.div`
  position: absolute;
  right: 2.8rem;
  top: 1.3rem;
`;

export const LinkContainer = styled.div`
  width: auto;
  a {
    text-decoration: underline;
    &::after {
      display: none;
    }
  }
`;

export const StyledText = styled(Text)`
  strong {
    font-weight: 600;
    color: ${({ theme }) => theme.colors.primary};
  }
`;

export const SectionContainer = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 1.2rem;
`;

export const StyledImage = styled(Image)`
  width: 32rem;
  margin-top: 0.4rem;
`;
