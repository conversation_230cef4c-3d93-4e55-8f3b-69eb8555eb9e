import { FC, PropsWithChildren } from 'react';
import { useIntl } from 'react-intl';

import { H1, Text } from 'components/Typography';
import { Modal } from 'components/Modal';
import { IconButton } from 'components/Buttons/IconButton';
import { IconClose } from 'components/Icons';
import { Breadcrumbs } from 'components/Breadcrumbs';

import * as S from './styles';
import {
  CHEVRON_SEPARATOR,
  ICON_CLOSE_SIZE,
  NJU_BREADCRUMBS,
  SLASH_SEPARATOR,
  TV_BREADCRUMBS,
  TV_MOBILE_BREADCRUMBS,
} from './constants';
import { messages } from './messages';
import { TooltipViewProps } from './types';

export const LoginHelper: FC<PropsWithChildren<TooltipViewProps>> = ({
  isOpen,
  onClose,
}) => {
  const { formatMessage } = useIntl();

  const tvMobileBreadcrumbs = (
    <Breadcrumbs
      elements={TV_MOBILE_BREADCRUMBS}
      separator={SLASH_SEPARATOR}
      $sizeXSmall
      $highlight
    />
  );

  const tvBreadcrumbs = (
    <Breadcrumbs
      elements={TV_BREADCRUMBS}
      separator={SLASH_SEPARATOR}
      $sizeXSmall
      $highlight
    />
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <S.LoginHelperContainer>
        <S.CloseButtonContainer>
          <IconButton onClick={onClose}>
            <IconClose width={ICON_CLOSE_SIZE} height={42} />
          </IconButton>
        </S.CloseButtonContainer>
        <H1>{formatMessage(messages.titleTooltip)}</H1>
        <S.SectionContainer>
          <Text $sizeXXMedium>{formatMessage(messages.titleTv)}</Text>
          <S.StyledText $sizeXSmall $secondary>
            {formatMessage(messages.descriptionTv, {
              tvBreadcrumbs: tvMobileBreadcrumbs,
            })}
          </S.StyledText>
          <S.StyledText $sizeXSmall $secondary>
            {formatMessage(messages.descriptionTvMobile, {
              tvBreadcrumbs1: tvMobileBreadcrumbs,
              tvBreadcrumbs2: tvBreadcrumbs,
            })}
          </S.StyledText>
        </S.SectionContainer>
        <S.SectionContainer>
          <Text $sizeXXMedium>{formatMessage(messages.titleNjuMobile)}</Text>
          <S.StyledText $sizeXSmall $secondary>
            {formatMessage(messages.descriptionNjuMobile, {
              breakLine: <br />,
              njuMobilePortal: (
                <strong>{formatMessage(messages.njuMobilePortal)}</strong>
              ),
              njuBreadcrumbs: (
                <Breadcrumbs
                  elements={NJU_BREADCRUMBS}
                  separator={CHEVRON_SEPARATOR}
                  $sizeXSmall
                  $highlight
                />
              ),
              njuPortal: <strong>{formatMessage(messages.njuPortal)}</strong>,
              njuPortalLocation: (
                <strong>{formatMessage(messages.njuPortalHelp)}</strong>
              ),
            })}
          </S.StyledText>
        </S.SectionContainer>
      </S.LoginHelperContainer>
    </Modal>
  );
};
