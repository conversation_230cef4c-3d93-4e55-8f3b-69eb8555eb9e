import { FC } from 'react';
import { useIntl } from 'react-intl';

import { ModalConfirmation } from 'components/ModalConfirmation';

import { SubscriptionViewProps } from './types';
import { messages } from './messages';

export const SubscriptionView: FC<SubscriptionViewProps> = ({
  canBuy,
  isOpen,
  onClose,
}) => {
  const { formatMessage } = useIntl();

  const handleCloseModal = async () => {
    onClose();
  };

  const selectedMessages = canBuy
    ? messages.subscriptionRequiredModal
    : messages.subscriptionDisabledModal;

  return (
    <ModalConfirmation
      modalTitle={formatMessage(selectedMessages.title)}
      modalDescription={formatMessage(selectedMessages.description)}
      isOpen={isOpen}
      onClose={handleCloseModal}
      onSubmit={handleCloseModal}
      withSubmitClose={false}
      buttonSubmitText={formatMessage(selectedMessages.submitButton)}
    />
  );
};
