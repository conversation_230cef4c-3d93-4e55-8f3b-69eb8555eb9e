import React from 'react';
import { useIntl } from 'react-intl';

import { Image } from 'components/Image';
import { H4 } from 'components/Typography';

import * as S from './styles';
import { messages } from './messages';

export const QRLoginView: React.FC<React.PropsWithChildren<unknown>> = () => {
  const { formatMessage } = useIntl();

  return (
    <S.Column>
      <H4 $secondary>{formatMessage(messages.title.qr)}</H4>
      <S.OL $sizeXXSmall $secondary>
        {messages.text.qr.steps.map((step) => (
          <S.LI key={step.id}>{formatMessage(step)}</S.LI>
        ))}
      </S.OL>
      <S.ImageWrapper>
        <Image src='https://www.fillmurray.com/g/140/140' alt='QR Code' />
      </S.ImageWrapper>
    </S.Column>
  );
};
