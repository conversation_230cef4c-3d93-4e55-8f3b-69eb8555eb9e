export interface QuickLoginForm {
  code: string;
}

export interface FullLoginForm {
  username: string;
  code: string;
}

export interface QuickLoginViewProps {
  onLogin: (code: string) => void;
  onFocused: () => void;
  onFullLoginSelect: () => void;
  error?: boolean;
}

export interface FullLoginViewProps {
  onLogin: (username: string, code: string) => void;
  onFocused: () => void;
  onQuickLoginSelect: () => void;
  error?: boolean;
}

export interface ConsentsViewProps {
  onAccept: (accepted: boolean) => void;
}

export interface SubscriptionViewProps {
  canBuy: boolean | undefined;
  isOpen: boolean;
  onClose: () => void;
}

export interface UserInfoLoadingViewProps {
  onLoaded: () => void;
}
