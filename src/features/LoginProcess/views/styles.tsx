import styled from 'styled-components';

import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { H3, Text } from 'components/Typography';
import { Input } from 'components/Forms/Input';

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: 3.2rem;
  margin-bottom: 2.5rem;
`;

export const CloseButtonWrapper = styled.div`
  position: absolute;
  top: -3.5rem;
  right: -4rem;
`;

export const StyledButtonSubmit = styled(PrimaryButton)`
  margin-top: 2.6rem;
  margin-bottom: 1.6rem;
`;

export const CheckboxWrapper = styled.div`
  div {
    flex-direction: row-reverse;
    justify-content: flex-end;

    label {
      margin-left: 1.2rem;
    }
  }
`;

export const Center = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 3.5rem;
`;

export const Column = styled.div<{ wide?: boolean }>`
  display: flex;
  flex-direction: column;
  max-width: 40rem;

  & > button:last-child {
    padding: 1rem ${({ wide }) => (wide ? '3.5rem' : '2.2rem')};
  }
`;

export const Container = styled.div<{ isRow?: boolean }>`
  display: grid;
  grid-auto-flow: ${({ isRow }) => (isRow ? 'row' : 'column')};
  grid-auto-columns: 1fr;
  column-gap: 7.4rem;

  & > div:last-child {
    min-width: 26rem;
  }
`;

export const Form = styled.form`
  & > button {
    width: 100%;
  }
`;

export const InfoText = styled(Text)`
  white-space: pre-line;
`;

export const StyledH3 = styled(H3)`
  white-space: nowrap;
`;

export const StyledInputWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
`;

export const StyledInput = styled(Input)`
  border-top: 0;
  border-right: 0;
  border-left: 0;
  font-size: ${({ theme }) => theme.fontSizes.small};
  padding: 0.5rem 0;
  margin: 2rem 0;

  &::placeholder {
    font-style: italic;
  }
`;

export const MessageContainer = styled.div<{ margin?: string }>`
  margin: ${({ margin }) => (margin ? margin : '2.4rem 0')};
`;

export const AnalyticsContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

export const ButtonWrapper = styled.div`
  max-width: 30rem;
`;

export const ImageWrapper = styled.div`
  width: 14rem;
  height: auto;
  align-self: center;
  margin-top: 2.2rem;
`;

export const BottomWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;

export const LI = styled.li`
  margin-bottom: 0.5rem;
`;

export const OL = styled(InfoText).attrs({
  as: 'ol',
})`
  list-style-type: decimal;
  margin-left: 3.2rem;
`;

export const StyledText = styled(Text)`
  position: absolute;
  top: 6rem;
`;

ButtonWrapper.displayName = 'AnalyticsButtonWrapper';
CheckboxWrapper.displayName = 'LoginModalCheckboxWrapper';
Column.displayName = 'LoginModalColumn';
Container.displayName = 'AnalyticsContainer';
Container.displayName = 'LoginModalContainer';
Container.displayName = 'ProfilesContainer';
Form.displayName = 'LoginModalForm';
ImageWrapper.displayName = 'LoginModalImageWrapper';
InfoText.displayName = 'LoginModalInfoText';
LI.displayName = 'LoginModalLI';
MessageContainer.displayName = 'AnalyticsMessageContainer';
OL.displayName = 'LoginModalOL';
StyledButtonSubmit.displayName = 'LoginModalStyledButton';
StyledInput.displayName = 'LoginModalStyledInput';
StyledInputWrapper.displayName = 'LoginModalInputWrapper';
