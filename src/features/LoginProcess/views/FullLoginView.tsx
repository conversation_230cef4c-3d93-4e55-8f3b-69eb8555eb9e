import { FC, PropsWithChildren, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';

import { useGlobalLoaderContext } from 'services/loader';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { CheckboxControl } from 'components/Forms/CheckboxControl';
import { H1, H3 } from 'components/Typography';
import { IconQuestionmark } from 'components/Icons/IconQuestionmark';
import { IconButton } from 'components/Buttons/IconButton';
import { useTheme } from 'theme';

import { messages } from './messages';
import * as S from './styles';
import { FullLoginForm, FullLoginViewProps } from './types';
import { LoginHelper } from './LoginHelper';
import { CODE_PATTERN, PIN_LENGTH, USERNAME_LENGTH } from './constants';

export const FullLoginView: FC<PropsWithChildren<FullLoginViewProps>> = ({
  onLogin,
  onQuickLoginSelect,
  onFocused,
}) => {
  const { isLoaderVisible } = useGlobalLoaderContext();
  const { formatMessage } = useIntl();

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
    watch,
    resetField,
  } = useForm<FullLoginForm>({ defaultValues: { username: '', code: '' } });
  const appTheme = useTheme();
  const [isLoginHelperOpen, setIsLoginHelperOpen] = useState(false);
  const { username: usernameInput, code: codeInput } = watch();

  const isLoginButtonDisabled = useMemo(() => {
    return (
      usernameInput === undefined ||
      usernameInput?.length < USERNAME_LENGTH ||
      codeInput?.length < PIN_LENGTH ||
      isLoaderVisible
    );
  }, [usernameInput, codeInput, isLoaderVisible]);

  const onSubmit: SubmitHandler<FullLoginForm> = () => {
    if (isLoginHelperOpen) return;
    const { username, code } = getValues();
    onLogin(username, code);
    resetField('username');
    resetField('code');
  };

  const handleLoginHelperOpen = () => {
    setIsLoginHelperOpen(true);
  };
  const handleLoginHelperClose = () => {
    setIsLoginHelperOpen(false);
  };

  const isUsernameError =
    errors.username ||
    // error || // this error is refernce to backend response and it is boolean
    (usernameInput?.length >= 1 && usernameInput?.length < USERNAME_LENGTH);

  const isCodeError =
    errors.code ||
    // || error // // this error is refernce to backend response and it is boolean
    (codeInput?.length >= 1 && codeInput?.length < PIN_LENGTH);

  return (
    <>
      <LoginHelper
        isOpen={isLoginHelperOpen}
        onClose={handleLoginHelperClose}
      />
      <S.Column>
        <S.Header>
          <H1 $secondary>{formatMessage(messages.title.login)} </H1>
          <IconButton
            iconColorHover={appTheme.colors.info}
            onClick={handleLoginHelperOpen}
            autoFocus={false}
            type={'button'}
            data-testid='FullLoginView-TooltipButton'
          >
            <IconQuestionmark />
          </IconButton>
        </S.Header>
        <S.Form onSubmit={handleSubmit(onSubmit)}>
          <S.StyledInputWrapper>
            <S.StyledInput
              placeholder={formatMessage(messages.form.full.username)}
              maxLength={USERNAME_LENGTH}
              value={usernameInput && usernameInput.toUpperCase()}
              {...register('username', {
                required: true,
                minLength: USERNAME_LENGTH,
                maxLength: USERNAME_LENGTH,
              })}
              onFocus={onFocused}
            />
            {isUsernameError && (
              <S.StyledText $highlight $sizeXSmall>
                {formatMessage(messages.errors.invalidUsername)}
              </S.StyledText>
            )}
          </S.StyledInputWrapper>

          <S.StyledInputWrapper>
            <S.StyledInput
              type='password'
              placeholder={formatMessage(messages.form.full.code)}
              maxLength={4}
              {...register('code', {
                required: true,
                pattern: CODE_PATTERN,
                minLength: PIN_LENGTH,
                maxLength: PIN_LENGTH,
              })}
              onFocus={onFocused}
            />
            {isCodeError && (
              <S.StyledText $highlight $sizeXSmall>
                {formatMessage(messages.errors.invalidPassword)}
              </S.StyledText>
            )}
          </S.StyledInputWrapper>
          <S.StyledButtonSubmit
            data-testid='button-submit-login'
            variant='orange'
            type='submit'
            disabled={isLoginButtonDisabled}
          >
            <H3>{formatMessage(messages.form.button.login)}</H3>
          </S.StyledButtonSubmit>
          <PrimaryButton
            variant='ghostWhite'
            onClick={onQuickLoginSelect}
            type='button'
          >
            <S.StyledH3>{formatMessage(messages.form.button.quick)}</S.StyledH3>
          </PrimaryButton>
        </S.Form>
      </S.Column>
    </>
  );
};
