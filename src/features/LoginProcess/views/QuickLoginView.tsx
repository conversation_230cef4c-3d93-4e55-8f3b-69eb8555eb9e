import React, { FC, PropsWithChildren, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';

import { useGlobalLoaderContext } from 'services/loader';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { CheckboxControl } from 'components/Forms/CheckboxControl';
import { H1, H3 } from 'components/Typography';

import * as S from './styles';
import { messages } from './messages';
import { QuickLoginForm, QuickLoginViewProps } from './types';

const PIN_LENGTH = 4;

export const QuickLoginView: FC<PropsWithChildren<QuickLoginViewProps>> = ({
  onLogin,
  onFullLoginSelect,
  onFocused,
}) => {
  const { isLoaderVisible } = useGlobalLoaderContext();
  const { formatMessage } = useIntl();

  const { register, handleSubmit, watch, resetField } =
    useForm<QuickLoginForm>();

  const { code: codeInput } = watch();

  const isLoginButtonDisabled = useMemo(() => {
    return (
      codeInput === undefined ||
      codeInput?.length < PIN_LENGTH ||
      isLoaderVisible
    );
  }, [codeInput, isLoaderVisible]);

  const showCodeError =
    codeInput?.length >= 1 && codeInput?.length < PIN_LENGTH;

  const onSubmit: SubmitHandler<QuickLoginForm> = ({ code }) => {
    onLogin(code);
    resetField('code');
  };

  return (
    <>
      <S.Column wide>
        <S.Header>
          <H1 $secondary>{formatMessage(messages.title.login)}</H1>
        </S.Header>
        <S.Form onSubmit={handleSubmit(onSubmit)}>
          <S.StyledInputWrapper>
            <S.StyledInput
              placeholder={formatMessage(messages.form.quick.code)}
              defaultValue=''
              maxLength={PIN_LENGTH}
              autoFocus
              {...register('code', {
                required: true,
              })}
              onFocus={onFocused}
            />
            {showCodeError && (
              <S.StyledText $highlight $sizeXSmall>
                {formatMessage(messages.errors.invalidPin)}
              </S.StyledText>
            )}
          </S.StyledInputWrapper>
          <S.InfoText $sizeXXSmall $secondary>
            {formatMessage(messages.text.quick.description)}
          </S.InfoText>
          <S.StyledButtonSubmit
            data-testid='button-submit-login'
            variant='orange'
            type='submit'
            disabled={isLoginButtonDisabled}
          >
            <H3>{formatMessage(messages.form.button.login)}</H3>
          </S.StyledButtonSubmit>
          <PrimaryButton variant='ghostWhite' onClick={onFullLoginSelect}>
            <S.StyledH3>{formatMessage(messages.form.button.full)}</S.StyledH3>
          </PrimaryButton>
        </S.Form>
      </S.Column>
    </>
  );
};
