import { FC, PropsWithChildren, useState } from 'react';
import { useIntl } from 'react-intl';

import { PRIVACY_POLICY_EXTERNAL_LINK } from 'services/config/config';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { CheckboxControl } from 'components/Forms/CheckboxControl';
import { H1, H3, Link, Text } from 'components/Typography';
import { HoverTooltip } from 'components/HooverTooltip';

import * as S from './styles';
import { messages } from './messages';
import { ConsentsViewProps } from './types';

export const ConsentsView: FC<PropsWithChildren<ConsentsViewProps>> = ({
  onAccept,
}) => {
  const { formatMessage } = useIntl();

  const [accepted, setAccepted] = useState(false);
  const onAcceptClick = () => setAccepted((prev) => !prev);
  const onBeginClick = () => onAccept(accepted);

  return (
    <>
      <S.Header>
        <H1 $secondary>{formatMessage(messages.title.login)}</H1>
      </S.Header>
      <S.Container isRow>
        <S.MessageContainer>
          <Text $sizeXXSmall $secondary>
            {formatMessage(messages.text.consents.part_1, {
              anonymousStats: (
                <HoverTooltip
                  textContent={formatMessage(messages.text.consents.stats)}
                  tooltipContent={formatMessage(
                    messages.text.consents.consentsInfo,
                  )}
                  width={200}
                />
              ),
            })}
          </Text>
        </S.MessageContainer>
        <S.CheckboxWrapper onClick={onAcceptClick}>
          <CheckboxControl
            id='analytics-agreement'
            label={formatMessage(messages.form.accept)}
            isChecked={accepted}
            onCheck={onAcceptClick}
            $sizeSmall
            $secondary
          />
        </S.CheckboxWrapper>
        <S.MessageContainer margin='4.8rem 0'>
          <Text $sizeXXSmall $secondary>
            {formatMessage(messages.text.consents.part_2, {
              privacyPolicy: (
                <Link
                  $sizeXXSmall
                  href={PRIVACY_POLICY_EXTERNAL_LINK}
                  target='_blank'
                >
                  {formatMessage(messages.text.consents.privacyPolicy)}
                </Link>
              ),
            })}
          </Text>
        </S.MessageContainer>

        <S.ButtonWrapper>
          <PrimaryButton variant='orange' onClick={onBeginClick}>
            <H3>{formatMessage(messages.form.button.begin)}</H3>
          </PrimaryButton>
        </S.ButtonWrapper>
      </S.Container>
    </>
  );
};
