// Tymczasowy plik do testowania błędów maintenance
// <PERSON><PERSON><PERSON><PERSON> zaimport<PERSON> to w komponencie i wywołać funkcję

import { AxiosError } from 'axios';

export const createMaintenanceError = (isNewApi = true): AxiosError => {
  const errorData = isNewApi 
    ? {
        error: 'G000015',
        errCode: 'DOWN_FOR_MAINTENANCE',
        errMsg: 'Trwają prace serwisowe. System będzie znów dostępny po zakończeniu prac.',
      }
    : {
        errCode: 'DOWN_FOR_MAINTENANCE',
        errMsg: 'Trwają prace serwisowe. System będzie znów dostępny po zakończeniu prac.',
      };

  const mockError = {
    isAxiosError: true,
    response: {
      status: 503,
      data: errorData,
    },
    config: {},
  } as AxiosError;

  return mockError;
};

// Przykład użycia w komponencie:
// import { createMaintenanceError } from './testMaintenanceError';
// import { getUserErrorType, getErrorMessageForDownForMaintenance } from 'services/error';
// 
// const testError = () => {
//   const error = createMaintenanceError(true); // true = nowe API, false = stare API
//   const errorType = getUserErrorType(error);
//   const message = getErrorMessageForDownForMaintenance(error);
//   console.log('Error type:', errorType);
//   console.log('Error message:', message);
//   showErrorModal(errorType, message || undefined);
// };
