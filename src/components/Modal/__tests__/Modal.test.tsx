import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import { Modal } from '../Modal';

describe('Components:Modal', () => {
  it('should render Modal component', () => {
    render(
      <Modal title='Title Test' isOpen onClose={() => ({})}>
        <span>Children Test</span>
      </Modal>,
    );

    expect(screen.getByText('Title Test')).toBeInTheDocument();
    expect(screen.getByText('Children Test')).toBeInTheDocument();
  });
  it('should render Modal with close button', () => {
    render(
      <Modal title='Title Test' isOpen onClose={() => ({})} withCloseButton>
        <span>Children Test</span>
      </Modal>,
    );

    expect(screen.getByTestId('IconCloseSmall')).toBeInTheDocument();
  });
});
