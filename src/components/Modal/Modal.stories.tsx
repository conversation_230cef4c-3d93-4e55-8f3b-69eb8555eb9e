import { Story, Meta } from '@storybook/react';

import { Text } from 'components/Typography';

import { Modal } from './Modal';

export default {
  title: 'Components/Modal',
  component: Modal,
} as Meta;

const Template: Story = ({ title, isModalOpen }) => (
  <>
    <div style={{ color: 'white', width: '500px' }}>
      Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae corrupti
      expedita neque, voluptatibus perspiciatis id soluta eveniet quibusdam
      magni voluptatem nisi reprehenderit dolore fugiat fuga nam deserunt rerum
      consectetur animi sunt, laudantium corporis veritatis. Aliquam voluptatum
      fugiat voluptates maxime? Incidunt eligendi quae velit porro voluptas hic
      iste, id ducimus autem?
    </div>
    <Modal title={title} onClose={() => ({})} isOpen={isModalOpen}>
      <div
        style={{
          width: '500px',
          height: '250px',
          marginTop: '34px',
        }}
      >
        <Text>Modal Content</Text>
      </div>
    </Modal>
  </>
);

export const Default = Template.bind({});
Default.args = {
  title: 'Modal Header',
  isModalOpen: false,
};
