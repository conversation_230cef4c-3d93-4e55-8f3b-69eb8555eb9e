import styled from 'styled-components';

import { convertHexToRgbaString, RenderLayer } from 'theme';

export const variantsModal = {
  hidden: {
    transform: 'scale(0) translate(-50%, -50%)',
  },
  visible: {
    transform: 'scale(1) translate(-50%, -50%)',
  },
};

export const Container = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: ${({ theme }) => theme.colors.modalBackground};
  border: 0.1rem solid ${({ theme }) => theme.colors.doveGrayLight};
  border-radius: 0.8rem;
  min-width: 45rem;
  max-width: 75rem;
  padding: 0 4.8rem 3.2rem 4.8rem;

  ${RenderLayer('modal')};

  h1 {
    line-height: 4.4rem;
  }
`;

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: 3.2rem;
`;

export const LoaderWrapper = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  ${RenderLayer('modal')};
`;

export const Overlay = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: ${({ theme }) => convertHexToRgbaString(theme.colors.black, 0.5)};

  ${RenderLayer('modal')};
`;

Container.displayName = 'ModalContainer';
Header.displayName = 'ModalHeader';
LoaderWrapper.displayName = 'ModalLoaderWrapper';
Overlay.displayName = 'ModalOverlay';
