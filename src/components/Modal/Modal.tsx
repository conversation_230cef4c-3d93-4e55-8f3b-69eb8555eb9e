import { AnimatePresence, motion } from 'framer-motion';

import { H1 } from 'components/Typography';
import { IconButton } from 'components/Buttons/IconButton';
import { IconCloseSmall } from 'components/Icons';

import { ModalProps } from './types';
import * as S from './styles';
import { variantsModal } from './styles';

export function Modal({
  title,
  children,
  onClose,
  isOpen,
  withCloseButton,
  withOverlay = true,
}: ModalProps) {
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {withOverlay && <S.Overlay onClick={onClose} />}
      <AnimatePresence>
        <S.Container
          data-testid='modal'
          as={motion.div}
          variants={variantsModal}
          initial='hidden'
          animate='visible'
          exit='hidden'
        >
          {Boolean(title) && (
            <S.Header>
              <H1 $secondary>{title}</H1>
              {withCloseButton && (
                <IconButton onClick={onClose}>
                  <IconCloseSmall />
                </IconButton>
              )}
            </S.<PERSON>er>
          )}
          {children}
        </S.Container>
      </AnimatePresence>
    </>
  );
}
