import styled from 'styled-components';

export const Container = styled.div`
  width: 19.6rem;
  min-width: 19.6rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: pointer;
  &:hover {
    transform: scale(1.1);
  }
  padding: 1rem 0;
`;

export const ImageWrapper = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

export const ChannelLogoContainer = styled.div<{ $withBorder: boolean }>`
  position: relative;
  width: 100%;
  height: 14rem;
  border-radius: 12px;
  background-color: ${({ theme }) => theme.colors.channelBackground};
  margin-bottom: 0.6rem;
  border: ${({ $withBorder, theme }) =>
    $withBorder ? `0.4rem solid ${theme.colors.primary}` : 'none'};
`;
