import { Story, Meta } from '@storybook/react';

import { EpisodePreview } from './EpisodePreview';
import React from 'react';

export default {
  title: 'Components/EpisodePreview',
  component: EpisodePreview,
} as Meta;

const Template: Story = ({
  name,
  startDate,
  endDate,
  logoSignature,
  onClickHandler,
  seasonName,
  episodeNumber,
  isCurrentViewedEpisode,
}) => (
  <>
    <EpisodePreview
      name={name}
      startDate={startDate}
      endDate={endDate}
      logoSignature={logoSignature}
      onClickHandler={onClickHandler}
      seasonName={seasonName}
      episodeNumber={episodeNumber}
      isCurrentViewedEpisode={isCurrentViewedEpisode}
    />
  </>
);

export const Default = Template.bind({});
Default.args = {
  name: 'The boys',
  startDate: 1700622000,
  endDate: 1700625000,
  logoSignature: 'L2F0dGFjaG1lbnRzLzE0MTM1LUxPR08ucG5n',
  onClickHandler: () => {
    alert('Open new program details');
  },
  seasonName: '2',
  episodeNumber: '141',
  isCurrentViewedEpisode: true,
};
