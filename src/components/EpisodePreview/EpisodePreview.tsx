import { useIntl } from 'react-intl';
import { secondsToMilliseconds } from 'date-fns';

import { IMAGE_URL } from 'services/api/oldApi';
import {
  formatTimestampDate,
  getLiveStatus,
  getRelativeDayNameIfExist,
  RelativeDay,
} from 'utils/dateUtils';
import { getPublicAssetUrl } from 'utils/url';
import { Image } from 'components/Image';
import { Text } from 'components/Typography';

import { messages } from './messages';
import { EpisodePreviewProps } from './types';
import * as S from './styles';

export const EpisodePreview = ({
  name,
  logoSignature,
  onClickHandler,
  startDate,
  endDate,
  seasonName,
  episodeNumber,
  isCurrentViewedEpisode,
}: EpisodePreviewProps) => {
  const { formatMessage } = useIntl();

  const getEpisodeTimeDetails = () => {
    const relativeDay = getRelativeDayNameIfExist(startDate);
    const relativeDayName = {
      [RelativeDay.TODAY]: formatMessage(messages.today),
      [RelativeDay.TOMORROW]: formatMessage(messages.tomorrow),
      [RelativeDay.YESTERDAY]: formatMessage(messages.yesterday),
      [RelativeDay.OUT_OF_RANGE]: '',
    };
    const isLiveNow = getLiveStatus(
      secondsToMilliseconds(startDate),
      secondsToMilliseconds(endDate),
    );
    const relativeTimeName = isLiveNow
      ? formatMessage(messages.now)
      : relativeDayName[relativeDay];

    return relativeTimeName
      ? `${relativeTimeName} - ${formatTimestampDate(startDate, true)}`
      : formatTimestampDate(startDate);
  };

  return (
    <S.Container onClick={onClickHandler}>
      <S.ChannelLogoContainer $withBorder={isCurrentViewedEpisode}>
        <S.ImageWrapper>
          <Image
            src={getPublicAssetUrl(IMAGE_URL, logoSignature)}
            alt={name}
            placeholderScale={0.4}
          />
        </S.ImageWrapper>
      </S.ChannelLogoContainer>
      <Text title={name} $sizeMedium $withWrap>
        {name}
      </Text>
      <Text $sizeSmall $tertiaryLight>
        {Boolean(seasonName) &&
          formatMessage(messages.season, { seasonName: seasonName })}
        {Boolean(episodeNumber) &&
          formatMessage(messages.episode, { episodeNumber: episodeNumber })}
      </Text>
      <Text
        data-testid='EpisodePreview-DateAndTimeDetails'
        $sizeSmall
        $tertiaryLight
      >
        {getEpisodeTimeDetails()}
      </Text>
    </S.Container>
  );
};
