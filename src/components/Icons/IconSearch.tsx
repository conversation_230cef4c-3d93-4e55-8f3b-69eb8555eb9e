import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconSearch = ({
  color,
  scale,
  height = 24,
  width = 24,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconSearch' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 ${width} ${height}`}>
        <S.Path
          d='M23.781 22.654l-1.127 1.127a.752.752 0 0 1-1.061 0l-6.773-6.773a9.374 9.374 0 1 1 2.188-2.188l6.773 6.773a.752.752 0 0 1 0 1.061zM9.375 3a6.375 6.375 0 1 0 6.375 6.375A6.375 6.375 0 0 0 9.375 3z'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
