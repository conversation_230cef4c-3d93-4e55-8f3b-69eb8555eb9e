import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconLock = ({
  color,
  scale,
  height = 22,
  width = 16,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconLock' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 16 22'>
        <S.Path
          d='M13.964 8.36h-.582V5.133A5.335 5.335 0 0 0 8 0a5.335 5.335 0 0 0-5.382 5.133V8.36H0v11.587A2 2 0 0 0 2.036 22H16V10.413a2 2 0 0 0-2.036-2.053zm-4.946 6.893.291 3.52H6.691l.291-3.52a2.038 2.038 0 0 1-1.018-1.76A1.967 1.967 0 0 1 8 11.587a1.967 1.967 0 0 1 2.036 1.907 1.869 1.869 0 0 1-1.018 1.759zm2.327-6.893h-6.69V5.133a3.137 3.137 0 0 1 1.018-2.346A3.288 3.288 0 0 1 8 1.907a3.288 3.288 0 0 1 2.327.88 3.475 3.475 0 0 1 1.018 2.347V8.36z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
