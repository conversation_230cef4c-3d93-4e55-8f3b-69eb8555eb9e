import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconTv = ({
  color,
  scale,
  height = 48,
  width = 63,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconTv' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 63 48'>
        <S.Path
          d='M59.076,0L3.691,0C1.66,0.023 0.002,1.699 0,3.73L0,38.806C0.009,40.814 1.664,42.461 3.672,42.461C3.678,42.461 3.685,42.461 3.691,42.461L9.23,42.461L9.23,48L53.539,48L53.539,42.461L59.078,42.461C59.084,42.461 59.091,42.461 59.097,42.461C61.105,42.461 62.76,40.814 62.769,38.806L62.769,3.73C62.767,1.698 61.107,0.022 59.075,0L59.076,0ZM31.385,45.23C30.372,45.23 29.539,44.397 29.539,43.384C29.539,42.371 30.372,41.538 31.385,41.538C32.398,41.538 33.231,42.371 33.231,43.384C33.231,44.397 32.398,45.23 31.385,45.23ZM59.076,38.769L3.691,38.769L3.691,3.691L59.076,3.691L59.076,38.769Z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
