import { Story, Meta } from '@storybook/react';
import React from 'react';
import * as Icon from './index';

export default {
  title: 'Components/Icons',
} as Meta;

export const Default: Story = (args) => (
  <div style={{ display: 'flex', alignItems: 'center' }}>
    <Icon.IconActorPlaceholder {...args} />
    <Icon.IconArrowLeft {...args} />
    <Icon.IconArrowRight {...args} />
    <Icon.IconArrowLeftCircle {...args} />
    <Icon.IconCalendar {...args} />
    <Icon.IconBin {...args} />
    <Icon.IconCheckInCircle {...args} />
    <Icon.IconChevronDown {...args} />
    <Icon.IconClock {...args} />
    <Icon.IconClose {...args} />
    <Icon.IconCloseSmall {...args} />
    <Icon.IconCloseSmall {...args} />
    <Icon.IconCrossInCircle {...args} />
    <Icon.IconFilter {...args} />
    <Icon.IconHourglass {...args} />
    <Icon.IconFolder {...args} />
    <Icon.IconGoBackward {...args} />
    <Icon.IconGrid {...args} />
    <Icon.IconHomeArea {...args} />
    <Icon.IconHomeAreaError {...args} />
    <Icon.IconHourglass {...args} />
    <Icon.IconInfo scale={0.7} {...args} />
    <Icon.IconLock {...args} />
    <Icon.IconPC {...args} />
    <Icon.IconPhone {...args} />
    <Icon.IconPictureInPicture {...args} />
    <Icon.IconPlay scale={0.7} {...args} />
    <Icon.IconProfileDefault scale={0.48} {...args} />
    <Icon.IconQuestionmark {...args} />
    <Icon.IconRec scale={0.7} {...args} />
    <Icon.IconSearch {...args} />
    <Icon.IconSeries {...args} />
    <Icon.IconSettings {...args} />
    <Icon.IconStar isEmpty={false} {...args} />
    <Icon.IconStb {...args} />
    <Icon.IconStar isEmpty={true} {...args} />
    <Icon.IconTablet {...args} />
    <Icon.IconTv {...args} />
    <Icon.IconTVGo {...args} />
    <Icon.IconVideoPlaceholder {...args} />
    <Icon.IconWarning {...args} />
    <Icon.IconBlackout {...args} />
    <Icon.IconCantView {...args} />
  </div>
);

export const ActorPlaceholder: Story = (args) => (
  <Icon.IconActorPlaceholder {...args} />
);
export const ArrowLeft: Story = (args) => <Icon.IconArrowLeft {...args} />;
export const ArrowRight: Story = (args) => <Icon.IconArrowRight {...args} />;
export const ArrowLeftCircle: Story = (args) => (
  <Icon.IconArrowLeftCircle {...args} />
);
export const Calendar: Story = (args) => <Icon.IconCalendar {...args} />;
export const Bin: Story = (args) => <Icon.IconBin {...args} />;
export const CheckInCircle: Story = (args) => (
  <Icon.IconCheckInCircle {...args} />
);
export const ChevronDown: Story = (args) => <Icon.IconChevronDown {...args} />;
export const Clock: Story = (args) => <Icon.IconClock {...args} />;
export const Close: Story = (args) => <Icon.IconClose {...args} />;
export const CloseSmall: Story = (args) => <Icon.IconCloseSmall {...args} />;
export const CloseCircle: Story = (args) => <Icon.IconCloseCircle {...args} />;
export const CrossInCircle: Story = (args) => (
  <Icon.IconCrossInCircle {...args} />
);
export const Filter: Story = (args) => <Icon.IconFilter {...args} />;
export const Folder: Story = (args) => <Icon.IconFolder {...args} />;
export const GoBackward: Story = (args) => <Icon.IconGoBackward {...args} />;
export const Grid: Story = (args) => <Icon.IconGrid {...args} />;
export const HomeArea: Story = (args) => <Icon.IconHomeArea {...args} />;
export const HomeAreaError: Story = (args) => (
  <Icon.IconHomeAreaError {...args} />
);
export const Hourglass: Story = (args) => <Icon.IconHourglass {...args} />;
export const Info: Story = (args) => <Icon.IconInfo {...args} />;
export const Lock: Story = (args) => <Icon.IconLock {...args} />;
export const PC: Story = (args) => <Icon.IconPC {...args} />;
export const Phone: Story = (args) => <Icon.IconPhone {...args} />;
export const PictureInPicture: Story = (args) => (
  <Icon.IconPictureInPicture {...args} />
);
export const Play: Story = (args) => <Icon.IconPlay {...args} />;
export const ProfileDefault: Story = (args) => (
  <Icon.IconProfileDefault {...args} />
);
export const Questionmark: Story = (args) => (
  <Icon.IconQuestionmark {...args} />
);
export const Rec: Story = (args) => <Icon.IconRec {...args} />;
export const Search: Story = (args) => <Icon.IconSearch {...args} />;
export const Series: Story = (args) => <Icon.IconSeries {...args} />;
export const Settings: Story = (args) => <Icon.IconSettings {...args} />;
export const Star: Story = (args) => (
  <Icon.IconStar isEmpty={false} {...args} />
);
export const Stb: Story = (args) => <Icon.IconStb {...args} />;
export const StarEmpty: Story = (args) => (
  <Icon.IconStar isEmpty={true} {...args} />
);
export const Tablet: Story = (args) => <Icon.IconTablet {...args} />;
export const Tv: Story = (args) => <Icon.IconTv {...args} />;
export const TVGo: Story = (args) => <Icon.IconTVGo {...args} />;
export const VideoPlaceholder: Story = (args) => (
  <Icon.IconVideoPlaceholder {...args} />
);
export const Warning: Story = (args) => <Icon.IconWarning {...args} />;
export const CantView: Story = (args) => <Icon.IconCantView {...args} />;
