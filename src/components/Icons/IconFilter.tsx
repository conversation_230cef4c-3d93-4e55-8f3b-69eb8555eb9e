import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconFilter = ({
  color,
  scale,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconFilter' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M32 1.883c-0.034-1.047-0.891-1.883-1.943-1.883-0.020 0-0.040 0-0.060 0.001l0.003-0h-28c-0.017-0.001-0.037-0.001-0.057-0.001-1.052 0-1.909 0.836-1.943 1.88l-0 0.003v2.824l13 12.235v9.412l6 5.647v-15.059l13-12.236z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
