import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCatchup = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconCatchup' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 1229 1024'>
        <S.Path
          d='M517.907 307.2l-389.907-204.8v819.2l389.907-204.8 378.093-204.8z'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
