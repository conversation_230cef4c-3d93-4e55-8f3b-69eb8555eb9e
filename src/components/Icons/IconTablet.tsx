import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconTablet = ({
  color,
  scale,
  height = 60.001,
  width = 45.861,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconTablet' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 45.861 60.001'>
        <S.Path
          d='M42.326.6H3.535A3.526 3.526 0 0 0 0 4.135v52.93A3.589 3.589 0 0 0 3.535 60.6h38.79a3.589 3.589 0 0 0 3.535-3.535V4.135A3.526 3.526 0 0 0 42.326.6zM22.941 58.882a1.719 1.719 0 1 1 1.719-1.719 1.777 1.777 0 0 1-1.731 1.719zm19.385-5.349H3.535V4.135h38.79z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
          transform='translate(0 -.6)'
        />
      </S.SVG>
    </S.Container>
  );
};
