import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCantView = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconCantView' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 1024 1024'>
        <S.Path
          d='M143.77 768h-66.97c0 0 0 38.912 0 51.2 0 4.506 1.229 8.806 3.277 12.493l63.693-63.693zM220.57 691.2h-41.37v-435.2h476.57l51.2-51.2h-527.77c-28.262 0-51.2 22.938-51.2 51.2v408.576c0 0.307 0 0.717 0 1.024v0 76.8h41.37l51.2-51.2zM896 256c0-4.813-0.717-9.421-1.946-13.824l-49.254 49.254v399.77h-399.77l-51.2 51.2h502.17v-76.8c0-0.307 0-0.717 0-1.024v-408.576zM563.2 793.6h-102.4c-14.131 0-25.6-11.469-25.6-25.6h-66.97l-76.8 76.8h630.17c14.131 0 25.6-11.469 25.6-25.6 0-12.288 0-51.2 0-51.2h-358.4c0 14.131-11.469 25.6-25.6 25.6zM920.576 50.176l-870.4 870.4 54.272 54.272 870.4-870.4-54.272-54.272z
          '
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
