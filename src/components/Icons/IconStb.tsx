import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconStb = ({
  color,
  scale,
  height = 20,
  width = 27,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconStb' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0  ${width} ${height}`}>
        <S.Path
          d='M24.615,0l-23.077,0c-0.846,0.01 -1.537,0.708 -1.538,1.554l0,14.615c0.004,0.837 0.693,1.523 1.53,1.523c0.003,0 0.005,0 0.008,0l2.308,0l0,2.308l18.462,0l0,-2.308l2.308,0c0.003,0 0.005,0 0.008,0c0.837,0 1.526,-0.686 1.53,-1.523l0,-14.615c-0.001,-0.846 -0.693,-1.545 -1.539,-1.554Zm-11.538,18.846c-0.422,0 -0.769,-0.347 -0.769,-0.769c0,-0.422 0.347,-0.769 0.769,-0.769c0.422,0 0.769,0.347 0.769,0.769c0,0.422 -0.347,0.769 -0.769,0.769Zm11.538,-2.692l-23.077,0l0,-14.616l23.077,0l0,14.616Z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
