import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconChevronDown = ({
  color,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconChevronDown' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M28.794 11.242l-2.486-2.448-10.307 10.144-10.307-10.144-2.486 2.448 12.794 12.592 2.486-2.448z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
