import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconHourglass = ({
  scale,
  height = 32,
  width = 32,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconHourglass' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M16 9.6h-6.147c0.147 0.23 0.325 0.425 0.531 0.585l0.005 0.004 5.611 4.211 5.611-4.211c0.212-0.164 0.39-0.359 0.531-0.58l0.005-0.009h-6.147zM18.701 15.436l4.376-3.347c1.098-0.853 1.723-2.060 1.723-3.326v-3.963h0.8v-1.6c0-0.442-0.358-0.8-0.8-0.8v0h-17.6c-0.442 0-0.8 0.358-0.8 0.8v0 1.6h0.8v3.963c0 1.266 0.626 2.473 1.723 3.326l4.376 3.347c0.401 0.299 0.401 0.83 0 1.129l-4.376 3.347c-1.098 0.853-1.723 2.060-1.723 3.326v3.963h-0.8v1.6c0 0.442 0.358 0.8 0.8 0.8v0h17.6c0.442 0 0.8-0.358 0.8-0.8v0-1.6h-0.8v-3.963c0-1.266-0.626-2.473-1.723-3.326l-4.376-3.347c-0.401-0.299-0.401-0.83 0-1.129zM17.734 17.84l4.366 3.339c0.699 0.545 1.1 1.295 1.1 2.059v3.963h-1.085c-1.054-3.15-4.745-6.4-6.115-6.4-1.38 0-5.064 3.25-6.115 6.4h-1.085v-3.963c0-0.764 0.401-1.514 1.1-2.059l4.365-3.339c0.594-0.447 0.934-1.117 0.934-1.84s-0.34-1.392-0.934-1.84l-4.365-3.339c-0.699-0.545-1.1-1.295-1.1-2.059v-3.963h14.4v3.963c0 0.764-0.401 1.514-1.1 2.059l-4.365 3.339c-0.594 0.447-0.934 1.117-0.934 1.84s0.34 1.392 0.934 1.84z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
