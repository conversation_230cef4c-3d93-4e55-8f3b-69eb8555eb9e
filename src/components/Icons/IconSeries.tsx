import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconSeries = ({
  color,
  scale,
  height = 28,
  width = 34,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconSeries' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 34 28`}>
        <S.Path
          d='M34 28H8.5a2.068 2.068 0 0 1-2.125-2V6h25.5A2.067 2.067 0 0 1 34 8v20zm-4.249-4v2h2.125v-2zM25.5 24v2h2.125v-2zm-4.25 0v2h2.126v-2zM17 24v2h2.125v-2zm-4.25 0v2h2.125v-2zM8.5 24v2h2.126v-2zm12.75-12v10h10.626V12zM8.5 12v10h10.625V12zm21.251-4v2h2.125V8zM25.5 8v2h2.125V8zm-4.25 0v2h2.126V8zM17 8v2h2.125V8zm-4.25 0v2h2.125V8zM8.5 8v2h2.126V8zM5.312 25a2.068 2.068 0 0 1-2.123-2V3h25.5a2.067 2.067 0 0 1 2.124 2H5.312v20zm-3.187-3A2.068 2.068 0 0 1 0 20V0h25.5a2.067 2.067 0 0 1 2.125 2h-25.5v20z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
