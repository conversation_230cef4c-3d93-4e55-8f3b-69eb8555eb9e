import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconClock = ({
  color,
  scale,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconClock' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M22.495 22.231l0-0c0.263-0.256 0.426-0.614 0.426-1.010 0-0.383-0.153-0.731-0.401-0.985l-5.101-5.101v-9.769c0-0.78-0.632-1.412-1.412-1.412s-1.412 0.632-1.412 1.412v10.353c0 0.023 0 0.059 0 0.069s0 0.021 0 0.033c0 0.012 0 0.023 0 0.036s0 0.032 0 0.041c0 0.009-0.008 0.012 0 0.027s0.008 0.029 0.008 0.044v0.023c0.003 0.016 0.009 0.031 0.009 0.045v0.023l0.012 0.043 0.007 0.024 0.013 0.040 0.009 0.027 0.013 0.035 0.007 0.035 0.013 0.029 0.016 0.035 0.012 0.025 0.019 0.037 0.012 0.021 0.023 0.039 0.012 0.019 0.025 0.039 0.040 0.056 0.043 0.053 0.023 0.027 5.584 5.589c0.256 0.256 0.609 0.415 1 0.415s0.744-0.159 1-0.415zM16 0v0c-8.837 0-16 7.163-16 16v0c0 8.837 7.163 16 16 16s16-7.163 16-16c0-8.837-7.163-16-16-16zM16 2.824c7.277 0 13.176 5.899 13.176 13.176s-5.899 13.176-13.176 13.176c-7.277 0-13.176-5.899-13.176-13.176v0c0-7.277 5.899-13.176 13.176-13.176v0z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
