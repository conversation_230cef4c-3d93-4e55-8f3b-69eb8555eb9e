import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconSettings = ({
  color,
  scale,
  height = 24,
  width = 24,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconSettings' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 ${width} ${height}`}>
        <S.Path
          d='M113.661 105.035c0-3.258 3.555-.793 1.9-4.789s-2.426.26-4.73-2.043 1.953-3.074-2.043-4.729-1.532 1.9-4.789 1.9-.793-3.554-4.789-1.9.26 2.425-2.043 4.729-3.074-1.953-4.729 2.043 1.9 1.531 1.9 4.789-3.555.793-1.9 4.789 2.425-.26 4.729 2.042-1.952 3.075 2.043 4.73 1.531-1.9 4.789-1.9.792 3.554 4.789 1.9-.261-2.426 2.043-4.73 3.074 1.953 4.73-2.042-1.9-1.532-1.9-4.789zm-9.646-5.875a5.89 5.89 0 1 1-5.89 5.89 5.89 5.89 0 0 1 5.89-5.89z'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
          transform='translate(-92 -93.035)'
        />
      </S.SVG>
    </S.Container>
  );
};
