import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconRec = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconRec' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 48'>
        <S.Path
          d='M44.6,35.7l-5.4-4.9l-2-1.9v8.4c0,1.5-1.2,2.7-2.6,2.7H4.1c-1.5,0-2.7-1.2-2.6-2.7
          c0,0,0,0,0,0V10.7C1.5,9.2,2.7,8,4.1,8c0,0,0,0,0,0h30.4c1.5,0,2.7,1.2,2.6,2.7c0,0,0,0,0,0v8.5l2-1.9l5.4-5c0.4-0.4,1.1-0.4,1.6,0
          c0.2,0.2,0.3,0.4,0.3,0.7v22c0,0.6-0.6,1.1-1.2,1C45.1,36,44.8,35.9,44.6,35.7z M13.4,28.8c-0.1-0.2-0.1-0.5-0.1-0.7
          c0-0.2,0-0.5-0.1-0.7s0-0.4,0-0.6c0-0.3-0.1-0.5-0.1-0.8c-0.1-0.3-0.1-0.5-0.3-0.8c-0.1-0.2-0.3-0.4-0.5-0.6
          c-0.2-0.2-0.5-0.3-0.7-0.4v0c0.5-0.2,1-0.6,1.3-1.1c0.3-0.5,0.4-1.1,0.4-1.7c0-0.4-0.1-0.8-0.2-1.2c-0.1-0.4-0.4-0.7-0.6-0.9
          c-0.3-0.3-0.6-0.5-1-0.6c-0.4-0.2-0.9-0.2-1.3-0.2H4.4v10.8h2.4v-4.2h2.4c0.5,0,0.9,0.1,1.3,0.4c0.3,0.4,0.5,0.8,0.5,1.2
          c0.1,0.4,0.1,0.9,0.1,1.4c0,0.4,0.1,0.8,0.2,1.2h2.4C13.5,29.2,13.5,29,13.4,28.8z M23.3,27.3h-5.8v-2.7h5.2v-1.9h-5.2v-2.3h5.7v-2
          h-8v10.8h8.2L23.3,27.3z M26.8,22.6c0.1-0.4,0.3-0.8,0.5-1.2c0.2-0.3,0.5-0.6,0.9-0.8c0.4-0.2,0.9-0.3,1.4-0.3
          c0.6,0,1.1,0.2,1.6,0.5c0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.3,0.8h2.3c-0.1-0.6-0.2-1.2-0.5-1.7c-0.3-0.5-0.6-0.9-1-1.2
          c-0.4-0.3-0.9-0.6-1.4-0.8c-1.3-0.4-2.7-0.3-3.9,0.2c-0.6,0.3-1.2,0.7-1.7,1.2c-0.5,0.5-0.8,1.1-1,1.8c-0.2,0.7-0.4,1.5-0.4,2.3
          c0,0.8,0.1,1.5,0.4,2.2c0.2,0.7,0.6,1.3,1,1.8c0.5,0.5,1,0.9,1.7,1.2c0.7,0.3,1.5,0.4,2.2,0.4c0.6,0,1.2-0.1,1.8-0.3
          c0.5-0.2,1-0.5,1.4-0.9c0.4-0.4,0.8-0.9,1-1.4c0.3-0.6,0.4-1.2,0.5-1.8H32c-0.1,0.7-0.3,1.3-0.7,1.8c-0.4,0.5-1.1,0.7-1.7,0.7
          c-0.5,0-0.9-0.1-1.4-0.3c-0.4-0.2-0.7-0.5-0.9-0.8c-0.2-0.4-0.4-0.7-0.5-1.2c-0.1-0.4-0.2-0.9-0.2-1.3C26.6,23.5,26.7,23,26.8,22.6
          L26.8,22.6z M9.4,23.4H6.8v-3.1h2.6c0.4,0,0.9,0.1,1.2,0.4c0.3,0.3,0.4,0.7,0.4,1.1c0,0.4-0.1,0.9-0.4,1.2
          C10.2,23.3,9.8,23.4,9.4,23.4z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
