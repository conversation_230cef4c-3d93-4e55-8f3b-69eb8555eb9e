import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconActorPlaceholder = ({
  color,
  scale,
  height = 161,
  width = 134,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconAddProfile' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 134 161'>
        <S.Path
          d='M0 161c.005-18.57 0-19.3 0-20.766v-11.27h.016A53.013 53.013 0 0 1 8 102.206a54.419 54.419 0 0 1 20.581-19.235 56.851 56.851 0 0 0 76.84 0A53.537 53.537 0 0 1 134 130.158v30.48zm67-75.024A44.34 44.34 0 0 1 49.93 82.6a43.768 43.768 0 0 1-13.94-9.213 42.808 42.808 0 0 1-9.4-13.664 42.345 42.345 0 0 1 0-33.466 42.808 42.808 0 0 1 9.4-13.664 43.768 43.768 0 0 1 13.94-9.215 44.817 44.817 0 0 1 34.14 0 43.767 43.767 0 0 1 13.94 9.213 42.807 42.807 0 0 1 9.4 13.664 42.346 42.346 0 0 1 0 33.466 42.808 42.808 0 0 1-9.4 13.664A43.767 43.767 0 0 1 84.07 82.6 44.34 44.34 0 0 1 67 85.976z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
