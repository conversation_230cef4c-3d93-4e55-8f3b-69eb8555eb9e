import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconGrid = ({
  color,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconGrid' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 96 96'>
        <S.Path
          d='M147.4,150.6H128.2a3.2,3.2,0,0,1-3.2-3.2V128.2a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,147.4,150.6Zm35.2,0H163.4a3.2,3.2,0,0,1-3.2-3.2V128.2a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,182.6,150.6Zm35.2,0H198.6a3.2,3.2,0,0,1-3.2-3.2V128.2a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,217.8,150.6Zm-70.4,35.2H128.2a3.2,3.2,0,0,1-3.2-3.2V163.4a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,147.4,185.8Zm35.2,0H163.4a3.2,3.2,0,0,1-3.2-3.2V163.4a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,182.6,185.8Zm35.2,0H198.6a3.2,3.2,0,0,1-3.2-3.2V163.4a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,217.8,185.8ZM147.4,221H128.2a3.2,3.2,0,0,1-3.2-3.2V198.6a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,147.4,221Zm35.2,0H163.4a3.2,3.2,0,0,1-3.2-3.2V198.6a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,182.6,221Zm35.2,0H198.6a3.2,3.2,0,0,1-3.2-3.2V198.6a3.2,3.2,0,0,1,3.2-3.2h19.2a3.2,3.2,0,0,1,3.2,3.2v19.2A3.2,3.2,0,0,1,217.8,221Z'
          transform='translate(-125 -125)'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
