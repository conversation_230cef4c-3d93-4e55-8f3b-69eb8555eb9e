import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconClose = ({
  color,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconClose' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M20.525 16l5.658-5.658c0.624-0.624 0.624-1.638 0-2.262l-2.262-2.262c-0.624-0.624-1.638-0.624-2.262 0l-5.658 5.658-5.658-5.658c-0.624-0.624-1.638-0.624-2.262 0l-2.262 2.262c-0.624 0.624-0.624 1.638 0 2.262l5.658 5.658-5.658 5.658c-0.624 0.624-0.624 1.638 0 2.262l2.262 2.262c0.624 0.624 1.638 0.624 2.262 0l5.658-5.658 5.658 5.658c0.624 0.624 1.638 0.624 2.262 0l2.262-2.262c0.624-0.624 0.624-1.638 0-2.262l-5.658-5.658z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
