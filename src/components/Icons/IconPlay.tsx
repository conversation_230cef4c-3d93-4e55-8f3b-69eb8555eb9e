import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconPlay = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlay' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 48'>
        <S.Path
          d='M9,8l30,16L9,40V8z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
