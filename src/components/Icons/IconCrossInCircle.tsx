import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCrossInCircle = ({
  color,
  height = 27,
  width = 27,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconCrossInCircle' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 27 27'>
        <S.Path
          d='M13.5 27A13.5 13.5 0 0 1 3.954 3.954a13.5 13.5 0 1 1 19.092 19.092A13.412 13.412 0 0 1 13.5 27zm0-10.328 4.644 4.642 3.172-3.172-4.643-4.642 4.644-4.643-3.172-3.172-4.645 4.643-4.644-4.643-3.173 3.172 4.645 4.643-4.645 4.643 3.173 3.172 4.644-4.643z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
