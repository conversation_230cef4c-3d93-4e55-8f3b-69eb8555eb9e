import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconArrowLeftCircle = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} width={width} {...rest}>
      <S.SVG
        height={height}
        width={width}
        viewBox='0 0 66 66'
        filter='url(#player_back)'
      >
        <S.Defs>
          <filter
            id='player_back'
            x='0'
            y='0'
            width='66'
            height='66'
            filterUnits='userSpaceOnUse'
          >
            <feOffset dy='3' />
            <feGaussianBlur stdDeviation='3' result='blur' />
            <feFlood floodOpacity='0.329' />
            <feComposite operator='in' in2='blur' />
            <feComposite in='SourceGraphic' />
          </filter>
        </S.Defs>
        <S.G transform='matrix(1, 0, 0, 1, 0, 0)'>
          <S.Path
            data-name='player_back'
            d='M14153,1652a24,24,0,1,1,16.97-7.029A24,24,0,0,1,14153,1652Zm-1.794-35h0l-6.977,6.315-5.23,4.736,5.23,4.693,6.977,6.255v-7.856H14167v-6.287h-15.79V1617Z'
            transform='translate(-14120 -1598)'
            fill={color || appTheme.colors.white80}
          />
        </S.G>
      </S.SVG>
    </S.Container>
  );
};
