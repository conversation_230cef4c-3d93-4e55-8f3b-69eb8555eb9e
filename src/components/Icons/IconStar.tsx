import { useTheme } from 'theme';

import { IconStarProps } from './types';
import * as S from './styles';

export const IconStar = ({
  isEmpty,
  scale,
  color,
  height = 32,
  width = 32,
  ...rest
}: IconStarProps) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconStar' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M29.568 12.928l-9.6-1.6-4-8.32-4 8.32-9.6 1.6 7.2 6.4-2.4 9.6 8.8-4.8 8.8 4.8-2.4-9.6 7.2-6.4z'
          fill={
            !isEmpty
              ? color || appTheme.colors.primary
              : appTheme.colors.white80
          }
          fillRule='evenodd'
        />
        <S.Path
          d='M16 21.078l-5.178 2.826 1.411-5.654-4.234-3.766 5.648-0.941 2.352-4.902 2.352 4.899 5.648 0.944-4.237 3.766 1.414 5.654-5.178-2.826z'
          fill={
            !isEmpty
              ? color || appTheme.colors.primary
              : appTheme.colors.tertiary
          }
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
