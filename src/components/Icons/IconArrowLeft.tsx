import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconArrowLeft = ({
  color,
  scale,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconArrowLeft' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M8.4 15.6l15.2 12.8v-25.6z'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
