import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerRec = ({
  scale,
  height = 32,
  width = 48,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerRec' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 32'>
        <S.Path
          data-name='ic_player_rec'
          d='m46.015 29.432-5.755-5.169-2.142-1.987v8.89A2.828 2.828 0 0 1 35.294 34H2.824A2.828 2.828 0 0 1 0 31.167V2.833A2.828 2.828 0 0 1 2.824 0h32.47a2.828 2.828 0 0 1 2.824 2.833v9.059l2.142-1.986 5.756-5.338A1.176 1.176 0 0 1 48 5.321v23.357a1.176 1.176 0 0 1-1.985.754zm-33.307-7.354a5.064 5.064 0 0 1-.129-.734q-.04-.387-.056-.758t-.032-.645a6.992 6.992 0 0 0-.121-.871 2.941 2.941 0 0 0-.281-.8 2.193 2.193 0 0 0-.5-.629 1.947 1.947 0 0 0-.772-.4v-.032a2.679 2.679 0 0 0 1.391-1.129 3.475 3.475 0 0 0 .426-1.759 3.246 3.246 0 0 0-.233-1.234 2.98 2.98 0 0 0-.675-1.008 3.2 3.2 0 0 0-1.061-.686 3.685 3.685 0 0 0-1.391-.25H3.086v11.524H5.61v-4.5h2.524a1.875 1.875 0 0 1 1.367.42 2.3 2.3 0 0 1 .547 1.323q.1.694.145 1.452a4.476 4.476 0 0 0 .257 1.307h2.524a1.691 1.691 0 0 1-.266-.591zm10.538-1.541h-6.19v-2.823h5.564v-1.968h-5.563v-2.469h6.061v-2.13h-8.585v11.52h8.714zm3.738-5.05a3.892 3.892 0 0 1 .539-1.267 2.735 2.735 0 0 1 .965-.887A2.94 2.94 0 0 1 29.935 13a2.629 2.629 0 0 1 1.656.581 2.55 2.55 0 0 1 .563.637 2.075 2.075 0 0 1 .289.8h2.444a4.656 4.656 0 0 0-.547-1.775 4.315 4.315 0 0 0-1.1-1.3 4.855 4.855 0 0 0-1.511-.8 6.135 6.135 0 0 0-4.164.194 5.074 5.074 0 0 0-1.777 1.291 5.762 5.762 0 0 0-1.109 1.928 7.221 7.221 0 0 0-.386 2.4 6.98 6.98 0 0 0 .386 2.347 5.61 5.61 0 0 0 1.109 1.9 5.079 5.079 0 0 0 1.777 1.266 5.867 5.867 0 0 0 2.371.46 5.64 5.64 0 0 0 1.944-.329 4.509 4.509 0 0 0 1.543-.936 4.676 4.676 0 0 0 1.061-1.484 5.876 5.876 0 0 0 .5-1.952h-2.445a3.288 3.288 0 0 1-.8 1.871 2.331 2.331 0 0 1-1.809.71 2.937 2.937 0 0 1-1.447-.331 2.76 2.76 0 0 1-.965-.879 3.814 3.814 0 0 1-.539-1.234 5.879 5.879 0 0 1-.169-1.412 6.322 6.322 0 0 1 .175-1.466zm-18.609.871H5.61v-3.242h2.765a1.933 1.933 0 0 1 1.3.379 1.538 1.538 0 0 1 .434 1.218 1.61 1.61 0 0 1-.434 1.258 1.9 1.9 0 0 1-1.3.388z'
          transform='translate(1.5 .5)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
