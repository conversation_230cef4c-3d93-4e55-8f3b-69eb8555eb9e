import { useTheme } from 'theme';
import { IconI } from 'components/Icons/types';
import * as S from 'components/Icons/styles';

export const IconPlayerExpandedToFull = ({
  scale,
  height = 32,
  width = 32,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerExpandedToFull' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32.002 32.002'>
        <S.Path
          d='M153.8 128.2v23.468a2.136 2.136 0 0 1-2.133 2.133H128.2v-23.467a2.136 2.136 0 0 1 2.133-2.133H153.8M157 125h-26.666a5.334 5.334 0 0 0-5.334 5.334V157h26.668a5.334 5.334 0 0 0 5.332-5.332V125zm-15.108 17.184a1.958 1.958 0 0 0 2.768 0l4.094-4.094 2.914 2.91v-8.728a1.94 1.94 0 0 0-1.94-1.94H141l2.911 2.911-4.094 4.094a1.957 1.957 0 0 0 0 2.768z'
          transform='translate(-125 -125)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
