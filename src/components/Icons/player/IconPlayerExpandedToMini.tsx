import { useTheme } from 'theme';
import { IconI } from 'components/Icons/types';
import * as S from 'components/Icons/styles';

export const IconPlayerExpandedToMini = ({
  scale,
  height = 32,
  width = 32,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerExpandedToMini' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M15860 22916v-26.668a5.331 5.331 0 0 1 5.332-5.332H15892v26.668a5.331 5.331 0 0 1-5.331 5.332zm3.2-26.668v23.465h23.465a2.132 2.132 0 0 0 2.133-2.129v-23.468h-23.47a2.132 2.132 0 0 0-2.128 2.132zm16.126 22v-6.666a2 2 0 0 1 2-2h6v6.666a1.994 1.994 0 0 1-2 2zm-12-9.576 2.91-2.91-4.089-4.094a1.956 1.956 0 0 1 0-2.768l2.076-2.076a1.95 1.95 0 0 1 2.761 0l4.095 4.09 2.916-2.91v8.73a1.939 1.939 0 0 1-1.938 1.938z'
          transform='translate(-15860 -22884)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
