import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerMute = ({
  color,
  height = 30,
  width = 30,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconPlayerMute' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 38 38'>
        <S.Path
          d='M17.091 70.612h2.622l16.232-16.234v-6.91l-9.571 8.415h-9.283a2.109 2.109 0 0 0-2.1 2.1v10.52a2.112 2.112 0 0 0 2.1 2.1zm18.854 8.415V64.792l-7.464 7.464zm13.338-35.1L13.516 79.7l2.233 2.232 35.767-35.77z'
          transform='translate(-13.516 -43.93)'
          fill={color || appTheme.colors.black}
        />
      </S.SVG>
    </S.Container>
  );
};
