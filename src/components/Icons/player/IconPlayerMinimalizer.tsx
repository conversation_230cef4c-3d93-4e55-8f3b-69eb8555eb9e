import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerMinimalizer = ({
  color,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconPlayerMinimalizer' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 42.146'>
        <S.Path
          d='M78.173,111.014c5.174,0,10.349-.033,15.523.01a8.913,8.913,0,0,1,8.274,7.682,63.4,63.4,0,0,1,.1,7.493,2.894,2.894,0,0,1-3.049,2.865,2.923,2.923,0,0,1-2.887-3.018c-.015-1.862,0-3.725-.005-5.587a3.117,3.117,0,0,0-3.423-3.465q-14.572,0-29.144,0a3.1,3.1,0,0,0-3.441,3.434q0,11.558,0,23.116c0,2.452,1.147,3.609,3.575,3.611,1.852,0,3.7-.028,5.555.014a2.915,2.915,0,0,1,2.842,2.58,2.968,2.968,0,0,1-2.119,3.288,3.265,3.265,0,0,1-.9.1c-2.054.007-4.109-.018-6.163,0a8.888,8.888,0,0,1-8.737-8.772c-.067-8.292-.045-16.584-.009-24.876a9.018,9.018,0,0,1,6.613-8.211A10.786,10.786,0,0,1,63.487,111q7.343.007,14.686,0Z'
          transform='translate(-54.131 -111)'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
        <S.Path
          d='M200.121,262.656c-2.941,0-5.882.006-8.823,0a2.919,2.919,0,0,1-3.149-3.126q-.03-5.93,0-11.859a2.971,2.971,0,0,1,3.077-3.086q8.9-.042,17.8,0a3,3,0,0,1,3.087,3.168q.025,5.891,0,11.783a2.941,2.941,0,0,1-3.09,3.12C206.054,262.664,203.088,262.656,200.121,262.656Z'
          transform='translate(-164.147 -220.512)'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
        <S.Path
          d='M113.405,168.055c0-.877-.013-1.561,0-2.243a1.52,1.52,0,1,1,3.031-.039q.042,2.9,0,5.811a1.529,1.529,0,0,1-1.633,1.62q-2.888.03-5.777,0a1.439,1.439,0,0,1-1.568-1.56,1.424,1.424,0,0,1,1.614-1.416c.7-.006,1.4,0,2.313,0l-5.983-5.969c-.2-.2-.407-.384-.59-.595a1.486,1.486,0,0,1,1.94-2.225,5.558,5.558,0,0,1,.731.66C109.42,164.037,111.347,165.981,113.405,168.055Z'
          transform='translate(-95.422 -152.138)'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
