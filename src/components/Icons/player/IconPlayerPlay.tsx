import { useTheme } from 'theme';
import { IconI } from 'components/Icons/types';
import * as S from 'components/Icons/styles';

export const IconPlayerPlay = ({
  scale,
  height = 18,
  width = 18,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerPlay' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 22 32'>
        <S.Path
          data-name='icons/functional_UI/M-S/ic_Play'
          d='M15.231 24 0 32V0l15.231 8L30 16z'
          fill={color || appTheme.colors.black}
        />
      </S.SVG>
    </S.Container>
  );
};
