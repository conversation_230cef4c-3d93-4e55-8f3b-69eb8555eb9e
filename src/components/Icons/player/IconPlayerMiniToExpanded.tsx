import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerMiniToExpanded = ({
  scale,
  height = 26,
  width = 26,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerMiniToExpanded' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 24.439 21.333'>
        <S.Path
          data-name='Path 1115'
          d='M66.372 111.007c2.634 0 5.269-.016 7.9.005a4.527 4.527 0 0 1 4.213 3.888c.132 1.263.07 5.593.051 6.857a1.512 1.512 0 0 1-3.022-.078c-.008-.943 0-4.95 0-5.892a1.582 1.582 0 0 0-1.743-1.754H58.933a1.574 1.574 0 0 0-1.752 1.738v11.7A1.6 1.6 0 0 0 59 129.3c.943 0 8.014-.014 8.957.007a1.481 1.481 0 0 1 1.447 1.306 1.5 1.5 0 0 1-1.079 1.665 1.671 1.671 0 0 1-.458.051c-1.046 0-8.221-.009-9.267 0a4.513 4.513 0 0 1-4.448-4.44c-.034-4.2-.023-8.394 0-12.592a4.571 4.571 0 0 1 3.367-4.156 5.523 5.523 0 0 1 1.376-.141h7.477z'
          transform='translate(-54.131 -111)'
          fill={color || appTheme.colors.black}
        />
        <S.Path
          data-name='Path 1116'
          d='M6.639 9.963H1.752A1.612 1.612 0 0 1 .008 8.241q-.017-3.265 0-6.53a1.641 1.641 0 0 1 1.7-1.7q4.929-.023 9.858 0a1.654 1.654 0 0 1 1.71 1.744q.014 3.244 0 6.488a1.624 1.624 0 0 1-1.712 1.718c-1.639.007-3.282.002-4.925.002z'
          transform='rotate(180 8.733 7.118)'
          fill={color || appTheme.colors.black}
        />
        <S.Path
          data-name='Path 1117'
          d='M4.974 3.79c0-.483-.007-.859 0-1.235a.872.872 0 0 1 .848-.908.869.869 0 0 1 .831.887q.023 1.6 0 3.2a.845.845 0 0 1-.9.892q-1.6.017-3.2 0a.8.8 0 0 1-.868-.859c.011-.484.342-.775.894-.78H3.86L.544 1.7c-.109-.11-.225-.213-.327-.329A.8.8 0 0 1 .225.265.815.815 0 0 1 1.292.146 3.072 3.072 0 0 1 1.7.509C2.766 1.577 3.833 2.648 4.974 3.79z'
          transform='rotate(180 12.212 10.666)'
          fill={color || appTheme.colors.black}
        />
      </S.SVG>
    </S.Container>
  );
};
