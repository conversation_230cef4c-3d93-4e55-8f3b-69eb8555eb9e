import { useTheme } from 'theme';
import { IconI } from 'components/Icons/types';
import * as S from 'components/Icons/styles';

export const IconPlayerFullToExpanded = ({
  scale,
  height = 32,
  width = 32,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerFullToExpanded' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32.002 32.002'>
        <S.Path
          d='M112.831 86.962v23.468a2.136 2.136 0 0 1-2.133 2.133H87.231V89.1a2.136 2.136 0 0 1 2.133-2.133h23.467m3.2-3.2H89.365a5.334 5.334 0 0 0-5.334 5.333v26.666H110.7a5.334 5.334 0 0 0 5.332-5.332zm-7.98 5.9a1.958 1.958 0 0 0-2.768 0l-4.094 4.094-2.914-2.91v8.728a1.94 1.94 0 0 0 1.94 1.94h8.728l-2.911-2.911 4.094-4.094a1.958 1.958 0 0 0 0-2.768z'
          transform='translate(-84.031 -83.762)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
