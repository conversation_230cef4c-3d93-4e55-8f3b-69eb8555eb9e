import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerSettings = ({
  scale,
  height = 32,
  width = 32,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerSettings' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M120.881 109.035c0-4.343 4.74-1.057 2.533-6.385s-3.235.346-6.306-2.724 2.6-4.1-2.723-6.3-2.043 2.532-6.385 2.532-1.057-4.739-6.385-2.532.347 3.234-2.725 6.3-4.1-2.6-6.3 2.724 2.532 2.041 2.532 6.385-4.74 1.057-2.532 6.385 3.234-.346 6.3 2.723-2.6 4.1 2.725 6.306 2.041-2.532 6.385-2.532 1.056 4.739 6.385 2.532-.348-3.235 2.723-6.306 4.1 2.6 6.306-2.723-2.533-2.042-2.533-6.385zM108.02 101.2a7.853 7.853 0 1 1-7.853 7.853 7.853 7.853 0 0 1 7.853-7.853z'
          transform='translate(-92 -93.035)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
