import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerLive = ({
  scale,
  height = 48,
  width = 48,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerLive' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 41.999 48'>
        <S.Path
          data-name='Union 5'
          d='M8160 15386.408a20.75 20.75 0 0 1 21.1-20.518h5.575v-6.89l5.655 5.529 4.22 4.166-4.22 4.088-5.655 5.447v-6.891h-5.416a15.149 15.149 0 1 0 15.131 17.471 2.824 2.824 0 0 1 2.787-2.326h.479a2.689 2.689 0 0 1 2.309 3.127A21 21 0 0 1 8181.1 15407c-11.622 0-21.1-9.217-21.1-20.592zm16.762-7.437 7.212 3.84 7.053 3.918-7.053 3.844-7.212 3.838z'
          transform='translate(-8160.002 -15359)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
