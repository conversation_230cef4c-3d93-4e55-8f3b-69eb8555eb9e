import { useTheme } from 'theme';
import { IconI } from 'components/Icons/types';
import * as S from 'components/Icons/styles';

export const IconPlayerPause = ({
  scale,
  height = 20,
  width = 20,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerPause' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 14.4 19.2'>
        <path
          d='M23.6 35.6h-4.8V16.4h4.8zm9.6 0h-4.8V16.4h4.8z'
          transform='translate(-18.8 -16.4)'
          fill={appTheme.colors.black}
        />
      </S.SVG>
    </S.Container>
  );
};
