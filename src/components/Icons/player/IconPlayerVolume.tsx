import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerVolume = ({
  color,
  height = 30,
  width = 30,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconPlayerVolume' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 31.8 27.871'>
        <S.Path
          d='M19.226,27.871h0l-8.65-7.431H1.922A1.893,1.893,0,0,1,0,18.581V9.291A1.893,1.893,0,0,1,1.922,7.432h8.652L19.226,0V27.871Zm9.3-5.787h0l-2.171-1.835a9.084,9.084,0,0,0,0-12.625l2.172-1.836a11.78,11.78,0,0,1,0,16.3Zm-3.619-3.06h0l-2.172-1.837a4.532,4.532,0,0,0,0-6.5l2.173-1.838a7.264,7.264,0,0,1,0,10.177Z'
          transform='translate(0 0)'
          fill={color || appTheme.colors.black}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
