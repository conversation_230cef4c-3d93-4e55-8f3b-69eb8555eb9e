import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerChannels = ({
  scale,
  height = 48,
  width = 48,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerChannels' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 48'>
        <S.Path
          d='M6812-1430a23.845,23.845,0,0,1-16.971-7.029A23.84,23.84,0,0,1,6788-1454a23.84,23.84,0,0,1,7.03-16.97A23.845,23.845,0,0,1,6812-1478a23.841,23.841,0,0,1,16.97,7.029A23.841,23.841,0,0,1,6836-1454a23.841,23.841,0,0,1-7.03,16.97A23.841,23.841,0,0,1,6812-1430Zm-11.251-19.059a.69.69,0,0,0-.75.707v4.236a.69.69,0,0,0,.75.705h18.5a.69.69,0,0,0,.751-.705v-4.236a.691.691,0,0,0-.751-.707Zm0-7.765a.69.69,0,0,0-.75.707v4.236a.69.69,0,0,0,.75.705h12.5a.69.69,0,0,0,.75-.705v-4.236a.69.69,0,0,0-.75-.707Zm0-7.763a.69.69,0,0,0-.75.705v4.236a.69.69,0,0,0,.75.705h22.5a.69.69,0,0,0,.75-.705v-4.236a.689.689,0,0,0-.75-.705Z'
          transform='translate(-6788 1478)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
