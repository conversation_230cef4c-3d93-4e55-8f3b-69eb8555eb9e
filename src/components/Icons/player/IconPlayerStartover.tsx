import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerStartover = ({
  scale,
  height = 48,
  width = 48,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerStartover' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 42 48'>
        <S.Path
          d='M20.986 48A20.877 20.877 0 0 1 .235 30.607 2.768 2.768 0 0 1 3 27.428a2.782 2.782 0 0 1 2.764 2.326 15.309 15.309 0 0 0 15.218 12.76 15.285 15.285 0 0 0 15.396-14.588 15.318 15.318 0 0 0-15.522-15.583h-2.67V19.2l-5.6-5.46-4.2-4.095 4.2-4.134L18.186 0v6.857h2.8a20.789 20.789 0 0 1 21 20.571 20.789 20.789 0 0 1-21 20.572zm-11.2-34.26v5.46l-9.8-9.555L9.786 0v5.512l-4.2 4.134z'
          transform='translate(.014)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
