import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerScroll = ({
  color,
  height = 20,
  width = 20,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconPlayerScroll' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 16 17'>
        <S.Path
          id='path'
          d='M20.133,8.6,14,14.733,7.867,8.6,6,10.467l8,8,8-8Z'
          transform='translate(-6 -1.467)'
          fill={color || appTheme.colors.iconColorFill}
          fillRule='evenodd'
        />
        <S.Path
          id='path-2'
          data-name='path'
          d='M20.133,8.6,14,14.733,7.867,8.6,6,10.467l8,8,8-8Z'
          transform='translate(-6 -8.6)'
          fill={color || appTheme.colors.iconColorFill}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
