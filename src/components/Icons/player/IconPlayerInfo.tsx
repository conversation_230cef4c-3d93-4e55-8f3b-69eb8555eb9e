import { useTheme } from 'theme';

import { IconI } from '../types';
import * as S from '../styles';

export const IconPlayerInfo = ({
  scale,
  height = 48,
  width = 48,
  color,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPlayerInfo' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 48'>
        <S.Path
          d='M24 .5a24 24 0 1 0 24 24 24 24 0 0 0-24-24zm0 5.647a4.235 4.235 0 1 1-4.235 4.235A4.235 4.235 0 0 1 24 6.147zm7.059 33.882H18.353v-1.453l.3-.009a2.589 2.589 0 0 0 1.941-.742c.145-.168.393-.74.393-2.663v-12.83c0-1.829-.255-2.519-.469-2.775a2.65 2.65 0 0 0-1.873-.63l-.29-.018v-1.468h9.882v17.721a4.089 4.089 0 0 0 .691 2.775 2.529 2.529 0 0 0 1.842.63l.29.017z'
          transform='translate(0 -.5)'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
