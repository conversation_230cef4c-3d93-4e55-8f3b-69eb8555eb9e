import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCloseCircle = ({
  color,
  scale,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} width={width} {...rest}>
      <S.SVG
        height={height}
        width={width}
        viewBox='0 0 66.003 66.002'
        filter='url(#player_close_X)'
      >
        <S.Defs>
          <filter
            id='player_close_X'
            x='0'
            y='0'
            width='66.002'
            height='66.002'
            filterUnits='userSpaceOnUse'
          >
            <feOffset dy='3' />
            <feGaussianBlur stdDeviation='3' result='blur' />
            <feFlood floodOpacity='0.341' />
            <feComposite operator='in' in2='blur' />
            <feComposite in='SourceGraphic' />
          </filter>
        </S.Defs>
        <S.G transform='matrix(1, 0, 0, 1, 0, 0)'>
          <S.Path
            data-name='player_close_X'
            d='M27277.078,2753H27277a23.838,23.838,0,1,1,9.225-1.829A23.68,23.68,0,0,1,27277.078,2753Zm-.076-20.639h0l9.244,9.243,3.363-3.358-9.244-9.244,9.244-9.25-3.363-3.358-9.244,9.244-9.244-9.244-3.365,3.358,9.252,9.25-9.252,9.244,3.365,3.358,9.244-9.243Z'
            transform='translate(-27244 -2699)'
            fill={color || appTheme.colors.white80}
          />
        </S.G>
      </S.SVG>
    </S.Container>
  );
};
