import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconHomeAreaError = ({
  color,
  height = 27,
  width = 29.452,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconHomeAreaError' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 29.452 27'>
        <S.Path
          d='M21.17 27H8.283a2.761 2.761 0 0 1-2.761-2.754V13.189H0L11.966 1.213H12a3.684 3.684 0 0 1 5.519.06l11.933 11.916H23.93v11.057A2.76 2.76 0 0 1 21.17 27zm-6.444-7.963 3.715 3.714 2.537-2.537-3.714-3.714 3.715-3.714-2.537-2.537-3.716 3.714-3.715-3.714-2.538 2.536 3.716 3.715-3.716 3.715 2.538 2.537 3.714-3.714z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
