import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconHomeArea = ({
  color,
  scale,
  height = 27,
  width = 29.452,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconHomeArea' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 29.452 27'>
        <S.Path
          d='M125.014 926.274a3.684 3.684 0 0 0-5.518-.06h-.029L107.5 938.19h5.522v11.056a2.757 2.757 0 0 0 2.76 2.754h12.888a2.757 2.757 0 0 0 2.761-2.754V938.19h5.522zm-8.461 23.889a1.662 1.662 0 0 0-1.688-1.685v-1.685a3.4 3.4 0 0 1 3.375 3.366h-1.688zm3.433 0a5.054 5.054 0 0 0-5.117-5.111v-1.624a6.75 6.75 0 0 1 6.748 6.735zm3.356 0a8.756 8.756 0 0 0-8.474-8.458v-1.643a10.113 10.113 0 0 1 10.12 10.1h-1.646z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
          transform='translate(-107.5 -925)'
        />
      </S.SVG>
    </S.Container>
  );
};
