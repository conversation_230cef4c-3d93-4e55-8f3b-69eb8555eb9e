import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconBin = ({
  color,
  scale,
  height = 32,
  width = 24,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconBin' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 24 32`}>
        <S.Path
          d='M22.154 32H1.846V8.469h20.308V32zm-4.615-20.707a.934.934 0 0 0-.924.942v16a.923.923 0 1 0 1.846.006v-16a.934.934 0 0 0-.922-.948zm-5.539 0a.934.934 0 0 0-.924.942v16a.923.923 0 1 0 1.846.006v-16a.934.934 0 0 0-.922-.948zm-5.539 0a.934.934 0 0 0-.924.942v16a.923.923 0 1 0 1.846.006v-16a.934.934 0 0 0-.921-.948zM24 7.529H0V5.646a2.8 2.8 0 0 1 2.769-2.824h4.615v-.94A1.867 1.867 0 0 1 9.231 0h5.539a1.866 1.866 0 0 1 1.846 1.882v.94h4.615A2.8 2.8 0 0 1 24 5.646v1.883zM9.231 1.882v.94h5.539v-.94z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
