import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCalendar = ({
  color,
  scale,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconCalendar' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M28.903 3.097h-4.133v-1.548c0-0.855-0.693-1.549-1.549-1.549s-1.549 0.693-1.549 1.549v0 1.548h-11.355v-1.548c0-0.855-0.693-1.549-1.549-1.549s-1.549 0.693-1.549 1.549v0 1.548h-7.22v25.807c0.001 1.71 1.387 3.096 3.097 3.096h28.903v-25.807c-0.001-1.71-1.387-3.096-3.097-3.096v0zM8.257 29.932h-4.648c-0.855 0-1.548-0.693-1.548-1.548v0-3.609h6.196zM8.257 23.743h-6.196v-5.161h6.196zM8.257 17.549h-6.196v-5.165h6.196zM6.192 6.711c0-0.001 0-0.001 0-0.002 0-0.841 0.403-1.587 1.025-2.057l0.007-0.005v2.064c0 0.855 0.693 1.549 1.549 1.549s1.549-0.693 1.549-1.549v0-2.064c0.626 0.476 1.027 1.221 1.027 2.060 0 1.425-1.155 2.58-2.58 2.58-1.423 0-2.578-1.153-2.58-2.576v-0zM15.483 29.933h-6.193v-5.157h6.193zM15.483 23.744h-6.193v-5.161h6.193zM15.483 17.551h-6.193v-5.165h6.193zM22.708 29.935h-6.193v-5.157h6.193zM22.708 23.745h-6.193v-5.161h6.193zM22.708 17.552h-6.193v-5.165h6.193zM20.643 6.713c0-0.001 0-0.001 0-0.002 0-0.841 0.403-1.587 1.025-2.057l0.007-0.005v2.064c0 0.855 0.693 1.549 1.549 1.549s1.549-0.693 1.549-1.549v0-2.064c0.626 0.476 1.027 1.221 1.027 2.060 0 1.425-1.155 2.58-2.58 2.58-1.423 0-2.578-1.153-2.58-2.576v-0zM29.931 23.745h-6.191v-5.161h6.191zM29.931 17.552h-6.191v-5.165h4.643c0.855 0 1.548 0.693 1.548 1.548v0z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
