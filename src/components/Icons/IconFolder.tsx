import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconFolder = ({
  color,
  scale,
  height = 26,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconFolder' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 32 26`}>
        <S.Path
          d='M29.5 4H14l-3-4H0v23.5A2.5 2.5 0 0 0 2.5 26H32V6.5A2.5 2.5 0 0 0 29.5 4z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
