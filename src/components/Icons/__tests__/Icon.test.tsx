import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { IconHourglass } from '../IconHourglass';
import { IconSearch } from '../IconSearch';
import { IconSettings } from '../IconSettings';

describe('Components:Icons', () => {
  it('renders IconSearch component', () => {
    render(<IconSearch />);

    expect(screen.getByTestId('IconSearch')).toBeInTheDocument();
  });

  it('renders IconSettings component', () => {
    render(<IconSettings />);

    expect(screen.getByTestId('IconSettings')).toBeInTheDocument();
  });

  it('renders IconHourglass component', () => {
    render(<IconHourglass />);

    expect(screen.getByTestId('IconHourglass')).toBeInTheDocument();
  });
});
