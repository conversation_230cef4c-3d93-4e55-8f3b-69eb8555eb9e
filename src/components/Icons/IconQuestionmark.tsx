import { useTheme } from 'theme';

import { IconQuestionmarkI } from './types';
import * as S from './styles';

export const IconQuestionmark = ({
  color,
  height = 32,
  width = 32,
  onClick = () => ({}),
  ...rest
}: IconQuestionmarkI) => {
  const appTheme = useTheme();

  return (
    <S.Container onClick={onClick} data-testid='IconQuestionmark' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 32 32'>
        <S.Path
          d='M16 2.4c-7.511 0-13.6 6.089-13.6 13.6s6.089 13.6 13.6 13.6 13.6-6.089 13.6-13.6-6.089-13.6-13.6-13.6zM16.986 24.18c-0.307 0.283-0.72 0.457-1.172 0.457-0.005 0-0.010-0-0.015-0h0.001c-0.007 0-0.016 0-0.025 0-0.457 0-0.875-0.171-1.192-0.453l0.002 0.002q-0.519-0.451-0.519-1.26 0-0.719 0.502-1.209t1.232-0.49q0.719 0 1.209 0.49t0.49 1.209q-0 0.798-0.514 1.254zM21.245 13.619c-0.259 0.483-0.57 0.899-0.935 1.26l-0 0q-0.541 0.53-1.945 1.785c-0.221 0.198-0.423 0.401-0.615 0.614l-0.007 0.007c-0.13 0.143-0.245 0.304-0.341 0.477l-0.007 0.013c-0.067 0.126-0.127 0.274-0.173 0.428l-0.004 0.017q-0.063 0.223-0.188 0.781-0.216 1.186-1.357 1.186-0.593 0-0.998-0.388t-0.405-1.152q0-0.958 0.296-1.66c0.2-0.475 0.465-0.883 0.79-1.235l-0.003 0.003q0.49-0.53 1.323-1.261 0.729-0.638 1.055-0.964c0.21-0.21 0.392-0.448 0.539-0.708l0.008-0.016c0.14-0.246 0.223-0.541 0.223-0.854 0-0.004-0-0.009-0-0.013v0.001q0-0.913-0.679-1.54t-1.75-0.627q-1.255 0-1.848 0.633t-1.004 1.865q-0.388 1.289-1.472 1.289-0.639 0-1.078-0.451t-0.439-0.975q0-1.083 0.696-2.195t2.030-1.842 3.114-0.73q1.653 0 2.92 0.61 1.266 0.61 1.956 1.659t0.69 2.281q0 0.97-0.393 1.7z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
