import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconBlackout = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconBlackout' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 1229 1024'>
        <S.Path
          d='M1156.5 812.115h-1084.281c-0.363 0.007-0.791 0.010-1.219 0.010-38.775 0-70.292-31.079-70.998-69.685l-0.001-0.066v-670.709c0.348-39.633 32.541-71.632 72.215-71.666h1084.1c39.687 0.022 71.891 32.025 72.239 71.633v670.517c-0.432 38.707-31.911 69.918-70.679 69.918-0.484 0-0.967-0.005-1.448-0.015l0.072 0.001zM1156.5 70.724h-1084.281v670.811h1084.097v-670.811zM1105.863 1023.98h-983.007v-169.54h982.987zM191.798 960.873c3.721-0.223 6.654-3.295 6.654-7.052 0-3.901-3.163-7.064-7.064-7.064-3.868 0-7.011 3.109-7.064 6.965v0.005c0.046 3.957 3.264 7.147 7.228 7.147 0.029 0 0.058 0 0.087-0.001h-0.004zM191.798 920.863c-0.013 0-0.029 0-0.044 0-3.901 0-7.064 3.163-7.064 7.064s3.163 7.064 7.064 7.064c0.189 0 0.375-0.007 0.56-0.022l-0.024 0.002c3.792-0.18 6.798-3.298 6.798-7.118 0-0.010 0-0.020 0-0.030v0.002c-0.18-3.882-3.371-6.962-7.282-6.962-0.010 0-0.020 0-0.029 0h0.002zM599.76 924.835c-0.383-11.151-9.325-20.093-20.44-20.475l-0.036-0.001h-277.592c-11.151 0.383-20.093 9.325-20.475 20.44l-0.001 0.036v29.219c0 11.309 9.167 20.476 20.476 20.476v0h277.285c11.309 0 20.476-9.167 20.476-20.476v0zM802.328 939.168c-7.64 0.195-13.76 6.434-13.76 14.104 0 7.792 6.316 14.108 14.108 14.108 7.79 0 14.105-6.314 14.108-14.103v0c-0.217-7.825-6.602-14.088-14.454-14.108h-0.002zM860.111 939.168c-7.64 0.195-13.76 6.434-13.76 14.104 0 7.792 6.316 14.108 14.108 14.108 7.79 0 14.105-6.314 14.108-14.103v0c-0.217-7.825-6.602-14.088-14.454-14.108h-0.002zM917.894 939.168c-7.64 0.195-13.76 6.434-13.76 14.104 0 7.792 6.316 14.108 14.108 14.108 7.79 0 14.105-6.314 14.108-14.103v0c-0.126-7.819-6.494-14.108-14.331-14.108-0.044 0-0.088 0-0.131 0.001h0.007z'
          fill={color || appTheme.colors.white}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
