import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconPC = ({
  color,
  scale,
  height = 43.668,
  width = 60.004,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPC' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 60.004 43.668'>
        <S.Path
          d='M22710.223 22169.867h-56.447a1.761 1.761 0 0 1-1.248-.48 1.685 1.685 0 0 1-.529-1.2v-3.42h24.67a1.679 1.679 0 0 0 .523 1.2 1.785 1.785 0 0 0 1.254.48h7.057a1.761 1.761 0 0 0 1.248-.48 1.687 1.687 0 0 0 .531-1.2H22712v3.42a1.687 1.687 0 0 1-.531 1.2 1.755 1.755 0 0 1-1.205.48zm-54.748-7.826v-32.422a3.33 3.33 0 0 1 1.02-2.412 3.56 3.56 0 0 1 2.492-1.008h45.969a3.617 3.617 0 0 1 2.531.986 3.354 3.354 0 0 1 1.037 2.434v27.314h.059v5.107zm3.512-3.42h46.033v-29h-46.033z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
          transform='translate(-22651.998 -22126.199)'
        />
      </S.SVG>
    </S.Container>
  );
};
