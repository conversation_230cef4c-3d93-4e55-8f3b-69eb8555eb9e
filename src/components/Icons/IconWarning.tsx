import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconWarning = ({
  color,
  scale,
  height = 128,
  width = 136,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconWarning' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 ${width} ${height}`}>
        <S.Path
          d='M198.365,253H92.172a15.013,15.013,0,0,1-13.154-8.019,16.774,16.774,0,0,1,.052-16.042h0l.125-.225.006-.011,52.965-95.763h0a14.782,14.782,0,0,1,26.205,0h0l53.1,96h0a16.774,16.774,0,0,1,.052,16.042A15.013,15.013,0,0,1,198.365,253ZM145.43,235.929a8.119,8.119,0,1,1,7.676-8.107,7.9,7.9,0,0,1-7.676,8.107Zm7.654-72.369h0l-2.537,44.052h0c0,2.984-2.292,4.1-5.117,4.1s-5.116-1.121-5.117-4.1h0l-2.543-44.154h0c-.009-.162-.015-.324-.015-.489a7.688,7.688,0,1,1,15.352,0C153.107,163.168,153.1,163.365,153.084,163.56Z'
          fill={color || '#fc0'}
          transform='translate(-77.002 -124.995)'
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
