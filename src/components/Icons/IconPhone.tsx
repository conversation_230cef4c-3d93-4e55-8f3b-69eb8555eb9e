import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconPhone = ({
  color,
  scale,
  height = 60,
  width = 34.287,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPhone' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 34.287 60'>
        <S.Path
          d='M30.856.8H3.43A3.462 3.462 0 0 0 0 4.343v52.914A3.462 3.462 0 0 0 3.43 60.8h27.426a3.462 3.462 0 0 0 3.429-3.542V4.23A3.367 3.367 0 0 0 30.856.8zM14.629 2.515h5.144a.815.815 0 0 1 .8.923.884.884 0 0 1-.8.923h-5.144a.815.815 0 0 1-.8-.923.722.722 0 0 1 .8-.923zm2.515 56.457a1.758 1.758 0 0 1-1.715-1.715 1.682 1.682 0 0 1 1.715-1.715 1.715 1.715 0 1 1 0 3.429zm13.714-5.257H3.43V6.057h27.426z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
