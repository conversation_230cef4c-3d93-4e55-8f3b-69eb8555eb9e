import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconProfileDefault = ({
  color,
  scale,
  height = 128,
  width = 128,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconProfileDefault' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 128 128'>
        <S.Path
          d='M63.7,56c13,0,23.6-10.6,23.6-23.6S76.8,8.9,63.7,8.9c-13,0-23.6,10.6-23.6,23.6
          C40.2,45.4,50.7,56,63.7,56L63.7,56z M84.4,54.3c-11.6,10.9-29.7,10.9-41.3,0C33.8,59.4,28,69,27.7,79.5l0,0V107
          c0,7.2,5.9,13.1,13.1,13.1h58.9V80.2C99.8,69.4,93.9,59.5,84.4,54.3L84.4,54.3z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
