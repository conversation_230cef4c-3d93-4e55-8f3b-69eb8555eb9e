import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconInfo = ({
  color,
  scale,
  height = 48,
  width = 48,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconInfo' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 48 48'>
        <S.Path
          d='M24,8C15.2,8,8,15.2,8,24s7.2,16,16,16s16-7.2,16-16S32.8,8,24,8z M24,11.8
          c1.6,0,2.8,1.3,2.8,2.8c0,1.6-1.3,2.8-2.8,2.8c-1.6,0-2.8-1.3-2.8-2.8l0,0C21.2,13,22.4,11.8,24,11.8C24,11.8,24,11.8,24,11.8z
           M28.7,34.4h-8.5v-1l0.2,0c0.5,0,1-0.2,1.3-0.5c0.1-0.1,0.3-0.5,0.3-1.8v-8.6c0-1.2-0.2-1.7-0.3-1.9c-0.3-0.3-0.8-0.4-1.2-0.4
          l-0.2,0v-1h6.6v11.8c-0.1,0.7,0.1,1.3,0.5,1.9c0.3,0.3,0.8,0.4,1.2,0.4l0.2,0L28.7,34.4z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
