import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconVideoPlaceholder = ({
  color,
  scale,
  height = 93,
  width = 96,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconAddProfile' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 96 93'>
        <S.Path
          d='M89.8 93H24.772a6.214 6.214 0 0 1-6.192-6.2V27.9a6.226 6.226 0 0 1 6.192-6.2H89.8a6.219 6.219 0 0 1 6.159 5.529c.025.281.037.552.037.8V86.8A6.228 6.228 0 0 1 89.8 93zM67.2 52.7h.065a2.254 2.254 0 0 1 1.644.786l11.418 13.121L89.8 77.5V27.9H24.772v49.6l7-6.2 8.9-7.891a2.242 2.242 0 0 1 3.122.126l4.371 4.524a2.254 2.254 0 0 0 3.252 0l.827-.856L65.579 53.4a2.277 2.277 0 0 1 1.626-.7zM15.482 71.3h-9.29A6.226 6.226 0 0 1 0 65.1V6.2A6.226 6.226 0 0 1 6.192 0h68.129a6.226 6.226 0 0 1 6.192 6.2v12.4H8.454a2.264 2.264 0 0 0-2.262 2.26v29.579A2.264 2.264 0 0 0 8.454 52.7h7.028v18.6zM8.143 58.437a1.954 1.954 0 0 0-1.951 1.953v3.222a1.952 1.952 0 0 0 1.951 1.953H10.9a1.954 1.954 0 0 0 1.951-1.953V60.39a1.954 1.954 0 0 0-1.951-1.953zm0-53.052a1.954 1.954 0 0 0-1.951 1.953v3.222a1.952 1.952 0 0 0 1.951 1.953h3.428a1.952 1.952 0 0 0 1.951-1.953V7.338a1.954 1.954 0 0 0-1.951-1.953zm61.937-.117a1.952 1.952 0 0 0-1.951 1.953v3.227A1.952 1.952 0 0 0 70.08 12.4h3.22a1.954 1.954 0 0 0 1.951-1.953V7.221A1.954 1.954 0 0 0 73.3 5.268zm-12.388 0a1.954 1.954 0 0 0-1.951 1.953v3.227a1.954 1.954 0 0 0 1.951 1.952h3.219a1.954 1.954 0 0 0 1.951-1.953V7.221a1.954 1.954 0 0 0-1.951-1.953zm-12.388 0a1.952 1.952 0 0 0-1.951 1.953v3.227A1.952 1.952 0 0 0 45.3 12.4h3.224a1.952 1.952 0 0 0 1.951-1.953V7.221a1.954 1.954 0 0 0-1.951-1.953zm-12.384 0a1.952 1.952 0 0 0-1.951 1.953v3.227A1.952 1.952 0 0 0 32.92 12.4h3.219a1.954 1.954 0 0 0 1.951-1.953V7.221a1.954 1.954 0 0 0-1.951-1.953zm-12.388 0a1.952 1.952 0 0 0-1.952 1.953v3.227a1.952 1.952 0 0 0 1.951 1.952h3.219a1.954 1.954 0 0 0 1.95-1.951V7.221a1.954 1.954 0 0 0-1.95-1.953zM41.806 52.7a7.743 7.743 0 1 1 5.477-2.269 7.754 7.754 0 0 1-5.477 2.269z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
