import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconTVGo = ({
  scale,
  height = 72,
  width = 485.88,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconTVGo' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 485.88 72'>
        <S.G transform='translate(-64 -122)'>
          <S.Path
            d='M11.7-21.24a21.808,21.808,0,0,0,.63,5.25,14.188,14.188,0,0,0,2.01,4.59,10.3,10.3,0,0,0,3.6,3.27,11,11,0,0,0,5.4,1.23,11,11,0,0,0,5.4-1.23,10.3,10.3,0,0,0,3.6-3.27,14.188,14.188,0,0,0,2.01-4.59,21.808,21.808,0,0,0,.63-5.25,23.443,23.443,0,0,0-.63-5.46,14.46,14.46,0,0,0-2.01-4.71,10.2,10.2,0,0,0-3.6-3.3,11,11,0,0,0-5.4-1.23,11,11,0,0,0-5.4,1.23,10.2,10.2,0,0,0-3.6,3.3,14.46,14.46,0,0,0-2.01,4.71A23.443,23.443,0,0,0,11.7-21.24Zm-9.42,0a26.844,26.844,0,0,1,1.44-8.88,21.348,21.348,0,0,1,4.14-7.2,19,19,0,0,1,6.6-4.8,21.583,21.583,0,0,1,8.88-1.74,21.583,21.583,0,0,1,8.88,1.74,19,19,0,0,1,6.6,4.8,21.348,21.348,0,0,1,4.14,7.2,26.844,26.844,0,0,1,1.44,8.88,25.951,25.951,0,0,1-1.44,8.7,20.8,20.8,0,0,1-4.14,7.08,19.367,19.367,0,0,1-6.6,4.74,21.583,21.583,0,0,1-8.88,1.74A21.583,21.583,0,0,1,14.46-.72a19.367,19.367,0,0,1-6.6-4.74,20.8,20.8,0,0,1-4.14-7.08A25.951,25.951,0,0,1,2.28-21.24Zm47.64-9.78h8.1v5.76h.12a10.478,10.478,0,0,1,1.62-2.67,10.613,10.613,0,0,1,2.34-2.1,11.3,11.3,0,0,1,2.85-1.35,10.268,10.268,0,0,1,3.09-.48,5.843,5.843,0,0,1,1.86.3v7.92q-.6-.12-1.44-.21a15.215,15.215,0,0,0-1.62-.09,9.077,9.077,0,0,0-3.96.78,6.859,6.859,0,0,0-2.61,2.13,8.536,8.536,0,0,0-1.41,3.18,17.259,17.259,0,0,0-.42,3.87V0H49.92ZM72.9-21.48a9.9,9.9,0,0,1,1.5-4.98,10.214,10.214,0,0,1,3.36-3.18,14.653,14.653,0,0,1,4.56-1.71,25.949,25.949,0,0,1,5.16-.51,34.767,34.767,0,0,1,4.74.33,14.5,14.5,0,0,1,4.38,1.29,8.521,8.521,0,0,1,3.24,2.7,7.489,7.489,0,0,1,1.26,4.5V-6.9a32.415,32.415,0,0,0,.24,4.02A7.267,7.267,0,0,0,102.18,0H93.54a12.225,12.225,0,0,1-.39-1.47A12.8,12.8,0,0,1,92.94-3a11.359,11.359,0,0,1-4.8,2.97A18.653,18.653,0,0,1,82.5.84,14.485,14.485,0,0,1,78.36.27,9.879,9.879,0,0,1,75-1.44a7.82,7.82,0,0,1-2.25-2.88,9.737,9.737,0,0,1-.81-4.14,9.3,9.3,0,0,1,.9-4.35,7.4,7.4,0,0,1,2.4-2.73,11.135,11.135,0,0,1,3.39-1.53,34.925,34.925,0,0,1,3.87-.81q1.98-.3,3.78-.48a21.839,21.839,0,0,0,3.21-.54,5.743,5.743,0,0,0,2.25-1.05,2.451,2.451,0,0,0,.84-2.01,4.521,4.521,0,0,0-.45-2.19,3.309,3.309,0,0,0-1.2-1.26,4.721,4.721,0,0,0-1.74-.6,14.238,14.238,0,0,0-2.13-.15,6.493,6.493,0,0,0-3.93,1.08,4.771,4.771,0,0,0-1.71,3.6Zm19.68,6.3a3.822,3.822,0,0,1-1.35.75,13.788,13.788,0,0,1-1.74.45q-.93.18-1.98.3t-2.01.3a13.47,13.47,0,0,0-1.92.48,5.669,5.669,0,0,0-1.59.81,3.77,3.77,0,0,0-1.11,1.32,4.19,4.19,0,0,0-.42,1.95,3.97,3.97,0,0,0,.42,1.95,3.283,3.283,0,0,0,1.14,1.2,4.811,4.811,0,0,0,1.68.63,10.723,10.723,0,0,0,1.98.18,7.532,7.532,0,0,0,3.9-.84,5.618,5.618,0,0,0,2.04-2.01,6.428,6.428,0,0,0,.81-2.37A16.281,16.281,0,0,0,92.58-12ZM107.7-31.02h8.1v4.32h.18a10.072,10.072,0,0,1,4.2-3.9,11.908,11.908,0,0,1,5.28-1.26,14.47,14.47,0,0,1,5.61.93,8.317,8.317,0,0,1,3.45,2.58,9.985,9.985,0,0,1,1.77,4.02,25,25,0,0,1,.51,5.25V0h-8.52V-17.52a10.653,10.653,0,0,0-1.23-5.73q-1.23-1.89-4.23-1.89-3.48,0-5.04,2.07t-1.56,6.81V0H107.7Zm50.04,23.7a6.776,6.776,0,0,0,3.18-.72,6.652,6.652,0,0,0,2.28-1.92,8.5,8.5,0,0,0,1.35-2.73A11.318,11.318,0,0,0,165-15.9a17.725,17.725,0,0,0-.36-3.6,9.065,9.065,0,0,0-1.2-3.06,6.141,6.141,0,0,0-2.25-2.1,7.039,7.039,0,0,0-3.45-.78,6.247,6.247,0,0,0-3.09.72,6.5,6.5,0,0,0-2.16,1.95,8.222,8.222,0,0,0-1.26,2.82,13.932,13.932,0,0,0-.39,3.33,16.939,16.939,0,0,0,.33,3.33,9,9,0,0,0,1.14,2.97,6.659,6.659,0,0,0,2.13,2.16A6.034,6.034,0,0,0,157.74-7.32ZM173.1-1.98a20.664,20.664,0,0,1-.21,2.79,13.332,13.332,0,0,1-.81,3.06,12.092,12.092,0,0,1-1.68,2.94,10.529,10.529,0,0,1-2.88,2.52,15.763,15.763,0,0,1-4.38,1.77,24.924,24.924,0,0,1-6.12.66,19.843,19.843,0,0,1-4.65-.57,14.772,14.772,0,0,1-4.32-1.77,10.616,10.616,0,0,1-3.24-3.09,8.958,8.958,0,0,1-1.47-4.53h8.52a4.86,4.86,0,0,0,2.329,3.33,8.33,8.33,0,0,0,4,.93,6.16,6.16,0,0,0,5.2-2.16A8.494,8.494,0,0,0,165-1.62V-5.7h-.12a8.435,8.435,0,0,1-3.93,3.63A12.729,12.729,0,0,1,155.58-.9a13.552,13.552,0,0,1-6-1.23,11.385,11.385,0,0,1-4.14-3.36,14.018,14.018,0,0,1-2.37-4.98,23.608,23.608,0,0,1-.75-6.03,19.336,19.336,0,0,1,.87-5.79,15.134,15.134,0,0,1,2.55-4.92,12.323,12.323,0,0,1,4.17-3.39,12.522,12.522,0,0,1,5.73-1.26,11.992,11.992,0,0,1,5.37,1.14,9.267,9.267,0,0,1,3.87,3.84H165v-4.14h8.1Zm27.72-16.86a9.847,9.847,0,0,0-2.13-4.92,5.984,5.984,0,0,0-4.65-1.68,7.412,7.412,0,0,0-3.39.69,6.454,6.454,0,0,0-2.16,1.71,6.264,6.264,0,0,0-1.14,2.16,9.781,9.781,0,0,0-.39,2.04Zm-13.86,5.4q.18,4.08,2.1,5.97a7.542,7.542,0,0,0,5.52,1.89,7.6,7.6,0,0,0,4.44-1.29A5.351,5.351,0,0,0,201.3-9.6h7.5q-1.8,5.52-5.52,7.98a15.949,15.949,0,0,1-9,2.46,17.324,17.324,0,0,1-6.6-1.2,14.048,14.048,0,0,1-4.98-3.36,14.988,14.988,0,0,1-3.15-5.16,18.887,18.887,0,0,1-1.11-6.6,18.092,18.092,0,0,1,1.14-6.48,15.207,15.207,0,0,1,3.24-5.19,15.25,15.25,0,0,1,5.01-3.45,16.066,16.066,0,0,1,6.45-1.26,14.75,14.75,0,0,1,6.9,1.53,13.771,13.771,0,0,1,4.83,4.11,16.889,16.889,0,0,1,2.73,5.88,21.9,21.9,0,0,1,.6,6.9Zm54.48-21.48H228.6v-7.92h35.1v7.92H250.86V0h-9.42ZM288.54,0H277.92L264-42.84h9.72l9.54,30.12h.12l9.66-30.12h9.72Zm65.52-4.86A14.388,14.388,0,0,1,348.48-.3a15.478,15.478,0,0,1-6.18,1.32,21.583,21.583,0,0,1-8.88-1.74,19.367,19.367,0,0,1-6.6-4.74,20.8,20.8,0,0,1-4.14-7.08,25.951,25.951,0,0,1-1.44-8.7,26.845,26.845,0,0,1,1.44-8.88,21.347,21.347,0,0,1,4.14-7.2,19,19,0,0,1,6.6-4.8,21.583,21.583,0,0,1,8.88-1.74,20.816,20.816,0,0,1,6.39.99,17.729,17.729,0,0,1,5.55,2.91,16.527,16.527,0,0,1,4.11,4.74,15.711,15.711,0,0,1,2.01,6.48h-9a8.953,8.953,0,0,0-3.27-5.4,9.456,9.456,0,0,0-5.79-1.8,11,11,0,0,0-5.4,1.23,10.2,10.2,0,0,0-3.6,3.3,14.46,14.46,0,0,0-2.01,4.71,23.443,23.443,0,0,0-.63,5.46,21.809,21.809,0,0,0,.63,5.25,14.188,14.188,0,0,0,2.01,4.59,10.3,10.3,0,0,0,3.6,3.27,11,11,0,0,0,5.4,1.23,10.248,10.248,0,0,0,7.2-2.37,10.228,10.228,0,0,0,3-6.87h-9.48v-7.02h18V0h-6ZM375.3-15.48a18.166,18.166,0,0,0,.36,3.63,9.67,9.67,0,0,0,1.23,3.18,6.551,6.551,0,0,0,2.31,2.25,7.051,7.051,0,0,0,3.6.84,7.22,7.22,0,0,0,3.63-.84,6.476,6.476,0,0,0,2.34-2.25A9.671,9.671,0,0,0,390-11.85a18.168,18.168,0,0,0,.36-3.63,18.617,18.617,0,0,0-.36-3.66,9.337,9.337,0,0,0-1.23-3.18,6.822,6.822,0,0,0-2.34-2.25,7.019,7.019,0,0,0-3.63-.87,6.856,6.856,0,0,0-3.6.87,6.909,6.909,0,0,0-2.31,2.25,9.337,9.337,0,0,0-1.23,3.18A18.615,18.615,0,0,0,375.3-15.48Zm-8.52,0a19.042,19.042,0,0,1,1.14-6.75,14.526,14.526,0,0,1,3.24-5.16,14.519,14.519,0,0,1,5.04-3.3,17.717,17.717,0,0,1,6.6-1.17,17.952,17.952,0,0,1,6.63,1.17,14.46,14.46,0,0,1,5.07,3.3,14.526,14.526,0,0,1,3.24,5.16,19.042,19.042,0,0,1,1.14,6.75,18.8,18.8,0,0,1-1.14,6.72,14.578,14.578,0,0,1-3.24,5.13,14.46,14.46,0,0,1-5.07,3.3A17.952,17.952,0,0,1,382.8.84a17.717,17.717,0,0,1-6.6-1.17,14.519,14.519,0,0,1-5.04-3.3,14.578,14.578,0,0,1-3.24-5.13A18.8,18.8,0,0,1,366.78-15.48Z'
            transform='translate(151 181)'
            fill={appTheme.colors.alabaster80}
            fillRule='evenodd'
          />
          <S.Path
            d='M680.241-658.713a5.124,5.124,0,0,1-2.541-4.447v-10.189a8.431,8.431,0,0,0,10.165,1.278l31.1-18.146a5.061,5.061,0,0,1,6.949,1.872,5.188,5.188,0,0,1-1.866,7.021l-38.724,22.611a5.074,5.074,0,0,1-2.548.688A5,5,0,0,1,680.241-658.713Zm-2.524-20.937v-45.24a5.123,5.123,0,0,1,2.524-4.447,5.035,5.035,0,0,1,5.083,0l8.708,5.112a8.559,8.559,0,0,0-6.167,8.263v36.293a5.115,5.115,0,0,1-5.083,5.149A5.1,5.1,0,0,1,677.718-679.65Zm47.734-14.366L694.37-712.18A5.168,5.168,0,0,1,692.5-719.2a5.055,5.055,0,0,1,6.948-1.89l38.724,22.629a5.145,5.145,0,0,1,2.523,4.446,5.161,5.161,0,0,1-2.541,4.447l-8.726,5.095A8.637,8.637,0,0,0,725.452-694.016Z'
            transform='translate(-613.7 852.025)'
            fill={appTheme.colors.blazeOrange}
            fillRule='evenodd'
          />{' '}
        </S.G>
      </S.SVG>
    </S.Container>
  );
};
