import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconCheckInCircle = ({
  color,
  height = 27,
  width = 27,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container data-testid='IconCheckInCircle' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 27 27'>
        <S.Path
          d='M13.5 27A13.5 13.5 0 0 1 3.954 3.954a13.5 13.5 0 1 1 19.092 19.092A13.412 13.412 0 0 1 13.5 27zM7.555 11.606A1.34 1.34 0 0 0 6.6 12l-.541.541a1.349 1.349 0 0 0-.1 1.8l4.324 5.409a1.35 1.35 0 0 0 2.11 0L21.04 8.936a1.351 1.351 0 0 0-1.978-1.831l-7.995 7.746-2.632-2.92a1.349 1.349 0 0 0-.88-.325z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
