import styled, { css } from 'styled-components';

import { changePxToRem } from 'utils/math';

import { IconI } from './types';

export const Circle = styled.circle``;

export const Path = styled.path``;

export const G = styled.g``;

export const Defs = styled.defs``;

export const SVG = styled.svg.attrs(() => ({
  xmlns: 'http://www.w3.org/2000/svg',
}))`
  #player_back {
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.33);
  }
  #player_close_X {
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.33);
  }
`;

export const Container = styled.div<{ scale?: IconI['scale']; width?: number }>`
  display: flex;
  align-items: center;
  justify-content: center;

  ${({ scale }) =>
    scale &&
    css`
      transform: scale(${scale});
    `};

  ${({ width }) =>
    width &&
    css`
      width: ${changePxToRem(width)}rem;
      height: ${changePxToRem(width)}rem;
    `};
`;
