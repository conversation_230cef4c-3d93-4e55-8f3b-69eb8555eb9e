import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconOrange = ({
  scale,
  color,
  height = 32,
  width = 32,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconOrange' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 400 400'>
        <S.Path
          d='M363,175.3L145.1,49.6c-13.7-7.9-31.1-3.2-39.1,10.5c-7.9,13.7-3.2,31.1,10.5,39l0,0L291.4,200
          c18.9,10.9,27.5,32.8,22.4,53l49.1-28.3c8.8-5.1,14.3-14.5,14.3-24.7S371.8,180.4,363,175.3z'
          fill={appTheme.colors.primary}
        />
        <S.Path
          d='M254.9,221.1L254.9,221.1L79.9,321.9c-18.9,10.9-42.3,7.4-57.2-7.1v56.6c0,10.2,5.4,19.6,14.3,24.7
		c8.8,5.1,19.7,5.1,28.6,0l217.9-125.6c13.7-7.9,18.3-25.4,10.5-39C286.1,217.9,268.6,213.2,254.9,221.1z'
          fill={appTheme.colors.primary}
        />
        <S.Path
          d='M51.3,308.3c15.8,0,28.6-12.8,28.6-28.6V78.1c0-21.9,14.7-40.3,34.7-45.9L65.6,3.8c-8.8-5.1-19.7-5.1-28.6,0
		c-8.8,5-14.2,14.5-14.2,24.7v251.3C22.8,295.6,35.6,308.3,51.3,308.3z'
          fill={appTheme.colors.primary}
        />
      </S.SVG>
    </S.Container>
  );
};
