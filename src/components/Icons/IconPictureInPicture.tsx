import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconPictureInPicture = ({
  color,
  scale,
  height = 164,
  width = 187.665,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconPhone' {...rest}>
      <S.SVG height={height} width={width} viewBox='0 0 187.665 164'>
        <S.Path
          d='M106.089,164c-7.5,0-12.3-4.6-12.3-12.2-.1-15.4-.1-30.7,0-46.1,0-7,4.9-12,12.1-12q34.8-.15,69.6,0c7.2,0,12,5.1,12.1,12.3.1,15.4.1,30.7,0,46.1,0,7.1-5,11.8-12.1,11.8-11.7.1-23.3.1-34.9.1Zm-72-.3c-18.2-.2-33.7-15.2-33.9-33.5-.3-32.5-.2-65,0-97.5.1-14.2,11.8-28.2,25.9-31.6A43.944,43.944,0,0,1,36.39,0c19.1.1,38.3.2,57.5.2,20.1,0,40.3-.1,60.4,0,16,.1,31.2,14,32.5,29.9.8,9.7.6,19.4.5,29.1a11.605,11.605,0,0,1-23.2-.5c-.1-7.3,0-14.7,0-22,0-8.7-4.8-13.4-13.5-13.4H36.989c-8.9,0-13.6,4.7-13.6,13.5v89.5c0,9.7,4.4,14.1,14.2,14.1,7.1,0,14.3-.1,21.4.1a11.646,11.646,0,0,1,3,22.8,13.434,13.434,0,0,1-3.5.4c-5.4,0-10.845.044-16.274.044Q38.143,163.745,34.089,163.7Zm19.2-81.6c-4,0-6.4-2.3-6.4-5.9s2.3-5.7,6.4-5.7h7.9a5.542,5.542,0,0,1,.6-.8c-1.7-1.5-3.6-3-5.2-4.6-6.4-6.3-12.8-12.7-19.1-19.1-3-3-3.2-6.3-.7-8.9,2.4-2.5,6.1-2.3,8.9.6,7.3,7.3,14.6,14.6,21.9,21.8.7.6,1.5,1.2,2.6,2.3V53.6c.1-3.9,2.6-6.8,6-6.7,3.2.1,5.8,2.8,5.9,6.6.1,7.3.1,14.7,0,22,0,4.2-2.5,6.6-6.8,6.6-3.65.05-7.325.075-11,.075S56.94,82.15,53.29,82.1Z'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
