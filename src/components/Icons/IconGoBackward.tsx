import { useTheme } from 'theme';

import { IconI } from './types';
import * as S from './styles';

export const IconGoBackward = ({
  color,
  scale,
  height = 48,
  width = 62,
  ...rest
}: IconI) => {
  const appTheme = useTheme();

  return (
    <S.Container scale={scale} data-testid='IconGoBackward' {...rest}>
      <S.SVG height={height} width={width} viewBox={`0 0 62 48`}>
        <S.Path
          d='M28 14.5H9.7l8.4-8.4L16 4 4 16l12 12 2.1-2.1-8.4-8.4H28v-3z'
          transform='translate(15 8)'
          fill={color || appTheme.colors.white80}
          fillRule='evenodd'
        />
      </S.SVG>
    </S.Container>
  );
};
