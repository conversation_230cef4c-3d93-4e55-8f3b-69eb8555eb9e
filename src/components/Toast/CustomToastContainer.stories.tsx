import { Story, Meta } from '@storybook/react';
import { CustomToastContainer } from './CustomToastContainer';
import { toast } from 'react-toastify';
import { PrimaryButton } from '../Buttons/PrimaryButton';

export default {
  title: 'Components/Toast',
  component: CustomToastContainer,
  argTypes: {
    type: {
      options: ['success', 'error', 'info'],
      control: { type: 'radio' },
    },
  },
} as Meta;

const Template: Story = ({ type, content, autoCloseTime, closeButton }) => (
  <div
    style={{
      height: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <CustomToastContainer />
    <PrimaryButton
      variant='orange'
      onClick={() =>
        toast(content, {
          type: type,
          autoClose: autoCloseTime,
          closeButton: closeButton,
        })
      }
    >
      Show Toast
    </PrimaryButton>
  </div>
);

export const Default = Template.bind({});
Default.args = {
  type: 'success',
  content: 'Success Toast',
  autoCloseTime: 1500,
  closeButton: true,
};

export const Success = Template.bind({});
Success.args = {
  type: 'success',
  content: 'Success Toast',
  autoCloseTime: 1500,
  closeButton: true,
};

export const Error = Template.bind({});
Error.args = {
  type: 'error',
  content: 'Error Toast',
  autoCloseTime: 1500,
  closeButton: true,
};

export const Info = Template.bind({});
Info.args = {
  type: 'info',
  content: 'Info Toast',
  autoCloseTime: 1500,
  closeButton: true,
};
