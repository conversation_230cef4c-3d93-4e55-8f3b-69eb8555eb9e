import { act, fireEvent, waitFor } from '@testing-library/react';
import { render } from '@testing-library/react';
import { toast, TypeOptions } from 'react-toastify';

import { CustomToastContainer } from '../CustomToastContainer';
import { ApplicationThemeProvider } from 'theme';

describe('Toasts', () => {
  const setupToast = (
    toastText: string,
    type: TypeOptions,
    closeButton = true,
  ) =>
    render(
      <ApplicationThemeProvider>
        <CustomToastContainer />
        <button
          onClick={() =>
            toast(toastText, {
              type,
              closeButton,
            })
          }
        >
          {`Show ${type} toast`}
        </button>
      </ApplicationThemeProvider>,
    );

  it.each`
    type         | text
    ${'success'} | ${'Toast success message'}
    ${'error'}   | ${'Toast error message'}
    ${'info'}    | ${'Toast info message'}
  `('should render $type toast notification', async ({ type, text }) => {
    const { getByText, getByRole, debug } = setupToast(text, type);
    const button = getByRole('button', { name: `Show ${type} toast` });

    await act(() => {
      fireEvent.click(button);
    });

    const toast = await waitFor(() => getByText(text));
    expect(toast).toBeInTheDocument();
    expect(toast).toHaveTextContent(text);
  });
});
