import styled from 'styled-components';
import { ToastContainer } from 'react-toastify';

export const StyledToastContainer = styled(ToastContainer)`
  width: auto;
  right: 32px;
  top: 96px;

  .Toastify__toast {
    height: 48px;
    width: 100%;
    border-radius: 6px;
    padding: 0 8px;
    font-size: 1.8rem;
    font-weight: bold;
  }

  .Toastify__close-button {
    align-self: center;
    opacity: 1;
    padding-left: 1rem;

    & > svg {
      fill: ${({ theme }) => theme.colors.white};
      height: 20px;
      width: 20px;
    }
  }

  .Toastify__toast--error {
    background-color: ${({ theme }) => theme.colors.red};
    color: ${({ theme }) => theme.colors.white};

    .Toastify__toast-icon > svg {
      fill: ${({ theme }) => theme.colors.white};
    }
  }

  .Toastify__toast--success {
    background-color: ${({ theme }) => theme.colors.screaminGreen};
    color: ${({ theme }) => theme.colors.black};

    .Toastify__toast-icon > svg {
      fill: ${({ theme }) => theme.colors.black};
    }

    .Toastify__close-button > svg {
      fill: ${({ theme }) => theme.colors.black};
    }
  }

  .Toastify__toast--info {
    background-color: ${({ theme }) => theme.colors.havelockBlue};
    color: ${({ theme }) => theme.colors.white};

    .Toastify__toast-icon > svg {
      fill: ${({ theme }) => theme.colors.white};
    }
  }
`;
