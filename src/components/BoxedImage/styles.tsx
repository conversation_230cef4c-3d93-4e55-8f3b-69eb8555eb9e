import styled from 'styled-components';

import { BoxedImageProps } from './types';

export const Box = styled.div<{
  isActive: BoxedImageProps['isActive'];
  isSmall?: BoxedImageProps['isSmall'];
}>`
  background: ${({ theme }) => theme.colors.tundora75};
  border-radius: 1.2rem;
  padding: 0.8rem;
  width: ${({ isSmall }) => (isSmall ? '6.4rem' : '12.8rem')};
  height: ${({ isSmall }) => (isSmall ? '6.4rem' : '12.8rem')};
  display: flex;
  justify-content: center;
  align-items: center;
  border: ${({ isActive, theme }) =>
    isActive ? `${theme.colors.primary} solid 0.2rem` : 'none'};

  &:hover {
    cursor: pointer;
  }

  & > div > img {
    object-fit: contain;
  }
`;

Box.displayName = 'BoxedImageBox';
