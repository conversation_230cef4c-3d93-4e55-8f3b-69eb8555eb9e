import { Story, Meta } from '@storybook/react';

import { BoxedImage } from './BoxedImage';

export default {
  title: 'Components/BoxedImage',
  component: BoxedImage,
} as Meta;

const Template: Story = ({ src, alt, isActive, isSmall }) => (
  <BoxedImage src={src} alt={alt} isActive={isActive} isSmall={isSmall} />
);

export const Default = Template.bind({});
Default.args = {
  src: 'https://www.fillmurray.com/112/112',
  alt: 'test',
};

export const Active = Template.bind({});
Active.args = {
  src: 'https://www.fillmurray.com/112/112',
  alt: 'test',
  isActive: true,
};

export const Small = Template.bind({});
Small.args = {
  src: 'https://www.fillmurray.com/56/56',
  alt: 'test',
  isSmall: true,
};
