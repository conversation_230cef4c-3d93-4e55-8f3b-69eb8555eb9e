import { Image } from 'components/Image';

import { BoxedImageProps } from './types';
import * as S from './styles';

export function BoxedImage({
  src,
  alt = '',
  isActive = false,
  isSmall = false,
  photoRatio,
  onClick = () => ({}),
}: BoxedImageProps): JSX.Element {
  return (
    <S.Box onClick={onClick} isActive={isActive} isSmall={isSmall}>
      <Image src={src} alt={alt} photoRatio={photoRatio} />
    </S.Box>
  );
}
