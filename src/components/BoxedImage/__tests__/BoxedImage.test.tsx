import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { BoxedImage } from '../BoxedImage';

describe('Components:BoxedImage', () => {
  it('should render BoxedImage component', () => {
    render(
      <BoxedImage src='https://www.fillmurray.com/112/112' alt='Test image' />,
    );

    expect(screen.getByAltText('Test image')).toBeInTheDocument();
  });

  it('should run onClick function', async () => {
    const fn = vi.fn();

    render(
      <BoxedImage
        src='https://www.fillmurray.com/112/112'
        alt='Test image'
        onClick={fn}
      />,
    );

    await userEvent.click(screen.getByAltText('Test image'));

    expect(fn).toHaveBeenCalled();
  });
});
