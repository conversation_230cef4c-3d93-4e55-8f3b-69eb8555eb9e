import { Image } from 'components/Image';
import { useHover } from 'hooks/useHover';

import { PosterPreviewProps } from './types';
import * as S from './styles';

export const PosterPreview = ({
  title,
  src,
  placeholderType,
  photoRatio,
  scaleWidth,
}: PosterPreviewProps) => {
  const { handleMouseEnter, handleMouseLeave } = useHover();

  return (
    <S.Container>
      <S.ImageContainer
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        $scaleWidth={scaleWidth}
      >
        <Image
          src={src}
          placeholderType={placeholderType}
          photoRatio={photoRatio}
        />
      </S.ImageContainer>
      <S.Title $sizeSmall>{title}</S.Title>
    </S.Container>
  );
};
