import { Story, Meta } from '@storybook/react';

import { PosterPreview } from './PosterPreview';

export default {
  title: 'Components/PosterPreview',
  component: PosterPreview,
} as Meta;

const Template: Story = ({ src, title, remainingTime, hasBorder }) => (
  <div style={{ width: '300px' }}>
    <PosterPreview src={src} title={title} remainingTime={remainingTime} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  title: 'Default Title',
  src: 'https://www.fillmurray.com/240/300',
};
