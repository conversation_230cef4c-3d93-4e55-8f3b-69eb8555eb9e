import styled, { css } from 'styled-components';

import { Text } from 'components/Typography';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  cursor: pointer;
  height: 100%;
`;

export const ImageContainer = styled.div<{ $scaleWidth?: boolean }>`
  position: relative;
  height: 100%;
  min-height: 20rem;

  ${({ $scaleWidth }) =>
    $scaleWidth &&
    css`
      > div {
        width: 100%;
        background-color: ${({ theme }) => theme.colors.tertiary};
      }

      > div > img {
        max-width: 100%;
        width: auto;
      }
    `}
`;

export const Title = styled(Text)`
  font-weight: bold;
  margin: 1.2rem 0 0.4rem 0;
  padding-left: 0.4rem;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

Container.displayName = 'VodPosterPreviewContainer';
Title.displayName = 'VodPosterPreviewTitle';
