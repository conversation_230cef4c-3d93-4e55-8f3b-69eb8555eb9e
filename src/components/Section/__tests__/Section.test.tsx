import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { render } from 'utils/testing';
import { Section as SectionComponent } from '../Section';

describe('Components:SectionComponent', () => {
  it('renders SectionComponent component', () => {
    render(
      <SectionComponent title='Title test'>
        <>TEST</>
      </SectionComponent>,
    );

    expect(screen.getByText('Title test')).toBeInTheDocument();
  });

  it('should renders children of SectionComponent after click on SectionComponent', async () => {
    render(
      <SectionComponent title='Title test'>
        <>
          <>TEST</>
        </>
      </SectionComponent>,
    );

    await userEvent.click(screen.getByText('Title test'));
    expect(screen.getByText('TEST')).toBeInTheDocument();
  });
});
