import { useCallback, useState } from 'react';
import { AnimatePresence } from 'framer-motion';

import { IconChevronDown } from 'components/Icons';
import { H2 } from 'components/Typography';
import { useModalPrControl } from 'services/user/ModalPrControl';

import { SectionProps } from './types';
import * as S from './styles';

export const Section = ({
  children,
  title,
  withParentalControl,
}: SectionProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { showPrControlModal } = useModalPrControl();

  const handleOnSectionClick = useCallback(() => {
    if (!withParentalControl) {
      setIsOpen((previousState) => !previousState);
      return;
    }

    if (isOpen) {
      setIsOpen(false);
      return;
    }

    showPrControlModal(() => {
      setIsOpen(true);
    });
  }, [isOpen, showPrControlModal, withParentalControl]);

  return (
    <>
      <S.SectionHeader onClick={handleOnSectionClick} $isOpen={isOpen}>
        <H2 $highlight={isOpen}>{title}</H2>
        <IconChevronDown />
      </S.SectionHeader>
      <AnimatePresence initial={false}>
        {isOpen && (
          <S.SectionContainer
            transition={{ duration: 0.8, ease: [0.04, 0.62, 0.23, 0.98] }}
          >
            {children}
          </S.SectionContainer>
        )}
      </AnimatePresence>
    </>
  );
};
