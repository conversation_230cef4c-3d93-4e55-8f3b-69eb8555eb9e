import styled from 'styled-components';
import { motion } from 'framer-motion';

const variants = {
  open: { opacity: 1, height: 'auto' },
  collapsed: { opacity: 0, height: 0 },
};

export const SectionContainer = styled(motion.section).attrs(() => ({
  initial: 'collapsed',
  animate: 'open',
  exit: 'collapsed',
  variants: variants,
}))``;

export const SectionHeader = styled.div<{ $isOpen: boolean }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.2rem solid ${({ theme }) => theme.colors.tertiaryLight};
  padding-right: 2rem;

  svg {
    transform: ${({ $isOpen }) => ($isOpen ? 'rotate(-180deg)' : 'rotate(0)')};
    transition: transform 0.4s ease-in-out;
  }
`;

SectionContainer.displayName = 'SectionContainer';
