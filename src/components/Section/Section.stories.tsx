import { Story, Meta } from '@storybook/react';

import { Section } from './Section';
import { Text } from 'components/Typography';

export default {
  title: 'Components/Section',
  component: Section,
} as Meta;

const Template: Story = ({ children, title }) => (
  <>
    <Section title={title}>
      <Text primary sizeXLarge>
        {children} primary xLarge
      </Text>
      <br />
      <Text primary sizeLarge>
        {children} primary large
      </Text>
      <br />
      <Text primary sizeMedium>
        {children} primary medium
      </Text>
      <br />
      <Text primary sizeSmall>
        {children} primary small
      </Text>
      <br />
      <Text primary sizeXSmall>
        {children} primary xSmall
      </Text>
      <br />
      <Text primary sizeXXSmall>
        {children} primary xxSmall
      </Text>
    </Section>
    <Section title={title}>
      <Text primary sizeXLarge>
        {children} primary xLarge
      </Text>
      <br />
      <Text primary sizeLarge>
        {children} primary large
      </Text>
      <br />
      <Text primary sizeMedium>
        {children} primary medium
      </Text>
      <br />
      <Text primary sizeSmall>
        {children} primary small
      </Text>
      <br />
      <Text primary sizeXSmall>
        {children} primary xSmall
      </Text>
      <br />
      <Text primary sizeXXSmall>
        {children} primary xxSmall
      </Text>
    </Section>
  </>
);

export const Default = Template.bind({});
Default.args = {
  children: 'Text',
  title: 'Section title',
};
