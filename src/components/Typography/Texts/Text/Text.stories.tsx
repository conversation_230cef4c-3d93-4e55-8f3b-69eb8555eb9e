import { Story, Meta } from '@storybook/react';

import { Text as TextComponent } from './Text';

export default {
  title: 'Components/Typography/Texts',
  component: TextComponent,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <TextComponent {...rest}>{children} default</TextComponent>
    <br />
    <br />
    <TextComponent {...rest} primary sizeXLarge>
      {children} primary xLarge
    </TextComponent>
    <br />
    <TextComponent {...rest} primary sizeLarge>
      {children} primary large
    </TextComponent>
    <br />
    <TextComponent {...rest} primary sizeMedium>
      {children} primary medium
    </TextComponent>
    <br />
    <TextComponent {...rest} primary sizeSmall>
      {children} primary small
    </TextComponent>
    <br />
    <TextComponent {...rest} primary sizeXSmall>
      {children} primary xSmall
    </TextComponent>
    <br />
    <TextComponent {...rest} primary sizeXXSmall>
      {children} primary xxSmall
    </TextComponent>
    <br />
    <br />
    <TextComponent {...rest} secondary sizeXLarge>
      {children} secondary xLarge
    </TextComponent>
    <br />
    <TextComponent {...rest} secondary sizeLarge>
      {children} secondary large
    </TextComponent>
    <br />
    <TextComponent {...rest} secondary sizeMedium>
      {children} secondary medium
    </TextComponent>
    <br />
    <TextComponent {...rest} secondary sizeSmall>
      {children} secondary small
    </TextComponent>
    <br />
    <TextComponent {...rest} secondary sizeXSmall>
      {children} secondary xSmall
    </TextComponent>
    <br />
    <TextComponent {...rest} secondary sizeXXSmall>
      {children} secondary xxSmall
    </TextComponent>
    <br />
    <br />
    <TextComponent {...rest} highlight sizeXLarge>
      {children} highlight xLarge
    </TextComponent>
    <br />
    <TextComponent {...rest} highlight sizeLarge>
      {children} highlight large
    </TextComponent>
    <br />
    <TextComponent {...rest} highlight sizeMedium>
      {children} highlight medium
    </TextComponent>
    <br />
    <TextComponent {...rest} highlight sizeSmall>
      {children} highlight small
    </TextComponent>
    <br />
    <TextComponent {...rest} highlight sizeXSmall>
      {children} highlight xSmall
    </TextComponent>
    <br />
    <TextComponent {...rest} highlight sizeXXSmall>
      {children} highlight xxSmall
    </TextComponent>
  </>
);

export const TextDefault = Template.bind({});
TextDefault.args = {
  children: 'Text',
};
