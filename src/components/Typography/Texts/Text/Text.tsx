import styled from 'styled-components';

import {
  BaseTextColorProps,
  BaseTextSizeProps,
  BaseTextWrapProps,
} from 'theme';

export const Text = styled.span<
  BaseTextColorProps & BaseTextSizeProps & BaseTextWrapProps
>`
  ${({ theme }) => theme.mixins.baseTextColor};
  ${({ theme }) => theme.mixins.baseTextSize};
  ${({ theme }) => theme.mixins.baseTextWrap};
  display: inline-block;
  line-height: 1.25;
`;

Text.displayName = 'Text';
