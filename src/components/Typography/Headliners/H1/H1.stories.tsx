import { Story, Meta } from '@storybook/react';

import { H1 as H1Component } from './H1';

export default {
  title: 'Components/Typography/Headliners',
  component: H1Component,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <H1Component {...rest}>{children} default</H1Component>
    <br />
    <H1Component {...rest} primary>
      {children} primary
    </H1Component>
    <br />
    <H1Component {...rest} secondary>
      {children} secondary
    </H1Component>
    <br />
    <H1Component {...rest} highlight>
      {children} highlight
    </H1Component>
  </>
);

export const H1 = Template.bind({});
H1.args = {
  children: 'Headliner 1',
};
