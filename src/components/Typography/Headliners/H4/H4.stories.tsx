import { Story, Meta } from '@storybook/react';

import { H4 as H4Component } from './H4';

export default {
  title: 'Components/Typography/Headliners',
  component: H4Component,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <H4Component {...rest}>{children} default</H4Component>
    <br />
    <H4Component {...rest} primary>
      {children} primary
    </H4Component>
    <br />
    <H4Component {...rest} secondary>
      {children} secondary
    </H4Component>
    <br />
    <H4Component {...rest} highlight>
      {children} highlight
    </H4Component>
  </>
);

export const H4 = Template.bind({});
H4.args = {
  children: 'Headliner 4',
};
