import { Story, Meta } from '@storybook/react';

import { H3 as H3Component } from './H3';

export default {
  title: 'Components/Typography/Headliners',
  component: H3Component,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <H3Component {...rest}>{children} default</H3Component>
    <br />
    <H3Component {...rest} primary>
      {children} primary
    </H3Component>
    <br />
    <H3Component {...rest} secondary>
      {children} secondary
    </H3Component>
    <br />
    <H3Component {...rest} highlight>
      {children} highlight
    </H3Component>
  </>
);

export const H3 = Template.bind({});
H3.args = {
  children: 'Headliner 3',
};
