import { Story, Meta } from '@storybook/react';

import { H2 as H2Component } from './H2';

export default {
  title: 'Components/Typography/Headliners',
  component: H2Component,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <H2Component {...rest}>{children} default</H2Component>
    <br />
    <H2Component {...rest} primary>
      {children} primary
    </H2Component>
    <br />
    <H2Component {...rest} secondary>
      {children} secondary
    </H2Component>
    <br />
    <H2Component {...rest} highlight>
      {children} highlight
    </H2Component>
  </>
);

export const H2 = Template.bind({});
H2.args = {
  children: 'Headliner 2',
};
