import { Story, Meta } from '@storybook/react';

import { Link as <PERSON>Component } from './Link';

export default {
  title: 'Components/Typography/Links',
  component: LinkComponent,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <LinkComponent {...rest}>{children} default</LinkComponent>
    <br />
    <br />
    <LinkComponent {...rest} sizeXLarge>
      {children} xLarge
    </LinkComponent>
    <br />
    <LinkComponent {...rest} sizeLarge>
      {children} large
    </LinkComponent>
    <br />
    <LinkComponent {...rest} sizeMedium>
      {children} medium
    </LinkComponent>
    <br />
    <LinkComponent {...rest} sizeSmall>
      {children} small
    </LinkComponent>
    <br />
    <LinkComponent {...rest} sizeXSmall>
      {children} xSmall
    </LinkComponent>
    <br />
    <LinkComponent {...rest} sizeXXSmall>
      {children} xxSmall
    </LinkComponent>
  </>
);

export const LinkDefault = Template.bind({});
LinkDefault.args = {
  children: 'Link',
  bright: false,
  isActive: false,
};
