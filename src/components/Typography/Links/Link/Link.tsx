import styled, { css } from 'styled-components';

import { BaseTextSizeProps, convertHexToRgbaString } from 'theme';

export interface LinkProps {
  bright?: boolean;
  isActive?: boolean;
}

export const Link = styled.a<BaseTextSizeProps & LinkProps>`
  ${({ theme }) => theme.mixins.baseTextSize};
  color: ${({ theme }) => theme.colors.primary};
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  line-height: 1.4;
  position: relative;
  text-decoration: none;
  transition:
    color 0.3s linear,
    border-color 0.3s linear;

  &:after {
    background-color: ${({ theme }) => theme.colors.primary};
    bottom: 0;
    content: '';
    display: block;
    height: 0.1rem;
    left: 0;
    position: absolute;
    transition:
      width 0.3s linear,
      background-color 0.3s linear;
    width: 0;
  }

  ${({ isActive, theme }) =>
    !isActive &&
    css`
      &:hover {
        color: ${convertHexToRgbaString(theme.colors.primary, 0.8)};

        &:after {
          background-color: ${convertHexToRgbaString(
            theme.colors.primary,
            0.8,
          )};
          width: 100%;
        }
      }
    `}

  ${({ bright, isActive, theme }) =>
    bright &&
    css`
      color: ${isActive ? theme.colors.primary : theme.colors.white};
    `}
`;

Link.displayName = 'Link';
