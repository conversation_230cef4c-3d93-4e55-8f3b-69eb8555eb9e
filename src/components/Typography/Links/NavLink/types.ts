import { NavLink } from 'react-router-dom';

import { BaseTextSizeProps } from 'theme';

interface CustomProps extends BaseTextSizeProps {
  $sizeXXSmall?: boolean;
  $sizeXSmall?: boolean;
  $sizeSmall?: boolean;
  $sizeMedium?: boolean;
  $sizeLarge?: boolean;
  $sizeXLarge?: boolean;
}

// Extend types from NavLink (react-router-dom) with CustomProps
export type NavLinkProps = React.ComponentProps<typeof NavLink> & CustomProps;
