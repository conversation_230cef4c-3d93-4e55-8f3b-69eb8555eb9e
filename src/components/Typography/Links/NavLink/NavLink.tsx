import styled, { css } from 'styled-components';
import { NavLink as NavigationLink } from 'react-router-dom';

import { convertHexToRgbaString } from 'theme';

import { NavLinkProps } from './types';

export const NavLink = styled(NavigationLink).attrs(({ theme, ...props }) => {
  const newProps = Object.fromEntries(
    Object.entries(props).filter(([key]) => !key.startsWith('$')),
  );
  return { ...newProps };
})<NavLinkProps>`
  ${({ theme }) => theme.mixins.baseTextSize};
  color: ${({ theme }) => theme.colors.white};
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  line-height: 1.4;
  position: relative;
  text-decoration: none;
  transition:
    color 0.3s linear,
    border-color 0.3s linear;

  &:after {
    background-color: ${({ theme }) => theme.colors.primary};
    bottom: 0;
    content: '';
    display: block;
    height: 0.1rem;
    left: 0;
    position: absolute;
    transition:
      width 0.3s linear,
      background-color 0.3s linear;
    width: 0;
  }

  ${({ theme }) => css`
    &:hover {
      color: ${convertHexToRgbaString(theme.colors.primary, 0.8)};

      &:after {
        background-color: ${convertHexToRgbaString(theme.colors.primary, 0.8)};
        width: 100%;
      }
    }
  `}
  &.active {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

NavLink.displayName = 'NavLink';
