import { useIntl } from 'react-intl';
import styled from 'styled-components';

import { ModalConfirmation } from 'components/ModalConfirmation';
import { Text } from 'components/Typography';

import { messages } from './messages';
import { UserMsgPopupProps } from './types';
import * as S from './styles';

export const UserMsgPopup = ({
  isOpen,
  onClose,
  userMsg,
}: UserMsgPopupProps) => {
  const { formatMessage } = useIntl();

  const userMessageContent = (
    <S.UserMessageContent>
      {userMsg.split('\n').map((line, index) => (
        <Text key={index} $secondary $sizeSmall>
          {line}
        </Text>
      ))}
    </S.UserMessageContent>
  );

  return (
    <ModalConfirmation
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onClose}
      buttonSubmitText={formatMessage(messages.okButton)}
    >
      {userMessageContent}
    </ModalConfirmation>
  );
};
