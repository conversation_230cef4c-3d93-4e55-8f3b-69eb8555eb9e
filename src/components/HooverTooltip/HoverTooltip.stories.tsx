import { Story, Meta } from '@storybook/react';
import React from 'react';

import { HoverTooltip } from './HoverTooltip';

export default {
  title: 'Components/HoverTooltip',
  component: HoverTooltip,
} as Meta;

const Template: Story = ({ textContent, tooltipContent, width }) => (
  <div style={{ marginLeft: '20px', display: 'flex', paddingTop: '40px' }}>
    <HoverTooltip
      textContent={textContent}
      tooltipContent={tooltipContent}
      width={width}
    />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  textContent: 'Lorem ipsum',
  tooltipContent:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse vel enim in ligula feugiat mollis quis id dui.',
  width: 200,
};
