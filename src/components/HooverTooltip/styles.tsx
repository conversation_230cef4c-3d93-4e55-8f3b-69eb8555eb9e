import styled from 'styled-components';
import { motion } from 'framer-motion';

import { convertHexToRgbaString } from 'theme/colors';

export const TooltipContainer = styled.span`
  position: relative;
`;

export const TooltipText = styled(motion.div)<{ width?: number }>`
  background-color: ${({ theme }) => theme.colors.info};
  padding: 0.8rem 1.4rem;
  border-radius: 0.8rem;
  position: absolute;
  top: -100%;
  left: calc(100% + 1rem);
  width: ${({ width }) => (width ? `${width / 10}rem` : 'auto')};

  &:before {
    content: '';
    position: absolute;
    display: block;
    width: 0.8rem;
    height: 0.8rem;
    top: 1.6rem;
    left: 0;
    background-color: ${({ theme }) => theme.colors.info};
    clip-path: polygon(100% 0, 30% 50%, 100% 100%);
    transform: translateX(-100%);
  }
`;

export const StyledTooltipText = styled.p`
  color: ${({ theme }) => theme.colors.primary};
  cursor: pointer;
  display: inline-block;
  font-weight: bold;
  line-height: 1.4;
  position: relative;
  text-decoration: none;
  transition:
    color 0.3s linear,
    border-color 0.3s linear;
  &:after {
    background-color: ${({ theme }) =>
      convertHexToRgbaString(theme.colors.primary, 0.8)};
    bottom: 0;
    content: '';
    display: block;
    height: 0.1rem;
    left: 0;
    position: absolute;
    transition:
      width 0.3s linear,
      background-color 0.3s linear;
    width: 0;
  }

  &:hover {
    color: ${({ theme }) => convertHexToRgbaString(theme.colors.primary, 0.8)};

    &:after {
      background-color: ${({ theme }) =>
        convertHexToRgbaString(theme.colors.primary, 0.8)};
      width: 100%;
    }
  }
`;
