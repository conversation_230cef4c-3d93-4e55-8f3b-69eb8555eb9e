import { useState } from 'react';

import { TooltipProps } from './types';
import * as S from './styles';

const variants = {
  open: { opacity: 1, display: 'block' },
  closed: { opacity: 0, display: 'none' },
};

export const HoverTooltip = ({
  textContent,
  tooltipContent,
  width,
}: TooltipProps) => {
  const [isTooltipVisible, setIsToolTipVisible] = useState(false);

  return (
    <S.TooltipContainer data-testid='hoverTooltip'>
      <S.StyledTooltipText
        onMouseEnter={() => setIsToolTipVisible(true)}
        onMouseLeave={() => setIsToolTipVisible(false)}
      >
        {textContent}
      </S.StyledTooltipText>
      <S.TooltipText
        animate={isTooltipVisible ? 'open' : 'closed'}
        variants={variants}
        transition={{ duration: 0.2 }}
        width={width}
      >
        {tooltipContent}
      </S.TooltipText>
    </S.TooltipContainer>
  );
};
