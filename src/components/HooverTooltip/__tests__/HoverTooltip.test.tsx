import { screen } from '@testing-library/react';

import { render } from 'utils/testing';
import { HoverTooltip } from '../HoverTooltip';

describe('Components:Tooltip', () => {
  it('should render Tooltip component', () => {
    render(
      <HoverTooltip
        textContent='Test'
        tooltipContent='Tooltip content'
        width={200}
      />,
    );
    expect(screen.getByTestId('hoverTooltip')).toBeInTheDocument();
  });
});
