import { useIntl } from 'react-intl';

import { Text } from 'components/Typography';
import { IconHourglass } from 'components/Icons';
import { useTheme } from 'theme';
import {
  calculateExpirationDate,
  calculateExpirationTime,
  parseExpirationDateToMinutes,
} from 'utils/dateUtils';

import { messages } from './messages';
import { RemainingTimerProps } from './types';
import * as S from './styles';

export const RemainingTime = ({
  expirationDate,
  highlight,
  isExpired,
  expiredMessage,
}: RemainingTimerProps) => {
  const appTheme = useTheme();
  const { formatMessage } = useIntl();

  const remainingMinutes = parseExpirationDateToMinutes(expirationDate);
  const expirationDateObj = calculateExpirationDate(remainingMinutes);

  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const isExpiringToday =
    expirationDateObj.toDateString() === today.toDateString();
  const isExpiringTomorrow =
    expirationDateObj.toDateString() === tomorrow.toDateString();
  const expirationHour = calculateExpirationTime(remainingMinutes);

  if (isExpired && Boolean(expiredMessage)) {
    return (
      <S.Container data-testid='remaining-time'>
        <S.ExpiredRecordingText>
          <Text $sizeXXSmall>{expiredMessage}</Text>
        </S.ExpiredRecordingText>
      </S.Container>
    );
  }

  return (
    <S.Container data-testid='remaining-time'>
      {isExpiringToday ? (
        <S.ExpiredRecordingText>
          <Text $sizeXXSmall>
            {`${formatMessage(messages.expiresToday)} ${expirationHour}`}
          </Text>
        </S.ExpiredRecordingText>
      ) : isExpiringTomorrow ? (
        <S.ExpiredRecordingText>
          <Text $sizeXXSmall>
            {`${formatMessage(messages.expiresTomorrow)} ${expirationHour}`}
          </Text>
        </S.ExpiredRecordingText>
      ) : (
        <>
          <Text $sizeXXSmall>{formatMessage(messages.expiresIn)}</Text>
          <IconHourglass
            height={16}
            width={16}
            color={(highlight && appTheme.colors.primary) || undefined}
          />
          <Text $sizeXXSmall>{expirationDate}</Text>
        </>
      )}
    </S.Container>
  );
};
