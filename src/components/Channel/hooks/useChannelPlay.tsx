import { useCallback } from 'react';

import { usePlayContent } from 'features/Player/Hook';
import { useRegionalTvContext } from 'services/regionalTv';
import { Channel } from 'services/api/newApi/live/channels';

import { useChannelProgram } from './useChannelProgram';

export const useChannelPlay = (channel: Channel) => {
  const { channelExtId, isRegionalTv } = channel;
  const { playChannel, playStartover } = usePlayContent();
  const { currentProgram } = useChannelProgram(channel);
  const { showRegionalTvSelect, isRegionNotSelectedBefore } =
    useRegionalTvContext();

  const handlePlayChannel = useCallback(() => {
    if (isRegionalTv && isRegionNotSelectedBefore) {
      return showRegionalTvSelect(() =>
        playChannel({
          channelExtId: channelExtId,
          programExtId: currentProgram
            ? currentProgram.programExtId
            : undefined,
        }),
      );
    }

    return playChannel({
      channelExtId: channelExtId,
      programExtId: currentProgram?.programExtId || undefined,
      programParentalControlLevel: currentProgram?.prLevel || undefined,
    });
  }, [
    channelExtId,
    currentProgram,
    isRegionNotSelectedBefore,
    isRegionalTv,
    playChannel,
    showRegionalTvSelect,
  ]);

  const handlePlayChannelStartover = useCallback(() => {
    if (currentProgram) {
      playStartover({
        channelExtId: channel.channelExtId,
        programExtId: currentProgram.programExtId,
        programParentalControlLevel: currentProgram.prLevel,
      });
    }
  }, [currentProgram, playStartover, channel.channelExtId]);

  return { handlePlayChannelStartover, handlePlayChannel };
};
