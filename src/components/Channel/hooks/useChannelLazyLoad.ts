import { RefObject, useEffect, useState } from 'react';

import { useIsOnScreen } from 'hooks/useIsOnScreen';

export const useChannelLazyLoad = (channelRef: RefObject<Element>) => {
  const isChannelVisible = useIsOnScreen(channelRef, 0);
  const [isChannelLoaded, setIsChannelLoaded] = useState(isChannelVisible);

  useEffect(() => {
    !isChannelLoaded && setIsChannelLoaded(isChannelVisible);
  }, [isChannelLoaded, isChannelVisible, setIsChannelLoaded]);

  return { isChannelLoaded };
};
