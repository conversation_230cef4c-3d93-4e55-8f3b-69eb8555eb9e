import { useMemo } from 'react';
import { millisecondsToSeconds } from 'date-fns';

import { getDateEnd } from 'utils/dateUtils';
import { ProgramId } from 'services/api/common/types';
import { useEpgTimeContext } from 'features/Program/EpgControls/context';
import { useRecordingsByDateQuery } from 'services/api/newApi/optional/npvr';

export const useChannelRecording = (currentProgramId?: ProgramId) => {
  const { selectedDateTime } = useEpgTimeContext();

  const { data: recordings } = useRecordingsByDateQuery({
    startDate: millisecondsToSeconds(selectedDateTime.getTime()).toString(),
    endDate: millisecondsToSeconds(
      getDateEnd(selectedDateTime).getTime(),
    ).toString(),
  });

  const isRecordingNow: boolean = useMemo((): boolean => {
    return recordings
      ? recordings.recordingLiteList.some((recordingProgram) => {
          return recordingProgram.programExtId === currentProgramId;
        })
      : false;
  }, [currentProgramId, recordings]);

  return { isRecordingNow };
};
