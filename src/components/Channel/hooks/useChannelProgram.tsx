import { useCallback, useEffect, useMemo, useState } from 'react';
import { millisecondsToSeconds, secondsToMilliseconds } from 'date-fns';

import { useDailyEpg } from 'routes/Program/hook';
import { TimeInMilliseconds, TimeInSeconds } from 'services/api/common/types';
import { ScheduleProgram } from 'services/api/newApi/core/tvguide';
import { decodeTimestamp } from 'utils/dateUtils';
import { ChannelWithPosition } from 'features/Program/planby';
import { Channel } from 'services/api/newApi/live/channels';

export const useChannelProgram = (channel: Channel | ChannelWithPosition) => {
  const [currentProgram, setCurrentProgram] = useState<ScheduleProgram>();
  const { epg } = useDailyEpg();
  const [timeToNextProgram, setTimeToNextProgram] =
    useState<TimeInMilliseconds>(0);

  const channelSchedule = useMemo(() => {
    return epg?.guide.find(
      (schedule) => schedule.channelExtId === channel.channelExtId,
    )?.programs;
  }, [channel, epg]);

  const loadCurrentProgram = useCallback(() => {
    const currentTime: TimeInSeconds = millisecondsToSeconds(Date.now());
    setCurrentProgram(
      channelSchedule?.find(
        (scheduleProgram) =>
          scheduleProgram.startTimeUtc <= currentTime &&
          scheduleProgram.endTimeUtc > currentTime,
      ),
    );
  }, [channelSchedule]);

  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime: TimeInSeconds = millisecondsToSeconds(Date.now());
      const timeToProgramEnd: TimeInSeconds = currentProgram
        ? currentProgram?.endTimeUtc - currentTime
        : 0;
      setTimeToNextProgram(secondsToMilliseconds(timeToProgramEnd));
      loadCurrentProgram();
    }, timeToNextProgram);

    return () => {
      clearInterval(interval);
    };
  }, [currentProgram, loadCurrentProgram, timeToNextProgram]);

  const programTimeRange = useMemo(() => {
    if (currentProgram) {
      const programStartDate = decodeTimestamp(
        secondsToMilliseconds(currentProgram?.startTimeUtc),
      );
      const programEndDate = decodeTimestamp(
        secondsToMilliseconds(currentProgram?.endTimeUtc),
      );
      return `${programStartDate.time} - ${programEndDate.time}`;
    }
    return '';
  }, [currentProgram]);

  const isStartoverAvailable = useMemo(() => {
    if (currentProgram) {
      const { programExtId } = currentProgram;
      const startOverDisabled = currentProgram.properties?.startOverDisabled;

      if (
        !startOverDisabled &&
        channel.playFeatures.otg.isStartOver &&
        programExtId
      ) {
        return true;
      }
    }
    return false;
  }, [channel.playFeatures.otg.isStartOver, currentProgram]);

  const isBlackout = useMemo(() => {
    if (currentProgram) {
      const { programExtId, properties } = currentProgram;
      if (properties?.tveStreamDisabled && programExtId) {
        return true;
      }
    }
    return false;
  }, [currentProgram]);

  return { currentProgram, programTimeRange, isStartoverAvailable, isBlackout };
};
