import { useCallback, useMemo } from 'react';

import { ChannelId } from 'services/api/common/types';
import {
  useDeleteMyListMutation,
  useMyListMutation,
  useMyListQuery,
} from 'services/api/oldApi/myzone';

export const useChannelFavorite = (
  channelExtId: ChannelId,
  channelNumber: number,
) => {
  const { data: myList } = useMyListQuery();
  const { mutate: addChannel } = useMyListMutation();
  const { mutate: deleteChannel } = useDeleteMyListMutation();

  const isFavorite = useMemo(() => {
    return Boolean(
      myList?.channelList?.find(
        (myListChannel) => myListChannel.channelExternalId === channelExtId,
      ),
    );
  }, [channelExtId, myList?.channelList]);

  const toggleFavorite = useCallback(() => {
    if (isFavorite) {
      deleteChannel(channelExtId);
    } else {
      addChannel({ channelExternalId: channelExtId, channelNumber });
    }
  }, [addChannel, channelExtId, channelNumber, deleteChannel, isFavorite]);

  return { isFavorite, toggleFavorite };
};
