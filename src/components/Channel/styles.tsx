import styled from 'styled-components';

import { blink } from 'theme/animations';
import { IconButton } from 'components/Buttons/IconButton';
import { convertHexToRgbaString } from 'theme/colors';

export interface TitleWrapperProps {
  $backgroundImage: string;
}

export const TileWrapper = styled.div<TitleWrapperProps>`
  background: ${({ theme }) => theme.colors.channelBackground};
  border-radius: 1.2rem;
  padding: 0.8rem;
  width: 19.6rem;
  height: 14rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
  transition: 0s;
  background-image: url(${({ $backgroundImage }) => $backgroundImage});

  background-size: cover;
  background-position: center;

  & > div > div > img {
    width: auto;
  }

  &:hover {
    outline: 4px solid ${({ theme }) => theme.colors.primary};

    & > div > div > img {
      opacity: 0;
    }
    &:before {
      content: '';
      background-color: ${({ theme }) =>
        convertHexToRgbaString(theme.colors.black, 0.33)};
      position: absolute;
      border-radius: 1.2rem;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
    }
  }
`;

export const CenterWrapper = styled.div`
  position: absolute;
  top: 50%;
  height: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
`;

export const ChannelControls = styled.div`
  width: 10.4rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const ProgramTime = styled.div`
  top: 6.4rem;
  position: absolute;
  white-space: nowrap;
  text-align: center;
`;

export const ChannelWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 19.6rem;
`;

export const IconsTop = styled.div`
  width: 100%;
  height: 2.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const RecordingNowDot = styled.span`
  width: 1.6rem;
  height: 1.6rem;
  background-color: ${({ theme }) => theme.colors.red};
  border-radius: 50%;
  animation: ${blink} 1.6s linear infinite;
`;

export const IconsBottom = styled.div`
  width: 100%;
  height: 2.2rem;
  display: flex;
  justify-content: left;
  align-items: end;
`;

export const ChannelName = styled.div`
  height: 3rem;
  & span {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;

export const ProgramBlackout = styled.div`
  & span {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;

export const ProgramName = styled(ProgramBlackout)`
  & span {
    opacity: 0.5;
  }
`;

export const StarWrapper = styled.div`
  cursor: pointer;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;

  &:hover {
    svg {
      & > path:first-child {
        fill: ${({ theme }) => theme.colors.primary};
      }
    }
  }
`;

export const FavoriteButtonContainer = styled.div`
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
`;

export const ChannelDetails = styled.div`
  margin-top: 6px;
  max-width: 19.6rem;
`;

export const ChannelButton = styled(IconButton)`
  background-color: ${({ theme }) => theme.colors.white};
  filter: ${({ disabled }) => (disabled ? 'opacity(50%)' : '')};
  border-radius: 50%;
  height: 4rem;
  width: 4rem;
  transition: 0.5s;
  display: flex;
  justify-content: center;
  align-items: center;

  &:only-child {
    margin: 0 auto;
  }

  svg {
    & > path {
      fill: ${({ theme }) => theme.colors.tertiary};
    }
  }

  &:hover {
    svg {
      & > path {
        fill: ${({ theme }) => theme.colors.primary};
      }
    }
  }
`;

ChannelWrapper.displayName = 'ChannelWrapper';
TileWrapper.displayName = 'TileWrapper';
IconsTop.displayName = 'TopWrapper';
IconsBottom.displayName = 'BottomWrapper';
ChannelName.displayName = 'ChannelName';
ProgramName.displayName = 'ProgramName';
