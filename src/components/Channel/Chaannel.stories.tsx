import { Story, Meta } from '@storybook/react';

import { worker } from 'services/api/mock/mock.worker';
import { epgMockHandlers } from 'services/api/newApi/core/tvguide/mocks/epg.mock';

import { Channel } from './Channel';

const dummyChannel = {
  channelExtId: '14135',
  name: 'TVP 1 HD',
  logoSignature: 'L2F0dGFjaG1lbnRzLzE0MTM1LUxPR08ucG5n',
  isHomeZoneRestricted: true,
  isSubscribed: true,
};

export default {
  title: 'Components/Channel',
  component: Channel,
  decorators: [
    (Story) => {
      worker.use(...epgMockHandlers);
      return <Story />;
    },
  ],
} as Meta;

const Template: Story = (dummyChannel) => <Channel channel={dummyChannel} />;

export const Default = Template.bind({});
Default.args = {
  ...dummyChannel,
};
