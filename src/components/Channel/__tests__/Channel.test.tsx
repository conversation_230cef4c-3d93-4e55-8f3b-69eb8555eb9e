import { render, waitFor } from 'utils/testing';

import { Channel } from '../Channel';

const dummyChannel = {
  channelExtId: '14135',
  name: 'TVP1',
  channelNumber: 1,
  extraNumbers: [],
  category: 'Ogólne',
  logoSignature: 'L2F0dGFjaG1lbnRzLzE0MTM1LUxPR08ucG5n',
  logoImageId: 'cf76c8f2612158e1920e728765223e1f',
  logoUrl:
    'https://tvgo.ppd.orange.pl/mnapi/btm-009/cf76c8f2612158e1920e728765223e1f',
  catchupDuration: 604800,
  is4K: false,
  isHomeZoneRestricted: true,
  isPayPerView: false,
  isRegionalTv: false,
  isRecordingAllowed: false,
  isInteractive: false,
  openWindowId: 'test',
  dai: 'test',
  isAdult: false,
  isSubscribed: true,
  frameUrl:
    'https://redir.cache.orange.pl/otv/OTF/0/0/0/0/0/pool05/bpk-tv/hz_14135/DASH/dash/thumbnails/TVP1HD-thumbnail-[time].jpeg',
  playFeatures: {
    otg: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    conTv: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    stb: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    boxless: {
      isCatchUp: false,
      isChannelAllowed: false,
      isFastForwardBlocked: false,
      isNpvr: false,
      isStartOver: false,
    },
  },
  multicast: {
    ip: '***********',
    port: 5500,
    originalNetworkId: '1',
    transportStreamId: '241',
    serviceId: '340',
    columnFecPort: 0,
    fecEnabled: false,
    protocol: '',
    rowFecPort: 0,
  },
};

describe('Components:Channel', () => {
  it('should render Channel with name', () => {
    const { getByTestId } = render(<Channel channel={dummyChannel} />);
    waitFor(() =>
      expect(getByTestId('Channel-ChannelWrapper')).toBeInTheDocument(),
    );
  });
});
