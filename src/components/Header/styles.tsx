import styled from 'styled-components';
import { motion } from 'framer-motion';

import { devices, RenderLayer } from 'theme';
import { Text } from 'components/Typography';

export const Container = styled(motion.div)`
  position: fixed;
  width: 100%;
  height: 6.4rem;
  top: 0;
  left: 0;
  background: ${({ theme }) => theme.colors.secondary};
  align-items: center;
  padding: 0 1.8rem;
  justify-content: space-between;
  border-bottom: 0.1rem solid ${({ theme }) => theme.colors.doveGray};

  ${RenderLayer('header')};

  @media ${devices.tabletS} {
    padding: 0 3.2rem;
  }
`;

export const MainContentContainer = styled.div`
  display: flex;
  height: 100%;
  width: 50%;

  @media ${devices.mobileM} {
    flex-grow: 1;
  }
`;

export const RightColumnContainer = styled.div`
  display: inline-flex;
  align-items: center;
`;

export const StyledText = styled(Text)`
  font-weight: bold;
`;

export const ClosedSearchWrapper = styled.div`
  margin-right: 3.2rem;
`;

Container.displayName = 'HeaderContainer';
MainContentContainer.displayName = 'HeaderMainContentContainer';
RightColumnContainer.displayName = 'HeaderRightColumnContainer';
StyledText.displayName = 'HeaderStyledText';
ClosedSearchWrapper.displayName = 'ClosedSearchWrapper';
