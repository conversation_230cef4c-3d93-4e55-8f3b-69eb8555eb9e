import { FC, useCallback } from 'react';
import { Link } from 'react-router-dom';

import { Search } from 'features/Search';
import { IconSearch, IconSettings } from 'components/Icons';
import { Navigation } from 'containers/Navigation';
import { IconButton } from 'components/Buttons/IconButton';
import { routes } from 'routes/routes-map';
import { usePlayerPlayback } from 'features/Player/Context';
import { useAppLayoutMode } from 'services/appLayoutMode';

import * as S from './styles';
import { animate } from './constant';

export const Header: FC = () => {
  const { reset: resetPlayback } = usePlayerPlayback();
  const { isHeaderVisible, isSearchOpen, setIsSearchOpen } = useAppLayoutMode();

  const handleSettingLink = useCallback(() => resetPlayback(), [resetPlayback]);
  const toggleIsSearchOpen = useCallback(() => {
    return setIsSearchOpen((prev) => !prev);
  }, [setIsSearchOpen]);

  if (isSearchOpen) {
    return (
      <S.Container>
        <Search />;
      </S.Container>
    );
  }

  return (
    <>
      <S.Container {...animate(isHeaderVisible)}>
        <S.MainContentContainer>
          <Navigation />
        </S.MainContentContainer>
        <S.ClosedSearchWrapper>
          <IconButton
            onClick={toggleIsSearchOpen}
            data-testid='Search-CloseIconBtn'
          >
            <IconSearch />
          </IconButton>
        </S.ClosedSearchWrapper>
        <S.RightColumnContainer>
          <>
            <Link to={routes.settings} onClick={handleSettingLink}>
              <IconButton>
                <IconSettings />
              </IconButton>
            </Link>
          </>
        </S.RightColumnContainer>
      </S.Container>
    </>
  );
};
