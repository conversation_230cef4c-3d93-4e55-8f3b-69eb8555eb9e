import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import { ModalConfirmation } from '../ModalConfirmation';

describe('Components:ModalConfirmation', () => {
  const dummyProps = {
    modalTitle: '<PERSON><PERSON><PERSON><PERSON><PERSON>wy tytuł',
    buttonSubmitText: 'Zapisz',
    modalDescription: 'Opis',
    onSubmit: () => {},
  };

  it('should render ModalConfirmation component with appropriate props', () => {
    render(<ModalConfirmation isOpen onClose={() => ({})} {...dummyProps} />);

    expect(screen.getByText('Przykładowy tytuł')).toBeInTheDocument();
    expect(screen.getByText('Zapisz')).toBeInTheDocument();
    expect(screen.getByText('Opis')).toBeInTheDocument();
  });
});
