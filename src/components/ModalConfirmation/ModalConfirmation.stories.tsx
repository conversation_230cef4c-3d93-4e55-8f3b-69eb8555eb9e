import { Story, Meta } from '@storybook/react';
import { useArgs } from '@storybook/client-api';

import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { Text } from 'components/Typography';

import { ModalConfirmation } from './ModalConfirmation';

export default {
  title: 'Components/ModalConfirmation',
  component: ModalConfirmation,
  argTypes: {
    clickOnBackdrop: { action: 'clickOnBackdrop' },
  },
} as Meta;

const Template: Story = ({
  modalTitle,
  modalDesc,
  onSubmit,
  buttonSubmitText,
  onDenied,
  buttonDeniedText,
  children,
  ...args
}) => {
  const [{ isOpen }, updateArgs] = useArgs();
  const handleClose = () => updateArgs({ isOpen: !isOpen });

  return (
    <div>
      <PrimaryButton
        variant='ghostWhite'
        onClick={() => updateArgs({ isOpen: !isOpen })}
      >
        <Text> Open Modal</Text>
      </PrimaryButton>
      <ModalConfirmation
        isOpen={isOpen}
        onClose={handleClose}
        modalTitle={modalTitle}
        modalDesc={modalDesc}
        onSubmit={onSubmit}
        buttonSubmitText={buttonSubmitText}
        onDenied={onDenied}
        buttonDeniedText={buttonDeniedText}
        children={children}
      />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  onClose: () => ({}),
  isOpen: true,
  modalTitle: 'Przykładowy tytuł',
  modalDesc: 'Super opis...',
  onSubmit: () => {},
  buttonSubmitText: 'Zapisz',
  onDenied: () => {},
  buttonDeniedText: 'Anuluj',
};

export const WithTitleOnly = Template.bind({});
WithTitleOnly.args = {
  onClose: () => ({}),
  isOpen: true,
  modalTitle: 'Przykładowy tytuł',
  onSubmit: true,
  buttonSubmitText: 'Zapisz',
  onDenied: true,
  buttonDeniedText: 'Anuluj',
};

export const WithDescOnly = Template.bind({});
WithDescOnly.args = {
  onClose: () => ({}),
  isOpen: true,
  modalDesc: 'Super opis...',
  onSubmit: true,
  buttonSubmitText: 'Zapisz',
  onDenied: true,
  buttonDeniedText: 'Anuluj',
};

export const WithOneButton = Template.bind({});
WithOneButton.args = {
  onClose: () => ({}),
  isOpen: true,
  modalTitle: 'Przykładowy tytuł',
  modalDesc: 'Super opis...',
  onSubmit: true,
  buttonSubmitText: 'Zapisz',
};

export const WithChildren = Template.bind({});
WithChildren.args = {
  onClose: () => ({}),
  isOpen: true,
  modalTitle: 'Przykładowy tytuł',
  onSubmit: true,
  buttonSubmitText: 'Zapisz',
  children: (
    <div>
      <input value='Type name here...' />
    </div>
  ),
};
