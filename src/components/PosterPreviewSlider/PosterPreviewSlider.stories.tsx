import { Story, Meta } from '@storybook/react';

import { PosterPreviewSlider } from './PosterPreviewSlider';

export default {
  title: 'Components/PosterPreviewSlider',
  component: PosterPreviewSlider,
} as Meta;

const renderItems = () => {
  return Array.from({ length: 10 }, (x, i) => {
    return <img alt='test' src='https://www.fillmurray.com/240/300' />;
  });
};

const Template: Story = ({ title, isFetching, slidesInViewport }) => (
  <PosterPreviewSlider
    title={title}
    isFetching={isFetching}
    slidesInViewport={slidesInViewport}
  >
    {renderItems()}
  </PosterPreviewSlider>
);

export const Default = Template.bind({});
Default.args = {
  title: { id: 'home.rentedMovies', defaultMessage: 'Wypożyczone filmy' },
  isFetching: false,
  slidesInViewport: 5,
};
