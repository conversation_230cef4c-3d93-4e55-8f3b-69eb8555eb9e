import { useIntl } from 'react-intl';

import { H2 } from 'components/Typography';
import { Loader } from 'components/Loader';
import { Slider } from 'components/Slider';
import { SliderWithControl } from 'components/SliderWithControl';

import { PosterPreviewSliderProps } from './types';
import * as S from './styles';

export const PosterPreviewSlider = ({
  children,
  isFetching,
  renderHeader,
  slidesGap = 16,
  slidesInViewport = 1,
  title,
  sliderOptions,
  ...rest
}: PosterPreviewSliderProps) => {
  const intl = useIntl();

  return (
    <S.Container>
      {title ? (
        <H2>{intl.formatMessage(title)}</H2>
      ) : (
        renderHeader && renderHeader()
      )}
      {!isFetching ? (
        <>
          {sliderOptions ? (
            <SliderWithControl
              renderOptions={sliderOptions}
              withButtons
              {...rest}
            >
              {children}
            </SliderWithControl>
          ) : (
            <Slider withButtons {...rest}>
              {children}
            </Slider>
          )}
        </>
      ) : (
        <Loader />
      )}
    </S.Container>
  );
};
