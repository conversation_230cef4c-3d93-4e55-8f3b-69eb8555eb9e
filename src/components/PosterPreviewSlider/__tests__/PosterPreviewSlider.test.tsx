import { render } from 'utils/testing';

import { PosterPreviewSlider } from '../PosterPreviewSlider';

const testTitle = {
  id: 'test',
  defaultMessage: 'Test Message',
};

const testSlidesArray = [<div key='testSlide'>Test slide</div>];

describe('Components:PosterPreviewSlider', () => {
  const setupSlider = (
    isFetching: boolean,
    title?: {
      id: string;
      defaultMessage: string;
    },
    renderHeader?: undefined | (() => JSX.Element),
  ) =>
    render(
      <PosterPreviewSlider
        isFetching={false}
        title={title}
        renderHeader={renderHeader}
      >
        {testSlidesArray}
      </PosterPreviewSlider>,
    );

  it('should render PosterPreviewSlider component with title', () => {
    const { getByText } = setupSlider(false, testTitle);
    expect(getByText('Test Message')).toBeInTheDocument();
  });

  it('should render PosterPreviewSlider component with custom header', () => {
    const { getByText } = setupSlider(true, undefined, () => (
      <h2>Test Render Header</h2>
    ));
    expect(getByText('Test Render Header')).toBeInTheDocument();
  });

  it('should render Slider component while isFetching is false', () => {
    const { getByText } = setupSlider(false);
    expect(getByText('Test slide')).toBeInTheDocument();
  });
});
