import styled from 'styled-components';

export const RawButton = styled.button.attrs(() => ({
  type: 'button',
}))`
  background-color: ${({ theme }) => theme.colors.transparent};
  border: 0;
  color: ${({ theme }) => theme.colors.white};
  cursor: pointer;
  margin: 0;
  outline: 0;
  padding: 0;

  &:active,
  &:focus {
    outline: 0;
  }

  &:disabled {
    cursor: not-allowed;

    & > * {
      color: ${({ theme }) => theme.colors.white50} !important;
    }
  }
`;
