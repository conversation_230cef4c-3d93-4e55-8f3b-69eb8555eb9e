import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { RawButton } from 'components/Buttons/RawButton';

describe('Components:Commons:Buttons:IconButton', () => {
  it('should render IconButton component with default variant', () => {
    render(<RawButton onClick={() => ({})}>Test</RawButton>);

    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
