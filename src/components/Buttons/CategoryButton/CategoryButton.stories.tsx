import { Story, Meta } from '@storybook/react';

import { CategoryButton } from './CategoryButton';

export default {
  title: 'Components/Buttons/CategoryButton',
  component: CategoryButton,
} as Meta;
const Template: Story = ({ onClick }) => (
  <CategoryButton onClick={onClick} category='category'>
    <p>click</p>
  </CategoryButton>
);

export const Default = Template.bind({});
Default.args = {
  onClick: () => {},
};
