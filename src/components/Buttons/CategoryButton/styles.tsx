import styled from 'styled-components';

import { CategoryButtonProps } from './types';

export const Button = styled.button<{ $active: CategoryButtonProps['active'] }>`
  height: 3.8rem;
  box-sizing: border-box;
  border-radius: 50px;
  padding: 0.5rem 2rem;
  outline: none;
  position: relative;
  font-weight: bold;
  border: 0.2rem solid transparent;
  background-color: ${({ $active, theme }) =>
    $active ? theme.colors.primary : theme.colors.tertiaryLight};

  &:hover {
    cursor: pointer;
    border: 0.2rem solid ${({ theme }) => theme.colors.primary};
  }
`;
