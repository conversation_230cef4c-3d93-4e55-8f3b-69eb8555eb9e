import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { themeColorsDefault, colors } from 'theme/colors';

import { IconButton } from 'components/Buttons/IconButton';

describe('Components:Commons:Buttons:IconButton', () => {
  it('should render IconButton component with default variant', () => {
    render(<IconButton onClick={() => ({})}>Test</IconButton>);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveStyle(`background: rgba(0,0,0,0)`);
  });

  it("should render disabled IconButton component with type 'button'", () => {
    render(
      <IconButton onClick={() => ({})} type='button'>
        Test
      </IconButton>,
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveAttribute('type', 'button');
  });

  it('should render PrimaryButton component with inCircle variant', () => {
    render(
      <IconButton onClick={() => ({})} type='button' variant='inCircle'>
        Test
      </IconButton>,
    );

    expect(screen.getByRole('button')).toHaveStyle(
      `background: ${themeColorsDefault.primary}`,
    );
  });

  it('should run callback function properly when PrimaryButton component is clicked', async () => {
    const mock = vi.fn();

    render(<IconButton onClick={mock}>Test</IconButton>);
    await userEvent.click(screen.getByRole('button'));

    expect(mock).toHaveBeenCalled();
  });
});
