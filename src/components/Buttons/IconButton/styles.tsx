import styled, { css, DefaultTheme } from 'styled-components';

import { IconButtonProps } from './types';

export const buttonVariants = (theme: DefaultTheme) => ({
  default: {
    'background-color': theme.colors.transparent,
    'border': 'none',
    'border-radius': 'none',
  },
  inCircle: {
    'background-color': theme.colors.primary,
    'border-radius': '50%',
    'padding': '1rem 1.1rem',
    'cursor': 'pointer',
  },
});

export const Button = styled.button<{
  $variant: IconButtonProps['variant'];
  $iconColorHover: IconButtonProps['iconColorHover'];
  $disabled?: boolean;
}>`
  border: none;
  cursor: pointer;
  outline: none;
  position: relative;

  ${({ theme, $variant }) => css(buttonVariants(theme)[$variant || 'default'])}

  &:hover {
    svg {
      & > path {
        fill: ${({ theme, $iconColorHover }) =>
          $iconColorHover ? $iconColorHover : theme.colors.primary};
        transition: fill 0.3s linear;
      }
    }
  }

  ${({ disabled }) =>
    disabled &&
    css`
      pointer-events: none;

      svg {
        & > path {
          fill: ${({ theme }) => theme.colors.doveGray};
        }
      }
    `}
`;
