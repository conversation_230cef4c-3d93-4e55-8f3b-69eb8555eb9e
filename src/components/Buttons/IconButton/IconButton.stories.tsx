import { Story, Meta } from '@storybook/react';
import { IconSearch } from 'components/Icons';

import { IconButton } from './IconButton';

export default {
  title: 'Components/Buttons/IconButton',
  component: IconButton,
} as Meta;

const Template: Story = (args) => (
  <IconButton onClick={() => ({})} {...args}>
    <IconSearch />
  </IconButton>
);

export const Default = Template.bind({});

export const inCircle = Template.bind({});

inCircle.args = {
  variant: 'inCircle',
};
