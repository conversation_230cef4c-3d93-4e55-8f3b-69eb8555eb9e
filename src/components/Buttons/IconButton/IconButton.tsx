import { IconButtonProps } from './types';
import * as S from './styles';

export function IconButton({
  children,
  onClick,
  type,
  autoFocus,
  variant = 'default',
  iconColorHover,
  disabled,
  ...rest
}: IconButtonProps): JSX.Element {
  return (
    <S.Button
      autoFocus={autoFocus}
      onClick={onClick}
      type={type}
      $variant={variant}
      $iconColorHover={iconColorHover}
      disabled={disabled}
      {...rest}
    >
      {children}
    </S.Button>
  );
}
