import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { DescribedIconButton } from 'components/Buttons/DescribedIconButton';

describe('Components:Commons:Buttons:DescribedIconButton', () => {
  it('should render DescribedIconButton component with text', () => {
    const textValue = 'test value';
    render(
      <DescribedIconButton onClick={() => ({})} text={textValue}>
        child
      </DescribedIconButton>,
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByTestId('text-container')).toHaveTextContent(textValue);
  });

  it('should run callback function properly when PrimaryButton component is clicked', async () => {
    const mock = vi.fn();
    render(
      <DescribedIconButton onClick={mock} text='Default'>
        child
      </DescribedIconButton>,
    );
    await userEvent.click(screen.getByRole('button'));
    expect(mock).toHaveBeenCalled();
  });
});
