import { Story, Meta } from '@storybook/react';
import { IconGrid } from 'components/Icons';

import { DescribedIconButton } from './DescribedIconButton';

export default {
  title: 'Components/Buttons/DescribedIconButton',
  component: DescribedIconButton,
} as Meta;
const Template: Story = ({ text, circleColor, ...args }) => (
  <DescribedIconButton
    onClick={() => ({})}
    text={text}
    circleColor={circleColor}
    {...args}
  >
    <IconGrid color='#000000' />
  </DescribedIconButton>
);

export const Default = Template.bind({});
Default.args = {
  text: 'Default',
  circleColor: '#3333cc',
};
