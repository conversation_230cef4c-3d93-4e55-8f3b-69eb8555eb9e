import { Text } from 'components/Typography';
import { IconButton } from 'components/Buttons/IconButton';

import { DescribedIconButtonProps } from './types';
import * as S from './styles';

export const DescribedIconButton = ({
  children,
  text,
  circleColor,
  iconColorHover,
  circleColorHover,
  onClick,
  ...rest
}: DescribedIconButtonProps) => {
  return (
    <S.Container>
      <IconButton onClick={onClick} iconColorHover={iconColorHover} {...rest}>
        <S.IconWrapper bgColor={circleColor} hoverColor={circleColorHover}>
          {children}
        </S.IconWrapper>
      </IconButton>
      <Text $primary $sizeXXSmall data-testid='text-container'>
        {text}
      </Text>
    </S.Container>
  );
};
