import styled from 'styled-components';

import { IconWrapperProps } from './types';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: fit-content;

  button {
    visibility: visible;
  }
`;

export const IconWrapper = styled.div<IconWrapperProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6.4rem;
  height: 6.4rem;
  border-radius: 50%;
  margin-bottom: 1.2rem;
  background-color: ${({ bgColor }) =>
    bgColor ? bgColor : ({ theme }) => theme.colors.white};
  transition: background-color 0.3s linear;

  &:hover {
    background-color: ${({ hoverColor }) => hoverColor && hoverColor};
  }
`;

IconWrapper.displayName = 'DescribedIconButtonIconWrapper';
Container.displayName = 'DescribedIconButtonContainer';
