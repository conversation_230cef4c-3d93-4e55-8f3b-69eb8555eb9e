import styled from 'styled-components';

import { ButtonStyledProps, IconWrapperProps } from './types';

export const Button = styled.button<ButtonStyledProps>`
  width: ${({ $diameterRem }) => `${$diameterRem}rem`};
  height: ${({ $diameterRem }) => `${$diameterRem}rem`};
  background-color: ${({ theme }) => theme.colors.mineShaft};
  border-radius: 50%;
  border: none;
  outline: none;
  border: 0.3rem solid ${({ theme }) => theme.colors.white};

  &:hover {
    cursor: pointer;
    background-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    opacity: 0.3;
    background-color: ${({ theme }) => theme.colors.mineShaft};
  }
`;
export const IconWrapper = styled.div<{
  $transformX: IconWrapperProps['transformX'];
}>`
  transform: ${({ $transformX }) => `translateX(${$transformX}rem)`};
`;

Button.displayName = 'ArrowButton.Button';
IconWrapper.displayName = 'ArrowButton.IconWrapper';
