import { Story, Meta } from '@storybook/react';
import React from 'react';

import { ArrowButton } from './ArrowButton';

export default {
  title: 'Components/Buttons/ArrowButton',
  component: ArrowButton,
  argTypes: {
    direction: {
      options: ['left', 'right'],
      control: { type: 'radio' },
    },
  },
} as Meta;
const Template: Story = ({ onClick, direction, diameterRem }) => (
  <ArrowButton
    onClick={onClick}
    direction={direction}
    diameterRem={diameterRem}
  />
);

export const Default = Template.bind({});
Default.args = {
  onClick: () => {},
  direction: 'right',
  diameterRem: 4.8,
};
