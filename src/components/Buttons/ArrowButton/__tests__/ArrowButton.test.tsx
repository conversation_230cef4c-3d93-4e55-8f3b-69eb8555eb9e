import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { ArrowButton } from '../ArrowButton';

describe('Components:ArrowButton', () => {
  it('should render CurrentDatetime component', () => {
    render(<ArrowButton direction='right' />);
    expect(screen.getByTestId('ArrowButton'));
  });
  it('should call callback onClick', () => {
    const mockFn = vi.fn();
    render(<ArrowButton direction='right' onClick={mockFn} />);
    fireEvent.click(screen.getByTestId('ArrowButton'));
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
  it('should render correct direction arrow right', () => {
    render(<ArrowButton direction='right' />);
    expect(screen.getByTestId('IconArrowRight'));
  });
  it('should render correct direction arrow left', () => {
    render(<ArrowButton direction='left' />);
    expect(screen.getByTestId('IconArrowLeft'));
  });
});
