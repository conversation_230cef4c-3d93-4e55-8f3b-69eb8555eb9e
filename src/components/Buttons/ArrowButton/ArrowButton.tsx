import { useMemo } from 'react';

import { IconArrowLeft, IconArrowRight } from 'components/Icons';

import * as S from './styles';
import { CategoryButtonProps } from './types';
import {
  ICON_PROPORTION_TO_DIAMETER,
  TRANSFORM_X_PROPORTION_TO_DIAMETER,
} from './constants';

export const ArrowButton = ({
  onClick,
  direction,
  diameterRem = 4.8,
  disabled = false,
  ...rest
}: CategoryButtonProps) => {
  // transform for keep center of mass for triangle icon (without it, it is looked as not centered in circle)
  const transformX = useMemo(() => {
    if (direction === 'right') {
      return diameterRem * TRANSFORM_X_PROPORTION_TO_DIAMETER;
    }
    if (direction === 'left') {
      return -diameterRem * TRANSFORM_X_PROPORTION_TO_DIAMETER;
    }
    return 0;
  }, [diameterRem, direction]);

  const renderedArrowIcon = useMemo(() => {
    if (direction === 'right') {
      return (
        <IconArrowRight scale={diameterRem * ICON_PROPORTION_TO_DIAMETER} />
      );
    }
    if (direction === 'left') {
      return (
        <IconArrowLeft scale={diameterRem * ICON_PROPORTION_TO_DIAMETER} />
      );
    }
    return null;
  }, [diameterRem, direction]);

  return (
    <S.Button
      data-testid='ArrowButton'
      onClick={onClick}
      disabled={disabled}
      $diameterRem={diameterRem}
      {...rest}
    >
      <S.IconWrapper $transformX={transformX}>
        {renderedArrowIcon}
      </S.IconWrapper>
    </S.Button>
  );
};
