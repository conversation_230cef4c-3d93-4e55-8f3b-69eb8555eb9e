import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { HamburgerButton } from 'components/Buttons/HamburgerButton';

describe('Components:Commons:Buttons:HamburgerButton', () => {
  it('should render IconButton component with default variant', () => {
    render(<HamburgerButton isOpen={true} onClick={() => ({})} />);

    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('should display text next to the button', () => {
    const text = 'Programy TV';
    render(<HamburgerButton isOpen={true} onClick={() => ({})} text={text} />);

    expect(screen.getByText(/Programy TV/i)).toBeInTheDocument();
  });
});
