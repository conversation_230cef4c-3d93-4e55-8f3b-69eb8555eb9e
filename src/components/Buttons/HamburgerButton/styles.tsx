import styled from 'styled-components';

import { convertHexToRgbaString } from 'theme/colors';

export const Button = styled.span<{ $isOpen: boolean }>`
  top: 50%;
  left: 0;
  width: 2.4rem;
  height: 0.2rem;
  background-color: ${({ $isOpen, theme }) =>
    $isOpen ? theme.colors.transparent : theme.colors.white};
  position: absolute;
  box-shadow: ${({ $isOpen, theme }) =>
    $isOpen
      ? `0 0.2rem 0.5rem ${theme.colors.transparent}`
      : `0 0.2rem 0.5rem ${convertHexToRgbaString(theme.colors.black, 0.2)}`};
  transition: 0.5s;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2.4rem;
    height: 0.2rem;
    background: ${({ theme }) => theme.colors.white};
    box-shadow: 0 0.2rem 0.5rem
      ${({ theme }) => convertHexToRgbaString(theme.colors.black, 0.2)};
    transition: 0.3s;
  }

  &::before {
    transform: ${({ $isOpen }) =>
      $isOpen
        ? 'translateY(0) rotate(45deg)'
        : 'translateY(-0.8rem) rotate(0)'};
  }

  &::after {
    transform: ${({ $isOpen }) =>
      $isOpen
        ? 'translateY(0) rotate(135deg)'
        : 'translateY(0.8rem) rotate(0)'};
    box-shadow: ${({ $isOpen, theme }) =>
      $isOpen &&
      `0 -0.2rem 0.5rem ${convertHexToRgbaString(theme.colors.black, 0.2)}`};
  }
  /* hover is in container */
`;

export const Container = styled.button<{ $isOpen: boolean }>`
  position: relative;
  display: inline-block;
  background-color: transparent;
  border: none;
  height: 3rem;
  cursor: pointer;
  width: 100%;

  &:hover > span,
  &:hover > span::before,
  &:hover > span::after {
    background-color: ${({ theme }) => theme.colors.primary};
  }

  &:hover > span {
    background-color: ${({ theme, $isOpen }) =>
      $isOpen && theme.colors.transparent};
  }
`;

export const HamburgerText = styled.p`
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.primary};
  ${({ theme }) => theme.mixins.baseTextSize};
  margin-left: 4rem;
  text-align: left;
  line-height: 3rem;
`;

Button.displayName = 'Button';
Container.displayName = 'Container';
HamburgerText.displayName = 'HeaderIconButtonWrapper';
