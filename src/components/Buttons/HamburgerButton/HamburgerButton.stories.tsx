import { Story, Meta } from '@storybook/react';
import { useArgs } from '@storybook/client-api';

import { HamburgerButton } from './HamburgerButton';

export default {
  title: 'Components/Buttons/HamburgerButton',
  component: HamburgerButton,
} as Meta;

const Template: Story = ({ ...args }) => {
  const [{ isOpen }, updateArgs] = useArgs();
  const handleClose = () => updateArgs({ isOpen: !isOpen });

  return (
    <div style={{ position: 'relative' }}>
      <HamburgerButton isOpen={isOpen} onClick={handleClose} {...args} />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  isOpen: false,
};

export const WithText = Template.bind({});
WithText.args = {
  isOpen: false,
  text: 'Program TV',
};
