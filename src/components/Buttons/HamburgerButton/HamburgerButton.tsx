import { FC, PropsWithChildren } from 'react';

import { HamburgerButtonProps } from './types';
import * as S from './styles';

export const HamburgerButton: FC<PropsWithChildren<HamburgerButtonProps>> = ({
  onClick,
  text,
  isOpen,
}) => {
  return (
    <S.Container
      data-testid='hamburger-button'
      onClick={onClick}
      $isOpen={isOpen}
    >
      <S.Button data-testid='hamburger-lines' $isOpen={isOpen}></S.Button>
      {text && <S.HamburgerText>{text}</S.HamburgerText>}
    </S.Container>
  );
};
