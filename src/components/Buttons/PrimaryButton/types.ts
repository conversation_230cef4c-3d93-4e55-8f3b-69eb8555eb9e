import { ReactNode } from 'react';

import { ButtonI } from '../types';

export interface PrimaryButtonProps extends ButtonI {
  children: ReactNode;
  variant?: 'default' | 'orange' | 'ghostWhite';
  disabled?: boolean;
  dataTestId?: string;
  width?: number;
  variants?: ButtonAnimationVariants;
  whileHover?: 'hover';
  whileTap?: 'tap';
}

type ButtonAnimationVariants = {
  hover: scaleType;
  tap: scaleType;
};
type scaleType = { scale: number };
