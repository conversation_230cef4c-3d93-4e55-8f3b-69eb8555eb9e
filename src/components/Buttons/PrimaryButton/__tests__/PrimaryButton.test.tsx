import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect } from 'vitest';

import { render } from 'utils/testing';

import { themeColorsDefault } from 'theme/colors';

import { PrimaryButton } from 'components/Buttons/PrimaryButton';

describe('Components:Commons:Buttons:PrimaryButton', () => {
  it('should render PrimaryButton component with default variant', () => {
    render(<PrimaryButton onClick={() => ({})}>Test</PrimaryButton>);

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveStyle(
      `background-color: ${themeColorsDefault.tertiary}`,
    );
  });

  it("should render disabled PrimaryButton component with type 'button'", () => {
    render(
      <PrimaryButton onClick={() => ({})} type='button' disabled>
        Test
      </PrimaryButton>,
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveAttribute('type', 'button');
    expect(screen.getByRole('button')).toHaveAttribute('disabled');
  });

  it('should render PrimaryButton component with orange variant', () => {
    render(
      <PrimaryButton onClick={() => ({})} type='button' variant='orange'>
        Test
      </PrimaryButton>,
    );

    expect(screen.getByRole('button')).toHaveStyle(
      `background-color: ${themeColorsDefault.primary}`,
    );
  });

  it('should run callback function properly when PrimaryButton component is clicked', async () => {
    const mock = vi.fn();

    render(<PrimaryButton onClick={mock}>Test</PrimaryButton>);
    await userEvent.click(screen.getByRole('button'));

    expect(mock).toHaveBeenCalled();
  });
});
