import { forwardRef } from 'react';

import * as S from './styles';
import { PrimaryButtonProps } from './types';

export const PrimaryButton = forwardRef<HTMLButtonElement, PrimaryButtonProps>(
  (
    {
      onClick,
      onMouseDown,
      onMouseUp,
      type,
      autoFocus,
      children,
      disabled = false,
      variant = 'default',
      dataTestId,
      width,
      ...rest
    },
    buttonRef,
  ) => {
    return (
      <S.Button
        ref={buttonRef}
        data-testid={dataTestId}
        onClick={onClick}
        onMouseDown={onMouseDown}
        onMouseUp={onMouseUp}
        type={type}
        autoFocus={autoFocus}
        $variant={variant}
        disabled={disabled}
        width={width}
        variants={S.buttonAnimationVariants}
        whileHover='hover'
        whileTap='tap'
        {...rest}
      >
        {children}
      </S.Button>
    );
  },
);
