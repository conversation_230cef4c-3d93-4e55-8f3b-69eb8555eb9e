import styled, { css, DefaultTheme } from 'styled-components';
import { motion } from 'framer-motion';

import { convertHexToRgbaString } from 'theme/colors';

import { PrimaryButtonProps } from './types';

export const buttonAnimationVariants = {
  hover: { scale: 1.05 },
  tap: { scale: 0.95 },
};
const buttonVariants = (theme: DefaultTheme) => ({
  default: {
    'background-color': theme.colors.tertiary,
  },
  orange: {
    'background-color': theme.colors.primary,
  },
  ghostWhite: {
    'background-color': theme.colors.transparent,
    'border': `0.4rem solid ${theme.colors.alabaster}`,
  },
});

const textVariants = (theme: DefaultTheme) => ({
  default: {},
  orange: {
    color: `${theme.colors.primaryOpposite}!important`,
  },
  ghostWhite: {},
});

export const Button = styled(motion.button)<{
  $variant: PrimaryButtonProps['variant'];
  width?: number;
}>`
  box-sizing: border-box;
  border-radius: 0.8rem;
  padding: 0.9rem 4rem;
  outline: none;
  position: relative;
  font-weight: bold;
  border: 0.1rem solid transparent;
  width: ${({ width }) => (width ? `${width}rem` : 'auto')};

  &:hover {
    cursor: pointer;
  }

  &:focus {
    border: ${({ theme, $variant }) =>
      $variant !== 'ghostWhite'
        ? css`0.1rem solid ${theme.colors.alabaster}`
        : css`0.5rem solid ${theme.colors.primary}`};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.doveGray};
    border: 0.1rem solid transparent;
    cursor: not-allowed;
    mix-blend-mode: screen;

    & > * {
      color: ${({ theme }) =>
        convertHexToRgbaString(theme.colors.black, 0.5)} !important;
    }
  }
  & > * {
    ${({ theme, $variant }) => css(textVariants(theme)[$variant || 'default'])}
  }
  ${({ theme, $variant }) => css(buttonVariants(theme)[$variant || 'default'])}
`;
