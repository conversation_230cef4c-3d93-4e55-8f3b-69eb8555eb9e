import { Story, Meta } from '@storybook/react';
import { Text } from 'components/Typography';

import { PrimaryButton } from './PrimaryButton';

export default {
  title: 'Components/Buttons/PrimaryButton',
  component: PrimaryButton,
  argTypes: {
    variant: {
      options: ['default', 'orange', 'ghostWhite'],
      control: { type: 'radio' },
    },
  },
} as Meta;

const Template: Story = (args) => (
  <PrimaryButton onClick={() => ({})} {...args}>
    <Text primary sizeMedium>
      Primary Button
    </Text>
  </PrimaryButton>
);

export const Default = Template.bind({});
Default.args = {
  variant: 'default',
};

export const AutoFocused = Template.bind({});
AutoFocused.args = {
  autoFocus: true,
  variant: 'default',
};

export const Orange = Template.bind({});
Orange.args = {
  variant: 'orange',
};

export const GhostWhite = Template.bind({});
GhostWhite.args = {
  variant: 'ghostWhite',
};

export const Disabled = Template.bind({});
Disabled.args = {
  disabled: true,
};
