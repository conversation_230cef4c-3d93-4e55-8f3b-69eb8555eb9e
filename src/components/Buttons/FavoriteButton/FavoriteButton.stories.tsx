import { Story, Meta } from '@storybook/react';

import { FavoriteButton } from './FavoriteButton';

export default {
  title: 'Components/Buttons/FavoriteButton',
  component: FavoriteButton,
  args: {
    isFavorite: true,
  },
} as Meta;

const Template: Story = ({ isFavorite }) => {
  return (
    <div style={{ position: 'relative', left: '20px', width: '40px' }}>
      <FavoriteButton isFavorite={isFavorite} handleClick={() => {}} />
    </div>
  );
};

export const Default = Template.bind({});
