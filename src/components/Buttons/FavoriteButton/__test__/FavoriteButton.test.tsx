import { act, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';
import { FavoriteButton } from '../FavoriteButton';

vi.mock('services/user', async (importOriginal) => {
  return {
    ...(await importOriginal<typeof import('services/user')>()),
    useUserProfile: () => ({
      activeProfile: {},
    }),
  };
});

describe('Components:Commons:Buttons:FavoriteButton', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render FavoriteButton', () => {
    render(<FavoriteButton handleClick={() => {}} isFavorite={false} />);

    expect(screen.getByTestId('IconStar')).toBeInTheDocument();
  });

  it('should run callback function properly when FavoriteButton is clicked', async () => {
    const mock = vi.fn();
    render(<FavoriteButton handleClick={mock} isFavorite={false} />);
    await act(async () => {
      await userEvent.click(screen.getByTestId('IconStar'));
    });
    expect(mock).toHaveBeenCalled();
  });

  it('Should render FavoriteButton filled', async () => {
    const { container } = render(
      <FavoriteButton handleClick={() => {}} isFavorite={true} />,
    );

    const pathElements = container.querySelectorAll('path');
    expect(pathElements[0]).toHaveAttribute('fill', '#ff6600');
    expect(pathElements[1]).toHaveAttribute('fill', '#ff6600');
  });
});
