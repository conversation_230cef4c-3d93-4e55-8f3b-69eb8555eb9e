import { IconStar } from 'components/Icons';

import { FavoriteStarProps } from './types';
import * as S from './styles';

export const FavoriteButton = ({
  handleClick,
  isFavorite,
  width = 24,
  height = 24,
  isVisible = true,
}: FavoriteStarProps) => {
  const shouldShowIcon = isFavorite || isVisible;

  if (shouldShowIcon) {
    return (
      <S.StarWrapper onClick={handleClick}>
        <IconStar isEmpty={!isFavorite} height={height} width={width} />
      </S.StarWrapper>
    );
  }

  return <></>;
};
