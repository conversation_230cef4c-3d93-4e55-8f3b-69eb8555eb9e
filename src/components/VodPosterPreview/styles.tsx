import styled from 'styled-components';

import { Text } from 'components/Typography';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  cursor: pointer;
  height: 100%;
`;

export const ImageContainer = styled.div`
  position: relative;
  height: 100%;
  min-height: 20rem;
`;

export const IconContainer = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  z-index: 1;
`;

export const TextContainer = styled.div`
  margin-top: 1.2rem;
  padding-left: 0.4rem;
  width: 100%;

  & > div > span {
    color: ${({ theme }) => theme.colors.white50};
  }
  & > span + span {
    color: ${({ theme }) => theme.colors.white50};
  }
`;

export const Title = styled(Text)`
  font-weight: bold;
  margin-bottom: 0.4rem;
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export const FavoriteButtonContainer = styled.div`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
`;

Container.displayName = 'PosterPreviewContainer';
TextContainer.displayName = 'PosterPreviewTextContainer';
Title.displayName = 'PosterPreviewTitle';
