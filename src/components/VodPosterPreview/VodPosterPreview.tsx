import { useIntl } from 'react-intl';
import { useMemo } from 'react';

import { Image } from 'components/Image';
import { RemainingTime } from 'components/RemainingTime';
import { Text } from 'components/Typography';
import { useHover } from 'hooks/useHover';
import { FavoriteButton } from 'components/Buttons/FavoriteButton/FavoriteButton';
import { useVodFavorite } from 'features/VOD/hooks/useVodFavorite';
import { useConfig } from 'services/config';

import { VodPosterPreviewProps } from './types';
import * as S from './styles';
import { messages } from './messages';

export const VodPosterPreview = ({
  title,
  src,
  id = '',
  photoRatio,
  remainingTime = '',
  isTicketActive = true,
}: VodPosterPreviewProps) => {
  const { formatMessage } = useIntl();
  const { isFavorite, toggleFavorite } = useVodFavorite(id);
  const { hover, handleMouseEnter, handleMouseLeave } = useHover();
  const { getTechConfig } = useConfig();
  const {
    vods: { isFavouriteActionVisible },
  } = getTechConfig();

  const favoriteButton = useMemo(() => {
    if (isFavouriteActionVisible && Boolean(id)) {
      return (
        <S.FavoriteButtonContainer>
          <FavoriteButton
            isFavorite={isFavorite}
            handleClick={toggleFavorite}
            isVisible={hover}
          />
        </S.FavoriteButtonContainer>
      );
    }

    return null;
  }, [hover, id, isFavorite, isFavouriteActionVisible, toggleFavorite]);

  const posterText = useMemo(() => {
    if (remainingTime.length > 0 && isTicketActive) {
      return <RemainingTime highlight expirationDate={remainingTime} />;
    }
    if (!isTicketActive) {
      return <Text $sizeXXSmall>{formatMessage(messages.available)}</Text>;
    }
    return null;
  }, [formatMessage, isTicketActive, remainingTime]);

  return (
    <S.Container>
      <S.ImageContainer
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {favoriteButton}
        <Image src={src} photoRatio={photoRatio} />
      </S.ImageContainer>
      <S.TextContainer>
        <S.Title $sizeSmall>{title}</S.Title>
        {posterText}
      </S.TextContainer>
    </S.Container>
  );
};
