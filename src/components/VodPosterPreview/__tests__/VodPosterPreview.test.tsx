import { screen, within } from '@testing-library/react';
import { render } from 'utils/testing';
import { vi } from 'vitest';

import { setupMockServer } from 'services/api/mock/mock.server';

import { VodPosterPreview } from '../VodPosterPreview';
import { messages } from '../messages';

describe('Components:VodPosterPreview', () => {
  const mockedDate = new Date(2022, 1, 22);
  vi.setSystemTime(mockedDate);

  setupMockServer();
  it('should render VodPosterPreview component', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
      />,
    );

    expect(screen.getByText('Vod test')).toBeInTheDocument();
  });

  it('should render VodPosterPreview component with RemainingTime for expiration today', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
        remainingTime='2h 8min'
        isTicketActive={true}
      />,
    );

    const element = screen.getByTestId('remaining-time');
    const textContent = within(element).getByText((content) =>
      content.includes('Dostępny do dzisiaj'),
    );
    expect(textContent).toBeInTheDocument();
  });

  it('should render VodPosterPreview component with RemainingTime for expiration within two days', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
        remainingTime='Dostępny do jutra 30h 0min'
        isTicketActive={true}
      />,
    );

    const element = screen.getByTestId('remaining-time');
    expect(element).toHaveTextContent('Dostępny do jutra');
  });

  it('should render VodPosterPreview component with RemainingTime for long-term expiration', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
        remainingTime='3d 5h 20min'
        isTicketActive={true}
      />,
    );

    const element = screen.getByTestId('remaining-time');
    expect(element).toHaveTextContent('Wygasa za');
    expect(element).toHaveTextContent('3d 5h 20min');
  });

  it('should render VodPosterPreview component with RemainingTime for expiration more than 24 hours', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
        remainingTime='Dostępny do jutra 22:10'
        isTicketActive={true}
      />,
    );

    const remainingTimeElement = screen.getByTestId('remaining-time');
    expect(remainingTimeElement).toHaveTextContent('Dostępny do jutra');
  });

  it('should render available VodPosterPreview with not active ticket', () => {
    render(
      <VodPosterPreview
        src='https://www.fillmurray.com/240/300'
        title='Vod test'
        isTicketActive={false}
      />,
    );

    expect(
      screen.getByText(messages.available.defaultMessage),
    ).toBeInTheDocument();
  });
});
