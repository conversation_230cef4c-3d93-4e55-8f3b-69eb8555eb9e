import { Story, Meta } from '@storybook/react';
import React from 'react';
import { VodPosterPreview } from './VodPosterPreview';

export default {
  title: 'Components/VodPosterPosterPreview',
  component: VodPosterPreview,
} as Meta;

const Template: Story = ({ src, title, remainingTime, isTicketActive }) => (
  <div style={{ width: '300px' }}>
    <VodPosterPreview
      src={src}
      title={title}
      remainingTime={remainingTime}
      isTicketActive={isTicketActive}
    />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  title: 'Default Title',
  src: 'https://www.fillmurray.com/240/300',
  remainingTime: '16h 23min',
  isTicketActive: true,
};

export const Restricted = Template.bind({});
Restricted.args = {
  title: 'Default Title',
  src: 'https://www.fillmurray.com/240/300',
};
