import { Story, Meta } from '@storybook/react';
import React, { useState } from 'react';

import { SearchBox } from './SearchBox';

export default {
  title: 'Components/SearchBox',
  component: SearchBox,
} as Meta;

const Template: Story = ({ isSearchOpen }) => {
  const [isOpen, setIsOpen] = useState<boolean>(isSearchOpen);
  const [searchValue, setSearchValue] = useState('');
  const toggleIsOpen = () => {
    setIsOpen((prev) => !prev);
  };

  return (
    <SearchBox
      searchValue={searchValue}
      setSearchValue={setSearchValue}
      toggleIsOpen={toggleIsOpen}
      isOpen={isOpen}
    />
  );
};

export const Default = Template.bind({});
