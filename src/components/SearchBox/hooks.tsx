import { ChangeEvent, useCallback, useEffect } from 'react';

import { useScrollLock } from 'hooks/useScrollLock';
import { KEY_CODES, useKeyPress } from 'hooks/useKeyPress';

export const useSearchBox = (
  isOpen: boolean,
  toggleIsOpen: () => void,
  setSearchValue: (searchValue: string) => void,
) => {
  const { lockScroll, unlockScroll } = useScrollLock();
  const isEscPressed = useKeyPress(KEY_CODES.ESCAPE);

  const clearSearchValue = useCallback(() => {
    setSearchValue('');
  }, [setSearchValue]);

  useEffect(() => {
    clearSearchValue();
    isOpen ? lockScroll() : unlockScroll();
  }, [isOpen, lockScroll, clearSearchValue, unlockScroll]);

  const handleSearchValueChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
    },
    [setSearchValue],
  );

  useEffect(() => {
    if (isOpen && isEscPressed) {
      toggleIsOpen();
    }
  }, [isEscPressed, isOpen, toggleIsOpen]);

  return {
    setSearchValue,
    handleSearchValueChange,
  };
};
