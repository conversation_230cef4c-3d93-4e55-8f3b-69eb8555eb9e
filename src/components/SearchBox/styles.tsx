import { motion } from 'framer-motion';
import styled from 'styled-components';

import { devices } from 'theme';

export const ClosedSearchWrapper = styled.div`
  margin-right: 3.2rem;
`;

export const SearchWrapper = styled(motion.div).attrs(() => ({
  initial: { translateX: 200 },
  animate: { translateX: 0 },
  transition: { duration: 0.15 },
}))`
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  height: 6.4rem;
`;

export const SearchInput = styled.input`
  border: none;
  background-image: none;
  background-color: transparent;
  box-shadow: none;
  outline: none;

  width: 34.4rem;
  padding: 0.6rem 3.8rem;
  color: ${({ theme }) => theme.colors.white80};
  font-size: 2.4rem;
  border-bottom: 0.2rem solid ${({ theme }) => theme.colors.tertiary};

  @media ${devices.mobileL} {
    width: 70.6rem;
  }
`;

export const SearchButtonWrapper = styled.div`
  transform: translateX(100%);
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CloseButtonWrapper = styled.div`
  transform: translateX(-100%);
  display: flex;
  align-items: center;
  justify-content: center;
`;

ClosedSearchWrapper.displayName = 'SearchClosedSearchWrapper';
SearchWrapper.displayName = 'SearchSearchWrapper';
SearchButtonWrapper.displayName = 'SearchSearchButtonWrapper';
CloseButtonWrapper.displayName = 'SearchCloseButtonWrapper';
