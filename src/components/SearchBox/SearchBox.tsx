import { IconClose, IconSearch } from 'components/Icons';
import { IconButton } from 'components/Buttons/IconButton';

import { SearchBoxProps } from './types';
import * as S from './styles';
import { useSearchBox } from './hooks';

export const SearchBox = ({
  isOpen,
  toggleIsOpen,
  searchValue,
  setSearchValue,
}: SearchBoxProps) => {
  const { handleSearchValueChange } = useSearchBox(
    isOpen,
    toggleIsOpen,
    setSearchValue,
  );

  if (isOpen)
    return (
      <S.SearchWrapper>
        <S.SearchButtonWrapper>
          <IconSearch />
        </S.SearchButtonWrapper>
        <S.SearchInput
          autoFocus
          value={searchValue}
          onChange={handleSearchValueChange}
          data-testid='SearchBox-Input'
        />
        <S.CloseButtonWrapper>
          <IconButton
            onClick={toggleIsOpen}
            data-testid='SearchBox-CloseSearchButton'
          >
            <IconClose />
          </IconButton>
        </S.CloseButtonWrapper>
      </S.SearchWrapper>
    );

  return (
    <S.ClosedSearchWrapper>
      <IconButton onClick={toggleIsOpen} data-testid='Search-CloseIconButton'>
        <IconSearch />
      </IconButton>
    </S.ClosedSearchWrapper>
  );
};
