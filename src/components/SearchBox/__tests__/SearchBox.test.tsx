import { act, fireEvent, waitFor } from '@testing-library/react';
import { render } from 'utils/testing';
import { vi } from 'vitest';

import { SearchBox } from '../SearchBox';
import { KEY_CODES } from '../../../hooks/useKeyPress';

describe('Components:RecordingPreviewSlider', () => {
  const setupSearchBox = (
    isOpen = false,
    toggleIsOpen = () => {},
    searchValue = 'searchTest',
    setSearchValueFn = () => {},
  ) => {
    return render(
      <SearchBox
        isOpen={isOpen}
        toggleIsOpen={toggleIsOpen}
        searchValue='test search'
        setSearchValue={setSearchValueFn}
      />,
    );
  };

  it('should render closed Search', () => {
    const { getByTestId } = setupSearchBox();
    expect(getByTestId('Search-CloseIconButton')).toBeInTheDocument();
  });

  it('should render open Search', () => {
    const { getByTestId } = setupSearchBox(true);
    expect(getByTestId('SearchBox-Input')).toBeInTheDocument();
  });

  it('should call toggleOpen on click', async () => {
    const mockedToggleFn = vi.fn();
    const { getByTestId } = setupSearchBox(false, mockedToggleFn);
    act(() => {
      fireEvent.click(getByTestId('Search-CloseIconButton'));
    });
    await waitFor(() => expect(mockedToggleFn).toHaveBeenCalledTimes(1));
  });

  it('should call toggle and close on ESC keydown', async () => {
    const mockedToggleFn = vi.fn();
    setupSearchBox(true, mockedToggleFn);
    act(() => {
      fireEvent.keyDown(window, {
        key: KEY_CODES.ESCAPE,
        code: KEY_CODES.ESCAPE,
      });
    });
    await waitFor(() => {
      expect(mockedToggleFn).toHaveBeenCalledTimes(1);
    });
  });

  it('should display input value', () => {
    const { getByTestId } = setupSearchBox(true);
    expect(getByTestId('SearchBox-Input')).toHaveValue('test search');
  });

  it('should call setSearchValue on input change 2 times with "" and "test"', () => {
    const mockedToggleFn = vi.fn();
    const { getByTestId } = setupSearchBox(true, vi.fn(), '', mockedToggleFn);
    const searchInput = getByTestId('SearchBox-Input');
    act(() => {
      fireEvent.change(searchInput, {
        target: { value: 'test' },
      });
    });
    expect(mockedToggleFn).toHaveBeenCalledTimes(2);
    expect(mockedToggleFn).toHaveBeenNthCalledWith(1, expect.any(String));
    expect(mockedToggleFn).toHaveBeenNthCalledWith(2, 'test');
  });
});
