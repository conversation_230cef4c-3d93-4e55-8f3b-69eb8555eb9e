import styled from 'styled-components';

import { Text } from 'components/Typography';
import { convertHexToRgbaString } from 'theme/colors';

export const Container = styled.div`
  display: flex;
  flex-direction: column;

  margin: 2.4rem 0 0 0;
`;

export const Grid = styled.div`
  display: grid;
  grid-gap: 2.4rem;
  grid-template-columns: repeat(auto-fill, 19.6rem);
`;

export const NoDataInfo = styled(Text)`
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${({ theme }) => convertHexToRgbaString(theme.colors.white, 0.8)};
  min-height: 11rem;
`;

export const SlideContainer = styled.div`
  width: 19.6rem;
  min-width: 19.6rem;
  margin: 0.8rem;
`;

Grid.displayName = 'ChannelsGrid';
SlideContainer.displayName = 'SlideContainer';
