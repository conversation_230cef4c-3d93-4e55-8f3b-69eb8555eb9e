import { Story, Meta } from '@storybook/react';

import { ChannelsGrid } from './ChannelsGrid';
import { ChannelsGridProps } from './types';

export default {
  title: 'Components/ChannelsGrid',
  component: ChannelsGrid,
} as Meta;

const data: ChannelsGridProps['channels'] = [
  {
    catchupDuration: 1,
    category: 'category',
    channelExtId: 'channelExtId',
    channelNumber: 1,
    extraNumbers: [1, 2],
    is4K: true,
    isAdult: true,
    isHomeZoneRestricted: true,
    isRecordingAllowed: true,
    isSubscribed: true,
    logoSignature: 'logoSignature',
    name: 'name',
    playFeatures: {
      conTv: {
        isCatchUp: true,
        isChannelAllowed: true,
        isNpvr: true,
        isStartOver: true,
        isFastForwardBlocked: true,
      },
      otg: {
        isCatchUp: true,
        isChannelAllowed: true,
        isNpvr: true,
        isStartOver: true,
        isFastForwardBlocked: true,
      },
      stb: {
        isCatchUp: true,
        isChannelAllowed: true,
        isNpvr: true,
        isStartOver: true,
      },
    },
  },
];

const Template: Story = ({ title }) => (
  <ChannelsGrid title={title} channels={data} />
);

export const Default = Template.bind({});
Default.args = {
  title: 'ChannelsGrid',
};
