import { Story, Meta } from '@storybook/react';

import { Tooltip } from './Tooltip';

export default {
  title: 'Components/Tooltip',
  component: Tooltip,
} as Meta;

const Template: Story = ({ content, width }) => (
  <div style={{ marginLeft: '20px', display: 'flex', paddingTop: '40px' }}>
    <Tooltip content={content} width={width} sizeSmall />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  content:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse vel enim in ligula feugiat mollis quis id dui.',
  width: 500,
};
