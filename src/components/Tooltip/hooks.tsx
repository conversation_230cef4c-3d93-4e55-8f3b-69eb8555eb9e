import { useCallback, useRef, useState } from 'react';

export const useTooltip = (width?: number) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isOnRightSide, setIsOnRightSide] = useState<boolean>();
  const ref = useRef<HTMLDivElement>(null);

  const openTooltip = useCallback(
    (event: MouseEvent) => {
      const spaceToRightEdge = window.innerWidth - event.clientX;
      const spaceToLeftEdge = event.clientX;
      if (width && spaceToRightEdge > width + 50) {
        setIsOnRightSide(true);
      } else if (spaceToRightEdge < spaceToLeftEdge) {
        setIsOnRightSide(false);
      }
      setIsOpen(true);
    },
    [width],
  );

  const closeTooltip = useCallback(() => {
    setIsOpen(false);
  }, []);

  return { ref, isOpen, isOnRightSide, openTooltip, closeTooltip };
};
