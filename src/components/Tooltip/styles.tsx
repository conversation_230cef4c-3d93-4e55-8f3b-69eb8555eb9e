import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';

import { TooltipProps } from './types';

export const Container = styled.div`
  position: relative;
`;

export const IconWrapper = styled.div`
  position: relative;
  cursor: pointer;
  &:hover {
    svg:hover {
      & > path:first-child {
        fill: ${({ theme }) => theme.colors.info};
      }
    }
  }
`;

export const Info = styled(motion.div)<{
  width: TooltipProps['width'];
  tooltipShift: TooltipProps['tooltipShift'];
  isOnRightSide?: boolean;
}>`
  align-items: flex-start;
  background-color: ${({ theme }) => theme.colors.info};
  padding: 0.8rem 1.4rem 0.8rem 1.4rem;
  border-radius: 0.8rem;
  position: absolute;
  top: 50%;

  & > * {
    color: ${({ theme }) => theme.colors.infoText};
  }

  ${({ isOnRightSide, tooltipShift }) =>
    isOnRightSide
      ? css`
          left: ${() => (tooltipShift ? `${tooltipShift / 10}rem` : '4.2rem')};
        `
      : css`
          right: ${() => (tooltipShift ? `${tooltipShift / 10}rem` : '4.2rem')};
        `}
  transform: translateY(-50%);
  opacity: 0;
  width: ${({ width }) => (width ? `${width / 10}rem` : 'auto')};
  z-index: 2;

  &:before {
    content: '';
    position: absolute;
    display: block;
    width: 0.8rem;
    height: 0.8rem;
    top: calc(50% - 0.4rem);
    left: 0;
    background-color: ${({ theme }) => theme.colors.info};

    ${({ isOnRightSide, width }) =>
      isOnRightSide
        ? css`
            clip-path: polygon(100% 0, 30% 50%, 100% 100%);
            transform: translateX(-100%);
          `
        : css`
            clip-path: polygon(100% 50%, 0 0, 0 100%);
            transform: ${() => width && `translateX(${width / 10}rem)`};
          `}
  }
`;

Container.displayName = 'Tooltip';
Info.displayName = 'Info';
