import { BaseTextSizeProps } from 'theme';
import { Text } from 'components/Typography';
import { IconQuestionmark } from 'components/Icons';
import { colors } from 'theme/colors';

import { TooltipProps } from './types';
import * as S from './styles';
import { useTooltip } from './hooks';

import { useOutsideClick } from '../../hooks';

const variants = {
  open: { opacity: 1 },
  closed: { opacity: 0 },
};

export const Tooltip = ({
  content,
  width,
  tooltipShift,
  renderIcon,
  ...rest
}: TooltipProps & BaseTextSizeProps): JSX.Element => {
  const { ref, isOpen, isOnRightSide, openTooltip, closeTooltip } =
    useTooltip(width);

  const icon = renderIcon ? (
    renderIcon()
  ) : (
    <IconQuestionmark color={colors.white80} />
  );

  useOutsideClick(ref, () => isOpen && closeTooltip());

  return (
    <S.Container ref={ref} data-testid='tooltip'>
      <S.IconWrapper
        onClick={
          isOpen
            ? () => closeTooltip()
            : (event) => openTooltip(event as unknown as MouseEvent)
        }
      >
        {icon}
      </S.IconWrapper>
      {isOpen && (
        <S.Info
          animate={isOpen ? 'open' : 'closed'}
          variants={variants}
          transition={{ duration: 0.2 }}
          width={width}
          tooltipShift={tooltipShift}
          isOnRightSide={isOnRightSide}
        >
          <Text {...rest} $sizeSmall>
            {content}
          </Text>
        </S.Info>
      )}
    </S.Container>
  );
};
