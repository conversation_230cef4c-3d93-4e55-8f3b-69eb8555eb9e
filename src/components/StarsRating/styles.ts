import styled from 'styled-components';
import { motion } from 'framer-motion';

import { StarButtonProps } from './types';

export const StarButton = styled(motion.button)<StarButtonProps>`
  background-color: transparent;
  border: none;
  outline: none;
  cursor: ${({ disabled }) => (disabled ? 'default' : 'pointer')};

  svg {
    & > path:first-child {
      fill: ${({ theme, disabled }) => disabled && theme.colors.codGray};
    }
  }

  &.on {
    svg {
      & > path:first-child {
        fill: ${({ theme }) => theme.colors.primary};
      }
    }
  }

  &.off {
    svg {
      & > path:first-child {
        fill: ${({ theme }) => theme.colors.white80};
      }
    }
  }
`;

export const StarsRatingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
`;
