import { render, fireEvent, cleanup } from 'utils/testing';
import { Mock, vi } from 'vitest';

import { StarsRating } from '../StarsRating';

describe('StarsRating', () => {
  let setRatingMock: Mock;
  const starButtonsTestId = 'StarsRating-StarButton';

  beforeEach(() => {
    setRatingMock = vi.fn();
  });

  afterEach(() => {
    setRatingMock.mockReset();
    cleanup();
  });

  const setupStarsRating = (
    maxRating = 5,
    rating = 3,
    setRating: (rating: number) => void = setRatingMock,
  ) => {
    const { getAllByTestId } = render(
      <StarsRating
        maxRating={maxRating}
        rating={rating}
        setRating={setRating}
      />,
    );
    const starButtons = getAllByTestId(starButtonsTestId);
    return { starButtons };
  };

  it('renders the correct number of stars', () => {
    const { starButtons } = setupStarsRating();
    expect(starButtons.length).toBe(5);
  });

  it.each`
    index | rating
    ${0}  | ${1}
    ${1}  | ${2}
    ${2}  | ${3}
    ${3}  | ${0}
    ${4}  | ${0}
  `(
    'renders the correct initial rating when rating is $rating',
    ({ index, rating }) => {
      const { starButtons } = setupStarsRating(5, rating);
      expect(starButtons[index].getAttribute('data-rating')).toBe(
        String(rating),
      );
    },
  );

  it('updates the rating when a star is clicked', () => {
    const { starButtons } = setupStarsRating(5, 0);
    fireEvent.click(starButtons[2]);
    expect(setRatingMock).toHaveBeenCalledWith(3);
  });

  it('does not allow a rating higher than maxRating', () => {
    const { starButtons } = setupStarsRating(5, 7);
    expect(starButtons[4].getAttribute('data-rating')).toBe('5');
  });

  it('should not call change rating when onMouseEnter and onMouseLeave events', () => {
    const { starButtons } = setupStarsRating(5, 3);
    fireEvent.mouseEnter(starButtons[1]);
    expect(setRatingMock).not.toHaveBeenCalled();
    fireEvent.mouseLeave(starButtons[1]);
    expect(setRatingMock).not.toHaveBeenCalled();
  });

  it('should call setRating when onClick event', () => {
    const { starButtons } = setupStarsRating(5, 3);
    fireEvent.click(starButtons[1]);
    expect(setRatingMock).toHaveBeenCalledWith(2);
  });
});
