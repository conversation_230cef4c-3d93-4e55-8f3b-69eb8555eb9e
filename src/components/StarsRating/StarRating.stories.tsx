import { useState } from 'react';
import { Story, Meta } from '@storybook/react';

import { StarsRating } from './StarsRating';
import { StarsRatingProps } from './types';

export default {
  title: 'Components/StarsRating',
  component: StarsRating,
} as Meta;

const Template: Story<StarsRatingProps> = (args) => {
  const [rating, setRating] = useState(0);

  return <StarsRating {...args} rating={rating} setRating={setRating} />;
};

export const Default = Template.bind({});
Default.args = {
  maxRating: 5,
  starHeight: 32,
  starWidth: 32,
};
