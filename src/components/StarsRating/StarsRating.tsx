import { useEffect, useState } from 'react';

import { IconStar } from 'components/Icons';

import * as S from './styles';
import { StarsRatingProps } from './types';

export const StarsRating = ({
  maxRating = 5,
  starHeight = 32,
  starWidth = 32,
  rating = 0,
  setRating,
  disabled = false,
}: StarsRatingProps) => {
  const [hover, setHover] = useState(0);

  useEffect(() => {
    if (rating === 0) {
      setHover(0);
    }
  }, [rating]);

  return (
    <S.StarsRatingContainer>
      {[...Array(maxRating)].map((_star, index) => {
        const starIndex = ++index;
        return (
          <S.StarButton
            key={index}
            index={index}
            onMouseEnter={() => setHover(index)}
            onMouseLeave={() => setHover(rating)}
            onClick={() => setRating(Math.min(starIndex, maxRating))}
            rating={starIndex <= rating ? starIndex : 0}
            data-rating={starIndex <= rating ? starIndex : 0}
            hover={hover}
            data-testid='StarsRating-StarButton'
            disabled={disabled}
          >
            <IconStar
              isEmpty={Boolean(rating < starIndex)}
              height={starHeight}
              width={starWidth}
            />
          </S.StarButton>
        );
      })}
    </S.StarsRatingContainer>
  );
};
