import { FC, PropsWithChildren } from 'react';

import { PICTOGRAMS_ICONS } from './constants';
import { Image, PictogramsContainer } from './styles';
import { PictogramsType, VodPictogramsProps } from './types';

export const VodPictograms: FC<PropsWithChildren<VodPictogramsProps>> = ({
  pictograms,
}) => {
  const getPictogramIcon = () => {
    return pictograms.map((pictogram) => (
      <Image
        key={pictogram}
        src={PICTOGRAMS_ICONS[pictogram as PictogramsType]}
        alt={`vod-pictogram-${pictogram}`}
      />
    ));
  };

  return <PictogramsContainer>{getPictogramIcon()}</PictogramsContainer>;
};
