import { useIntl } from 'react-intl';

import { Image } from 'components/Image';
import { Text } from 'components/Typography';
import { Modal } from 'components/Modal';
import { getStaticImageUrl } from 'utils/url';

import {
  FBB_TV_PROMO_GRAPHIC_1X_IMAGE_NAME,
  FBB_TV_PROMO_GRAPHIC_2X_IMAGE_NAME,
  FBB_TV_PROMO_GRAPHIC_3X_IMAGE_NAME,
} from './constants';
import { messages } from './messages';
import * as S from './styles';
import { AtvAppPopupProps } from './types';

export const AtvAppPopup = ({ isOpen, onClose }: AtvAppPopupProps) => {
  const { formatMessage } = useIntl();
  return (
    <Modal
      title={formatMessage(messages.popupTitle)}
      isOpen={isOpen}
      onClose={onClose}
      withCloseButton
    >
      <>
        <S.ImageWrapper>
          <Image
            srcSet={`${getStaticImageUrl(
              FBB_TV_PROMO_GRAPHIC_1X_IMAGE_NAME,
            )}, ${getStaticImageUrl(
              FBB_TV_PROMO_GRAPHIC_2X_IMAGE_NAME,
            )} 2x, ${getStaticImageUrl(FBB_TV_PROMO_GRAPHIC_3X_IMAGE_NAME)} 3x`}
            src={getStaticImageUrl(FBB_TV_PROMO_GRAPHIC_3X_IMAGE_NAME)}
          />
        </S.ImageWrapper>
        <Text $secondary $sizeSmall>
          {formatMessage(messages.popupDescription)}
        </Text>
      </>
    </Modal>
  );
};
