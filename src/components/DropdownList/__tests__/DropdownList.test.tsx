import { act, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { DropdownList } from '../DropdownList';

describe('Components:DropdownList', () => {
  it('should render DropdownList component', () => {
    render(<DropdownList data={['Test']} />);
    const dropdownTitle = screen.getAllByText('Test')[0];

    expect(dropdownTitle).toBeInTheDocument();
  });

  it('should render DropdownList component with activeItem', () => {
    render(<DropdownList data={['Test']} activeItem='Test2' />);
    const activeTitle = screen.getByText('Test2');

    expect(activeTitle).toBeInTheDocument();
  });

  it('should run callback function on ListItem click', async () => {
    const fn = vi.fn();
    render(<DropdownList data={['Test', 'test2']} onChange={fn} />);
    const itemToClick = screen.getByText('test2');

    await userEvent.click(itemToClick);

    expect(fn).toHaveBeenCalled();
  });

  it('should toggle dropdown list height after button is clicked', async () => {
    render(<DropdownList data={['listItem1', 'listItem2', 'listItem3']} />);
    expect(screen.getByTestId('DropdownList-List')).toHaveStyle(
      `max-height: 0`,
    );
    const itemToClick = screen.getByTestId('dropdown-button-test');
    await act(async () => {
      itemToClick.click();
    });
    expect(screen.getByTestId('DropdownList-List')).toHaveStyle(
      `max-height: 70rem`,
    );
    await act(async () => {
      itemToClick.click();
    });
    expect(screen.getByTestId('DropdownList-List')).toHaveStyle(
      `max-height: 0`,
    );
  });
});
