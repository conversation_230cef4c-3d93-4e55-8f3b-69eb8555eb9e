export type DropdownListTypes = 'default' | 'withoutDownRadius';

export interface DropdownListProps {
  data: Array<string>;
  activeItem?: string;
  type?: DropdownListTypes;
  onChange?: (item: any) => void;
  renderOptions?: () => JSX.Element;
  fullWidth?: boolean;
}

export type DropdownListStylesProps = {
  $isOpen: boolean;
  type?: DropdownListTypes;
  $fullWidth?: boolean;
};

export type TitleContainerStyleProps = {
  $isOpen: boolean;
  type?: DropdownListTypes;
};
