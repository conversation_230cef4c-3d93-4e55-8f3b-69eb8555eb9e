import styled, { css } from 'styled-components';

import { H2, Text } from 'components/Typography';

import {
  DropdownListStylesProps,
  DropdownListTypes,
  TitleContainerStyleProps,
} from './types';

export const Arrow = styled.div<{ $isOpen: boolean }>`
  width: 0;
  height: 0;
  border-left: 1rem solid transparent;
  border-right: 1rem solid transparent;
  border-top: 0.8rem solid ${({ theme }) => theme.colors.alabaster};
  transition: transform 0.4s ease-out;
  ${({ $isOpen }) =>
    $isOpen &&
    css`
      transform: rotate(180deg);
    `};
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;
export const ControlsContainer = styled.div<{ $fullWidth?: boolean }>`
  display: flex;
  align-items: flex-end;
  padding: ${({ $fullWidth }) => ($fullWidth ? '0 0 0 3.2rem' : '0')};
`;

export const List = styled.ul<DropdownListStylesProps>`
  border-radius: ${({ type }) =>
    type === 'withoutDownRadius' ? '0' : '0 1.2rem 1.2rem 1.2rem'};
  background: ${({ theme }) => theme.colors.tertiary};
  overflow: hidden;
  max-height: ${({ $isOpen }) => ($isOpen ? '70rem' : 0)};
  transition: max-height 0.4s ease-out;
  list-style: none;
  width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'calc(100vw - 8rem)')};
`;

export const ListItem = styled(Text).attrs({
  as: 'li',
})`
  display: inline-block;
  margin-right: 4.8rem;
  margin-bottom: 1.6rem;
`;

export const ListWrapper = styled.div`
  padding: 1.6rem 1.6rem 0 1.6rem;
`;

export const OptionsWrapper = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
`;

export const TextStyled = styled(Text)`
  line-height: 0.93;
  color: ${({ theme }) => theme.colors.white};

  &:active {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

export const Title = styled(H2)`
  white-space: nowrap;
  margin-right: 3.3rem;
  line-height: 0.93;
`;

export const TitleContainer = styled.div<TitleContainerStyleProps>`
  height: 5.2rem;
  background: ${({ theme }) => theme.colors.tertiary};
  display: inline-flex;
  border-radius: ${({ $isOpen, type }) =>
    type === 'withoutDownRadius'
      ? `1.2rem 1.2rem 0 0`
      : $isOpen
        ? `1.2rem 1.2rem 0 0`
        : `1.2rem`};
  transition: ${({ $isOpen }) =>
    $isOpen ? `border-radius 0 ease-out` : `border-radius .4s ease-in .2s`};
  align-items: center;
  padding: 0.8rem 1.6rem 1.1rem 1.6rem;

  &:hover {
    cursor: pointer;
  }
`;

Arrow.displayName = 'DropdownListArrow';
List.displayName = 'DropdownListDataList';
ListItem.displayName = 'DropdownListListItem';
ListWrapper.displayName = 'DropdownListListWrapper';
OptionsWrapper.displayName = 'DropdownListOptionsWrapper';
Title.displayName = 'DropdownListTitle';
TitleContainer.displayName = 'DropdownListTitleContainer';
