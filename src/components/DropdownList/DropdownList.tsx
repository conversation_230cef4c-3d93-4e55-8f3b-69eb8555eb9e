import { useCallback } from 'react';

import { RawButton } from 'components/Buttons/RawButton';

import { useDropdownList } from './hooks';
import { DropdownListProps } from './types';
import * as S from './styles';

export const DropdownList = ({
  data,
  activeItem = data[0],
  onChange,
  type,
  renderOptions,
  fullWidth,
}: DropdownListProps) => {
  const { setIsOpen, isOpen, onItemClick, ref } = useDropdownList(onChange);

  const toggleIsOpen = useCallback(
    () => setIsOpen((prev) => !prev),
    [setIsOpen],
  );

  return (
    <S.Container ref={ref}>
      <S.ControlsContainer $fullWidth={Boolean(fullWidth)}>
        <RawButton onClick={toggleIsOpen} data-testid='dropdown-button-test'>
          <S.TitleContainer $isOpen={isOpen} type={type}>
            <S.Title>{activeItem}</S.Title>
            <S.Arrow $isOpen={isOpen} />
          </S.TitleContainer>
        </RawButton>
        {renderOptions && renderOptions()}
      </S.ControlsContainer>
      <S.List
        $isOpen={isOpen}
        data-testid='DropdownList-List'
        type={type}
        $fullWidth={Boolean(fullWidth)}
      >
        <S.ListWrapper>
          {data.map((item, i) => (
            <S.ListItem key={i}>
              <RawButton onClick={() => onItemClick(item)}>
                <S.TextStyled>{item}</S.TextStyled>
              </RawButton>
            </S.ListItem>
          ))}
        </S.ListWrapper>
      </S.List>
    </S.Container>
  );
};
