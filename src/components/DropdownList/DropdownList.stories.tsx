import { Story, <PERSON>a } from '@storybook/react';

import { DropdownList } from './DropdownList';

export default {
  title: 'Components/DropdownList',
  component: DropdownList,
} as Meta;

const Template: Story = ({ data }) => <DropdownList data={data} />;

export const Default = Template.bind({});
Default.args = {
  data: [
    'Mój pakiet',
    'Sport',
    'Ogólne',
    'Programy 1',
    'Programy 2',
    'Programy 3',
  ],
};

export const MultipleRows = Template.bind({});
MultipleRows.args = {
  data: [
    'Mój pakiet',
    'Sport',
    'Ogólne',
    'Programy 1',
    'Programy 2',
    'Programy 3',
    'Programy 4',
    'Programy 5',
    'Programy 6',
    'Programy 7',
    'Programy 8',
    'Programy 9',
    'Programy 10',
    'Programy 11',
    'Programy 12',
  ],
};
