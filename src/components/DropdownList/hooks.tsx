import { useRef, useState } from 'react';

import { useOutsideClick } from 'hooks/useOutsideClick';

import { DropdownListProps } from './types';

export const useDropdownList = (onChange: DropdownListProps['onChange']) => {
  const [isOpen, setIsOpen] = useState(false);

  const ref = useRef<HTMLDivElement>(null);

  useOutsideClick(ref, () => isOpen && setIsOpen(() => false));

  const onItemClick = (item: any) => {
    setIsOpen(false);
    onChange && onChange(item);
  };

  return { setIsOpen, isOpen, onItemClick, ref };
};
