import { motion } from 'framer-motion';
import styled from 'styled-components';

import { ContainerProps, ProgressBarContainerProps } from './types';

export const Overlay = styled.div`
  width: 50rem;
  height: 12rem;
  bottom: 30rem;
  right: 0;
`;

export const ContainerAnimation = {
  initial: { translateX: 200 },
  animate: { translateX: 0 },
  transition: { duration: 0.25 },
};

export const Container = styled(motion.div)<ContainerProps>`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: ${({ theme }) => theme.colors.tertiary};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: ${({ isStick }) => (isStick ? '6rem 0 0 6rem' : '6rem')};
  row-gap: 1.6rem;
  cursor: pointer;
  z-index: 1;
`;
export const ProgressBarContainer = styled.div<ProgressBarContainerProps>`
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: ${({ isStick }) => (isStick ? '6rem 0 0 6rem' : '6rem')};
  z-index: -1;
`;

export const ProgressBar = styled(motion.div).attrs(() => ({
  initial: { translateX: '-100%' },
  animate: { translateX: 0 },
}))`
  position: relative;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${({ theme }) => theme.colors.primary};
`;
