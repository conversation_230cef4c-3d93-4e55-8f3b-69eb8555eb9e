import { useEffect } from 'react';
import { millisecondsInSecond } from 'date-fns/constants';

import { Text } from 'components/Typography';

import * as S from './styles';
import { OvalModalProps } from './types';

export const OvalModal = ({
  isOpen,
  modalTitle,
  modalDesc,
  onClose,
  onClick,
  timeout,
  isStick = true,
}: OvalModalProps) => {
  useEffect(() => {
    const timeoutReference = setTimeout(onClose, timeout);
    return () => clearTimeout(timeoutReference);
  }, [onClose, timeout]);

  if (isOpen) {
    return (
      <S.Container
        onClick={onClick}
        isStick={isStick}
        {...S.ContainerAnimation}
      >
        <Text>{modalTitle}</Text>
        <Text $secondary $sizeSmall>
          {modalDesc}
        </Text>
        <S.ProgressBarContainer isStick={isStick}>
          <S.ProgressBar
            transition={{
              ease: 'linear',
              duration: timeout / millisecondsInSecond,
            }}
          />
        </S.ProgressBarContainer>
      </S.Container>
    );
  }

  return null;
};
