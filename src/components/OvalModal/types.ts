import { TimeInMilliseconds } from 'services/api/common/types';

export type OvalModalProps = {
  isOpen: boolean;
  modalTitle: string;
  modalDesc: string;
  onClose: () => void;
  onClick: () => void;
  progressPercent?: number;
  timeout: TimeInMilliseconds;
  isStick?: boolean;
};

export interface ContainerProps {
  isStick: boolean;
}
export interface ProgressBarContainerProps {
  isStick: boolean;
}
