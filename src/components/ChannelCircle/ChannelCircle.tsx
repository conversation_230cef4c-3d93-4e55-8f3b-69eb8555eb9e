import { useIntl } from 'react-intl';

import { IMAGE_URL } from 'services/api/oldApi';
import { Image } from 'components/Image';
import { Text } from 'components/Typography';
import { getPublicAssetUrl } from 'utils/url/asset';
import { useChannelPlay, useChannelProgram } from 'components/Channel/hooks';

import { messages } from './messages';
import { ChannelCircleProps } from './types';
import * as S from './styles';

export const ChannelCircle = ({
  channel,
  isCurrentChannel,
}: ChannelCircleProps) => {
  const { logoSignature, name } = channel;
  const { formatMessage } = useIntl();
  const { currentProgram, isBlackout } = useChannelProgram(channel);
  const { handlePlayChannel } = useChannelPlay(channel);

  return (
    <S.ChannelCircleContainer data-testid='ChannelCircle-ChannelCircleContainer'>
      <S.ImageWrapper
        $withBorder={isCurrentChannel}
        onClick={handlePlayChannel}
      >
        <Image
          src={getPublicAssetUrl(IMAGE_URL, logoSignature)}
          alt={name}
          placeholderScale={0.5}
        />
      </S.ImageWrapper>
      <S.ChannelName>
        <Text $sizeXSmall>{name}</Text>
      </S.ChannelName>
      {isBlackout ? (
        <Text $sizeXXSmall $highlight>
          {formatMessage(messages.blackout)}
        </Text>
      ) : (
        <S.ProgramName>
          <Text $sizeXXSmall $primary>
            {currentProgram?.name}
          </Text>
        </S.ProgramName>
      )}
    </S.ChannelCircleContainer>
  );
};
