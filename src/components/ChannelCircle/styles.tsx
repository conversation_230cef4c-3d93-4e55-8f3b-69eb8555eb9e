import styled from 'styled-components';

export const ChannelCircleContainer = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 0.6rem;
  justify-content: center;
  align-items: center;
  max-width: 14rem;
  padding: 1rem 0;

  &:hover {
    transform: scale(1.1);
  }
`;

export const ImageWrapper = styled.div<{ $withBorder: boolean }>`
  width: 14rem;
  height: 14rem;
  background-color: ${({ theme }) => theme.colors.mineShaft};
  border-radius: 50%;
  cursor: pointer;
  border: ${({ $withBorder, theme }) =>
    $withBorder ? `0.3rem solid ${theme.colors.primary}` : 'none'};

  img {
    height: auto;
    max-height: 9rem;
    width: auto;
    max-width: 9rem;
  }
`;

export const ChannelName = styled.div`
  width: 12rem;
  height: 2rem;
  text-align: center;
  & span {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;

export const ProgramName = styled.div`
  width: 12rem;
  height: 2rem;
  text-align: center;
  & span {
    width: 100%;
    opacity: 0.5;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;
