import { render, waitFor } from 'utils/testing';
import { screen } from '@testing-library/react';

import { ChannelCircle } from '../ChannelCircle';

const dummyChannel = {
  channelExtId: '14135',
  name: 'TVP1',
  channelNumber: 1,
  extraNumbers: [],
  category: 'Ogólne',
  logoSignature: 'L2F0dGFjaG1lbnRzLzE0MTM1LUxPR08ucG5n',
  logoImageId: 'cf76c8f2612158e1920e728765223e1f',
  logoUrl:
    'https://tvgo.ppd.orange.pl/mnapi/btm-009/cf76c8f2612158e1920e728765223e1f',
  catchupDuration: 604800,
  is4K: false,
  isHomeZoneRestricted: true,
  isPayPerView: false,
  isRegionalTv: false,
  isRecordingAllowed: false,
  isInteractive: false,
  openWindowId: 'test',
  dai: 'test',
  isAdult: false,
  isSubscribed: true,
  frameUrl:
    'https://redir.cache.orange.pl/otv/OTF/0/0/0/0/0/pool05/bpk-tv/hz_14135/DASH/dash/thumbnails/TVP1HD-thumbnail-[time].jpeg',
  playFeatures: {
    otg: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    conTv: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    stb: {
      isChannelAllowed: true,
      isCatchUp: true,
      isStartOver: true,
      isNpvr: false,
      isFastForwardBlocked: false,
    },
    boxless: {
      isCatchUp: false,
      isChannelAllowed: false,
      isFastForwardBlocked: false,
      isNpvr: false,
      isStartOver: false,
    },
  },
  multicast: {
    ip: '***********',
    port: 5500,
    originalNetworkId: '1',
    transportStreamId: '241',
    serviceId: '340',
    columnFecPort: 0,
    fecEnabled: false,
    protocol: '',
    rowFecPort: 0,
  },
};

describe('Components:ChannelCircle', () => {
  it('should render ChannelCircle ', () => {
    const { getByTestId } = render(
      <ChannelCircle channel={dummyChannel} isCurrentChannel={false} />,
    );
    waitFor(() =>
      expect(
        getByTestId('ChannelCircle-ChannelCircleContainer'),
      ).toBeInTheDocument(),
    );
  });

  it('should render ChannelCircle with border', () => {
    render(<ChannelCircle channel={dummyChannel} isCurrentChannel={true} />);
    const image = screen.getByTestId(
      'ChannelCircle-ChannelCircleContainer',
    ).firstChild;
    expect(image).toHaveStyle(`border:0.3rem solid #ff6600`);
  });
});
