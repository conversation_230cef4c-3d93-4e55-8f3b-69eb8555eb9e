import { screen } from '@testing-library/react';
import { Breadcrumbs } from '../Breadcrumbs';
import { render } from 'utils/testing';

const breadcrumbsElements = ['Test', 'Test2'];

describe('Components:Breadcrumbs', () => {
  it('should render Breadcrumbs component', () => {
    render(<Breadcrumbs elements={breadcrumbsElements} separator='/' />);

    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
