import { BaseTextColorProps, BaseTextSizeProps } from 'theme';

import * as S from './styles';
import { BreadcrumbsProps } from './types';

export const Breadcrumbs = ({
  elements,
  separator,
  ...rest
}: BreadcrumbsProps & BaseTextColorProps & BaseTextSizeProps) => {
  return (
    <S.BreadcrumbsContainer data-testid='Breadcrumbs-BreadcrumbsContainer'>
      {elements.map((element, elementsIndex) => {
        const isNotLastElement = elementsIndex < elements.length - 1;
        return (
          <S.BreadcrumbsText key={elementsIndex} {...rest}>
            {element}
            {isNotLastElement && <span>{separator}</span>}
          </S.BreadcrumbsText>
        );
      })}
    </S.BreadcrumbsContainer>
  );
};
