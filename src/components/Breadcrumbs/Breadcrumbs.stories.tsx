import React from 'react';
import { Story, Meta } from '@storybook/react';
import { Breadcrumbs } from './Breadcrumbs';

export default {
  title: 'Components/Breadcrumbs',
  component: Breadcrumbs,
} as Meta;

const Template: Story = ({ elements, separator }) => (
  <>
    <Breadcrumbs
      elements={elements}
      separator={separator}
      sizeXXSmall
      highlight
    />
    <br />
    <Breadcrumbs elements={elements} separator={separator} sizeLarge primary />
    <br />
    <Breadcrumbs
      elements={elements}
      separator={separator}
      sizeXMedium
      secondary
    />
  </>
);

export const Default = Template.bind({});

Default.args = {
  elements: ['Stories', 'Default', 'Test'],
  separator: '/',
};
