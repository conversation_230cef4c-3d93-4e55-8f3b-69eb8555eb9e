import styled, { css } from 'styled-components';

import { StyledDeviceIconWrapper } from './types';

export const SingleDeviceWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  margin: 4.8rem 0;
  gap: 2.4rem;
`;

export const DeviceIconWrapper = styled.div<StyledDeviceIconWrapper>`
  ${({ $isSmall }) =>
    $isSmall
      ? css`
          width: 7.2rem;
          height: 7.2rem;
          margin: 0 2.4rem 0 0;
        `
      : css`
          width: 9.6rem;
          height: 9.6rem;
          margin: 0 0 1.6rem 0;
        `}

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.1rem solid ${({ theme }) => theme.colors.white};
  border-radius: 50%;
  flex-shrink: 0;
`;

export const TerminalDeleteButton = styled.button`
  position: absolute;
  background-color: ${({ theme }) => theme.colors.red};
  width: 3.2rem;
  height: 3.2rem;
  right: 0;
  top: 0;
  border: 0.4rem solid ${({ theme }) => theme.colors.white};
  border-radius: 50%;
  padding: 0;
  cursor: pointer;

  &::after,
  &::before {
    position: absolute;
    content: '';
    width: 0.4rem;
    height: 1.6rem;
    background-color: ${({ theme }) => theme.colors.white};
  }
  &::after {
    transform: translate(-0.2rem, -0.8rem) rotate(45deg);
  }
  &::before {
    transform: translate(-0.2rem, -0.8rem) rotate(-45deg);
  }
`;

export const DeviceWrapperRow = styled.div`
  display: flex;
  align-items: center;
`;

export const DeviceWrapperColumn = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 14.6rem;
  text-align: center;
`;
