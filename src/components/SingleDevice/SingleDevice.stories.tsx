import { Story, Meta } from '@storybook/react';

import { SingleDevice } from './SingleDevice';

export default {
  title: 'Components/SingleDevice',
  component: SingleDevice,
  argTypes: {
    deviceType: {
      options: ['android_mobile_otf', 'android_tablet_otf', 'web_otf'],
      control: { type: 'select' },
    },
  },
} as Meta;

const Template: Story = ({ isSmall, deviceType }) => (
  <SingleDevice
    device={{
      deviceType,
      name: 'WEB_TEST',
      registrationDate: 160000,
      serialNumber: '2F2-FDSF2',
    }}
    onDelete={() => {}}
    isSmall={isSmall}
  />
);

export const Default = Template.bind({});
Default.args = {
  isSmall: true,
  deviceType: 'web_otf',
};
