import { FC, PropsWithChildren, useMemo } from 'react';
import { useIntl } from 'react-intl';

import { Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { IconPC, IconPhone, IconTablet, IconTv } from 'components/Icons';
import { DeviceType } from 'services/api/newApi/core/device/types';

import { messages } from './messages';
import { SingleDeviceProps } from './types';
import * as S from './styles';

const deviceIconScale = {
  small: 0.65,
  normal: 1,
};
export const SingleDevice: FC<PropsWithChildren<SingleDeviceProps>> = ({
  device,
  onDelete,
  isSmall,
}) => {
  const { formatMessage } = useIntl();

  const renderedDeviceIcon = useMemo(() => {
    const iconScale = isSmall ? deviceIconScale.small : deviceIconScale.normal;
    const { deviceType } = device;

    const deviceIcons: Partial<Record<DeviceType, JSX.Element>> = {
      [DeviceType.androidMobileOtf]: <IconPhone scale={iconScale} />,
      [DeviceType.iosMobileOtf]: <IconPhone scale={iconScale} />,
      [DeviceType.androidTabletOtf]: <IconTablet scale={iconScale} />,
      [DeviceType.iosTabletOtf]: <IconTablet scale={iconScale} />,
      [DeviceType.webOtf]: <IconPC scale={iconScale} />,
      [DeviceType.boxlessAndroidL1]: <IconTv scale={iconScale} />,
    };

    return deviceIcons[deviceType] || null;
  }, [device, isSmall]);

  const smallDevice = useMemo(
    () => (
      <>
        <S.DeviceWrapperRow>
          <S.DeviceIconWrapper $isSmall={Boolean(isSmall)}>
            {renderedDeviceIcon}
          </S.DeviceIconWrapper>
          <Text>{device.name}</Text>
        </S.DeviceWrapperRow>
        <PrimaryButton onClick={onDelete}>
          <Text $secondary>{formatMessage(messages.deleteButton)}</Text>
        </PrimaryButton>
      </>
    ),
    [device.name, formatMessage, isSmall, onDelete, renderedDeviceIcon],
  );

  const bigDevice = useMemo(
    () => (
      <S.DeviceWrapperColumn>
        <S.DeviceIconWrapper $isSmall={Boolean(isSmall)}>
          <>
            {renderedDeviceIcon}
            <S.TerminalDeleteButton onClick={onDelete} />
          </>
        </S.DeviceIconWrapper>
        <Text $secondary>{device.name}</Text>
      </S.DeviceWrapperColumn>
    ),
    [device.name, isSmall, onDelete, renderedDeviceIcon],
  );

  return (
    <S.SingleDeviceWrapper>
      {isSmall ? smallDevice : bigDevice}
    </S.SingleDeviceWrapper>
  );
};
