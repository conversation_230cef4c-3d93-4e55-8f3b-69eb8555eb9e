import styled from 'styled-components';

import { CheckboxProps } from './types';

export const CheckboxContainer = styled.label`
  display: inline-block;
  vertical-align: middle;
  position: relative;
`;

export const HiddenCheckbox = styled.input.attrs({ type: 'checkbox' })`
  border: 0;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 0.1rem;
  margin: -0.1rem;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 0.1rem;
`;

export const StyledCheckbox = styled.div<{
  $isTicked: CheckboxProps['isChecked'];
}>`
  display: inline-block;
  outline: 0;
  position: relative;
  width: 2.2rem;
  height: 2.2rem;
  background-color: ${({ theme }) => theme.colors.transparent};
  border: solid 0.2rem ${({ theme }) => theme.colors.alabaster};
  border-radius: 10%;
  transition: background-color 0.3s ease-out;

  &:active {
    background-color: ${({ theme }) => theme.colors.alabaster};
  }

  &:hover {
    cursor: pointer;
  }

  &:before,
  &:after {
    background-color: ${({ theme }) => theme.colors.primary};
    content: '';
    opacity: ${({ $isTicked }) => ($isTicked ? 1 : 0)};
    position: absolute;
    transition: opacity 0.3s linear;
  }

  &:after {
    width: 1.2rem;
    height: 0.2rem;
    bottom: 48%;
    right: 0;
    transform: rotate(-45deg);
  }

  &:before {
    width: 0.8rem;
    height: 0.2rem;
    bottom: 0.6rem;
    left: 0.1rem;
    transform: rotate(45deg);
  }
`;
