import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { render } from 'utils/testing';

import { Checkbox } from '../Checkbox';

describe('Components:Forms:Checkbox', () => {
  it('should render Checkbox component', () => {
    render(<Checkbox id='test' />);

    expect(screen.getByTestId('checkbox-control')).toBeInTheDocument();
  });

  it('should fire onClick callback function and show tick', async () => {
    const mock = vi.fn();

    render(<Checkbox id='test' onCheck={mock} />);

    expect(mock).not.toHaveBeenCalled();

    await userEvent.click(screen.getByTestId('checkbox-control'));

    expect(mock).toHaveBeenCalled();
  });
});
