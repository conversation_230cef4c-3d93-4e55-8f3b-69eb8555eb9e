import { render } from 'utils/testing';

import { Input } from '../Input';

describe('Components:Forms:Input', () => {
  it('renders Input component', () => {
    render(<Input id='test-id' name='test-id' />);

    const testInput = document.getElementById('test-id');

    expect(testInput).toBeInTheDocument();
    expect(testInput).toHaveAttribute('id', 'test-id');
    expect(testInput).toHaveAttribute('name', 'test-id');
    expect(testInput).toHaveAttribute('type', 'text');
  });
});
