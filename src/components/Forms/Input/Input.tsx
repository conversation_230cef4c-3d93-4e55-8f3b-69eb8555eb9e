import styled from 'styled-components';

export const Input = styled.input.attrs(({ type, maxLength, autoFocus }) => ({
  type: type || 'text',
  maxLength: maxLength,
  autoFocus: autoFocus,
}))`
  background-color: ${({ theme }) => theme.colors.transparent};
  border: solid 0.3rem ${({ theme }) => theme.colors.white};
  color: ${({ theme }) => theme.colors.alabaster};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  font-weight: 400;
  line-height: 1.2;
  padding: 0.7rem 1.8rem;
  transition: border-color 0.3s linear;
  width: 100%;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    border-color: ${({ theme }) => theme.colors.white50};
    color: ${({ theme }) => theme.colors.white50};
  }
  &:-webkit-autofill {
    box-shadow: 0 0 0 100rem ${({ theme }) => theme.colors.black} inset !important;
    -webkit-text-fill-color: ${({ theme }) =>
      theme.colors.alabaster} !important;
  }
`;

Input.displayName = 'Input';
