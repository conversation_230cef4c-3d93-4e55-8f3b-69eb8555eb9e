import styled from 'styled-components';

import { BaseTextColorProps, BaseTextSizeProps } from 'theme';

export const Label = styled.label<BaseTextColorProps & BaseTextSizeProps>`
  ${({ theme }) => theme.mixins.baseTextColor};
  ${({ theme }) => theme.mixins.baseTextSize};
  display: inline-block;
  line-height: 1.25;
  transition: color 0.2s ease-out;

  &:hover {
    cursor: pointer;
  }

  &:hover {
    color: ${({ theme }) => theme.colors.philippineOrange};
  }
`;

Label.displayName = 'Label';
