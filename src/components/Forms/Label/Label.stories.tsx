import { Story, Meta } from '@storybook/react';

import { Label } from './Label';

export default {
  title: 'Components/Forms/Label',
  component: Label,
} as Meta;

const Template: Story = ({ children, ...rest }) => (
  <>
    <Label {...rest}>{children} default</Label>
    <br />
    <br />
    <Label {...rest} primary sizeXLarge>
      {children} primary xLarge
    </Label>
    <br />
    <Label {...rest} primary sizeLarge>
      {children} primary large
    </Label>
    <br />
    <Label {...rest} primary sizeMedium>
      {children} primary medium
    </Label>
    <br />
    <Label {...rest} primary sizeSmall>
      {children} primary small
    </Label>
    <br />
    <Label {...rest} primary sizeXSmall>
      {children} primary xSmall
    </Label>
    <br />
    <Label {...rest} primary sizeXXSmall>
      {children} primary xxSmall
    </Label>
    <br />
    <br />
    <Label {...rest} secondary sizeXLarge>
      {children} secondary xLarge
    </Label>
    <br />
    <Label {...rest} secondary sizeLarge>
      {children} secondary large
    </Label>
    <br />
    <Label {...rest} secondary sizeMedium>
      {children} secondary medium
    </Label>
    <br />
    <Label {...rest} secondary sizeSmall>
      {children} secondary small
    </Label>
    <br />
    <Label {...rest} secondary sizeXSmall>
      {children} secondary xSmall
    </Label>
    <br />
    <Label {...rest} secondary sizeXXSmall>
      {children} secondary xxSmall
    </Label>
    <br />
    <br />
    <Label {...rest} highlight sizeXLarge>
      {children} highlight xLarge
    </Label>
    <br />
    <Label {...rest} highlight sizeLarge>
      {children} highlight large
    </Label>
    <br />
    <Label {...rest} highlight sizeMedium>
      {children} highlight medium
    </Label>
    <br />
    <Label {...rest} highlight sizeSmall>
      {children} highlight small
    </Label>
    <br />
    <Label {...rest} highlight sizeXSmall>
      {children} highlight xSmall
    </Label>
    <br />
    <Label {...rest} highlight sizeXXSmall>
      {children} highlight xxSmall
    </Label>
  </>
);

export const Default = Template.bind({});
Default.args = {
  children: 'Label',
};
