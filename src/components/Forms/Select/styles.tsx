import Select from 'react-select';
import styled from 'styled-components';

import { devices } from 'theme';

export const CustomSelect = styled(Select)`
  flex: 1;

  .basic-select {
    &__control {
      background-color: ${({ theme }) => theme.colors.tertiary};
      border-radius: 0.8rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 0;
      box-shadow: none;
      outline: none;
      cursor: pointer;
      min-height: 3.6rem;

      &--menu-is-open {
        border-radius: 0.8rem 0.8rem 0 0;
      }
    }

    &__value-container {
      padding: 0.8rem 1.6rem 0.8rem 1.6rem;
      height: 3.6rem;
    }

    &__indicators {
      height: 3.6rem;
    }

    &__single-value {
      color: ${({ theme }) => theme.colors.alabaster};
      font-size: ${({ theme }) => theme.fontSizes.medium};
      font-weight: bold;
      position: relative;
      max-width: 100%;
    }

    &__menu {
      background-color: ${({ theme }) => theme.colors.tertiaryLight};
      margin-top: 0;
      padding-bottom: 0.8rem;
      opacity: 1;
      border-radius: 0 0 0.8rem 0.8rem;

      &-list {
        background-color: ${({ theme }) => theme.colors.transparent};
        margin: 0;
        padding: 0;
      }
    }

    &__option {
      background-color: ${({ theme }) => theme.colors.transparent};
      color: ${({ theme }) => theme.colors.alabaster};
      font-size: ${({ theme }) => theme.fontSizes.medium};

      font-weight: bold;
      padding: 1.2rem 0 1.2rem 1.6rem;
      cursor: pointer;

      &:hover {
        background: ${({ theme }) => theme.colors.primary};
      }

      &--is-selected {
        color: ${({ theme }) => theme.colors.primary};

        &:hover {
          color: ${({ theme }) => theme.colors.alabaster};
        }
      }

      &--is-focused {
        &:hover {
          color: ${({ theme }) => theme.colors.alabaster};
        }
      }
    }
    @media ${devices.tabletS} {
      &__value-container {
        padding: 0.8rem 1.6rem 0.8rem 1.6rem;
        height: 4.8rem;
      }
      &__indicators {
        height: 4.8rem;
      }
    }
  }
`;

CustomSelect.displayName = 'Select';
