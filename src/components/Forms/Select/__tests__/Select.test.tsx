import { render } from 'utils/testing';

import { Select } from '../Select';

describe('Components:Forms:Select', () => {
  it('renders Select component', () => {
    render(
      <Select id='test-id' options={[{ label: 'test', value: 'test' }]} />,
    );

    const testSelect = document.getElementById('test-id');

    expect(testSelect).toBeInTheDocument();
    expect(testSelect).toHaveAttribute('id', 'test-id');
  });
});
