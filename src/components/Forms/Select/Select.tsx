import { SelectProps } from './types';
import * as S from './styles';

export const Select = <SelectType,>({
  defaultInputValue,
  isClearable,
  options,
  value,
  onChange,
  ...rest
}: SelectProps<SelectType>) => {
  return (
    <S.CustomSelect
      classNamePrefix='basic-select'
      components={{ IndicatorSeparator: () => null }}
      defaultValue={defaultInputValue || options[0]}
      isClearable={isClearable || false}
      options={options}
      value={value}
      isSearchable={false}
      onChange={
        onChange
          ? (newValue: unknown) => onChange(newValue as SelectType)
          : () => {}
      }
      {...rest}
    />
  );
};
