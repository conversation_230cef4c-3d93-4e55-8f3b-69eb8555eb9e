import { Story, Meta } from '@storybook/react';

import { Select } from './Select';

export default {
  title: 'Components/Forms/Select',
  component: Select,
} as Meta;

const options = [
  { value: 'chocolate', label: 'Chocolate' },
  { value: 'strawberry', label: 'Strawberry' },
  { value: 'vanilla', label: 'Vanilla' },
];

const Template: Story = ({ args }) => (
  <>
    <Select options={options} {...args} />
  </>
);

export const Default = Template.bind({});
Default.args = {
  id: 'Select',
  isClearable: false,
  isSearchable: false,
  isChecked: false,
  defaultInputValue: '',
};
