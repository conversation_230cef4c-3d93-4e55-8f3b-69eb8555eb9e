import { BaseTextColorProps, BaseTextSizeProps } from 'theme';
import { Checkbox } from 'components/Forms/Checkbox';
import { Label } from 'components/Forms/Label';

import { CheckboxControlProps } from './types';
import * as S from './styles';

export const CheckboxControl = ({
  id,
  label,
  isChecked,
  onCheck,
  onClick,
  ...rest
}: CheckboxControlProps & BaseTextColorProps & BaseTextSizeProps) => {
  return (
    <S.Container>
      {label && (
        <Label htmlFor={id} onClick={onClick} {...rest}>
          {label}
        </Label>
      )}
      <Checkbox id={id} isChecked={isChecked} onCheck={onCheck} />
    </S.Container>
  );
};
