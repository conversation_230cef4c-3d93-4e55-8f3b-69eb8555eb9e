import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from 'utils/testing';
import { vi } from 'vitest';

import { CheckboxControl } from '../CheckboxControl';

describe('Components:Forms:Controls:CheckboxControl', () => {
  it('should render CheckboxControl component', () => {
    render(<CheckboxControl id='test-id' />);

    const testCheckboxControl = document.getElementById('test-id');

    expect(testCheckboxControl).toBeInTheDocument();
    expect(testCheckboxControl).toHaveAttribute('id', 'test-id');
  });

  it('should run onChange function when label is pressed', async () => {
    const mock = vi.fn();
    render(<CheckboxControl id='test-id' label='Test' onCheck={mock} />);

    expect(mock).not.toHaveBeenCalled();

    await userEvent.click(screen.getByTestId('checkbox-control'));

    expect(mock).toHaveBeenCalled();
  });
});
