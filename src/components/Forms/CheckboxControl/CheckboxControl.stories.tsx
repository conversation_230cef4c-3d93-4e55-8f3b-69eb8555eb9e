import { Story, Meta } from '@storybook/react';

import { CheckboxControl } from './CheckboxControl';

export default {
  title: 'Components/Forms/CheckboxControl',
  component: CheckboxControl,
} as Meta;

const Template: Story = ({ label, isChecked }) => (
  <div style={{ width: '25%' }}>
    <CheckboxControl label={label} id='checkbox' isChecked={isChecked} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  id: 'test_id',
  label: 'Checkbox',
  isChecked: false,
};
