import * as S from './styles';
import { ControlledInputProps } from './types';

export const ControlledInput = ({
  value,
  onChange,
  placeholder,
  errorText,
  isHidden = false,
}: ControlledInputProps) => {
  return (
    <S.Wrapper>
      <S.Input
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        $isHidden={isHidden}
      />
      {Boolean(errorText) && (
        <S.ErrorText $highlight $sizeXSmall>
          {errorText}
        </S.ErrorText>
      )}
    </S.Wrapper>
  );
};
