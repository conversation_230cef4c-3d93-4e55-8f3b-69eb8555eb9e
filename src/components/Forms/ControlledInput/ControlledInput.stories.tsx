import { Story, Meta } from '@storybook/react';

import { ControlledInput } from './ControlledInput';

export default {
  title: 'Components/Forms/ControlledInput',
  component: ControlledInput,
} as Meta;

const Template: Story = (props) => (
  <ControlledInput
    value={''}
    onChange={function (event: React.FormEvent<HTMLInputElement>): void {
      throw new Error('Function not implemented.');
    }}
    {...props}
  />
);

export const Default = Template.bind({});
Default.args = {
  id: 'test',
  disabled: false,
  value: 'Value',
};

export const WithError = Template.bind({});
WithError.args = {
  id: 'test',
  disabled: false,
  value: 'Value',
  isInputError: true,
  errorText: 'Wprowadzona wartość jest błędna',
};
