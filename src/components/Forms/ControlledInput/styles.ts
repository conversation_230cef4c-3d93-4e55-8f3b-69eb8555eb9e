import styled from 'styled-components';

import { Text } from 'components/Typography';

export const Input = styled.input.attrs(({ type, maxLength, autoFocus }) => ({
  type: type || 'text',
  maxLength: maxLength,
  autoFocus: autoFocus,
}))<{ $isHidden: boolean }>`
  background-color: ${({ theme }) => theme.colors.transparent};
  color: ${({ theme }) => theme.colors.alabaster};
  font-weight: 400;
  line-height: 1.2;
  transition: border-color 0.3s linear;
  width: 100%;
  border: 0;
  border-bottom: solid 0.3rem ${({ theme }) => theme.colors.white};
  font-size: ${({ theme }) => theme.fontSizes.small};
  padding: 0.5rem 0;
  margin: 2rem 0;
  font-family: ${({ $isHidden }) => ($isHidden ? 'Hidden' : 'inherit')};

  &::placeholder {
    font-family: Helvetica, Arial, sans-serif;
  }

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }

  &:disabled {
    border-color: ${({ theme }) => theme.colors.white50};
    color: ${({ theme }) => theme.colors.white50};
  }
  &:-webkit-autofill {
    box-shadow: 0 0 0 100rem ${({ theme }) => theme.colors.black} inset !important;
    -webkit-text-fill-color: ${({ theme }) =>
      theme.colors.alabaster} !important;
  }
`;

Input.displayName = 'Input';

export const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
`;

export const ErrorText = styled(Text)`
  position: absolute;
  top: 6rem;
`;
