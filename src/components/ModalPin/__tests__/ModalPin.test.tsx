import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import { ModalPin } from '../ModalPin';

describe('Components:ModalPin', () => {
  const dummyProps = {
    title: '<PERSON>rz<PERSON>ładowy tytuł',
    inputPlaceholder: 'Placeholder',
    inputValue: '',
    onInputChange: () => {},
    isError: false,
    errorMessage: '',
    denyText: 'Anuluj',
    submitText: 'Akceptuj',
    onClose: () => {},
    onSubmit: () => {},
    maxPinLength: 4,
  };

  it('should render ModalPin component with appropriate props', () => {
    render(<ModalPin {...dummyProps} />);

    expect(screen.getByText('Przykładowy tytuł')).toBeInTheDocument();
    expect(screen.getByText('Anuluj')).toBeInTheDocument();
    expect(screen.getByText('Akceptuj')).toBeInTheDocument();
  });
});
