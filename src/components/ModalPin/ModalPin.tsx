import { PrimaryButton } from 'components/Buttons/PrimaryButton';
import { H3, Text } from 'components/Typography';

import { ModalPinProps } from './types';
import * as S from './styles';

export const ModalPin = ({
  title,
  onSubmit,
  inputPlaceholder,
  inputValue,
  onInputChange,
  isError,
  errorMessage,
  information,
  onClose,
  denyText,
  submitText,
  maxPinLength,
}: ModalPinProps) => {
  return (
    <S.Container>
      <S.StyledH2>{title}</S.StyledH2>
      <S.Form onSubmit={onSubmit}>
        {isError && (
          <Text $highlight $sizeXSmall>
            {errorMessage}
          </Text>
        )}
        <S.StyledInput
          type='text'
          placeholder={inputPlaceholder}
          value={inputValue}
          onChange={onInputChange}
          name='pinModal'
          autoComplete='new-password'
          autoFocus
        />
        {Boolean(information) && (
          <S.StyledText $secondary $sizeXSmall>
            {information}
          </S.StyledText>
        )}
        <S.ButtonContainer>
          {Boolean(onClose) && Boolean(denyText) && (
            <PrimaryButton
              variant='ghostWhite'
              type='button'
              onClick={onClose}
              data-testid='prControl-cancelBtn'
            >
              <H3>{denyText}</H3>
            </PrimaryButton>
          )}

          <PrimaryButton
            variant='orange'
            type='submit'
            disabled={inputValue.length < maxPinLength || isError}
          >
            <H3>{submitText}</H3>
          </PrimaryButton>
        </S.ButtonContainer>
      </S.Form>
    </S.Container>
  );
};
