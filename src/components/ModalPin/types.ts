import { ChangeEventHandler, SyntheticEvent } from 'react';

export interface ModalPinProps {
  title: string;
  onSubmit: (event: SyntheticEvent) => void;
  inputPlaceholder: string;
  inputValue: string;
  onInputChange: ChangeEventHandler<HTMLInputElement>;
  information?: string;
  isError: boolean;
  errorMessage: string;
  onClose?: () => void;
  denyText?: string;
  submitText: string;
  maxPinLength: number;
}
