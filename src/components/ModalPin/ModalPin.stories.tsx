import { Story, Meta } from '@storybook/react';

import { ModalPin } from './ModalPin';
import React from 'react';

export default {
  title: 'Components/ModalPin',
  component: ModalPin,
  argTypes: { onSubmit: { action: 'clicked' } },
} as Meta;

const Template: Story = ({
  title,
  placeholder,
  onSubmit,
  isError,
  onInputChange,
  errorMessage,
  onClose,
  denyText,
  submitText,
  inputValue,
  information,
  maxPinLength,
}) => {
  return (
    <ModalPin
      title={title}
      onSubmit={onSubmit}
      inputPlaceholder={placeholder}
      inputValue={inputValue}
      onInputChange={onInputChange}
      isError={isError}
      errorMessage={errorMessage}
      onClose={onClose}
      denyText={denyText}
      submitText={submitText}
      information={information}
      maxPinLength={maxPinLength}
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  title: '<PERSON><PERSON><PERSON><PERSON><PERSON>wy tytuł',
  placeholder: 'Placeholder',
  onSubmit: (e) => {
    e.preventDefault();
  },
  isError: false,
  buttonSubmitText: 'Akceptuj',
  onDenied: 'Odrzuć',
  onInputChange: () => ({}),
  errorMessage: '',
  onClose: () => ({}),
  denyText: 'Odrzuć',
  submitText: 'Akceptuj',
  inputValue: '1234',
  maxPinLength: 4,
};

export const WithInformation = Template.bind({});
WithInformation.args = {
  title: 'Przykładowy tytuł',
  placeholder: 'Placeholder',
  onSubmit: (e) => {
    e.preventDefault();
  },
  isError: false,
  buttonSubmitText: 'Akceptuj',
  onDenied: 'Odrzuć',
  onInputChange: () => ({}),
  errorMessage: '',
  onClose: () => ({}),
  denyText: 'Odrzuć',
  submitText: 'Akceptuj',
  inputValue: '1234',
  information: 'Tutaj przykładowa informacja',
  maxPinLength: 4,
};

export const WithOneButton = Template.bind({});
WithOneButton.args = {
  title: 'Przykładowy tytuł',
  placeholder: 'Placeholder',
  onSubmit: (e) => {
    e.preventDefault();
  },
  isError: false,
  buttonSubmitText: 'Akceptuj',
  onInputChange: () => ({}),
  errorMessage: '',
  submitText: 'Akceptuj',
  inputValue: '1234',
  information: 'Tutaj przykładowa informacja',
  maxPinLength: 4,
};

export const WithInformationAndError = Template.bind({});
WithInformationAndError.args = {
  title: 'Przykładowy tytuł',
  placeholder: 'Placeholder',
  onSubmit: (e) => {
    e.preventDefault();
  },
  isError: true,
  buttonSubmitText: 'Akceptuj',
  onDenied: 'Odrzuć',
  onInputChange: () => ({}),
  errorMessage: 'Error!',
  onClose: () => ({}),
  denyText: 'Odrzuć',
  submitText: 'Akceptuj',
  inputValue: '1234',
  information: 'Tutaj przykładowa informacja',
  maxPinLength: 4,
};
