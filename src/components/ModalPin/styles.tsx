import styled from 'styled-components';

import { H2, Text } from 'components/Typography';
import { Input } from 'components/Forms/Input';
import { RenderLayer } from 'theme';

export const Container = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: ${({ theme }) => theme.colors.secondary};
  border: 0.1rem solid ${({ theme }) => theme.colors.doveGrayLight};
  border-radius: 0.8rem;
  width: 52rem;
  padding: 0 4.8rem 3.2rem 4.8rem;

  ${RenderLayer('modal')};
`;

export const StyledH2 = styled(H2)`
  margin: 2.4rem 0;
  color: ${({ theme }) => theme.colors.white80};
`;

export const StyledText = styled(Text)`
  white-space: pre-line;
`;

export const ButtonContainer = styled.div`
  margin-top: 5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  & > button:last-child {
    margin-left: auto;
  }
`;

export const Form = styled.form`
  position: relative;
`;

export const StyledInput = styled(Input)`
  border-top: 0;
  border-right: 0;
  border-left: 0;
  padding: 0.7rem 0;
  text-align: center;
  font-style: italic;
  margin-bottom: 2.4rem;
  &::placeholder {
    text-align: left;
  }
`;
