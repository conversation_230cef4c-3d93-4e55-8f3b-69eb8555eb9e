import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { Slider as SliderComponent } from '../Slider';

describe('Components:SliderComponent', () => {
  it('renders SliderComponent component', () => {
    render(
      <SliderComponent>
        <p>TEST</p>
        <p>TE</p>
      </SliderComponent>,
    );

    expect(screen.getByText('TEST')).toBeInTheDocument();
  });
});
