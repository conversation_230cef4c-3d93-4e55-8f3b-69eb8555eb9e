import styled, { css } from 'styled-components';

import { Info } from 'components/Tooltip/styles';
import { IconsTop } from 'components/Channel/styles';

export const Wrapper = styled.div`
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  user-select: none;

  button {
    visibility: hidden;
  }

  &:hover button {
    visibility: visible;
  }
`;

export const Container = styled.div<{ amountLines?: number }>`
  display: flex;
  ${({ amountLines }) =>
    amountLines &&
    css`
      flex-flow: column wrap;
      display: inline-flex;
      height: ${amountLines * 150}px;
    `}
`;

const ButtonWrapperCSS = css`
  position: absolute;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 4rem;
  height: 100%;
`;

export const NextButtonWrapper = styled.div<{ $transformXArrowRem?: number }>`
  ${ButtonWrapperCSS}
  right: ${({ $transformXArrowRem }) => `${$transformXArrowRem}rem`};
  transform: ${({ $transformXArrowRem }) =>
    !$transformXArrowRem && 'translateX(-1rem)'};
`;

export const PrevButtonWrapper = styled.div<{ $transformXArrowRem?: number }>`
  ${ButtonWrapperCSS}
  left:${({ $transformXArrowRem }) => `${$transformXArrowRem}rem`};
  transform: ${({ $transformXArrowRem }) =>
    !$transformXArrowRem && 'translateX(-50%)'};
`;

export const EmblaViewport = styled.div`
  overflow: hidden;

  &.is-draggable {
    cursor: move;
    cursor: grab;
  }

  &.is-dragging {
    cursor: grabbing;
  }

  ${IconsTop} ${Info} {
    transform: translateY(-12%);

    &::before {
      top: calc(16% - 0.4rem);
    }
  }
`;
