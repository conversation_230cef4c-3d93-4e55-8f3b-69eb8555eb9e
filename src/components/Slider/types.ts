import { BannerAction } from 'services/api/newApi/optional/banners/types';

export type OnSlideClick = {
  (id: string): void;
  (action: BannerAction): void;
};
export interface SliderOptionsProps {
  withButtons?: boolean;
  loop?: boolean;
  slidesToScroll?: number;
  align?: 'start' | 'end' | 'center';
  onSlideClick?: OnSlideClick;
  dragFree?: boolean;
}

export interface SliderProps extends SliderOptionsProps {
  children: JSX.Element[] | JSX.Element;
  amountLines?: number;
  reset?: string;
  autoScrollTime?: number;
  indexScrollTo?: number;
  isTwoDirectional?: boolean;
  transformXArrowRem?: number;
}

export interface SelectContextValue {
  handleSlideClick: OnSlideClick;
}

export type useSliderParams = Pick<
  SliderProps,
  | 'align'
  | 'loop'
  | 'slidesToScroll'
  | 'reset'
  | 'onSlideClick'
  | 'children'
  | 'dragFree'
  | 'autoScrollTime'
  | 'indexScrollTo'
  | 'isTwoDirectional'
>;

export enum ScrollDirection {
  ScrollX = 'x',
  ScrollY = 'y',
}
