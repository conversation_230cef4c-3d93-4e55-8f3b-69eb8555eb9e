import { Story, Meta } from '@storybook/react';

import { Image } from 'components/Image';

import { Slider } from './Slider';

export default {
  title: 'Components/Slider',
  component: Slider,
} as Meta;

const slidesArray = [0, 1, 2, 3, 4, 5, 6];

const Template: Story = (args) => (
  <Slider {...args}>
    {slidesArray.map((i) => (
      <div style={{ flex: '0 0 100%', height: '500px' }}>
        <Image src='https://www.fillmurray.com/700/500' />
      </div>
    ))}
  </Slider>
);

export const Default = Template.bind({});
Default.args = {
  withButtons: true,
  loop: false,
  slidesToScroll: 1,
};
