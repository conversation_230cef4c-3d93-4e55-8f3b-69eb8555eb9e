import { useCallback } from 'react';

import { ArrowButton } from 'components/Buttons/ArrowButton';

import { SliderProps } from './types';
import { SelectContext, useSlider } from './hooks';
import * as S from './styles';

export const Slider = ({
  align = 'start',
  children,
  withButtons = false,
  loop = false,
  slidesToScroll = 1,
  amountLines,
  reset,
  onSlideClick,
  autoScrollTime = undefined,
  indexScrollTo,
  isTwoDirectional = false,
  transformXArrowRem = 0,
}: SliderProps) => {
  const {
    viewportRef,
    scrollPrevious,
    previousButtonEnabled,
    nextButtonEnabled,
    scrollNext,
    handleSlideClick,
  } = useSlider({
    align,
    loop,
    slidesToScroll,
    reset,
    onSlideClick,
    children,
    autoScrollTime,
    indexScrollTo,
    isTwoDirectional,
  });

  const renderPrevAndNextButtons = useCallback(() => {
    if (previousButtonEnabled || nextButtonEnabled) {
      return (
        <>
          <S.PrevButtonWrapper $transformXArrowRem={transformXArrowRem}>
            <ArrowButton
              onClick={scrollPrevious}
              direction={'left'}
              disabled={!previousButtonEnabled}
            />
          </S.PrevButtonWrapper>
          <S.NextButtonWrapper $transformXArrowRem={transformXArrowRem}>
            <ArrowButton
              onClick={scrollNext}
              direction={'right'}
              disabled={!nextButtonEnabled}
            />
          </S.NextButtonWrapper>
        </>
      );
    }
    return null;
  }, [
    nextButtonEnabled,
    previousButtonEnabled,
    scrollNext,
    scrollPrevious,
    transformXArrowRem,
  ]);

  return (
    <S.Wrapper>
      <SelectContext.Provider value={{ handleSlideClick }}>
        <S.EmblaViewport ref={viewportRef}>
          <S.Container amountLines={amountLines}>{children}</S.Container>
        </S.EmblaViewport>
      </SelectContext.Provider>
      {withButtons && renderPrevAndNextButtons()}
    </S.Wrapper>
  );
};
