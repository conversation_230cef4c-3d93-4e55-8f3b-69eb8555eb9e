import useEmblaCarousel from 'embla-carousel-react';
import { createContext, useCallback, useEffect, useState } from 'react';
import { WheelGesturesPlugin } from 'embla-carousel-wheel-gestures';

import { ScrollDirection, SelectContextValue, useSliderParams } from './types';

export const SelectContext: React.Context<SelectContextValue> =
  createContext<SelectContextValue>({} as SelectContextValue);

export const useSlider = ({
  align,
  loop = false,
  reset,
  onSlideClick,
  slidesToScroll,
  dragFree,
  children,
  autoScrollTime,
  indexScrollTo,
  isTwoDirectional,
}: useSliderParams) => {
  const [scrollDirection, setScrollDirection] = useState<ScrollDirection>(
    ScrollDirection.ScrollY,
  );
  const [viewportRef, embla] = useEmblaCarousel(
    {
      align,
      loop,
      dragFree: dragFree ?? false,
      slidesToScroll: slidesToScroll ?? 'auto',
      containScroll: 'trimSnaps',
    },
    [
      WheelGesturesPlugin({
        forceWheelAxis: isTwoDirectional
          ? scrollDirection
          : ScrollDirection.ScrollX,
      }),
    ],
  );

  const [previousButtonEnabled, setPreviousButtonEnabled] = useState(false);
  const [nextButtonEnabled, setNextButtonEnabled] = useState(false);

  const scrollPrevious = useCallback(
    () => embla && embla.scrollPrev(),
    [embla],
  );
  const scrollNext = useCallback(() => embla && embla.scrollNext(), [embla]);
  const scrollToBegin = useCallback(
    () => embla && embla.scrollTo(0, true),
    [embla],
  );
  const scrollToSlide = useCallback(
    (slideIndex: number) => embla && embla.scrollTo(slideIndex, true),
    [embla],
  );

  const handleSlideClick = useCallback(
    (id: any) => {
      if (embla && onSlideClick) onSlideClick(id);
    },
    [embla, onSlideClick],
  );

  const onSelect = useCallback(() => {
    if (!embla) {
      return;
    }
    setPreviousButtonEnabled(embla.canScrollPrev());
    setNextButtonEnabled(embla.canScrollNext());
  }, [embla, setPreviousButtonEnabled, setNextButtonEnabled]);

  useEffect(() => {
    embla?.reInit();
  }, [embla, children]);

  useEffect(() => {
    if (!embla) return;
    embla.on('init', onSelect);
    embla.on('reInit', onSelect);
    embla.on('select', onSelect);
  }, [embla, onSelect, children]);

  useEffect(() => {
    if (indexScrollTo) {
      return scrollToSlide(indexScrollTo);
    }
    scrollToBegin();
  }, [indexScrollTo, reset, scrollToBegin, scrollToSlide]);

  useEffect(() => {
    embla && setPreviousButtonEnabled(embla.canScrollPrev());
    embla && setNextButtonEnabled(embla.canScrollNext());
  }, [embla]);

  useEffect(() => {
    if (autoScrollTime) {
      const sliderAutoScrollInterval = setInterval(() => {
        embla && embla.canScrollNext() && embla.scrollNext();
      }, autoScrollTime);
      return () => clearInterval(sliderAutoScrollInterval);
    }
  }, [autoScrollTime, embla]);

  useEffect(() => {
    const handleScroll = (event: WheelEvent) => {
      //detect if this is mouse scroll or touchpad scroll Y, mouse scroll Y and touchpad scroll Y always have deltaX = -0
      if (Math.abs(event.deltaX) !== 0) {
        return setScrollDirection(ScrollDirection.ScrollX);
      }
      return setScrollDirection(ScrollDirection.ScrollY);
    };

    window.addEventListener('wheel', handleScroll);

    return () => {
      window.removeEventListener('wheel', handleScroll);
    };
  }, [isTwoDirectional]);

  return {
    viewportRef,
    scrollPrevious,
    previousButtonEnabled,
    nextButtonEnabled,
    scrollNext,
    handleSlideClick,
  };
};
