import styled from 'styled-components';

import { LoaderStyled } from 'components/Loader';

export const Dot = styled.div<{ $color: string }>`
  background-color: ${({ $color }) => $color};
  width: 2.8rem;
  height: 2.8rem;
  border-radius: 50%;
`;

export const Wrapper = styled.div`
  width: 2.8rem;
  height: 2.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Loader = styled(LoaderStyled)`
  width: 2.8rem;
  height: 2.8rem;
`;
