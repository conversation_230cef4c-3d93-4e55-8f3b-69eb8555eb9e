import { StoryObj, Meta } from '@storybook/react';

import { StatusIndicator } from './StatusIndicator';
import { Status, StatusIndicatorProps } from './types';
import { IconCheckInCircle, IconCrossInCircle } from '../Icons';

export default {
  title: 'Components/StatusIndicator',
  component: StatusIndicator,
  argTypes: {
    status: {
      options: Object.keys(Status),
      control: { type: 'select' },
    },
  },
} as Meta<StatusIndicatorProps>;

export const Default: StoryObj<StatusIndicatorProps> = {
  args: {
    status: Status.SUCCESS,
    IconSuccess: IconCheckInCircle,
    IconError: IconCrossInCircle,
  },
};
