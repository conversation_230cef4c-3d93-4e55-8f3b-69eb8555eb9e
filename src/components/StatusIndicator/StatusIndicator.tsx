import { useTheme } from 'theme';

import * as S from './styles';
import { Status, StatusIndicatorProps } from './types';

export const StatusIndicator = ({
  status: indicatorStatus = Status.INACTIVE,
  IconSuccess,
  IconError,
}: StatusIndicatorProps) => {
  const theme = useTheme();

  const statusToIcon = {
    [Status.SUCCESS]: <IconSuccess color={theme.colors.green} />,
    [Status.ERROR]: <IconError color={theme.colors.red} />,
    [Status.INACTIVE]: <S.Dot $color={theme.colors.doveGray} />,
    [Status.LOADING]: <S.Loader />,
  };

  return <S.Wrapper>{statusToIcon[indicatorStatus]}</S.Wrapper>;
};
