import styled, { css } from 'styled-components';

import { PlaceholderType } from './types';

export const PlaceholderContainer = styled.div<{
  $placeholderType?: PlaceholderType;
  $photoRatio?: number;
}>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.mineShaft};
  border: 2px solid ${({ theme }) => theme.colors.doveGray};
  ${({ $placeholderType }) =>
    $placeholderType === 'actor' &&
    css`
      align-items: flex-end;
      padding-bottom: 1.2rem;
    `}
  ${({ $photoRatio }) =>
    $photoRatio &&
    css`
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      object-fit: cover;
    `}
`;

PlaceholderContainer.displayName = 'ImagePlaceholder';
