import { IconActorPlaceholder, IconVideoPlaceholder } from 'components/Icons';

import * as S from './styles';
import { ImagePlaceholderProps } from './types';

export const ImagePlaceholder = ({
  placeholderType = 'video',
  scale,
  photoRatio,
}: ImagePlaceholderProps) => {
  const selectPlaceholder = {
    actor: <IconActorPlaceholder scale={scale} />,
    video: <IconVideoPlaceholder scale={scale} />,
  };
  return (
    <S.PlaceholderContainer
      $placeholderType={placeholderType}
      $photoRatio={photoRatio}
    >
      {selectPlaceholder[placeholderType]}
    </S.PlaceholderContainer>
  );
};
