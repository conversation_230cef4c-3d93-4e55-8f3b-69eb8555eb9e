import { Story, Meta } from '@storybook/react';

import { ImagePlaceholder } from './ImagePlaceholder';
import { ImagePlaceholderProps } from './types';

export default {
  title: 'Components/ImagePlaceholder',
  component: ImagePlaceholder,
} as Meta;

const Template: Story<ImagePlaceholderProps> = ({ placeholderType }) => (
  <div style={{ width: '200px', height: '300px' }}>
    <ImagePlaceholder placeholderType={placeholderType} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  placeholderType: 'video',
};

export const ActorPlaceholder = Template.bind({});
ActorPlaceholder.args = {
  placeholderType: 'actor',
};
