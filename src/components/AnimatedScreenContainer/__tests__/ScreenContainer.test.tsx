import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { AnimatedScreenContainer } from '../AnimatedScreenContainer';

describe('Components:AnimatedScreenContainer', () => {
  it('renders AnimatedScreenContainer component', () => {
    render(<AnimatedScreenContainer>TEST</AnimatedScreenContainer>);

    expect(screen.getByText('TEST')).toBeInTheDocument();
  });
});
