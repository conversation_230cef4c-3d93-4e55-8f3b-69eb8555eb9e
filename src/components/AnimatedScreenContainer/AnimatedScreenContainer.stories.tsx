import { Story, Meta } from '@storybook/react';
import { useState } from 'react';

import { Text } from 'components/Typography';
import { AnimatedScreenContainer } from './AnimatedScreenContainer';

export default {
  title: 'Components/AnimatedScreenContainer',
  component: AnimatedScreenContainer,
} as Meta;

const Template: Story = ({ children, ...rest }) => {
  const [iterator, setIterator] = useState<number>(0);

  return (
    <AnimatedScreenContainer {...rest} key={`animated-text-${iterator}`}>
      <Text
        onClick={() => {
          setIterator(iterator + 1);
        }}
      >
        {children}
        {iterator}
      </Text>
    </AnimatedScreenContainer>
  );
};

export const Default = Template.bind({});
Default.args = {
  children: 'Hit to refresh: ',
};
