import { FC, PropsWithChildren } from 'react';
// eslint-disable-next-line import/no-duplicates
import { addDays, subDays } from 'date-fns';
// eslint-disable-next-line import/no-duplicates
import { pl } from 'date-fns/locale';
import DatePicker from 'react-datepicker';

import { IconCalendar } from 'components/Icons';
import { globalConfig } from 'services/config/config';

import { useDateTimePicker } from './hooks';
import { DataTimePickerProps } from './types';
import * as S from './styles';

export const DateTimePicker: FC<PropsWithChildren<DataTimePickerProps>> = ({
  setSelectedDateTime,
  selectedHour,
  setSelectedHour,
}) => {
  const { minDays, maxDays } = globalConfig.epgDatePicker;
  const { ref, createButtonText, handleChange } = useDateTimePicker(
    setSelectedDateTime,
    setSelectedHour,
    selectedHour,
  );

  return (
    <S.Container data-testid='date-picker' ref={ref}>
      <DatePicker
        minDate={subDays(new Date(), minDays)}
        maxDate={addDays(new Date(), maxDays)}
        selected={selectedHour}
        onChange={(date) => {
          handleChange(date);
        }}
        locale={pl}
        showTimeSelect
        timeCaption=''
        customInput={
          <S.PrimaryButtonStyled>
            <IconCalendar height={24} width={24} />
            <S.TextStyled>{createButtonText(selectedHour)}</S.TextStyled>
          </S.PrimaryButtonStyled>
        }
      />
    </S.Container>
  );
};
