import React from 'react';
import { Story, Meta } from '@storybook/react';

import { DateTimePicker } from './DateTimePicker';

export default {
  title: 'Components/DateTimePicker',
  component: DateTimePicker,
} as Meta;

const Template: Story = ({ selectedDateTime, setSelectedDateTime }) => (
  <DateTimePicker
    selectedDateTime={selectedDateTime}
    setSelectedDateTime={setSelectedDateTime}
  />
);

export const Default = Template.bind({});
Default.args = {
  setSelectedDateTime: () => {},
  selectedDateTime: new Date(),
};
