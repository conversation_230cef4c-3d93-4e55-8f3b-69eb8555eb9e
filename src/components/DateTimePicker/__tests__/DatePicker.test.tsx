import { screen } from '@testing-library/react';

import { render } from 'utils/testing';

import { DateTimePicker as DatePickerComponent } from '../DateTimePicker';
import { vi } from 'vitest';

describe('Components:DatePickerComponent', () => {
  it('renders DatePicker component', () => {
    render(
      <DatePickerComponent
        selectedHour={new Date()}
        setSelectedDateTime={vi.fn()}
        setSelectedHour={vi.fn()}
      />,
    );

    expect(screen.getByTestId('date-picker')).toBeInTheDocument();
  });
});
