// eslint-disable-next-line import/no-duplicates
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';
// eslint-disable-next-line import/no-duplicates
import { pl } from 'date-fns/locale';
import { useRef } from 'react';
import { useIntl } from 'react-intl';

import { getDateStart } from 'utils/dateUtils/dateUtils';

import { DATE_FORMAT, TIME_FORMAT } from './constants';
import { messages } from './messages';
import { DataTimePickerProps } from './types';

export const useDateTimePicker = (
  setSelectedDateTime: DataTimePickerProps['setSelectedDateTime'],
  setSelectedHour: DataTimePickerProps['setSelectedHour'],
  selectedHour: DataTimePickerProps['selectedHour'],
) => {
  const intl = useIntl();
  const dateToday = format(new Date(), DATE_FORMAT);
  const ref = useRef<HTMLDivElement>(null);

  const handleChange = (newDate: Date | null) => {
    if (!newDate) {
      return;
    }
    const isTheSameDate = selectedHour.getTime() === newDate.getTime();
    const formattedDate = format(newDate, DATE_FORMAT);

    const isNewDate =
      dateToday !== formattedDate &&
      format(selectedHour, TIME_FORMAT) === format(newDate, TIME_FORMAT);

    if (isTheSameDate) return;

    if (isNewDate) {
      const setEpgOnStart = () => {
        setSelectedDateTime(getDateStart(newDate));
        setSelectedHour(getDateStart(newDate));
      };
      return setEpgOnStart();
    }

    setSelectedDateTime(newDate);
    setSelectedHour(newDate);
  };

  const createButtonText = (date: Date): string => {
    if (isToday(date))
      return `${intl.formatMessage(messages.today)}, ${format(date, 'd MMMM', {
        locale: pl,
      })}`;
    if (isYesterday(date))
      return `${intl.formatMessage(messages.yesterday)}, ${format(
        date,
        'd MMMM',
        { locale: pl },
      )}`;
    if (isTomorrow(date))
      return `${intl.formatMessage(messages.tomorrow)}, ${format(
        date,
        'd MMMM',
        { locale: pl },
      )}`;
    return format(date, 'EEEE, d MMMM', { locale: pl });
  };

  return {
    ref,
    createButtonText,
    handleChange,
  };
};
