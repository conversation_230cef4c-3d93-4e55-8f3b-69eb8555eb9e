import 'react-datepicker/dist/react-datepicker.css';
import styled from 'styled-components';

import { Text } from 'components/Typography';
import { PrimaryButton } from 'components/Buttons/PrimaryButton';

import Clock from './Clock.svg';

export const PrimaryButtonStyled = styled(PrimaryButton)`
  display: flex;
  width: 100%;
  min-width: 35rem;
  justify-content: center;

  div {
    margin-right: 1.2rem;
  }
`;

export const Container = styled.div`
  margin: 0 2rem;
  display: flex;

  .react-datepicker-wrapper {
    position: static;
  }

  & > div {
    position: absolute;
    width: auto;
    max-width: 40rem;
    min-width: 35rem;

    .react-datepicker {
      min-width: 35rem;
      display: flex;
      justify-content: space-between;
      padding: 0;

      ${({ theme }) => theme.mixins.baseTextColor};
      font-size: ${({ theme }) => theme.fontSizes.small};
      background-color: ${({ theme }) => theme.colors.woodSmoke};
      border-color: ${({ theme }) => theme.colors.whiteLight};
      border-radius: 0.6rem;

      &__month {
        margin: 0;

        &-container {
          flex-basis: 80%;
        }
      }

      &__week {
        display: flex;
        justify-content: space-around;
      }

      &-time {
        &__header {
          color: ${({ theme }) => theme.colors.alabaster};
        }
      }

      &__header {
        background-color: ${({ theme }) => theme.colors.woodSmoke};
        height: 8rem;
        border-color: ${({ theme }) => theme.colors.whiteLight};

        &--time {
          background: url(${Clock}) center center no-repeat;
        }
      }

      &__time {
        background: ${({ theme }) => theme.colors.woodSmoke} url(${Clock})
          no-repeat center center;

        &-container {
          border-left-color: ${({ theme }) => theme.colors.whiteLight};
        }
      }

      &__day {
        display: flex;
        justify-content: center;
        align-items: center;

        width: 3.2rem;
        height: 3.2rem;

        color: ${({ theme }) => theme.colors.alabaster};

        &:hover,
        &--selected {
          background-color: ${({ theme }) => theme.colors.primary};
          border-radius: 50%;
        }

        &--selected {
          background-color: ${({ theme }) => theme.colors.primary};
        }

        &--disabled {
          color: ${({ theme }) => theme.colors.emperor75};
        }

        &-name {
          color: ${({ theme }) => theme.colors.alabaster};
          font-size: ${({ theme }) => theme.fontSizes.xSmall};
        }

        &-names {
          display: flex;
          justify-content: space-around;
        }
      }

      &__current-month {
        color: ${({ theme }) => theme.colors.alabaster};
        font-size: ${({ theme }) => theme.fontSizes.small};
        text-transform: capitalize;
        padding-bottom: 1.6rem;
      }

      &__time-list {
        &-item {
          background-color: ${({ theme }) => theme.colors.woodSmoke};

          &--selected {
            background-color: ${({ theme }) => theme.colors.primary} !important;
          }

          &:hover {
            background-color: ${({ theme }) => theme.colors.primary} !important;
          }
        }
      }

      .react-datepicker__triangle {
        display: none;
      }
    }
  }
`;

export const TextStyled = styled(Text)`
  white-space: nowrap;
`;

Container.displayName = 'DatePicker';
