import { useState } from 'react';

import { Loader } from 'components/Loader';
import { ImagePlaceholder } from 'components/ImagePlaceholder';

import { ImageProps } from './types';
import * as S from './styles';
import { IMAGE_PLACEHOLDER } from './constatns';

export function Image({
  alt,
  src,
  srcSet,
  photoRatio,
  placeholderType,
  placeholderScale,
  withoutLoader,
  onLoadHandler,
  ...rest
}: ImageProps): JSX.Element {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);

  const source = src ? (hasError ? IMAGE_PLACEHOLDER : src) : IMAGE_PLACEHOLDER;
  const shouldShowPlaceholder = !src || hasError;

  const handleOnLoad = () => {
    setIsLoading(false);
    onLoadHandler && onLoadHandler();
  };

  const handleOnError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <S.Container {...rest} $photoRatio={photoRatio}>
      {shouldShowPlaceholder ? (
        <ImagePlaceholder
          placeholderType={placeholderType}
          scale={placeholderScale}
          photoRatio={photoRatio}
        />
      ) : (
        <>
          {withoutLoader ? null : isLoading && <Loader />}
          <S.ImageTag
            alt={alt}
            $photoRatio={photoRatio}
            srcSet={srcSet}
            src={source}
            $isVisible={!isLoading}
            onLoad={handleOnLoad}
            onError={handleOnError}
          />
        </>
      )}
    </S.Container>
  );
}
