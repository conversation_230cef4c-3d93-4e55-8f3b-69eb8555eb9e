import styled, { css } from 'styled-components';

export const Container = styled.div<{
  $photoRatio?: number;
}>`
  background-color: ${({ theme }) => theme.colors.transparent};
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 3rem;
  min-width: 3rem;
  position: relative;
  text-align: center;
  width: 100%;

  ${({ $photoRatio }) =>
    $photoRatio &&
    css`
      padding-top: calc(${$photoRatio} * 100%);
    `}
`;

export const ImageTag = styled.img<{
  $isVisible?: boolean;
  $photoRatio?: number;
}>`
  ${({ theme }) => theme.mixins.visibility};
  width: 100%;
  height: 100%;

  ${({ $photoRatio }) =>
    $photoRatio &&
    css`
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      object-fit: cover;
    `}
`;

Container.displayName = 'ImageContainer';
ImageTag.displayName = 'ImageTag';
