import { Story, Meta } from '@storybook/react';

import { Image } from './Image';
import { ImageProps } from './types';

export default {
  title: 'Components/Image',
  component: Image,
} as Meta;

const Template: Story<ImageProps> = (args) => (
  <div style={{ width: '450px', height: '450px' }}>
    <Image {...args} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  src: 'https://www.fillmurray.com/500/600',
  alt: 'Placeholder Image',
};

export const Error = Template.bind({});
Error.args = {
  src: '',
  alt: 'Error',
};

export const Loading = Template.bind({});
Loading.args = {
  src: 'http://www.deelay.me/10000/https://www.fillmurray.com/500/600',
  alt: 'Long Loading Placeholder',
};
