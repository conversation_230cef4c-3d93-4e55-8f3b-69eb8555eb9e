import { Story, Meta } from '@storybook/react';

import { EmissionDate } from './EmissionDate';

export default {
  title: 'Components/EmissionDate',
  component: EmissionDate,
} as Meta;

const Template: Story = ({ dateOfEmission }) => (
  <>
    <EmissionDate dateOfEmission={dateOfEmission} sizeXLarge />
    <br />
    <EmissionDate dateOfEmission={dateOfEmission} sizeLarge />
    <br />
    <EmissionDate dateOfEmission={dateOfEmission} sizeMedium />
    <br />
    <EmissionDate dateOfEmission={dateOfEmission} sizeSmall />
    <br />
    <EmissionDate dateOfEmission={dateOfEmission} sizeXSmall />
    <br />
    <EmissionDate dateOfEmission={dateOfEmission} sizeXXSmall />
  </>
);

export const Default = Template.bind({});
Default.args = {
  dateOfEmission: '22.07.2021',
};
