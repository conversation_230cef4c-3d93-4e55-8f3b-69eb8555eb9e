import { useIntl } from 'react-intl';

import { Text } from 'components/Typography';

import { messages } from './messages';
import { EmissionDateProps } from './types';

export function EmissionDate({
  dateOfEmission,
  ...rest
}: EmissionDateProps): JSX.Element {
  const intl = useIntl();

  return (
    <Text $secondary {...rest}>
      {intl.formatMessage(messages.dateOfEmission)}
      {dateOfEmission}
    </Text>
  );
}
