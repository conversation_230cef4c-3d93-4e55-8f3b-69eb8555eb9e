import { Story, Meta } from '@storybook/react';

import { Image } from 'components/Image';

import { SliderWithControl } from './SliderWithControl';

export default {
  title: 'Components/SliderWithControl',
  component: SliderWithControl,
} as Meta;

const slidesArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];

const Template: Story = (args) => (
  <SliderWithControl
    renderOptions={() => {
      return (
        <div style={{ backgroundColor: 'blue', width: '100%', height: '40px' }}>
          Controls
        </div>
      );
    }}
    {...args}
  >
    {slidesArray.map((i) => (
      <div style={{ flex: '1 0 355px', height: '200px' }}>
        <Image src='https://www.fillmurray.com/355/200' />
      </div>
    ))}
  </SliderWithControl>
);

export const Default = Template.bind({});
Default.args = {
  withButtons: true,
  loop: false,
  slidesToScroll: 1,
};
