import { screen } from '@testing-library/react';
import { render } from 'utils/testing';

import { SliderWithControl } from '../SliderWithControl';

describe('Components:SliderWithControl', () => {
  it('should render SliderWithControl component', () => {
    render(
      <SliderWithControl>
        <p>TEST</p>
        <p>TEN</p>
      </SliderWithControl>,
    );
    expect(screen.getByText('TEST')).toBeInTheDocument();
  });

  it('should render SliderWithControl with controls', () => {
    render(
      <SliderWithControl renderOptions={() => <>CONTROLS</>}>
        <div style={{ flex: '0 0 100%', height: '500px' }} />
        <div style={{ flex: '0 0 100%', height: '500px' }} />
      </SliderWithControl>,
    );
    expect(screen.getByText('CONTROLS')).toBeInTheDocument();
  });
});
