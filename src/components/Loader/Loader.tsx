import { FC } from 'react';
import styled, { keyframes } from 'styled-components';

import { RenderLayer } from 'theme';

const spinner = keyframes`
  to {
    transform: rotate(360deg);
  }
`;

export const LoaderStyled = styled.div`
  animation: ${spinner} 0.8s linear infinite;
  border: 5px solid ${({ theme }) => theme.colors.dustyGray};
  border-radius: 50%;
  border-top-color: ${({ theme }) => theme.colors.primary};
  height: 60px;
  width: 60px;
`;

export const Container = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 120px;
  width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;

  ${RenderLayer('loader')}
`;

export const Loader: FC<React.PropsWithChildren<unknown>> = () => {
  return (
    <Container data-testid='loader'>
      <LoaderStyled />
    </Container>
  );
};

Loader.displayName = 'Loader';
