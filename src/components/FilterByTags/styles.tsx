import styled from 'styled-components';
import { motion } from 'framer-motion';

import { PrimaryButton } from 'components/Buttons/PrimaryButton';

import { IconFilterButtonStylesProps } from './types';

export const ClearInputButton = styled.button`
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  cursor: pointer;
`;

export const Container = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin: 0 1rem;
`;

export const InputWithButtonWrapper = styled.div``;

export const IconFilterButton = styled(
  PrimaryButton,
)<IconFilterButtonStylesProps>`
  display: ${({ $isInputVisible }) => ($isInputVisible ? 'none' : 'flex')};
  justify-content: center;
  width: 7.2rem;
  height: 4.8rem;
`;

export const DeleteButton = styled.button`
  background-color: transparent;
  border: none;
  cursor: pointer;
  position: absolute;
  right: 1rem;
`;

export const Input = styled.input`
  border: none;
  color: ${({ theme }) => theme.colors.white};
  font-size: ${({ theme }) => theme.fontSizes.small};
  outline: none;
  background-color: transparent;
  width: 100%;
  &::placeholder {
    color: ${({ theme }) => theme.colors.white75};
    font-style: italic;
  }
`;

export const InputWrapper = styled(motion.div).attrs(() => ({
  initial: { opacity: 0, scale: 0 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.15 },
}))`
  display: flex;
  align-items: center;
  position: relative;
  background-color: ${({ theme }) => theme.colors.primary};
  width: 18.4rem;
  height: 4.8rem;
  border-radius: 2.4rem;
  padding: 0 3.7rem 0 2rem;
`;

export const SingleTag = styled.div`
  position: relative;
  border-radius: 30px;
  height: 4.8rem;
  max-width: 19rem;
  padding: 1.4rem 1.6rem;
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.white};
  font-size: ${({ theme }) => theme.fontSizes.medium};
  display: flex;
  align-items: center;
  margin: 0.4rem 0.4rem 0 0.4rem;
  overflow: hidden;

  p {
    margin-right: 1.9rem;
    text-overflow: ellipsis;
    max-width: 13rem;
    overflow: hidden;
  }
`;

export const TagsWrapper = styled.div`
  display: flex;
  flex-wrap: wrap-reverse;
  flex-grow: 1;
  margin-left: 1rem;
`;
