import { Story, Meta } from '@storybook/react';
import { useArgs } from '@storybook/client-api';

import { FilterByTags } from './FilterByTags';

export default {
  title: 'Components/FilterByTags',
  component: FilterByTags,
} as Meta;

const Template: Story = () => {
  const [{ filterChannelsList }, updateArgs] = useArgs();

  const handleAddFilter = (filter: string) => {
    updateArgs({ filterChannelsList: [...filterChannelsList, filter] });
  };

  const handleDeleteSingleFilter = (index: number) => {
    const newArr = [...filterChannelsList];
    newArr.splice(index, 1);
    updateArgs({ filterChannelsList: newArr });
  };

  return (
    <FilterByTags
      filterChannelsList={filterChannelsList}
      deleteSingleFilter={handleDeleteSingleFilter}
      onAddFilter={handleAddFilter}
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  onAddFilter: () => {},
  deleteSingleFilter: () => {},
  filterChannelsList: [],
};
