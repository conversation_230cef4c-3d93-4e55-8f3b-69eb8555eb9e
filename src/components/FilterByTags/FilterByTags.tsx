import { FC, PropsWithChildren, useRef, useState } from 'react';
import { useIntl } from 'react-intl';

import { IconCloseSmall, IconFilter } from 'components/Icons';
import { useOutsideClick } from 'hooks';
import { useTheme } from 'theme';

import { FilterByTagsProps } from './types';
import { messages } from './messages';
import * as S from './styles';
import { MINIMUM_INPUT_LENGTH } from './constants';

export const FilterByTags: FC<PropsWithChildren<FilterByTagsProps>> = ({
  onAddFilter,
  filterChannelsList,
  deleteSingleFilter,
  canAddMoreFilters,
}) => {
  const { formatMessage } = useIntl();
  const [inputValue, setInputValue] = useState('');
  const [isInputActive, setIsInputActive] = useState(false);
  const filterContainerRef = useRef<HTMLDivElement>(null);
  const appTheme = useTheme();

  const handleSubmit = () => {
    if (inputValue.length > MINIMUM_INPUT_LENGTH) {
      onAddFilter(inputValue);
    }
    setInputValue('');
    setIsInputActive(false);
  };

  useOutsideClick(filterContainerRef, () => {
    handleSubmit();
  });

  const handleIconFilterClick = () => {
    setIsInputActive(true);
  };

  const tagsRender = filterChannelsList.map((tag, i) => (
    <S.SingleTag key={`${tag}-${i}}`}>
      <p>{tag}</p>
      <S.DeleteButton onClick={() => deleteSingleFilter(+i)}>
        <IconCloseSmall height={16} width={16} />
      </S.DeleteButton>
    </S.SingleTag>
  ));

  return (
    <S.Container data-testid='filer-by-tag'>
      <S.TagsWrapper data-testid='filer-by-tag-wrapper-tags'>
        {tagsRender}
      </S.TagsWrapper>
      <S.InputWithButtonWrapper ref={filterContainerRef}>
        {isInputActive && (
          <S.InputWrapper>
            <S.Input
              placeholder={formatMessage(messages.placeholder)}
              value={inputValue}
              onChange={(event) => setInputValue(event.target.value)}
              onKeyPress={(event) => event.code === 'Enter' && handleSubmit()}
              autoFocus
            />
            {inputValue && (
              <S.ClearInputButton
                data-testid='filer-by-tag-clear-button'
                onClick={() => setInputValue('')}
              >
                <IconCloseSmall />
              </S.ClearInputButton>
            )}
          </S.InputWrapper>
        )}
        <S.IconFilterButton
          data-testid='renderIconFilter'
          onClick={handleIconFilterClick}
          $isInputVisible={isInputActive}
          disabled={!canAddMoreFilters}
        >
          <IconFilter
            color={
              canAddMoreFilters
                ? appTheme.colors.white
                : appTheme.colors.white50
            }
          />
        </S.IconFilterButton>
      </S.InputWithButtonWrapper>
    </S.Container>
  );
};
