import { act, screen } from '@testing-library/react';
import { render } from 'utils/testing';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { FilterByTags } from '../FilterByTags';
import { setupMockServer } from 'services/api/mock/mock.server';
import { appConfigsMockHandlers } from 'services/api/mock/mocksIndex';

describe('Components:FilterByTags', () => {
  setupMockServer(...appConfigsMockHandlers);

  it('should render FilterByTags component with appropriate props', () => {
    render(
      <FilterByTags
        filterChannelsList={[]}
        deleteSingleFilter={vi.fn()}
        onAddFilter={vi.fn()}
        canAddMoreFilters={true}
      />,
    );

    expect(screen.getByTestId('renderIconFilter')).toBeInTheDocument();
  });

  it('should render FilterByTags with appropriate tags', () => {
    render(
      <FilterByTags
        filterChannelsList={['tvn']}
        deleteSingleFilter={vi.fn()}
        onAddFilter={vi.fn()}
        canAddMoreFilters={true}
      />,
    );

    expect(screen.getByText('tvn')).toBeInTheDocument();
  });

  it('should clear input after click ClearButton', async () => {
    render(
      <FilterByTags
        filterChannelsList={[]}
        deleteSingleFilter={vi.fn()}
        onAddFilter={vi.fn()}
        canAddMoreFilters={true}
      />,
    );
    const icon = screen.getByTestId('renderIconFilter');
    await userEvent.click(icon);
    const input = screen.getByPlaceholderText('Wpisz kanał');
    const inputValue = 'dowolna treść';
    await act(async () => {
      await userEvent.type(input, inputValue);
    });
    expect(input).toHaveValue(inputValue);
    const clearButton = await screen.findByTestId('filer-by-tag-clear-button');
    await userEvent.click(clearButton);
    expect(input).toHaveValue('');
  });
});
