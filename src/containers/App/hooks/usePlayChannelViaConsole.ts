import { useEffect } from 'react';

import { usePlayContent } from 'features/Player/Hook';
import { useLogger } from 'services/logger';
import { useAuthenticationStatus } from 'services/user';

export const usePlayChannelViaConsole = () => {
  const { playChannel } = usePlayContent();
  const { isAuthenticated } = useAuthenticationStatus();
  const { logger } = useLogger();

  useEffect(() => {
    window.playChannel = (channelNumber: number) => {
      if (!isAuthenticated) {
        return logger.error('Unauthorized to use this method');
      }

      const parsedChannel = channelNumber.toString();
      if (parsedChannel) {
        playChannel({ channelExtId: parsedChannel });
      } else {
        logger.error('Invalid channel number:', { parsedChannel });
      }
    };

    return () => {
      delete window.playChannel;
    };
  }, [logger, playChannel, isAuthenticated]);
};
