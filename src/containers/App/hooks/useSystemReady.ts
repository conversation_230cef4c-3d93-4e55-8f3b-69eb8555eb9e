import { useEffect } from 'react';

import { useCoreReadyQuery } from 'services/api/newApi/core/maintenance';
import { useLiveReadyQuery } from 'services/api/newApi/live/maintenance';
import {
  getErrorMessageForDownForMaintenance,
  getUserErrorType,
  isAxiosError,
  useErrorScreen,
} from 'services/error';
import { useLogger } from 'services/logger';
import { useHealthQuery } from 'services/api/oldApi/system';

import { SystemReadyState } from '../types';

export const useSystemReady = (): SystemReadyState => {
  const { logger } = useLogger();
  const { showErrorPage } = useErrorScreen();
  const { isFetching: isHealthFetching } = useHealthQuery();

  const {
    isFetching: isCoreReadyFetching,
    isError: isCoreReadyError,
    error: coreError,
  } = useCoreReadyQuery();

  const {
    isFetching: isLiveReadyFetching,
    isError: isLiveReadyError,
    error: liveError,
  } = useLiveReadyQuery();

  const isLoading =
    isHealthFetching || isCoreReadyFetching || isLiveReadyFetching;

  const hasErrors = isCoreReadyError || isLiveReadyError;

  useEffect(() => {
    if (!hasErrors) return;

    const mainError = coreError || liveError;
    const serviceName = coreError ? 'Core Service' : 'Live Service';

    logger.error(`${serviceName} ready check failed`, {
      error: mainError,
      status: isAxiosError(mainError) ? mainError.response?.status : 'unknown',
    });

    const errorType = getUserErrorType(mainError);
    const finalErrorType =
      typeof errorType === 'object' ? errorType.error : errorType;

    showErrorPage(finalErrorType);
  }, [hasErrors, coreError, liveError, logger, showErrorPage]);

  return {
    isLoading,
  };
};
