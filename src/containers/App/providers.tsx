import { FC, PropsWithChildren } from 'react';
import { CookiesProvider } from 'react-cookie';

import { AppProvidersProps } from 'containers/App/types';
import { ApiProvider } from 'services/api';
import { DeviceProvider } from 'services/device';
import { ErrorBoundary, ErrorScreenProvider } from 'services/error';
import { LoggerProvider } from 'services/logger';
import { TranslationsProvider } from 'services/translation';
import {
  AuthenticationStatusProvider,
  UserPinActivationProvider,
  UserProfileProvider,
  UserSessionProvider,
} from 'services/user';
import { ConfigProvider } from 'services/config';
import { GlobalLoaderProvider } from 'services/loader';
import { ModalPrControlProvider } from 'services/user/ModalPrControl/ModalPrControlContext';
import { UserSubscriptionTVEProvider } from 'services/user/ModalSubscriptionTVE/ModalSubsriptionTVEContext/ModalSubscriptionTVEContext';
import { PlayerTimeChannelLimitProvider } from 'features/Player/BasePlayer/Context';
import { TerminalUnregisterProcessProvider } from 'features/TerminalUnregisterProcess/Context';
import { PlayerInitialLanguagesProvider } from 'features/Player/Context';
import { UserNpvrProvider } from 'services/user/ModalNpvr';
import { RegionalTvProvider } from 'services/regionalTv';
import { LocalApiProvider } from 'services/localApi/LocalApiContext';
import { EpgTimeProvider } from 'features/Program/EpgControls/context';
import { PmsReporterProvider } from 'services/pmsReporter';

export const AppProviders: FC<PropsWithChildren<AppProvidersProps>> = ({
  logger,
  children,
}) => {
  return (
    <LoggerProvider logger={logger}>
      <GlobalLoaderProvider>
        <TranslationsProvider>
          <ErrorBoundary>
            <ErrorScreenProvider>
              <CookiesProvider>
                <AuthenticationStatusProvider>
                  <DeviceProvider>
                    <ApiProvider>
                      <ConfigProvider>
                        <PmsReporterProvider>
                          <UserSessionProvider>
                            <PlayerTimeChannelLimitProvider>
                              <ModalPrControlProvider>
                                <UserProfileProvider>
                                  <LocalApiProvider>
                                    <PlayerInitialLanguagesProvider>
                                      <RegionalTvProvider>
                                        <UserSubscriptionTVEProvider>
                                          <UserPinActivationProvider>
                                            <UserNpvrProvider>
                                              <TerminalUnregisterProcessProvider>
                                                <EpgTimeProvider>
                                                  {children}
                                                </EpgTimeProvider>
                                              </TerminalUnregisterProcessProvider>
                                            </UserNpvrProvider>
                                          </UserPinActivationProvider>
                                        </UserSubscriptionTVEProvider>
                                      </RegionalTvProvider>
                                    </PlayerInitialLanguagesProvider>
                                  </LocalApiProvider>
                                </UserProfileProvider>
                              </ModalPrControlProvider>
                            </PlayerTimeChannelLimitProvider>
                          </UserSessionProvider>
                        </PmsReporterProvider>
                      </ConfigProvider>
                    </ApiProvider>
                  </DeviceProvider>
                </AuthenticationStatusProvider>
              </CookiesProvider>
            </ErrorScreenProvider>
          </ErrorBoundary>
        </TranslationsProvider>
      </GlobalLoaderProvider>
    </LoggerProvider>
  );
};
