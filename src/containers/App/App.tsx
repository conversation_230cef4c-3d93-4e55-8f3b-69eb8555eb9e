import { FC } from 'react';

import { LazyComponentLoader } from 'components/LazyComponentLoader';
import { AppProviders } from 'containers/App/providers';
import { PlayerWrapper } from 'features/Player';
import { useRandomFeedbackToast } from 'features/Settings/Feedback';
import { Routes } from 'routes';
import { ApplicationThemeProvider } from 'theme';
import { AppLayoutModeProvider } from 'services/appLayoutMode';
import { ConsoleLogger } from 'services/logger';
import { CustomToastContainer } from 'components/Toast';
import { UserThemeProvider } from 'theme/UserThemeContext';

import { Container, InnerContainer } from './styles';
import { usePlayChannelViaConsole } from './hooks/usePlayChannelViaConsole';
import { useSystemReady } from './hooks/useSystemReady';

const GlobalUtilities = () => {
  usePlayChannelViaConsole();
  return null;
};

const AppShell: FC<{}> = () => {
  useRandomFeedbackToast();
  const { isLoading: isSystemLoading } = useSystemReady();

  if (isSystemLoading) {
    return <LazyComponentLoader />;
  }

  return (
    <div id='appShell'>
      <PlayerWrapper>
        <InnerContainer>
          <GlobalUtilities />
          <Routes />
        </InnerContainer>
      </PlayerWrapper>
    </div>
  );
};

export const App: FC = () => {
  const logger = new ConsoleLogger();

  return (
    <UserThemeProvider>
      <ApplicationThemeProvider>
        <AppLayoutModeProvider>
          <Container data-testid='app-container'>
            <AppProviders logger={logger}>
              <AppShell />
              <CustomToastContainer />
            </AppProviders>
          </Container>
        </AppLayoutModeProvider>
      </ApplicationThemeProvider>
    </UserThemeProvider>
  );
};
