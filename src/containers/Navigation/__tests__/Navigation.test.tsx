import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from 'utils/testing';

import { Navigation } from 'containers/Navigation';
import { RouteOriginProvider } from '../context';

describe('Components:Navigation', () => {
  it('should render Navigation component', () => {
    render(
      <RouteOriginProvider>
        <Navigation />
      </RouteOriginProvider>,
    );

    expect(screen.getByText(/kanały/i)).toBeInTheDocument();
  });

  it('should show navigation on mobile after click hamburger', async () => {
    render(
      <RouteOriginProvider>
        <Navigation />
      </RouteOriginProvider>,
    );

    const buttonHamburger = screen.getByTestId('hamburger-button');
    const navigationList = screen.getByTestId('navigation-list');

    expect(navigationList).toHaveStyle(`transform: translateX(-100%)`);
    await userEvent.click(buttonHamburger);
    expect(navigationList).toHaveStyle(`transform: translateX(0)`);
  });

  // hamburger test with function in navigation
  it('should hidden one line after click', async () => {
    render(
      <RouteOriginProvider>
        <Navigation />
      </RouteOriginProvider>,
    );

    const buttonHamburger = screen.getByTestId('hamburger-button');
    const hamburgerLines = screen.getByTestId('hamburger-lines');
    await userEvent.click(buttonHamburger);
    expect(hamburgerLines).toHaveStyle(`background-color: rgba(0, 0, 0, 0)`);
  });
});
