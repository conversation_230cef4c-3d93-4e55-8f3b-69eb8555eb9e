import styled from 'styled-components';

import { convertHexToRgbaString, devices } from 'theme';

export const Backdrop = styled.div<{ $isMenuOpen?: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: ${({ theme }) =>
    convertHexToRgbaString(theme.colors.black, 0.5)};
  display: ${({ $isMenuOpen }) => ($isMenuOpen ? 'block' : 'none')};

  @media ${devices.tabletS} {
    display: none;
  }
`;

export const Container = styled.nav`
  width: 100%;
`;

export const HamburgerWrapper = styled.div<{ $isMenuOpen?: boolean }>`
  height: 100%;
  display: flex;
  align-items: center;
  width: 100%;

  @media ${devices.tabletS} {
    display: none;
  }
`;

export const NavigationItem = styled.li<{ isHidden?: boolean }>`
  margin-bottom: 3.2rem;
  display: ${({ isHidden }) => (isHidden ? 'none' : 'block')};

  @media ${devices.tabletS} {
    margin-left: 4.8rem;
    margin-bottom: 0;
  }

  &:first-child {
    margin-left: 0;
  }
`;

export const NavigationList = styled.ul<{ $isMenuOpen: boolean }>`
  position: fixed;
  top: 6.4rem;
  left: 0;
  transform: ${({ $isMenuOpen }) =>
    $isMenuOpen ? 'translateX(0)' : 'translateX(-100%)'};
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  background: ${({ theme }) => theme.colors.black};
  height: 100vh;
  width: 22.9rem;
  padding: 1.4rem 2.4rem;
  transition: 0.3s;

  @media ${devices.tabletS} {
    position: static;
    flex-direction: row;
    align-items: center;
    background-color: inherit;
    margin: 0;
    padding: 0;
    width: auto;
    height: 100%;
    transform: translateX(0);
  }
`;

Backdrop.displayName = 'Backdrop';
Container.displayName = 'Container';
HamburgerWrapper.displayName = 'HamburgerWrapper';
NavigationItem.displayName = 'NavigationItem';
NavigationList.displayName = 'NavigationList';
