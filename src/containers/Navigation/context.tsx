import {
  createContext,
  FC,
  PropsWithChildren,
  useContext,
  useState,
} from 'react';
import { useLocation } from 'react-router-dom';

import { routes } from 'routes/routes-map';

import { RouteOriginContextValue } from './types';

const RouteOriginContext = createContext<RouteOriginContextValue>(
  {} as RouteOriginContextValue,
);

export const RouteOriginProvider: FC<PropsWithChildren<unknown>> = (props) => {
  const { children } = props;
  const [originRoute, setOriginRoute] = useState(routes.home);
  const [playerRoute, setPlayerRoute] = useState('');
  const { pathname } = useLocation();

  const handlePlayerRoute = () => {
    setPlayerRoute(pathname);
  };

  return (
    <RouteOriginContext.Provider
      value={{
        originRoute,
        playerRoute,
        setOriginRoute,
        setPlayerRoute,
        handlePlayerRoute,
      }}
    >
      {children}
    </RouteOriginContext.Provider>
  );
};

export const useRouteOrigin = (): RouteOriginContextValue => {
  const context = useContext(RouteOriginContext);
  if (context) {
    return context;
  }

  throw new Error('Component beyond RouteOriginContext');
};
