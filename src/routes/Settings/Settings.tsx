import { useOrdersQuery } from 'services/api/oldApi/billing';
import {
  Devices,
  DevSettings,
  DiagnosticsCenter,
  Profile,
  Purchases,
  QRLogin,
  Security,
  Service,
} from 'features/Settings';
import { globalConfig } from 'services/config/config';
import { useConfig } from 'services/config';
import { Feedback } from 'features/Settings/Feedback';
import { useCoreHouseholdQuery } from 'services/api/newApi/core/household';
import { Loader } from 'components/Loader';

import * as S from './styles';

export const Settings = () => {
  const { data: householdInfo, isFetching: isFetchingHouseholdInfo } =
    useCoreHouseholdQuery();
  const { getTechConfig } = useConfig();
  const {
    userInfo: { isPurchaseHistoryVisible },
  } = getTechConfig();

  const { data: orders = [] } = useOrdersQuery({ isContentTypeAdult: false });

  if (!householdInfo && isFetchingHouseholdInfo) {
    return <Loader />;
  }

  if (!householdInfo) {
    return null;
  }

  return (
    <S.AnimatedContainer>
      <S.Container data-testid='settings-screen-container'>
        <Profile householdInfo={householdInfo} />
        <Security householdInfo={householdInfo} />
        <Devices />
        {isPurchaseHistoryVisible && <Purchases orders={orders} />}
        <QRLogin />
        <Service />
        <Feedback />
        <DiagnosticsCenter />
        {globalConfig.isDev && <DevSettings />}
      </S.Container>
    </S.AnimatedContainer>
  );
};
