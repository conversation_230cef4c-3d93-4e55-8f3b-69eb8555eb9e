import { NormalizedRecording } from 'features/Recording/types';
import { ImagePath } from 'services/api/common/types';
import {
  Recording,
  RecordingListResponse,
  RecordingStatus,
  Series,
} from 'services/api/newApi/optional/npvr';
import { createProgramName } from 'utils/misc/createProgramName';
import { getPublicAssetUrl } from 'utils/url';

export const normalizeRecordings = (
  recordings: Array<Recording>,
  imagePath: ImagePath | undefined,
) => {
  return [
    ...recordings.map((recording) => ({
      recordingExtId: recording.recordingExtId,
      channelExtId: recording.channelExtId,
      name: createProgramName(recording.name || '', recording.episodeNumber),
      imageSrc: getPublicAssetUrl(`${imagePath}${recording.image}`),
      isSeries: false,
      startDateEpg: recording.startDateEpg,
      expirationDate: recording.expirationDate,
      status: recording.status,
      prLevel: recording.prLevel,
    })),
  ];
};

export const normalizeSeries = (
  series: Array<Series>,
  imagePath: ImagePath | undefined,
  status: RecordingStatus,
) => {
  return [
    ...series.map((recording) => ({
      recordingExtId: recording.newestRecording?.recordingExtId,
      recordingSeriesId: recording.recordingSeriesId,
      channelExtId: recording.channelExtId,
      name: recording.seriesName,
      imageSrc: getPublicAssetUrl(`${imagePath}${recording.image}`),
      isSeries: true,
      status: status,
      recordingSeason: recording.recordingSeason,
      seriesNewestRecording: recording.newestRecording,
      seriesOldestRecording: recording.oldestRecording,
    })),
  ];
};

export const normalizeRecordingsAndSeries = (
  status: RecordingStatus,
  recordingsResponse?: RecordingListResponse,
): Array<NormalizedRecording> => {
  const recordings = recordingsResponse?.recordingList ?? [];
  const series = recordingsResponse?.seriesList ?? [];
  return [
    ...normalizeRecordings(recordings, recordingsResponse?.imagePaths?.thumb),
    ...normalizeSeries(series, recordingsResponse?.imagePaths?.thumb, status),
  ];
};
