import { useEffect, useState } from 'react';

import {
  RecordingSeriesParams,
  RecordingSeriesResponse,
  useRecordingSeriesQuery,
} from 'services/api/newApi/optional/npvr';

export const useSelectedRecordingSeries = () => {
  const [selectedSeriesParams, setSelectedSeriesParams] =
    useState<RecordingSeriesParams>();
  const [selectedSeries, setSelectedSeries] =
    useState<RecordingSeriesResponse>();

  const { data: recordingSeries } =
    useRecordingSeriesQuery(selectedSeriesParams);

  const clearSelectedSeries = () => {
    setSelectedSeries(undefined);
    setSelectedSeriesParams(undefined);
  };

  useEffect(() => {
    if (recordingSeries) {
      setSelectedSeries(recordingSeries);
    }
  }, [recordingSeries]);

  return {
    selectedSeries: selectedSeries,
    setSelectedSeriesParams,
    clearSelectedSeries,
  };
};
