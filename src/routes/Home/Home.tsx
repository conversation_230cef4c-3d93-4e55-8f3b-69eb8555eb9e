import { FC } from 'react';

import {
  Banners,
  ContinueWatching,
  FavoritesChannels,
  FavoritesVods,
  Purchases,
  WatchedChannels,
} from 'features/Home/views';
import { UpdatePopup } from 'features/UpdatePopup';
import { AtvAppPopup } from 'components/AtvAppPopup';
import { UserMsgPopup } from 'components/UserMsgPopup';

import * as S from './styles';
import { WITH_UPDATE_POPUP } from './constants';
import { useShowAtvAppPromoPopup, useUserMsgPopup } from './hooks';

export const Home: FC = () => {
  const { isPopupOpen, handlePopupClose } = useShowAtvAppPromoPopup();
  const {
    isPopupOpen: isUserMsgPopupOpen,
    handlePopupClose: handleUserMsgPopupClose,
    userMsg,
  } = useUserMsgPopup();

  return (
    <S.AnimatedContainer>
      <S.Container data-testid='home-screen-container'>
        {WITH_UPDATE_POPUP && <UpdatePopup />}
        <AtvAppPopup isOpen={isPopupOpen} onClose={handlePopupClose} />
        <UserMsgPopup
          isOpen={isUserMsgPopupOpen}
          onClose={handleUserMsgPopupClose}
          userMsg={userMsg}
        />
        <Banners />
        <ContinueWatching />
        <FavoritesChannels />
        <WatchedChannels />
        <Purchases />
        <FavoritesVods />
      </S.Container>
    </S.AnimatedContainer>
  );
};
