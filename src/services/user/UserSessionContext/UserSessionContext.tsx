import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
} from 'react';

import {
  useAutoLoginMutation,
  useLoginMutation,
  useLogoutMutation,
} from 'services/api/newApi/auth';
import { globalConfig } from 'services/config/config';
import {
  getErrorMessageForDownForMaintenance,
  getUserErrorType,
  useErrorScreen,
} from 'services/error';
import { usePmsReporter } from 'services/pmsReporter';

import { UserSessionValue } from './types';

import { useAuthenticationStatus } from '../AuthenticationStatusContext/AuthenticationStatusContext';
import {
  deleteRefreshToken,
  setNewRefreshToken,
} from '../RefreshTokenHandlers';

export const UserSessionContext = createContext<UserSessionValue>(
  {} as UserSessionValue,
);

export const UserSessionProvider: FC<PropsWithChildren<unknown>> = ({
  children,
}) => {
  const { showErrorModal } = useErrorScreen();
  const { setAuthenticated } = useAuthenticationStatus();
  const { mutateAsync: loginAsync } = useLoginMutation();
  const { isLoading: isFullLoginLoading } = useLoginMutation();
  const { isSuccess: isFullLoginSuccess } = useLoginMutation();
  const { mutateAsync: logoutAsync } = useLogoutMutation();
  const { mutateAsync: autoLoginAsync } = useAutoLoginMutation();
  const { clearTimestampOfLastPmsReport } = usePmsReporter();

  const quickLogin = useCallback(
    async (code: string) => {
      const quickLoginResponse = await autoLoginAsync({
        autologinPin: code,
        deviceAppVersion: '',
        deviceFirmwareVersion: '',
        deviceInfo: '',
        deviceMac: '',
        deviceModel: '',
        deviceSn: '',
        deviceType: globalConfig.api.deviceType,
      });

      setAuthenticated(true);
      setNewRefreshToken(quickLoginResponse.data.refreshToken);
    },
    [autoLoginAsync, setAuthenticated],
  );

  const fullLogin = useCallback(
    async (username: string, code: string) => {
      const fullLoginResponse = await loginAsync({
        login: username,
        password: code,
      });

      setAuthenticated(true);
      setNewRefreshToken(fullLoginResponse.data.refreshToken);
    },
    [loginAsync, setAuthenticated],
  );

  const logout = useCallback(async (): Promise<void> => {
    setAuthenticated(false);
    deleteRefreshToken();

    try {
      await logoutAsync();
    } catch (error: unknown) {
      const errorType = getUserErrorType(error);
      const maintenanceMessage = getErrorMessageForDownForMaintenance(error);
      showErrorModal(errorType, maintenanceMessage || undefined);
    }

    clearTimestampOfLastPmsReport();
  }, [
    clearTimestampOfLastPmsReport,
    logoutAsync,
    setAuthenticated,
    showErrorModal,
  ]);

  return (
    <UserSessionContext.Provider
      value={{
        quickLogin,
        fullLogin,
        isFullLoginLoading,
        isFullLoginSuccess,
        logout,
      }}
    >
      {children}
    </UserSessionContext.Provider>
  );
};

export const useUserSession = (): UserSessionValue => {
  const context = useContext(UserSessionContext);

  if (context) {
    return context;
  }
  throw new Error('Component beyond UserSessionContext');
};
