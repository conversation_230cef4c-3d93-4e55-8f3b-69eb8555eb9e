import {
  ChannelId,
  ImagePath,
  ParentControlLevel,
  Timestamp,
} from 'services/api/common/types';

export type SearchType =
  | 'live'
  | 'vod'
  | 'actor'
  | 'director'
  | 'title'
  | 'description'
  | 'channelName';

export type SearchResultType =
  | 'live'
  | 'channel'
  | 'actor'
  | 'vod'
  | 'vod-catchup';

export interface SearchVersion {
  definition?: string;
  externalId?: string;
  serviceName?: string;
}

export interface SearchResult {
  channelExtId?: ChannelId;
  endDate?: Timestamp;
  hit: number;
  id: string;
  image: ImagePath;
  isCatchUpDisabled?: boolean;
  isRecordDisabled?: boolean;
  isStartOverDisabled?: boolean;
  isTveStreamDisabled?: boolean;
  isRegionalTv?: boolean;
  prLevel: ParentControlLevel;
  startDate?: Timestamp;
  supportedDevices?: string[];
  title: string;
  type: SearchResultType;
  versions?: SearchVersion[];
}

export interface SearchImage {
  imageName: string;
  imageUrl: ImagePath;
}

export interface SearchAction {
  actionType?: string;
  actionValue?: string;
  deviceCat: string;
}

export interface SearchInfo {
  actionList: SearchAction[];
  additionalKeyList: string[];
  description: string;
  endDateTimestamp?: Timestamp;
  imageList: SearchImage[];
  key: string;
  startDateTimestamp?: Timestamp;
  title: string;
}

export interface SearchRequest {
  query: string;
  searchType?: SearchType;
  maxCategoryResults?: number;
  hhTech?: string;
}

export interface SearchResponse {
  results: SearchResult[];
  searchInfoList?: SearchInfo[];
}
