import { useQuery } from 'react-query';

import { useUserProfile } from 'services/user';

import { ENDPOINTS } from './endpoints';
import { searchKeys } from './keys';
import { SearchRequest, SearchResponse } from './types';

import { oldApiClient } from '../client';

export const useSearchQuery = (params: SearchRequest) => {
  const { query } = params;
  const { userHhTech } = useUserProfile();

  return useQuery(
    searchKeys.result(params),
    async ({ signal }) => {
      const response = await oldApiClient.get<SearchRequest, SearchResponse>(
        ENDPOINTS.GET_SEARCH_DATA,
        { ...params, hhTech: userHhTech },
        signal,
      );

      return response.data.results;
    },
    {
      enabled: Boolean(query),
    },
  );
};
