import { createOldApiMock } from 'services/api/mock/mock.generator';
import { ENDPOINTS } from 'services/api/oldApi/search/endpoints';
import { SearchResponse } from 'services/api/oldApi/search/types';

import mockJSON from './searchData.mock.json';

export const searchDataResponseJSON = mockJSON as SearchResponse;

export const searchDataMockHandlers = createOldApiMock<SearchResponse>(
  'get',
  ENDPOINTS.GET_SEARCH_DATA.url,
  searchDataResponseJSON,
);

export const searchDataMockHandlersWithSpy = (spy: jest.Mock) =>
  createOldApiMock<SearchResponse>(
    'get',
    ENDPOINTS.GET_SEARCH_DATA.url,
    searchDataResponseJSON,
    spy,
  );
