import { rest } from 'msw';

// Mock handler dla testowania DOWN_FOR_MAINTENANCE błędów
export const maintenanceErrorHandlers = [
  // Mock dla nowego API (G000015)
  rest.get('*/api/v2/*', (req, res, ctx) => {
    return res(
      ctx.status(503),
      ctx.json({
        error: 'G000015',
        errCode: 'DOWN_FOR_MAINTENANCE',
        errMsg: 'Trwają prace serwisowe. System będzie znów dostępny po zakończeniu prac.',
      })
    );
  }),

  // Mock dla starego API (DOWN_FOR_MAINTENANCE)
  rest.get('*/api/v1/*', (req, res, ctx) => {
    return res(
      ctx.status(503),
      ctx.json({
        errCode: 'DOWN_FOR_MAINTENANCE',
        errMsg: 'Trwają prace serwisowe. System będzie znów dostępny po zakończeniu prac.',
      })
    );
  }),

  // Mock dla konkretnego endpointu - np. household
  rest.get('*/core/household', (req, res, ctx) => {
    return res(
      ctx.status(503),
      ctx.json({
        error: 'G000015',
        errCode: 'DOWN_FOR_MAINTENANCE', 
        errMsg: 'Trwają prace serwisowe. System będzie znów dostępny po zakończeniu prac.',
      })
    );
  }),
];
