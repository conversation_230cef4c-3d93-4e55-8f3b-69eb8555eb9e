import { rest, setupWorker } from 'msw';

import { maintenanceErrorHandlers } from './mocksIndex';
declare global {
  interface Window {
    msw: any;
  }
}

const mockResponse = rest.get(
  'gpapi/v2/vod/purchases/user-purchases',
  (req, res, ctx) => {
    return res(ctx.status(500));
  },
);

export const worker = setupWorker(mockResponse, ...maintenanceErrorHandlers);

window.msw = {
  worker,
  rest,
};
