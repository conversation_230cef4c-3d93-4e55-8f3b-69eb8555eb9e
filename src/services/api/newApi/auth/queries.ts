import {
  QueryClient,
  useMutation,
  useQuery,
  useQueryClient,
} from 'react-query';

import { globalConfig } from 'services/config/config';
import { myzoneKeys } from 'services/api/oldApi/myzone/keys';
import { authKeys, loginMetaKey } from 'services/api/newApi/auth/keys';
import { householdKeys } from 'services/api/newApi/core/household';
import { deviceKeys } from 'services/api/newApi/core/device';
import {
  getErrorMessageForDownForMaintenance,
  getUserErrorType,
  useErrorScreen,
} from 'services/error';
import { UserTheme, useUserTheme } from 'theme/UserThemeContext';
import { tvguideKeys } from 'services/api/newApi/core/tvguide';
import { oldApiClient } from 'services/api/oldApi/client';

import { ENDPOINTS } from './endpoints';
import {
  AutoLoginData,
  AutoLoginParams,
  LoginParams,
  LoginResponse,
} from './types';

import { newApiClient } from '../client';

const invalidateQueriesDependingOnUser = (queryClient: QueryClient) => {
  return Promise.all([
    queryClient.invalidateQueries(myzoneKeys.all),
    queryClient.invalidateQueries(tvguideKeys.all),
    queryClient.invalidateQueries(householdKeys.all),
    queryClient.invalidateQueries(deviceKeys.all),
  ]);
};

export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  const params = {
    deviceCat: globalConfig.api.deviceCat,
    deviceType: globalConfig.api.deviceType,
  };

  return useMutation(
    async (data: LoginParams) => {
      return await newApiClient.post<LoginParams, LoginResponse, {}>(
        ENDPOINTS.POST_FULL_LOGIN,
        data,
        params,
      );
    },
    {
      onSuccess: () => invalidateQueriesDependingOnUser(queryClient),
      meta: loginMetaKey,
    },
  );
};

export const useAutoLoginMutation = () => {
  const queryClient = useQueryClient();
  const { VITE_REACT_APP_PROXIMITY_IP, VITE_REACT_APP_PROXIMITY_PORT } =
    import.meta.env;

  const params = {
    'deviceCat': globalConfig.api.deviceCat,
    'deviceType': globalConfig.api.deviceType,
    'Proximity-Ip': VITE_REACT_APP_PROXIMITY_IP,
    'Proximity-Port': VITE_REACT_APP_PROXIMITY_PORT,
  };

  return useMutation(
    async (data: AutoLoginData) => {
      return await newApiClient.post<
        AutoLoginData,
        LoginResponse,
        AutoLoginParams
      >(ENDPOINTS.POST_LOGIN_AUTO, data, params);
    },
    {
      onSuccess: () => invalidateQueriesDependingOnUser(queryClient),
      meta: loginMetaKey,
    },
  );
};

export const useLogoutMutation = () => {
  const queryClient = useQueryClient();
  const { setUserTheme } = useUserTheme();

  return useMutation(
    async () => {
      const response = await newApiClient.get<{}, {}>(ENDPOINTS.GET_LOGOUT);

      newApiClient.updateHeaders();
      oldApiClient.updateHeaders();

      return response.data;
    },
    {
      onSuccess: () => {
        setUserTheme(UserTheme.DEFAULT);
        return Promise.all([queryClient.removeQueries({ inactive: true })]);
      },
    },
  );
};

export const useQRCodeQuery = () => {
  const { showErrorModal } = useErrorScreen();

  return useQuery(
    authKeys.qrCode(),
    async ({ signal }) => {
      const response = await newApiClient.get(
        ENDPOINTS.GET_LOGIN_QR_CODE,
        {},
        signal,
        'blob',
      );

      return URL.createObjectURL(response.data as Blob);
    },
    {
      useErrorBoundary: false,
      onError: (error) => {
        const errorType = getUserErrorType(error);
        const maintenanceMessage = getErrorMessageForDownForMaintenance(error);
        showErrorModal(errorType, maintenanceMessage || undefined);
      },
    },
  );
};
