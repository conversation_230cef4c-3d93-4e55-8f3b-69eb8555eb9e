import { useQuery } from 'react-query';

import { newApiClient } from 'services/api/newApi/client';

import { liveMaintenanceKeys } from './keys';
import { LiveReadyResponse } from './types';
import { ENDPOINTS } from './endpoints';

export const useLiveReadyQuery = () => {
  return useQuery(
    liveMaintenanceKeys.liveReady(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, LiveReadyResponse>(
        ENDPOINTS.GET_LIVE_READY,
        {},
        signal,
      );
      return response.data;
    },
    {
      useErrorBoundary: false,
      retry: 2,
      retryDelay: 2000,
    },
  );
};
