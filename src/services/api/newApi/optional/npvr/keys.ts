import {
  RecordingDetailsParams,
  RecordingsByChannelParams,
  RecordingsByDateParams,
  RecordingSeriesParams,
  RecordingsRequest,
} from './types';

export const npvrKeys = {
  all: ['npvr'] as const,

  recordings: (params: RecordingsRequest) =>
    [...npvrKeys.all, 'recordings', { ...params }] as const,

  recordingDetails: (params: RecordingDetailsParams) =>
    [...npvrKeys.all, 'recording-details', { ...params }] as const,

  recordingSeries: (params?: RecordingSeriesParams) =>
    [...npvrKeys.all, 'recording-series', { ...params }] as const,

  recordingsByChannel: (params?: RecordingsByChannelParams) =>
    [...npvrKeys.all, 'recordings-by-channel', { ...params }] as const,

  recordingsByDate: (params?: RecordingsByDateParams) =>
    [...npvrKeys.all, 'recordings-by-date', { ...params }] as const,
};
