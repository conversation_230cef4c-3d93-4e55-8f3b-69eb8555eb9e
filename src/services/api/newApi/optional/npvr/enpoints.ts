import { EndpointRecordType } from 'services/api/types';

import { API_VERSION } from '../../config';

export const ENDPOINTS = EndpointRecordType()({
  GET_RECORDING: {
    method: 'GET',
    url: '/optional/npvr/recording',
    acceptVersion: API_VERSION.V1,
  },

  GET_RECORDINGS: {
    method: 'GET',
    url: '/optional/npvr/recordings',
    acceptVersion: API_VERSION.V1,
  },

  GET_RECORDINGS_BY_CHANNEL: {
    method: 'GET',
    url: '/optional/npvr/recordings/by-channels',
    acceptVersion: API_VERSION.V1,
    withDisabledHTTPCache: true,
    RQCacheTime: 900 * 1000,
    RQStaleTime: 900 * 1000,
  },

  GET_RECORDINGS_BY_DATE: {
    method: 'GET',
    url: '/optional/npvr/recordings/by-date',
    acceptVersion: API_VERSION.V1,
    withDisabledHTTPCache: true,
    RQCacheTime: 900 * 1000,
    RQStaleTime: 900 * 1000,
  },

  GET_SERIES: {
    method: 'GET',
    url: '/optional/npvr/series',
    acceptVersion: API_VERSION.V1,
  },

  DELETE_RECORDING: {
    method: 'DELETE',
    url: '/optional/npvr/recording',
    acceptVersion: API_VERSION.V1,
  },

  POST_RECORDING: {
    method: 'POST',
    url: '/optional/npvr/recording',
    acceptVersion: API_VERSION.V1,
    contentTypeVersion: API_VERSION.V1,
  },

  DELETE_SERIES: {
    method: 'DELETE',
    url: '/optional/npvr/series',
    acceptVersion: API_VERSION.V1,
  },

  POST_SERIES: {
    method: 'POST',
    url: '/optional/npvr/series',
    acceptVersion: API_VERSION.V1,
    contentTypeVersion: API_VERSION.V1,
  },
});
