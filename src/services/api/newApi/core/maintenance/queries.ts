import { useQuery } from 'react-query';

import { newApiClient } from 'services/api/newApi/client';

import { coreMaintenanceKeys } from './keys';
import { CoreReadyResponse } from './types';
import { ENDPOINTS } from './endpoints';

export const useCoreReadyQuery = () => {
  return useQuery(
    coreMaintenanceKeys.ready(),
    async ({ signal }) => {
      const response = await newApiClient.get<{}, CoreReadyResponse>(
        ENDPOINTS.GET_CORE_READY,
        {},
        signal,
      );
      return response.data;
    },
    {
      useErrorBoundary: false,
      retry: 2,
      retryDelay: 2000,
    },
  );
};
