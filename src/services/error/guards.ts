import { AxiosError } from 'axios';

import { RequireField } from 'utils/types';

import { APIError, ErrorCodeMessages, NewAPIError } from './types';

const HTTP_HEADER_ERROR_CODES = [406, 415];

export const isAxiosError = (error: any): error is AxiosError<unknown> => {
  return Boolean(error?.isAxiosError) && typeof error.config == 'object';
};

export const isOldServiceError = (
  error: unknown,
): error is RequireField<AxiosError<APIError>, 'response'> => {
  return (
    isAxiosError(error) &&
    !!error.response?.data &&
    typeof (error.response.data as any).errCode === 'string' &&
    typeof error.response.data === 'object' &&
    !('error' in error.response.data)
  );
};

export const isNewServiceError = (
  error: unknown,
): error is RequireField<AxiosError<NewAPIError>, 'response'> => {
  return (
    isAxiosError(error) &&
    !!error.response?.data &&
    typeof (error.response.data as any).error === 'string'
  );
};

export const hasErrorCode = (status: number) => (error: any) =>
  isAxiosError(error) && error.response?.status === status;

export const isServerError = (error: AxiosError) => {
  if (!error.response) {
    return false;
  }

  const status = error.response.status;
  return status >= 500;
};

export const isRequestHeaderError = (error: AxiosError) => {
  if (!error.response) {
    return false;
  }

  const status = error.response.status;

  return HTTP_HEADER_ERROR_CODES.includes(status);
};

export const isClientError = (error: AxiosError) => {
  if (!error.response) {
    return false;
  }
  const status = error.response.status;
  return status >= 400 && status < 500;
};

export const hasCodeMessage = (code: string) => (error: any) =>
  isOldServiceError(error) && error.response?.data.errCode === code;

export const isUnauthorizedError = hasErrorCode(401);
export const isQuickLoginInvalidPin =
  hasErrorCode(401) && hasCodeMessage(ErrorCodeMessages.AUTOLOGIN_INVALID_PIN);
export const isForbiddenError = hasErrorCode(403);
export const isInternalServerError = hasErrorCode(403);
export const isNotFoundError = hasErrorCode(404);
export const isBadGatewayError = hasErrorCode(502);
export const isFullMaintenanceError = hasErrorCode(503);
export const isReadOnlyMaintenanceError = hasErrorCode(555);

export const isMaxSlotsExceededError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(412)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000404' || errorKey === 'G000403') {
      return true;
    }
  }
  return false;
};

export const isHouseholdSuspendError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(402)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000008') {
      return true;
    }
  }
  if (isOldServiceError(error) && hasErrorCode(402)(error)) {
    return true;
  }
  return false;
};

export const isMaxSlotsRemovalExceededError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(412)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000405') {
      return true;
    }
  }
  return false;
};

export const isVerificationLockedError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(429)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000500') {
      return true;
    }
  }
  return false;
};

export const isTargetConflictError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(409)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000016') {
      return true;
    }
  }
  return false;
};

export const isDownForMaintenanceError = (error: unknown) => {
  if (isNewServiceError(error) && hasErrorCode(503)(error)) {
    const errorKey = error?.response?.data.error;
    if (errorKey === 'G000015') {
      return true;
    }
  }

  if (isOldServiceError(error) && hasErrorCode(503)(error)) {
    const errorCode = error?.response?.data.errCode;
    if (errorCode === 'DOWN_FOR_MAINTENANCE') {
      return true;
    }
  }
  return false;
};
