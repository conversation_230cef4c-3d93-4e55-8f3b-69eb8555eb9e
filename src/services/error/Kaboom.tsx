import React, { FC, PropsWithChildren, useEffect } from 'react';

import { useErrorScreen } from './ErrorScreenContext';
import {
  getErrorMessageForDownForMaintenance,
  getUserErrorType,
} from './messages';
import { KaboomAsyncProps, KaboomSyncProps } from './types';

const throwAlways = () => true;

export const KaboomSync: React.FC<React.PropsWithChildren<KaboomSyncProps>> = (
  props,
) => {
  const { shouldFail = throwAlways, notify, children } = props;

  if (shouldFail()) {
    notify?.('KaboomSync: failed');
    throw new Error('Kaboom!');
  }

  notify?.('KaboomSync: passed');

  return <>{children}</>;
};

export const KaboomAsync: FC<PropsWithChildren<KaboomAsyncProps>> = ({
  kaboomFn,
  asModal = false,
}) => {
  const { showErrorPage, showErrorModal } = useErrorScreen();

  useEffect(() => {
    kaboomFn().catch((error: unknown) => {
      const errorType = getUserErrorType(error);
      const maintenanceMessage = getErrorMessageForDownForMaintenance(error);

      if (asModal) {
        showErrorModal(errorType, maintenanceMessage || undefined);
      } else {
        showErrorPage(errorType);
      }
    });
  }, [asModal, kaboomFn, showErrorModal, showErrorPage]);

  return <div>this will blink before error will be visible</div>;
};
