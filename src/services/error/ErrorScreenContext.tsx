import {
  createContext,
  FC,
  PropsWithChildren,
  useCallback,
  useContext,
  useState,
} from 'react';

import { ErrorPage } from './ErrorPage';
import { ErrorModal } from './ErrorModal';
import {
  ErrorScreenContextValue,
  ErrorScreenProviderProps,
  ErrorScreenState,
  ErrorType,
  ErrorWithDetails,
  Layouts,
  RenderErrorProps,
} from './types';

export const ErrorScreenContext = createContext<
  ErrorScreenContextValue | undefined
>(undefined);

export const ErrorScreenProvider: FC<
  PropsWithChildren<ErrorScreenProviderProps>
> = ({ children }) => {
  const [errorMessage, setErrorMessage] = useState('');
  const [errorDetails, setErrorDetails] = useState('');
  const [errorScreen, setErrorScreen] = useState<ErrorScreenState>({
    layout: 'NO_ERROR',
    errorType: '',
  });
  const showError = useCallback((screen: ErrorScreenState) => {
    setErrorScreen(screen);
  }, []);

  const formatDetails = (status: number, code: string) => {
    return `(${status}${Boolean(code) && ` ${code}`})`;
  };

  const handleErrorWithDetails = useCallback(
    (errorType: ErrorWithDetails) => {
      showError({
        layout: 'ERROR_MODAL',
        errorType: errorType.error,
      });
      return setErrorDetails(
        formatDetails(errorType.errorStatus, errorType.errorCode),
      );
    },
    [showError],
  );

  const showErrorPage = useCallback(
    (errorType: ErrorType | ErrorWithDetails) => {
      if (typeof errorType === 'object') {
        return handleErrorWithDetails(errorType);
      }
      showError({ layout: 'ERROR_PAGE', errorType });
    },
    [handleErrorWithDetails, showError],
  );

  const showErrorModal = useCallback(
    (errorType: ErrorType | ErrorWithDetails, configMessage?: string) => {
      if (typeof errorType === 'object') {
        return handleErrorWithDetails(errorType);
      }

      if (configMessage) {
        setErrorMessage(configMessage);
        return showError({
          layout: 'ERROR_MODAL',
          errorType,
        });
      }
      return showError({ layout: 'ERROR_MODAL', errorType });
    },
    [handleErrorWithDetails, showError],
  );

  const clear = useCallback(() => {
    setErrorScreen({ layout: 'NO_ERROR', errorType: '' });
    setErrorDetails('');
    setErrorMessage('');
  }, []);

  const renderErrorScreen: RenderErrorProps = {
    [Layouts.ERROR_MODAL]: (
      <>
        {children}
        <ErrorModal
          isOpen={true}
          errorType={errorScreen.errorType as ErrorType}
          onClose={clear}
          errorDetails={errorDetails}
          errorMessage={errorMessage}
        />
      </>
    ),
    [Layouts.ERROR_PAGE]: (
      <ErrorPage
        errorType={errorScreen.errorType as ErrorType}
        errorDetails={errorDetails}
        errorMessage={errorMessage}
      />
    ),
    [Layouts.NO_ERROR]: <>{children}</>,
  };

  const content = renderErrorScreen[errorScreen.layout];

  return (
    <ErrorScreenContext.Provider
      value={{
        showErrorPage,
        showErrorModal,
        clear,
      }}
    >
      {content}
    </ErrorScreenContext.Provider>
  );
};

export const useErrorScreen = () => {
  const context = useContext(ErrorScreenContext);

  if (context) {
    return context;
  }

  throw new Error('Component beyond ErrorScreenContext');
};
