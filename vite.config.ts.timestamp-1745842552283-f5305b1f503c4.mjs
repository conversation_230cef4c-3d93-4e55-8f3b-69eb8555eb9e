// vite.config.ts
import { defineConfig, loadEnv } from "file:///Users/<USER>/Desktop/Orange/ypt-frontend-2/node_modules/vite/dist/node/index.js";
import react from "file:///Users/<USER>/Desktop/Orange/ypt-frontend-2/node_modules/@vitejs/plugin-react/dist/index.mjs";
import viteTsconfigPaths from "file:///Users/<USER>/Desktop/Orange/ypt-frontend-2/node_modules/vite-tsconfig-paths/dist/index.mjs";
import browserslistToEsbuild from "file:///Users/<USER>/Desktop/Orange/ypt-frontend-2/node_modules/browserslist-to-esbuild/src/index.js";
import path from "path";
import fs from "fs";
var __vite_injected_original_dirname = "/Users/<USER>/Desktop/Orange/ypt-frontend-2";
var createViteConfig = ({ mode }) => {
  const envConfig = { ...process.env, ...loadEnv(mode, process.cwd(), "") };
  const generateSourceMap = envConfig.GENERATE_SOURCEMAP === "true";
  const isLocalDevelopment = mode === "proxy" || mode === "mock";
  return defineConfig({
    base: "",
    plugins: [
      react({
        babel: {
          plugins: [
            [
              "babel-plugin-styled-components",
              {
                displayName: true,
                fileName: true
              }
            ]
          ]
        }
      }),
      viteTsconfigPaths()
    ],
    build: {
      sourcemap: generateSourceMap,
      target: browserslistToEsbuild([">0.2%", "not dead", "not op_mini all"])
    },
    server: isLocalDevelopment ? {
      port: 3e3,
      open: true,
      proxy: {
        "/gpapi": {
          target: "https://tvgo.bis.orange.pl",
          changeOrigin: true
        }
      },
      https: {
        key: fs.readFileSync(path.resolve(__vite_injected_original_dirname, "localhost.key")),
        cert: fs.readFileSync(path.resolve(__vite_injected_original_dirname, "localhost.crt"))
      }
    } : void 0,
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: "./src/setupTests.ts",
      testTimeout: 2e4,
      hookTimeout: 2e4,
      css: true
    }
  });
};
var vite_config_default = createViteConfig;
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
