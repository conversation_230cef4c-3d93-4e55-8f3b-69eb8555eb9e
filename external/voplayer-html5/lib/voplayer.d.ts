import './src/player/css/voplayer.css';
import { Player, createPlayer, findNextFreeVideoElementId } from './src/player/Player';
import { PlayerPlatform } from './src/player/control/PlayerPlatforms';
import { Logger } from './src/log/Logger';
declare let voplayer: any;
export default voplayer;
export { Player, createPlayer, findNextFreeVideoElementId, Logger as Log, PlayerPlatform };
export * from './src/player/Events';
export * from './src/player/errors/Error';
export { AbrConfiguration, Accessibility, AdvancedDrmConfiguration, AudioTrack, BufferSensitivity, BufferingWatchDog, DrmAgentType, DrmConfiguration, Media, PlayerType, PlayerConfiguration, Quality, RequestFilterData, ResponseFilterData, Restrictions, StreamingConfiguration, SubtitleConfiguration, TextTrack, TimeRange, TizenNativeConfiguration, VOWatermarkingConfiguration, VideoSize } from './src/player/Types';
export { VOFmaManager, VOFmaParameters, VOFmaManagerInitListener, VOFmaErrorListener, VoFmaUiElementInfo, SearchResult, VoFmaUiLogger, VoFmaUiView, FmaRemoteConfig, FmaErrorCode, StartLoadingUiViewParams, VoFmaUiContentInfo } from 'fma';
