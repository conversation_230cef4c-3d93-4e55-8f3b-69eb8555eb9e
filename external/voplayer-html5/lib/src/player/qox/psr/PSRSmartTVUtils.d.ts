export declare abstract class PSRSmartTVUtils {
    static getTizenWifiBssid: () => string | undefined;
    static getTizenWifiSsid: () => string | undefined;
    static getTizenDeviceLocalIp: () => string | undefined;
    static getTizenNetworkType: () => string | undefined;
    private static convertTizenSignalStrengthLevelToRssiDbm;
    private static convertTizenNetworkType;
    static getTizenRssiValue: () => number | undefined;
    static getTizenGatewayIp: () => string | undefined;
    static getWebOsConnectionStatus(): Promise<any>;
    static ping(ip: any): Promise<number>;
}
