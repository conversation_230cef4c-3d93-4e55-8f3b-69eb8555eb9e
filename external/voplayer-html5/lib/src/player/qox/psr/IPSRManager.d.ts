import { AdvertisementPosition, AdvertisementType, ApplicationAnalytics, Media } from '../../Types';
export interface IPSR {
    APP_PAUSE_NB?: number;
    APP_SEEK_NB?: number;
    'MDA_APPLICATION_NAME#': string;
    'MDA_APPLICATION_VERSION#': string;
    'MDA_APPLICATION_LANGUAGE#'?: string;
    MDA_DEVICE_ID: string;
    MDA_DEVICE_MODEL: string;
    'MDA_DEVICE_OPERATOR#'?: string;
    MDA_DEVICE_OS_TYPE: string;
    MDA_DEVICE_BRAND: string;
    MDA_DEVICE_OS_VERSION: string;
    'MDA_DEVICE_SCREEN-SIZE'?: string;
    MDA_SDK_CUSTOMER: string;
    MDA_SDK_VERSION: string;
    MDA_DEVICE_WIDEVINE_SYSTEM_ID?: string;
    'MDA_DEVICE_TIME-ZONE': number;
    MDA_DEVICE_VIDEO_CODECS?: string[];
    'MDA_APPLICATION_USER-AGENT'?: string;
    SES_CDN_HOSTNAME_MAIN?: string;
    'SES_CDN_SESSION-ID#'?: string;
    SES_CDN_URL_MAIN?: string;
    SES_CONTENT_DURATION: number;
    'SES_CONTENT_ID#'?: string;
    'SES_CONTENT_NAME#'?: string;
    SES_DRM_LEVEL?: string;
    'SES_DRM_LICENSER-URL_MAIN'?: string;
    'SES_SESSION_ACCOUNT-ID#'?: string;
    'SES_SESSION_COMPLETION-PERCENT': number;
    SES_SESSION_DURATION: number;
    'SES_SESSION_END-REASON'?: string;
    SES_SESSION_ID: string;
    'SES_SESSION_PARENT-ID': string;
    'SES_SESSION_SERIAL-ID#'?: string;
    'SES_SESSION_TERMINATED-CODE'?: string;
    SES_SESSION_TIMESTAMP: number;
    'SES_SESSION_TYPE#': string;
    SES_SESSION_TYPE: string;
    SES_SESSION_STATE?: string;
    'SES_SESSION_USER-ID#'?: string;
    'SES_SESSION_PROFILE_TYPE#'?: string;
    'SES_CONTENT_GENRE#'?: string;
    'SES_CONTENT_TYPE#'?: string;
    'SES_CONTENT_SEASON_NB#'?: string;
    'SES_CONTENT_EPISODE_NB#'?: string;
    'SES_CONTENT_LANGUAGE#'?: string;
    'SES_CONTENT_PARENTAL_RATING#'?: string;
    'SES_CONTENT_PROGRAM_ID#'?: string;
    'SES_CONTENT_PROGRAM_NAME#'?: string;
    'SES_CONTENT_PROVIDER#'?: string;
    'SES_PLAYBACK_STARTUP_TIME#'?: number;
    'SES_SESSION_SUBSCRIPTION_TYPE#'?: string;
    'SES_CONTENT_BDCAST_START#'?: number;
    'SES_CONTENT_BDCAST_STOP#'?: number;
    'SES_TIME_SET-DATA-SOURCE'?: number;
    'SES_ADS_MAST-URL_MAIN'?: string;
    'SES_ADS_VMAP-URL_MAIN'?: string;
    'SES_ADS_VAST-URL_MAIN'?: string;
    SES_SESSION_START_POSITION?: number;
    SES_SESSION_LAST_POSITION?: number;
    STR_ALTERNATE_LAST?: number;
    STR_ALTERNATE_AVG?: number;
    STR_ALTERNATE_MAX?: number;
    STR_ALTERNATE_MIN?: number;
    STR_ALTERNATE_STD?: number;
    'STR_ALTERNATE_SWITCH-DN'?: number;
    'STR_ALTERNATE_SWITCH-UP'?: number;
    STR_BANDWIDTH_AVG?: number;
    STR_BANDWIDTH_MAX?: number;
    STR_BANDWIDTH_MIN?: number;
    STR_BANDWIDTH_STD?: number;
    'STR_BUFFERING-TIME-INITIAL'?: number;
    'STR_CONTINUOUS-PLAYBACK_AVG'?: number;
    'STR_CONTINUOUS-PLAYBACK_MAX'?: number;
    'STR_CONTINUOUS-PLAYBACK_MIN'?: number;
    'STR_CONTINUOUS-PLAYBACK_STD'?: number;
    'STR_LIVE-LATENCY_AVG'?: number;
    'STR_LIVE-LATENCY_MAX'?: number;
    'STR_LIVE-LATENCY_MIN'?: number;
    STR_REBUFFERING_NB?: number;
    'STR_REBUFFERING_NB_LAST-0.5MIN'?: number;
    'STR_REBUFFERING_NB_LAST-01MIN'?: number;
    'STR_REBUFFERING_NB_LAST-02MIN'?: number;
    'STR_REBUFFERING_NB_LAST-05MIN'?: number;
    'STR_REBUFFERING_NB_LAST-10MIN'?: number;
    STR_REBUFFERING_TIME_AVG?: number;
    STR_REBUFFERING_TIME_MAX?: number;
    STR_REBUFFERING_TIME_MIN?: number;
    STR_REBUFFERING_TIME_STD?: number;
    STR_RESOLUTION_HEIGHT_MIN?: number;
    STR_RESOLUTION_HEIGHT_MAX?: number;
    STR_RESOLUTION_HEIGHT_AVG?: number;
    STR_RESOLUTION_MAX?: string;
    STR_RESOLUTION_MIN?: string;
    'SVA_IN-STREAM-ERROR'?: boolean;
    'SVA_REBUFFERING-RATIO'?: number;
    'SVA_VIDEO-START-FAILURE'?: boolean;
    'SVA_VIDEO-START-TIME'?: number;
    SES_WIFI_RSSI_AVG?: number;
    SES_WIFI_RSSI_MIN?: number;
    SES_WIFI_RSSI_MAX?: number;
    SES_WIFI_RSSI_STD?: number;
    'SES_WIFI_LINK-SPEED_AVG'?: number;
    'SES_WIFI_LINK-SPEED_MIN'?: number;
    'SES_WIFI_LINK-SPEED_MAX'?: number;
    'SES_WIFI_LINK-SPEED_STD'?: number;
    SES_WIFI_BSSID?: string;
    MDA_DEVICE_SSID?: string;
    'SES_SESSION_NETWORK-TYPE'?: string;
    'MDA_DEVICE_IP-LOCAL'?: string;
    SQP_PSR_SES_DISCARD_THRESHOLD_REQUEST: boolean;
    SES_SESSION_ISR?: boolean;
    SES_ADS?: PSRAds;
}
export interface IPSRConfigBody {
    MDA_SDK_CUSTOMER: string;
    MDA_SDK_VERSION: string;
    REQUEST_CONFIG_ONLY: boolean;
    SQP_PSR_SES_DISCARD_THRESHOLD_REQUEST: boolean;
}
export interface IPSRManager {
    onStartLoadingMediaUrl(mediaObj: string | Media): void;
    onVideoCanPlayThrough(): void;
    setPsrEndpoint(psrEndpoint: string): void;
    setAnalyticsMetadata(config: ApplicationAnalytics): void;
    destroy(): void;
}
export interface IPSRInitWifiFields {
    SES_WIFI_BSSID: string;
    MDA_DEVICE_SSID: string;
    'SES_SESSION_NETWORK-TYPE': string;
    'MDA_DEVICE_IP-LOCAL': string;
}
export interface PSRAdBreak {
    POSITION: AdvertisementPosition;
    INSERTION_TIME_SEC?: number | undefined;
    NB_ADS: number;
    PLAYED_ADS: PSRAdInfo[];
}
export interface PSRAdInfo {
    ERROR?: string | undefined;
    MEDIA_DURATION: number;
    VIEW_DURATION: number;
    VIEW_COMPLETION: number;
    MIME_TYPE: string;
    MEDIA_ID: string;
    MEDIA_URL: string;
    TYPE: AdvertisementType;
    CLICK_THROUGH_URL?: string | undefined;
    CLICKED?: boolean | undefined;
    SKIPPABLE_IN_SEC?: number | undefined;
    SKIPPED?: boolean | undefined;
    CLOSABLE?: boolean | undefined;
    CLOSED?: boolean | undefined;
}
export interface PSRAds {
    URL: string;
    TYPE: string;
    PARSING_ERROR: boolean;
    AD_BREAKS?: PSRAdBreak[];
    PLAYED_ADS_NB?: number;
}
