import { IPSR, IPSRConfigBody } from './IPSRManager';
export declare abstract class PSREncryption {
    private static key;
    private static _strToByteArray;
    private static _strToUtf8ByteArray;
    private static _ord;
    private static _chr;
    private static _expandAlphRange;
    static toBase64(data: number[]): string;
    static fromBase64(base64String: string): number[];
    private static _fromHex;
    static xorEncrypt(input: string, key: string): number[];
    static xorDecrypt(data: number[], key: string): string;
    static encryptPSR(psr: IPSR | IPSRConfigBody): string;
    static decryptPSRResponse(response: string): any;
    static getAuthorizationHeader(username: string): string;
}
