import { ApplicationAnalytics, Media } from '../../Types';
import { Player } from '../../Player';
import { IPSRManager } from './IPSRManager';
export declare class PSRManager implements IPSRManager {
    static gatewayIp: string;
    static isFetchingNewConfig: boolean;
    private player_;
    private psrEndpoint_;
    private psr_;
    private previousOnResetLivePsr_;
    private selectedQualityList_;
    private startLoadingMedialUrlTime_;
    private sessionStartTime_;
    private firstBufferingStartTime_;
    private psrAlreadySent_;
    private strContinuousPlaybackTimes_;
    private startContinousPlaybackTimestamp_;
    private rebufferingTimeSpentList_;
    private rebufferingTimestampList_;
    private estimatedBandwidthSampleList_;
    private liveLatencySampleList_;
    private sampleIntervalId_;
    private lastLiveSessionTimer_;
    private firstSeekEventSkippedForLive_;
    private currentTime_;
    private onVideoPlayTimestamp_;
    private shouldSkipNextBuffering_;
    private currentQuality_;
    private sessionParentId_;
    private applicationAnalytics_;
    private networkSampleIntervalId_;
    private rssiSampleList_;
    private isrTimer_;
    private currentAd_;
    private currentAdBeforeHiddingTab;
    private adParsingError;
    private eventListenerRegistered;
    constructor(player: Player, psrEndpoint: string);
    private _onPlayerDestroyed;
    private _registerListeners;
    private _removeListeners;
    onStartLoadingMediaUrl: (mediaObj: string | Media) => Promise<void>;
    onVideoCanPlayThrough: () => void;
    destroy: () => void;
    setPsrEndpoint: (psrEndpoint: string) => void;
    setAnalyticsMetadata: (config: ApplicationAnalytics) => void;
    private _reset;
    private _resetForNewProgramOrContent;
    private _clearSampleInterval;
    private _onPlayerPause;
    private _onPlayerSeeking;
    private _onPlayerSeeked;
    private _onPlayerPlay;
    private _getCurrentPosition;
    private _onPlayerPlaying;
    private _onPlayerLoaded;
    private _handlePreviousLiveSession;
    private _addSamplingData;
    private _addNetworkSamplingData;
    private _onPlayerBuffering;
    private _updateContinuousPlaybackTimes;
    private _onPlayerError;
    private _onPlayerQualityChanged;
    private _updateCurrentQuality;
    private _onPlayerDurationChanged;
    private _onPlayerTimeupdate;
    private _updateSessionEndReasonVOD;
    private _onVisibilityChange;
    private _onUnload;
    private _onPlayerReset;
    private _onPlayerEnded;
    private _onPlayerEndOfLiveReached;
    private _onPlayerPublicKeySystemSelected;
    private _onPlayerAdEnded;
    private _onPlayerAdStarted;
    private _onPlayerSkipAd;
    private _onPlayerAdBreakStarted;
    private _onPlayerAdTimeUpdated;
    private _initSesAds;
    private _onPlayerAdActivated;
    private _onPlayerWarning;
    private _addPlayedAdd;
    private _currentPSRIsLive;
    private _addMetadataToPSR;
    private _handleEndOfSession;
}
