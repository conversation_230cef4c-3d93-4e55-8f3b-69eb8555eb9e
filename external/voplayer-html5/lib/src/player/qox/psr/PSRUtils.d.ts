import { Player } from '../../Player';
import { Media, Quality } from '../../Types';
import { IPSR, IPSRInitWifiFields } from './IPSRManager';
export declare enum PSRHandleActionType {
    SHOULD_SEND = 0,
    SHOULD_SAVE = 1,
    SHOULD_SAVE_AS_TEMPORARY = 2,
    LIVE_RESET = 3
}
export interface IPSRQuality {
    quality: Quality;
    timestampStart: number;
    duration: number;
}
export interface IValueWithDuration {
    value: number;
    duration: number;
}
export declare enum PSRSessionType {
    VOD = "VOD",
    LIVE = "LIVE",
    EVENT = "EVENT",
    UNKNOWN = "Unknown"
}
export declare enum PSRNetworkType {
    OFFLINE = "OFFLINE",
    CELLULAR = "CELLULAR",
    WIFI = "WIFI",
    ETHERNET = "ETHERNET",
    UNKNOWN = "Unknown"
}
export declare enum PSRSessionState {
    ON_PREPARING = "OnPreparing",
    PREPARED = "Prepared",
    PLAYING = "Playing"
}
export declare enum PSRDrmLevel {
    WV_L1 = "WV L1",
    WV_L3 = "WV L3",
    PLAYREADY_SL3000 = "PR SL3000",
    FAIRPLAY_HW = "FairPlay HW",
    CLEAR = "Clear",
    UNKNOWN = "Unknown"
}
export declare enum PSRSessionEndReason {
    UNKNOWN = "Unknown",
    ZAPPING = "Zapping",
    QUIT_BEFORE_90 = "Quit before 90% of the vod duration",
    QUIT_AFTER_90 = "Quit after 90% of the vod duration",
    END_OF_STREAMING = "End of streaming",
    END_OF_LIVE_PROGRAM = "End of Live Program",
    STOP = "Stop",
    ERROR = "Error"
}
interface TemporarySavedPsr {
    timestamp: number;
    psr: IPSR;
}
export declare abstract class PSRUtils {
    private static frequency;
    private static ath;
    private static dth;
    private static ith;
    private static option;
    static getArrayMinValue(array: number[]): number | undefined;
    static getArrayMaxValue(array: number[]): number | undefined;
    static getArrayLastValue(array: number[]): number | undefined;
    static getArrayAvgValue(array: number[], shouldRoundResult?: boolean): number | undefined;
    static getArrayStdValue(array: number[]): number | undefined;
    static getArrayWeightedAvgValue(array: IValueWithDuration[], shouldRoundResult?: boolean): number | undefined;
    static getArrayMinQuality(array: Quality[]): Quality | undefined;
    static getArrayMaxQuality(array: Quality[]): Quality | undefined;
    static countNbTimestampThatOccuredDuringLast(milliseconds: number, timestampList: number[]): number;
    static getElapsedTimeInSecondSince(timestamp: number | undefined): number | undefined;
    static getSessionType(player: Player): string;
    static getDrmLevel(keySystemString?: string): PSRDrmLevel;
    private static _getSupportedVideoCodecs;
    static getDefaultPSR(player: Player, mediaObj: string | Media): Promise<IPSR>;
    static getPsrWifiFieldsAsync(): Promise<IPSRInitWifiFields>;
    private static decryptAndSavePSRConfig;
    private static readPSRConfigFromLocalStorage;
    static fetchNewConfigIfNeeded(player: Player, psrEndpoint: string): Promise<void>;
    private static fetchNewPSRConfig;
    static canSendPSR(): boolean;
    static shouldDiscardPSR(sessionDuration: number, endReason: string): boolean;
    static shouldAggregatePSR(sessionDuration: number): boolean;
    static filterPSRFieldsBasedOnOption(psr: IPSR): IPSR;
    static sendISRIfNeeded(psrEndpoint: string, isr: IPSR): ReturnType<typeof setTimeout> | undefined;
    static postPsr(psrEndpoint: string, psr: IPSR): Promise<Response>;
    static savePSR(psr: IPSR): void;
    static saveTemporaryPSR(psr: IPSR, timestamp?: number): void;
    static shouldStartNewSession(sessionId: string): boolean;
    private static _deleteTemporarySavedPSR;
    static sendTemporarySavedPSR(data: Map<string, TemporarySavedPsr>, psrEndpoint: string): Promise<Response>;
    static sendSavedPSR(psrEndpoint: string): Promise<Response>;
}
export {};
