/// <reference types="node" />
import { EventEmitter } from 'events';
import { VOFmaManager, VOFmaParameters, VOFmaManagerInitListener } from 'fma';
import { PlayerType, Media, TimeRange, AudioTrack, TextTrack, Quality, PlayerConfiguration, ResponseFilterData, RequestFilterData, VmapAutrefreshConfig, Thumbnail, FilteredUrl, ParsedThumbnails, ApplicationAnalytics, Section } from './Types';
import 'regenerator-runtime/runtime';
import { PlayerControl } from './control/PlayerControl';
import { ICorePlayer } from './core/ICorePlayer';
import { VOTextManager } from './subtitles/VOTextManager';
import VOSubtitleRenderer from './subtitles/VOSubtitleRenderer';
import LegacyCompatibility from './subtitles/LegacyCompatibility';
import { PlayerError } from './errors/PlayerError';
import { IVRController } from './vr/IVRController';
import { IAdManager } from './ads/IAdManager';
import { DebugController } from './tools/debug/DebugController';
declare global {
    interface Window {
        webOS: any;
        voplayer: any;
    }
}
/**
 * The main player object for VO Player.
 */
export declare class Player extends EventEmitter {
    static VTT: string;
    static SRT: string;
    /**
     * Fired when the player has been reset
     * <br/>See {@link ResetEvent} for event callback/handler parameter properties
     * @event reset
     */
    static RESET: string;
    /**
     * Fired when the player has been destroyed
     * <br/>See {@link DestroyedEvent} for event callback/handler parameter properties
     * @event destroyed
     */
    static DESTROYED: string;
    /**
     * Fired when the HTML node used for subtitles rendering is adjusted to the video size.
     * May be used by the UI to externally adjust the node so that the UI controls does not cover the subtitles.
     * <br/>See {@link SubtitleRenderingNodeAdjustedEvent} for event callback/handler parameter properties
     * @event subtitlerenderingnodeadjusted   */
    static SUBTITLE_RENDERING_NODE_ADJUSTED: string;
    /**
     * Fired when the list of tracks (video, audio and subtitles) have changed
     * <br/>See {@link TracksChangedEvent} for event callback/handler parameter properties
     * @event trackschanged
     */
    static TRACKS_CHANGED: string;
    /**
     * Fired when the quality of the played streaming representation has changed
     * <br/>See {@link QualityChangedEvent} for event callback/handler parameter properties
     * @event qualitychanged
     */
    static QUALITY_CHANGED: string;
    /**
     * Fired when an error has occured
     * <br/>See {@link ErrorEvent} for event callback/handler parameter properties
     * @event error
     */
    static ERROR: string;
    /**
     * Fired when an error has occured, without breaking playback
     * <br/>See {@link WarningEvent} for event callback/handler parameter properties
     * @event warning
     */
    static WARNING: string;
    /**
     * Fired when the player starts playing
     * <br/>See {@link PlayEvent} for event callback/handler parameter properties
     * @event play
     */
    static PLAY: string;
    /**
     * Fired when the player is paused
     * <br/>See {@link PauseEvent} for event callback/handler parameter properties
     * @event pause
     */
    static PAUSE: string;
    /**
     * Fired when the duration of the stream has been modified
     * <br/>See {@link DurationChangeEvent} for event callback/handler parameter properties
     * @event durationchange
     */
    static DURATION_CHANGE: string;
    /**
     * Fired when the playback time of the stream has been modified
     * <br/>See {@link TimeUpdateEvent} for event callback/handler parameter properties
     * @event timeupdate
     */
    static TIMEUPDATE: string;
    /**
     * Fired when a startover stream has reached its end (now static)
     * <br/>See {@link EndOfLiveReachedEvent} for event callback/handler parameter properties
     * @event endoflivereached
     */
    static END_OF_LIVE_REACHED: string;
    /**
     * Fired when the player has been loaded
     * <br/>See {@link LoadedEvent} for event callback/handler parameter properties
     * @event loaded
     */
    static LOADED: string;
    /**
     * Repeated event from &lt;video&gt; tag
     * <br/>See {@link EndedEvent} for event callback/handler parameter properties
     * @event ended
     */
    static ENDED: string;
    /**
     * Repeated event from &lt;video&gt; tag
     * <br/>See {@link VolumeChangeEvent} for event callback/handler parameter properties
     * @event volumechange
     */
    static VOLUME_CHANGE: string;
    /**
     * Fired when the player enters or leave a buffering period
     * <br/>See {@link BufferingEvent} for event callback/handler parameter properties
     * @event buffering
     */
    static BUFFERING: string;
    /**
     * Fired when the player enters a buffering period
     * <br/>See {@link WaitingEvent} for event callback/handler parameter properties
     * @event waiting
     */
    static WAITING: string;
    /**
     * Fired when the player leaves a buffering period
     * <br/>See {@link PlayingEvent} for event callback/handler parameter properties
     * @event playing
     */
    static PLAYING: string;
    /**
     * Fired when the player.currentTime is set to a new value
     * <br/>See {@link SeekingEvent} for event callback/handler parameter properties
     * @event seeking
     */
    static SEEKING: string;
    /**
     * Fired when seeking in content is completed
     * <br/>See {@link SeekedEvent} for event callback/handler parameter properties
     * @event seeked
     */
    static SEEKED: string;
    /**
     * Fired when a DRM key session has been created
     * <br/>See {@link DrmSessionCreatedEvent} for event callback/handler parameter properties
     * @event seeked
     */
    static DRM_SESSION_CREATED: string;
    /**
     * Fired when adBreaks have been loaded (i.e: a VMAP file has been loaded)
     * <br/>See {@link AdBreaksLoadedEvent} for callback/handler parameter properties
     * @event adBreaksLoaded
     */
    static AD_BREAKS_LOADED: string;
    /**
     * Fired when a linear adBreak starts playing.
     * <br/>See {@link AdBreakStartedEvent} for callback/handler parameter properties
     * @event adBreakStarted
     */
    static AD_BREAK_STARTED: string;
    /**
     * Fired when a linear adbreak has completed playing
     * <br/>See {@link AdBreakEndedEvent} for callback/handler parameter properties
     * @event adBreakEnded
     */
    static AD_BREAK_ENDED: string;
    /**
     * Fired when an ad starts playing
     * <br/>See {@link AdStartedEvent} for callback/handler parameter properties
     * @event adStarted
     */
    static AD_STARTED: string;
    /**
     * Fired when an ad starts playing
     * <br/>See {@link AdLoadedEvent} for callback/handler parameter properties
     * @event adLoaded
     */
    static AD_LOADED: string;
    /**
     * Fired when the head position changes in the current ad
     * <br/>See {@link AdTimeUpdatedEvent} for callback/handler parameter properties
     * @event adTimeUpdated
     */
    static AD_TIMEUPDATED: string;
    /**
     * Fired when ad volume is changed or muted
     * <br/>See {@link AdVolumeChangeEvent} for callback/handler parameter properties
     * @event advolumechange
     */
    static AD_VOLUME_CHANGE: string;
    /**
     * Fired when advertisement plays or is resumed
     * <br/>See {@link AdPlayEvent} for callback/handler parameter properties
     * @event adplay
     */
    static AD_PLAY: string;
    /**
     * Fired when advertisement is paused
     * <br/>See {@link AdPauseEvent} for callback/handler parameter properties
     * @event adpause
     */
    static AD_PAUSE: string;
    /**
     * Fired when an ad completes playing
     * <br/>See {@link AdEndedEvent} for callback/handler parameter properties
     * @event adEnded
     */
    static AD_ENDED: string;
    /**
     * Fired when the user skips an ad.
     * <br/>See {@link SkipAdEvent} for callback/handler parameter properties
     * @event skipAd
     */
    static SKIP_AD: string;
    /**
     * Fired when the user activates (i.e: clicks or presses ENTER on it) the ad
     * <br/>See {@link AdActivatedEvent} for callback/handler parameter properties
     * @event adActivated
     */
    static AD_ACTIVATED: string;
    /**
     * Fired when the user starts seeking within the ad
     * <br/>See {@link AdSeekingEvent} for callback/handler parameter properties
     * @event adSeeking
     */
    static AD_SEEKING: string;
    /**
     * Fired when the user stops seeking within the ad
     * <br/>See {@link AdSeekedEvent} for callback/handler parameter properties
     * @event adSeeked
     */
    static AD_SEEKED: string;
    /**
     * Fired when a dash event is detected within a dash manifest
     * <br/>See {@link DashEventEvent} for callback/handler parameter properties
     * @event dashevent
     */
    static DASH_EVENT: string;
    /**
     * Fired when a scte35 cue is detected within a manifest
     * <br/>See {@link Scte35CueDetectedEvent} for callback/handler parameter properties
     * @event scte35cuedetected
     */
    static SCTE35_CUE_DETECTED: string;
    /**
     * Fired when a scte35 cue is starting
     * <br/>See {@link Scte35CueStartEvent} for callback/handler parameter properties
     * @event scte35cuestart
     */
    static SCTE35_CUE_START: string;
    /**
     * Fired when a scte35 cue is ending
     * <br/>See {@link Scte35CueEndEvent} for callback/handler parameter properties
     * @event scte35cueend
     */
    static SCTE35_CUE_END: string;
    /**
     * Fired when the player exit a VR session from a VR headset
     * <br/>See {@link ExitVRImmersiveEvent} for callback/handler parameter properties
     * @event exitvrimmersive
     */
    static EXIT_VR_IMMERSIVE: string;
    /**
     * Fired when the player enter a VR session in a VR headset
     * <br/>See {@link EnterVRImmersiveEvent} for callback/handler parameter properties
     * @event entervrimmersive
     */
    static ENTER_VR_IMMERSIVE: string;
    /**
     * Fired when the UI controller is ready and may be used
     * <br/>Only applicable in the case where the player is created using function {@link createPlayerWithControls}
     * <br/>See {@link ReadyEvent} for callback/handler parameter properties
     * @event ready
     */
    static READY: string;
    /**
     * Fired when the player enter a vodata section
     * <br/>See {@link SectionStartEvent} for callback/handler parameter properties
     * @event sectionstart
     */
    static SECTION_START: string;
    /**
     * Fired when the player leave a vodata section
     * <br/>See {@link SectionEndEvent} for callback/handler parameter properties
     * @event sectionend
     */
    static SECTION_END: string;
    /**
     * Fired when the player land in a vodata section (e.g. start time not null or seek within a section)
     * <br/>See {@link SectionInEvent} for callback/handler parameter properties
     * @event sectionin
     */
    static SECTION_IN: string;
    /**
     * Fired when the player leave a vodata section (e.g. end time not null or seek outside a section)
     * <br/>See {@link SectionOutEvent} for callback/handler parameter properties
     * @event sectionout
     */
    static SECTION_OUT: string;
    /**
     * @hidden
     */
    textManager: VOTextManager;
    /**
     * @hidden
     */
    legacyCompatibility: LegacyCompatibility;
    /**
     * @hidden
     */
    maxVideoWidthForTTML: number;
    /**
     * @hidden
     */
    maxVideoHeightForTTML: number;
    /**
     * @hidden
     */
    subtitleRenderer: VOSubtitleRenderer;
    /**
     * @hidden
     */
    licenseResponseFilters_: ((response: ResponseFilterData) => void)[];
    /**
     * @hidden
     */
    licenseRequestFilters_: ((request: RequestFilterData) => void)[];
    /**
     * @hidden
     */
    requestFilters_: ((xhr: XMLHttpRequest, url: string) => void)[];
    /**
     * @hidden
     */
    capabilitiesFilters_: ((representation: any) => boolean)[];
    /**
     * @hidden
     */
    playing_: boolean;
    /**
     * @hidden
     */
    adManager: IAdManager;
    /**
     * @hidden
     */
    control: PlayerControl;
    /**
     * @hidden
     */
    useTizenNative_: boolean;
    /**
     * @hidden
     */
    useWebosNative_: boolean;
    /**
     * @hidden
     */
    corePlayer_: ICorePlayer;
    /**
     * @hidden
     */
    corePlayerType_: PlayerType;
    /**
     * @hidden
     */
    videoContainerPaddingV: number;
    /**
     * @hidden
     */
    videoContainerPaddingH: number;
    /**
     * @hidden
     */
    videoContainerHeight: number;
    /**
     * @hidden
     */
    videoContainerWidth: number;
    /**
    * @hidden
    */
    uiControls: any;
    /**
    * @hidden
    */
    contentLayer: HTMLDivElement;
    /**
    * @hidden
    */
    isContentLayerCreatedByPlayer: boolean;
    /**
    * @hidden
    * get access to the vrController (IVRController)
    */
    vrController: IVRController;
    private video_;
    private userSubtitlesRenderingDiv_;
    private firstPlay;
    private currentlyLoading_;
    private packagedSmartTVApp_;
    private safariNativeiOSBrowser_;
    private safariNativeMacBrowser_;
    private safariNativeiPadBrowser_;
    private configuration_;
    private buffering_;
    private media;
    private smpteParser;
    private srtManager_;
    private vttManager_;
    private sectionManager_;
    private seeking_;
    private playCue_;
    private isReset_;
    private _playing;
    private loadingPromise;
    private playPromise_;
    private loadingResolve_;
    private _poster;
    private _subtitleRenderingNode;
    private bufferingWatchDog_;
    private prftNtpOffset_;
    private dashVideoBuffering_;
    private dashAudioBuffering_;
    private dashAllBuffering_;
    private isAdPlayer_;
    private useMainPlayerVideoElementAdShown_;
    private debugController_;
    private psrManager_;
    private videoAreaObj_;
    private platformVersion_;
    private delayedPlaying_;
    private handleExternalThumbnails_;
    private foundPrerollAd_;
    private startTime_;
    private playerVersion_;
    /**
     * Version of the player
     * @type {string}
     */
    static get version(): string;
    get version(): string;
    /**
     * @hidden called by PlayerBPKExtended.ts
     */
    isVideoElementProvided(): boolean;
    /**
     * A static method that tells if we are on Smart TV environment
     * @return true if SmartTV, false otherwise
     */
    static isSmartTv(): boolean;
    /**
     * @hidden
     */
    /**
     * A static method that tells if we are on Smart TV packaged application
     * @return true if Smart TV packaged application, false otherwise
     */
    static isPackagedSmartTVApp(): boolean;
    /**
     * A static method to get the deviceId, it is reliable on SmartTVs and unreliable on Browsers (no permanent unicity)
     * @return deviceId
     */
    static getDeviceId(): Promise<string>;
    /**
     * A static method to set the log level,to call it : voplayer.Player.setLevel(level)
     * @param level Log level number
     */
    static setLevel(level: number): void;
    /**
     * A static method that tells if the current browser is Safari running on iOS
     * @return true if the browser is Safari on iOS, false otherwise
     */
    static isBrowserSafariiOS(): boolean;
    /**
     * A static method that tells if the current browser is Safari running on iOS
     * @return true if the browser is Safari on iPadOS, false otherwise
     */
    static isBrowserSafariiPadOS(): boolean;
    /**
     * A static method that tells if the current browser is Safari running on MacOS
     * @return true if the browser is Safari on MacOS, false otherwise
     */
    static isBrowserSafariMacOS(): boolean;
    /**
     * A static method that tells if the current browser is Safari running on Iphone or Ipad or Ipod
     * @return true if the browser is Safari on Iphone or Ipad or Ipod, false otherwise
     */
    static isBrowserSafariIphoneIpadIpod(): boolean;
    /**
     * A static method that tells if the current browser is Microsoft IE11 or Edge
     * @return true if the browser is Microsoft IE11 or Edge, false otherwise
     */
    static isBrowserEdgeOrIE11(): boolean;
    /**
     * A static method that tells if the current browser is based on Chromium
     * @return true if the browser is based on Chromium, false otherwise
     */
    static isBrowserChromiumBased(): boolean;
    /**
     * A static method that returns a map of MediaSource support for well-known types or the types given in parameter if any.
     * @param types to probe support of
     */
    static getSupportedTypes(types: string[]): boolean[];
    /** A static method that returns true if the player is supported in the current environment, false otherwise
     * @return true if the current browser is supported, false otherwise
     */
    static isBrowserSupported(): boolean;
    /**
     * Returns a singleton instance of the VO FMA manager
     * @type {VOFmaManager}
     */
    static get fmaManager(): VOFmaManager;
    /**
     * Init the FMA.
     * It will fetch the config only if the previous one has expired (the expiration time can be set in the remote config, usually it is 1 day)
     * @param {VOFmaParameters} parameters - the initial parameter structure required for initialization.
     * @param {VOFmaManagerInitListener} initListener - listener to get notified if the initializetion was successful or not (meaning we could get the config from the url from VOFmaParameters)
     * @return {Promise<boolean>} a promise which is resolved to true if the FMA was successfuly initialized and is ready to be used
     */
    static init(parameters: VOFmaParameters, initListener: VOFmaManagerInitListener): Promise<boolean>;
    private static isSupported;
    /**
     * Creates a new instance of VOPlayer HTML 5 player
     * @constructor
     * @alias Player
     * @param video Any existing TextTracks attached to this
     *   element that were not created by VOPlayer will be discarded.
     */
    constructor(video: HTMLVideoElement, isAdPlayer?: boolean);
    /**
     * Destroys the player. The instance will not be usable after the call.
     */
    destroy(): Promise<void>;
    private handleEmptiedEvent;
    /**
     * Set the player license
     */
    set license(l: string);
    /**
     * Return a copy of the current configuration. Modifications of the returned value will not affect the Player's active configuration.
     * You must call player.configure() to make changes.
     */
    getConfiguration(): PlayerConfiguration;
    /**
     * Configure the Player instance.
     * The config object passed in needs not be complete. It will be merged with the existing Player configuration.
     * Config keys and types will be checked.
     * @param config This should follow the form of {@link PlayerConfiguration}, but you may omit any field you do not wish to change.
     */
    configure(config: PlayerConfiguration): void;
    /**
   * Function to update the ApplicationAnalytics metadata after the stream has started
   * It can be used on Live to notify that a new program has started by passing the sesContentProgramId and sesContentProgramName
   * The sessions will then be splitted by program
   * @param config the updated ApplicationAnalytics metadatas
   */
    setAnalyticsMetadata(config: ApplicationAnalytics): void;
    /**
     * Registers a filter for player capabilities.
     * This method allows you to register a callback function that can select dash representations that can be used by the player. The callback function should take a dash representation object as its argument, modify it as necessary, and then return a boolean value indicating whether the representation should be used.
     *
     * @param {Function} filter The callback function to register. This function should take a dash representation object as its argument.
     */
    registerCapabilitiesFilter(filter: ((representation: any) => boolean)): void;
    /**
     * Unregisters a filter for player capabilities.
     * This method allows you to unregister a callback function that can select dash representations that can be used by the player. The callback function should take a dash representation object as its argument, modify it as necessary, and then return a boolean value indicating whether the representation should be used.
     *
     * @param {Function} filter The callback function to unregister. This function should take a dash representation object as its argument.
     */
    unregisterCapabilitiesFilter(filter: ((representation: any) => boolean)): void;
    /**
     * Register a filter to be applied on streaming request<br>
     * See {@page Custom HTTP request} tutorial section for detailed use of this API
     */
    registerRequestFilter(filter: ((xhr: XMLHttpRequest, url: string) => void)): void;
    /**
     * Unregister a filter to be applies on streaming request<br>
     * See {@page Custom HTTP request} tutorial section for detailed use of this API
     */
    unregisterRequestFilter(filter: ((xhr: XMLHttpRequest, url: string) => void)): void;
    /**
     * Register a filter to be applied on DRM license requests<br>
     * See {@page DRM License Wrapping} tutorial section for detailed use of this API
     * @param filter
     */
    registerLicenseRequestFilter(filter: ((request: RequestFilterData) => void)): void;
    /**
     * Unregister a filter applied on DRM license requests<br>
     * See {@page DRM License Wrapping} tutorial section for detailed use of this API
     * @param filter
     */
    unregisterLicenseRequestFilter(filter: ((request: RequestFilterData) => void)): void;
    /**
     * Register a filter to be applied on DRM license responses<br>
     * See {@page DRM License Wrapping} tutorial section for detailed use of this API
     * @param filter
     */
    registerLicenseResponseFilter(filter: ((response: ResponseFilterData) => void)): void;
    /**
     * Unregister a filter applied on DRM license responses<br>
     * See {@page DRM License Wrapping} tutorial section for detailed use of this API
     * @param filter
     */
    unregisterLicenseResponseFilter(filter: ((response: ResponseFilterData) => void)): void;
    /**
     * Loads a stream's manifest
     * @param mediaObj {@link Media}
     * @param opt_startTime start time, in seconds, to begin playback. Defaults to 0 for VOD and to the live edge for live.
     * @return Promise that resolves when the loading is done
     */
    load(mediaObj: string | Media, opt_startTime?: number): Promise<void>;
    /**
     * @hidden called by PlayerDashJs on periodSwithc started to re-register loaded event
     */
    handleInternalLoadEvent(): void;
    /**
     * Stop/reset current stream playback.
     */
    reset(resetForAds?: boolean): Promise<void>;
    /**
     * Pauses the video playback (equivalent to video.pause() on the original video element)
     */
    pause(): void;
    /**
     * Starts the video playback (equivalent to video.play() on the original video element)
     * @returns the play promise
     */
    play: () => Promise<void>;
    /**
     * @return true if the current stream is a Live one, false otherwise (VOD)
     */
    isLive(): boolean;
    /**
     * @return true if the current stream was Live before ending (Start Over Streal that reaches end of Program)
     */
    wasLive(): boolean;
    /**
     * @return true if the current stream is a StartOver / HLS Event stream.
     */
    isEvent(): boolean;
    /**
     * Duration of the current stream in seconds.<br>
     * Duration is always 0 for Live streams.
     * @type {number}
     */
    get duration(): number;
    /** Current position in the video in seconds.<br>
     * Assign this value to jump in the video stream
     * @type {number}
     */
    get currentTime(): number;
    set currentTime(time: number);
    /**
     * @hidden
     */
    initPsrIfNeeded(): void;
    /**
     * Property providing the offset between NTP time and UTC time, when available in the stream (MP4 ProducerReferenceTimeBox).
     * Only applies to dash streams
     */
    get prftNtpOffset(): number;
    /**
     * @hidden
     */
    set prftNtpOffset(value: number);
    /**
     * Returns true if the player is currently playing (including re-buffering phases), false otherwise
     */
    get playing(): boolean;
    /**
     * @hidden => used by image player
     */
    set playing(value: boolean);
    /**
     * Returns true if the playback has ended
     */
    get ended(): boolean;
    /**
     * Return the available seek range in absolute video time values.
     * The range with start and end number properties are expressed in seconds
     */
    seekRange(): TimeRange;
    /**
     * Playaback rate of the current stream.<br>
     * 1.0 represents a normal playback.
     */
    get playbackRate(): number;
    set playbackRate(rate: number);
    /**
     * List of audio tracks (read-only)
     * @return Array of {@link AudioTrack}
     */
    get audioTracks(): AudioTrack[];
    /**
     * Index of the current audio track in the list of available audio tracks<br>
     * Assign this value to set a new audio track
     */
    get audioTrack(): number;
    set audioTrack(trackId: number);
    /**
     * List of available text tracks (read-only)
     * @return Array of {@link TextTrack}
     */
    get textTracks(): TextTrack[];
    /**
     * Index of the current text track in the list of available text tracks (-1 if no text track is selected)<br>
     * Assign this value to set a new text track
     */
    get textTrack(): number;
    set textTrack(trackId: number);
    /**
     * Add a new subtitle track with an SRT/VTT file downloaded from the url.<br>
     * It is possible to add multiple SRT/VTT files by using different names.<br>
     * <b>Warning: this function should not be called before the player is loaded</b><br>
     * <b>Note: This feature is only available in VOD.</b>
     * @param name name of the track
     * @param language language of the track
     * @param url url of the SRT/VTT file to be downloaded
     * @deprecated use {@link addExternalTextTrack}  instead
     */
    addSRTTextTrack(name: string, language: string, url: string): void;
    /**
     * Add a new subtitle track with an SRT/VTT file downloaded from the url.<br>
     * It is possible to add multiple SRT/VTT files by using different names.<br>
     * <b>Warning: this function should not be called before the player is loaded</b><br>
     * <b>Note: This feature is only available in VOD.</b>
     * @param name name of the track
     * @param language language of the track
     * @param url url of the SRT/VTT file to be downloaded
     * @param type subtitle type :'SRT' or 'VTT'
     */
    addExternalTextTrack(name: string, language: string, url: string, type: string): void;
    /**
     * Add a new thumbnail track with a VTT file downloaded from the url.<br>
     * @param url url of the VTT file to be downloaded
     * @param type Thumbnail file type :'VTT'
     */
    addExternalThumbnail(url: string, type: string): void;
    private updateVideoArea_;
    /**
     * Update Video Area. Should be call with 4 parameters to update the display area for video content playback.
     * The 4 parameters specify the left video area position, top video area position, video area width, and video area height based on a 1920 x 1080 resolution virtual screen, regardless of the actual application resolution.
     * This method should be call with no parameter to update the subtitle rendering area.
     * @param left position in pixels of the display area.
     * @param top position in pixels of the display area.
     * @param width display area width in pixels.
     * @param height display area height in pixels.
     */
    updateVideoArea(left?: number, top?: number, width?: number, height?: number, method?: string): void;
    /**
     * List of qualities available for playback <br>
     * The list is read-only, no actions on it will register.<br>
     * The Array is empty on platforms where video quality management is not available.
     * @return Array of {@link Quality}
     */
    get qualities(): Quality[];
    /** Index of the current Quality in the list of available qualities<br>
     * Assign this value to set a specific video quality for playback. Use the index of the desired quality from the list available qualities.<br>
     * Setting a quality disables the ABR.<br>
     * This feature may not be available on all platforms.
     */
    get quality(): number;
    set quality(levelId: number);
    /**
     * Volume of the player.
     * Value is between 0.0 and 1.0.
     */
    get volume(): number;
    /**
     * Number that will change the player volume
     */
    set volume(v: number);
    /**
     * Boolean representing the muted state of the player.
     */
    get muted(): boolean;
    /**
     * Boolean that will mute the player
     */
    set muted(m: boolean);
    private getUseNativePlayer_;
    /**
     * @hidden
     * Loads the given VAST or VMAP URL to be used for the next playback session.<br>
     * This method shall be called before calling player.load() to be taken into account<br>
     * <b>WARNING:</b> This feature is only available when the VAST/VMAP extension for VO Player is properly loaded (see {@page Advertisement with VMAP and VAST} for more details)
     * @param url Url of the VAST or VMAP or MAST description file
     * @param liveHack set to true to use relative timing for ad insertions when playing a Live stream
     * @param adProtocol ad protocol : VMAP , VAST or MAST
     * @deprecated use [load]{@link Player#load} with a {@link voplayer.player.MediaObject} instead
     */
    loadVMAP(url: string, liveHack: boolean, adProtocol: string): Promise<void>;
    /**
     * Inform the player that the videoContext div was set fullscreen.<br>
     * Will be used by the player to trigger VAST fullscreen tracking events.<br>
     * @param mode set to true to signal player is in fullscreen false otherwise
     */
    signalFullscreen(mode: boolean): void;
    /**
     * Return the program information as reported in the stream manifest, if any,
     * with program start time (`start`) and end time  (`end`) number properties in seconds
     */
    programInfo(): TimeRange;
    /**
     * Instantaneous current download speed (last video segment) in bits per seconds
     */
    get currentBandwidth(): number;
    /**
     * Estimated current download speed in bits per seconds
     */
    get estimatedBandwidth(): number;
    /**
     * @hidden
     */
    get poster(): string;
    /**
     * @hidden
     */
    set poster(value: string);
    /**
     * @hidden
     */
    setStartTime(startTime: number, opt_startTime: number, url: string): void;
    /**
     * @hidden
     */
    getStartTime(): number;
    /**
     * Returns true if current content has thumbnails.
     * @return {boolean} true if current content has thumbnails
     */
    hasThumbnails(): boolean;
    /**
     * Returns the thumbnail at time position.
     * @param {number} time - the time position, in seconds, based on the return value of the {@link duration} property for VOD streams or based on the `start`and `end` values of the {@link seekRange} method for LIVE streams.
     * @return {Promise} a promise which is resolved when thumbnail is retrieved for the given time position. Thumbnail value can be null if there is no thumbnails representation or
     * if there is no thumbnail at given time position
     */
    getThumbnail(time: number): Promise<Thumbnail | null>;
    getAllThumbnail(): Promise<ParsedThumbnails | null>;
    /**
     * Determines if the current content has sections.
     * This is an instance method that checks if the currently loaded content has associated sections. These sections are typically used to divide the content into meaningful parts.
     *
     * @returns {boolean} Returns true if the current content has sections. Returns false if the current content does not have sections, or if no content is loaded.
     */
    hasSections(): boolean;
    /**
     * Retrieves the sections of the current content.
     * This is an instance method that returns an array of Section objects, each representing a section in the currently loaded content. If no content is loaded, or if the content has no sections, returns an empty array.
     *
     * @returns {Section[]} An array of Section objects representing the sections of the current content. If no content is loaded, or if the content has no sections, returns an empty array.
     */
    getSections(): Section[];
    /**
     * Seeks to the start of the specified section in the current content.
     * This is an instance method that seeks to the start of the specified section in the currently loaded content. If no content is loaded, or if the content has no sections, or if the specified section does not exist, this method does nothing.
     * @param {string} sectionType - The type of the section to seek to. This is a string that represents the type of the section to seek to as returned via the getSections API.
     * @param {string} sectionName - The name of the section to seek to. This is a string that represents the name of the section to seek to as returned via the getSections API.
     */
    seekToSection(sectionType: string, sectionName: string): void;
    /**
     * Seeks to the end of the specified section in the current content.
     * This is an instance method that seeks to the end of the specified section in the currently loaded content. If no content is loaded, or if the content has no sections, this method does nothing.
     * @param {string} sectionType - The type of the section to seek to. This is a string that represents the type of the section to seek to as returned via the getSections API.
     * @param {string} sectionName - The name of the section to seek to. This is a string that represents the name of the section to seek to as returned via the getSections API.
     */
    seekToEndOfSection(sectionType: string, sectionName: string): void;
    /**
     * Skips the current section in the current content.
     * This is an instance method that skips the current section in the currently loaded content. If no content is loaded, or if the content has no sections, this method does nothing.
     */
    skipCurrentSection(): void;
    /**
     * @hidden
     */
    get corePlayer(): ICorePlayer;
    /**
     * @hidden
     */
    get videoElement(): HTMLVideoElement;
    /**
     * @hidden
     */
    emitEvent(event: string, data?: any): void;
    /**
     * @hidden
     */
    adjustSubtitleRenderingNode_(): void;
    /**
     * @hidden
     * @param buffering
     */
    setBuffering_(buffering: boolean, force?: boolean): void;
    /**
     * @hidden
     * @param buffering
     */
    isBuffering_(): boolean;
    /**
     * @hidden
     */
    videoElementErrorToPlayerError(error: any, message?: string): PlayerError;
    /**
     * @hidden
     */
    onVideoLoadedData_: () => void;
    /**
     * @hidden
     *   public by design called by player-dashjs
     */
    notifyDashBuffering(buffering: boolean, mediaType: string): void;
    /**
     * @hidden
     *
     * used by ui
     */
    isFeatureEnabled(feature: string): boolean;
    /**
     * @hidden
     */
    applyPreferredTextLanguage_(): void;
    /**
     * @hidden
     */
    applyPreferredAudioLanguage_(): void;
    /**
     * @hidden
     */
    _pauseMainContentOnly(): void;
    /**
     *
     * @hidden
     */
    isSMPTEEnabled_(): boolean;
    /**
     * @hidden
     */
    notifyPlaybackEnded(): void;
    /**
     * @hidden
     */
    getManifestUri(): string;
    /**
     * @hidden
     */
    get debugController(): DebugController;
    private removeQueryParam_;
    /**
     * @hidden public by design => used by Player.ts and PlayerBPKExtended.ts
     */
    filterAndSanitizeMediaUrl(url: string): FilteredUrl;
    /**
     * @hidden public by design => used by PlayerImgSrc
     */
    processDoneLoading: () => void;
    private useNativeHLSPlayer_;
    private onVideoCanPlayThrough_;
    private registerListeners;
    private unregisterListeners;
    private isValidStartTime;
    private isTypeOf;
    private isFileType;
    private isMimeType;
    private isHlsStream;
    private isMssStream;
    private displayVersion;
    private bufferingWatchDogExec_;
    private onVideoEvent_;
    private onVideoErrorEvent_;
    private _createSubtitleRenderingNode;
    private configContainsValidFairplayInfo_;
    private configContainsValidWidevineInfo_;
    private configContainsValidPlayreadyInfo_;
    private getUseTizenNative_;
    private getUseWebosNative_;
    /**
     * Load the player for a MP4 stream stream
     */
    private loadVideoSrc_;
    /**
     * Load the player for an image
     */
    private loadImgSrc_;
    /**
     * Load the player for an HLS stream
     */
    private loadHLS_;
    /**
     * Load the player for a native tizen
     */
    private loadNativeTizen_;
    /**
     * Load the player for a native webos
     */
    private loadNativeWebos_;
    /**
     * Load the player for a native apple player
     */
    private loadNativeHLS_;
    private loadDashJS_;
    private setQualityLevel_;
    private defaultConfig_;
    private configOverrides_;
    private setTextTrack;
    private setAudioTrack;
    private getTrackIndex_;
    private trackKindMatch_;
    /**
     * Callback triggered when the SRT/VTT manager has finished parsing a new  track.
     * @private
     */
    private onNewTextTrack_;
    setBufferingWatchdog_(enable: boolean): void;
    /**
     * Callback hooked on the 'timeupdate' event from the video element
     * used to synchronize display of SMPTE subtitles
     * used to trigger quality change event when playing DASH streams
     * @private
     */
    private onVideoTimeUpdate_;
    private initTextProviders_;
    private destroyCorePlayer_;
    private clearInternals_;
    private mergeConfigs_;
    /**
     * @hidden
     * @returns
     */
    _play(): Promise<void>;
    private bufferedAheadOf_;
    private bufferEnd_;
    /**
     * @hidden
     */
    clearVMAPAutoRefresh(): void;
    /**
     * @hidden
     * @param config
     * @returns
     */
    startVMAPAutoRefresh(config: VmapAutrefreshConfig): void;
    /**
     * @hidden
     */
    scte35Detected(event: any): void;
    protected loadAdrequest_(url: string, liveHack: boolean, adProtocol?: string): Promise<void>;
    private handleFmaError_;
}
/**
 * @hidden
 * @returns
 */
export declare function findNextFreeVideoElementId(): string;
/**
 * Integration facilitator function to create a 'bare' player using an existing video element<br>
 * No additional playback controls or UI elements are injected in the supporting web page
 * @param videoElementId id of the video element to use to create the player, without the '#' character
 * @param contentLayer <div> element that contains the player, used to render extended features such as ads
 * @return a new player instance
 */
export declare function createPlayer(videoElementId: string, contentLayer?: HTMLDivElement): Player;
