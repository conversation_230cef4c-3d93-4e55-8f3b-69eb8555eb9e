import { Player } from '../Player';
import { Section, VoDataSection } from '../Types';
export declare class SectionManager {
    private player_;
    private sections_;
    private previousPlayerTime_;
    private inSectionIdx_;
    private inAdBreak_;
    private wasInAdBreak_;
    constructor(player: Player);
    private static xorWithKey;
    /**
     * @hidden
     */
    static voDataDecrypt(encryptedData: string): any;
    /**
     * @hidden
     */
    setSections(sections: VoDataSection[]): void;
    /**
     * @hidden
     */
    seekToSection(sectionType: string, sectionName: string): void;
    /**
     * @hidden
     */
    seekToEndOfSection(sectionType: string, sectionName: string): void;
    /**
     * @hidden
     */
    skipCurrentSection(): void;
    /**
     * @hidden
     */
    reset(): void;
    /**
     * @hidden
     */
    getSections(): Section[];
    /**
     * @hidden
     */
    hasSections(): boolean;
    private checkSections_;
    private onPlayerSeeking_;
    private onPlayerSeeked_;
    private onAdBreakStarted_;
    private onAdBreakEnded_;
}
