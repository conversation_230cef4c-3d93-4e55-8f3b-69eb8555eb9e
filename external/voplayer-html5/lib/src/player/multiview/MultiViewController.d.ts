/**
 * @module MultiViewController
 */
/// <reference types="node" />
import 'clientjs';
import { Media } from '../Types';
import { Player } from '../Player';
import { EventEmitter } from 'events';
import { IMultiViewPlayerConfig, MultiViewControllerConfig, MultiViewSyncConfig } from './MultiviewTypes';
export default class MultiViewController extends EventEmitter {
    /**
     * Minimum number of player instance to use the MultiviewController
     * @type {number}
     */
    static MIN_NUMBER_OF_PLAYER_INSTANCES: number;
    /**
     * Maximum number of player instance to use the MultiviewController
     * @type {number}
     */
    static MAX_NUMBER_OF_PLAYER_INSTANCES: number;
    private _multiViewConfig;
    private _loaded;
    private _players;
    private _leaderPlayer;
    private _playersSyncTimer;
    private _playersSyncPaused;
    private _isInitialBuffering;
    private _audioSetOnSpecificPlayer;
    private _isDebug;
    constructor(multiViewConfig: MultiViewControllerConfig);
    /**
     * Set the Multiview synchronisation configuration
     * @param multiViewSyncConfig synchronisation configuration (MultiViewSyncConfig) can be set during playback to update the configuration
     */
    setSyncConfig: (multiViewSyncConfig: MultiViewSyncConfig) => void;
    /**
     * Add a new player to the MultiviewController
     * Emits an error if the load() was already called or if the maximum number of player has been reached
     * @param videoElementId (string) id of the html video element
     * @param mediaObj (string | Media) media object to pass to the player
     * @param config (IMultiViewPlayerConfig) synchronisation configuration
     * @return {Promise<Player>}
     */
    registerPlayer: (videoElementId: string, mediaObj: string | Media, config: IMultiViewPlayerConfig) => Promise<Player>;
    /**
     * Call load on all the registered players and start the playback on all players
     * Should be called only once after all the players have been registrered
    * Emits an error it was already called or is the minimum number of player has not been reached
    * @return {Promise<void>}
     */
    load: () => Promise<void>;
    /**
     * Call start on all the registered players
     * @return {Promise<void>}
     */
    startAllPlayers: () => Promise<void>;
    /**
     * Call pause on all the registered players
     * @return {Promise<void>}
     */
    pauseAllPlayers: () => Promise<void>;
    /**
     * Swap the playerRef player passed as parameter with the current leader player
     * By default it will set the audio to the new leader player
     * If possible, it will set lower qualities to the secondary player and higher quality to the leader player
     * @param playerRef (Player) player ref of the leader player
     * @param shouldSwapVideoElement (boolean) if true, it will emit MultiViewEvent.ON_SWAP_VIDEO_ELEMENT after the players have been swaped
     */
    swapLeaderPlayer: (playerRef: Player, shouldSwapVideoElement?: boolean) => void;
    /**
     * Set the audio to be used
     * If set, when swaping the player it will stay on this audio
     * @param playerRef (Player) player ref of the player from which we want to use the audio
     */
    setAudio: (playerRef?: Player) => void;
    /**
     * Set debug mode for the MultiviewController
     * @param isDebug (boolean) if true, the multiview controller will emit some debug events MultiViewEvent.ON_DEBUG and MultiViewEvent.ON_DEBUG_ACTION
     */
    setDebugMode: (isDebug: boolean) => void;
    /**
     * Reset the MultiviewController
     * @return {Promise<void>}
     */
    reset: () => Promise<void>;
    /**
     * Returns the list of registered players
     * @return {Player[]}
     */
    getPlayers: () => Player[];
    /**
     * Returns the current leader player
     * @return {Player}
     */
    getLeaderPlayer: () => Player;
    private _unsubscribeToLeaderEvents;
    private _subscribeToLeaderEvents;
    private _startAllPlayers;
    private _pauseAllPlayers;
    private _muteAllPlayers;
    private _isPlaying;
    private _startPlayersSync;
    private _pausePlayersSync;
    private _stopPlayerSync;
    private _synchronisePlayers;
    private _onPlayerBuffering;
    private _onPlayerPlay;
    private _onPlayerPause;
    private _emitEvent;
    private _isHlsJsSupported;
    private _isHvcSupported;
    private _isHvcStream;
    private _makeGetRequest;
}
