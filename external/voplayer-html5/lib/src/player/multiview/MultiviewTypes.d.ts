/**
 * @module MultiViewTypes
 */
import { Player } from '../Player';
import { Media, PlayerConfiguration } from '../Types';
/** @hidden */
export interface PlayerWrapper {
    player: Player;
    playerConfig: PlayerConfiguration;
    mediaObj: string | Media;
    videoElementId: string;
    vrConfig: IMultiViewPlayerVRConfig;
}
/**
 * Interface depicting multiview player VR configuration
 */
export interface IMultiViewPlayerVRConfig {
    isMonoscopic: boolean;
    isHorizontalSplit: boolean;
    is180: boolean;
}
/**
 * Interface depicting multiview player configuration
 */
export interface IMultiViewPlayerConfig {
    playerConfig: PlayerConfiguration;
    isLeader?: boolean;
    vrConfig?: IMultiViewPlayerVRConfig;
}
/**
 * Interface depicting multiview player synchronisation configuration
 */
export interface IMultiViewSyncConfig {
    playerSyncFrequency?: number;
    thresholdJumpPlayback?: number;
    thresoldForceSynchronization?: number;
    thresholdSpeedUpPlayback?: number;
    thresholdSpeedDownPlayback?: number;
    thresholdPausePlayback?: number;
}
/**
 * class depicting multiview player syncrhronisation configuration with default values
 */
export declare class MultiViewSyncConfig {
    playerSyncFrequency: number;
    thresholdJumpPlayback: number;
    thresoldForceSynchronization: number;
    thresholdSpeedUpPlayback: number;
    thresholdSpeedDownPlayback: number;
    thresholdPausePlayback: number;
    constructor({ playerSyncFrequency, thresholdJumpPlayback, thresoldForceSynchronization, thresholdSpeedUpPlayback, thresholdSpeedDownPlayback, thresholdPausePlayback }: IMultiViewSyncConfig);
}
/**
 * class depicting multiview controller configuration
 */
export declare class MultiViewControllerConfig {
    license: string;
    multiViewSyncConfig: MultiViewSyncConfig;
    constructor(license: string, multiViewSyncConfig?: MultiViewSyncConfig);
}
/**
* Event types emitted by the MultiviewController when an error specific to the MultiviewController happen (does not emit player related errors)
* @type {string}
*/
export declare class MultiViewEvent {
    static ERROR: string;
    static LEADER_PLAYER_PLAY: string;
    static LEADER_PLAYER_PAUSE: string;
    static ON_SWAP_VIDEO_ELEMENT: string;
    static ON_DEBUG: string;
    static ON_DEBUG_ACTION: string;
}
/**
* Error emited by the MultiviewController
* @type {MultiViewError}
*/
export interface MultiViewError {
    message: string;
}
/**
* Event emited by the MultiviewController when we are swapping the leader player with an other player
* @type {MultiViewSwapLeaderPlayer}
*/
export interface MultiViewSwapLeaderPlayer {
    oldLeaderVideoElementId: string;
    newLeaderVideoElementId: string;
    shouldActivateVRControls: boolean;
}
/**
* Event emited by the MultiviewController when debug mode is enabled to get some metadata about the players to help debugging
* @type {MultiViewDebug}
*/
export interface MultiViewDebug {
    videoElementId: string;
    gapWithLeader: number;
    currentTime: number;
    seekRangeStart: number;
    offset: number;
    playbackRate: number;
    isPlaying: boolean;
    isMuted: boolean;
    bandwidth: number;
}
/**
* Event emited by the MultiviewController when debug mode is enabled to get the latest action that occured on a specific player to help debugging
* @type {MultiViewDebugAction}
*/
export interface MultiViewDebugAction {
    videoElementId: string;
    action: string;
}
