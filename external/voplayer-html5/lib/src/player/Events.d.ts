/**
 * @module Events
 */
import { AdvertisementAdBreakInfo, AdvertisementInfo, AdvertisementType, Quality, Scte35ScheduledEvent, Section } from './Types';
export interface ResetEvent {
    /** the event type = 'reset' */
    type: string;
}
export interface DestroyedEvent {
    /** the event type = 'destroyed' */
    type: string;
}
export interface SubtitleRenderingNodeAdjustedEvent {
    /** the event type = 'subtitlerenderingnodeadjusted' */
    type: string;
}
export interface TracksChangedEvent {
    /** the event type = 'trackschanged' */
    type: string;
    /** signals which action has triggered the event:
     * <ul>
     * <li>'manifest' is coming from a change in the stream itself</li>
     * <li>'setAudio' signals a change of audio track trough the API</li>
     * <li>'setSubtitle' signals a change of subtitle track trough the API</li>
     * </ul>
     * */
    emittedBy: string;
}
export interface QualityChangedEvent {
    /** the event type = 'qualitychanged' */
    type: string;
    /** the current quality */
    currentLevel: Quality;
    /** indicates if the change of quality was forced (user selection) or not (ABR mechanism) */
    forced: boolean;
}
/**
 * Error events are fatal errors that are triggered by the player and lead to the stop of the playback.
 */
export interface ErrorEvent {
    /** the event type = 'error' */
    type: string;
    /** error category as depicted in {@link ErrorCategories} */
    category: number;
    /** error code as depicted in {@link ErrorCodes} */
    code: number;
    /** string message depicting the error */
    message: string;
    /** data information, could be a string, a blob or a stacktrace, provided by the core player */
    data: unknown;
}
/**
 * Warning events are non-fatal errors that are triggered by the player and do not lead to the stop of the playback.
 * They are usuallly triggered by the player when ads cannot be played, which are not blocking the main content playback.
 */
export interface WarningEvent {
    /** the event type = 'warning' */
    type: string;
    /** error category as depicted in {@link ErrorCategories} */
    category: number;
    /** error code as depicted in {@link ErrorCodes} */
    code: number;
    /** string message depicting the error */
    message: string;
    /** data information, could be a string, a blob or a stacktrace, provided by the core player. </br>
     * In the case of advertisment warnings data will include, when applicable, the following properties:
     * <ul>
     * <li>iabcode: VAST or VMAP error code as depicted in VAST3.0 or VMAP1.0.1 specification</li>
     * <li>adBreakInfo: adBreakInfo object {@link AdvertisementAdBreakInfo} </li>
     * <li>adInfo: adInfo object {@link AdvertisementInfo} </li>
     * </ul>
    */
    data: unknown;
}
export interface InfoEvent {
    type: string;
    message: string;
}
export interface PlayEvent {
    /** the event type = 'play' */
    type: string;
}
export interface PauseEvent {
    /** the event type = 'pause' */
    type: string;
}
export interface DurationChangeEvent {
    /** the event type = 'durationchange' */
    type: string;
}
export interface TimeUpdateEvent {
    /** the event type = 'timeupdate' */
    type: string;
}
export interface EndOfLiveReachedEvent {
    /** the event type = 'endoflivereached' */
    type: string;
}
export interface LoadedEvent {
    /** the event type = 'loaded' */
    type: string;
}
export interface EndedEvent {
    /** the event type = 'ended' */
    type: string;
}
export interface VolumeChangeEvent {
    /** the event type = 'volumechange' */
    type: string;
}
export interface BufferingEvent {
    /** the event type = 'buffering' */
    type: string;
    /** true when the player is re-buffering, false when the playback resumes */
    buffering: boolean;
}
export interface WaitingEvent {
    /** the event type = 'waiting' */
    type: string;
}
export interface PlayingEvent {
    /** the event type = 'playing' */
    type: string;
}
export interface SeekingEvent {
    /** the event type = 'seeking' */
    type: string;
}
export interface SeekedEvent {
    /** the event type = 'seeked' */
    type: string;
}
export interface DrmSessionCreatedEvent {
    /** the event type = 'drmSessionCreated' */
    type: string;
    /** The key system string */
    keySystem: string;
    /** The session id */
    sessionId: string;
    /** The session type, 'temporary' or 'persistent-license' */
    sessionType: string;
    /** The expiration date as epoch timecode */
    expirationTime: number;
}
export interface AdBreaksLoadedEvent {
    /** the event type = 'adBreaksLoaded' */
    type: string;
    /** list of expected adBreaks */
    adBreaksInfo: {
        [key: number]: AdvertisementAdBreakInfo;
    };
}
export interface AdBreakStartedEvent {
    /** the event type = 'adBreakStarted' */
    type: string;
    /** @deprecated id, as retrieved from the VMAT or MAST response of the started ad break => is now available in `adBreakInfo.id` */
    breakId: string;
    /** @deprecated list of advertiemsent ids (as retrieved from VAST) within the ad break => is now available in `adBreakInfo.adIds`*/
    breakAdIds: string[];
    /** @deprecated position, in seconds, of the ad break within the main stream (-1 represent the end of the main stream: postroll ad break) => is now available in `adBreakInfo.position`*/
    position: number;
    /** @deprecated Type of break according to vast (non linear or linear) => is now available in `adBreakInfo.type` */
    breakType: AdvertisementType;
    /** adBreaksInfo */
    adBreakInfo: AdvertisementAdBreakInfo;
}
export interface AdBreakEndedEvent {
    /** the event type = 'adBreakEnded' */
    type: string;
    /** @deprecated id, as retrieved from the VMAT or MAST response of the ended ad break => is now available in `adBreakInfo.id` */
    breakId: string;
    /** @deprecated Type of break according to vast (non linear or linear) => is now available in `adBreakInfo.type`  */
    breakType: AdvertisementType;
    /** adBreaksInfo */
    adBreakInfo: AdvertisementAdBreakInfo;
}
export interface AdStartedEvent {
    /** the event type = 'adStarted' */
    type: string;
    /** adBreaksInfo */
    adBreakInfo: AdvertisementAdBreakInfo;
    /** adInfo */
    adInfo: AdvertisementInfo;
    /** @deprecated id, as retrieved from the VAST response of the started advertisment => is now available in `adInfo.id`*/
    id: string;
    /** @deprecated clickThroughUrl as retrieved from the VAST response of the started advertisment => is now available in `adInfo.clickThroughUrl`*/
    clickThroughUrl: string;
    /** @deprecated Type of ad according to vast (non linear or linear)  => is now available in `adInfo.type` */
    adType: AdvertisementType;
    /** @deprecated url of the adevertisement media that will be played  => is now available in `adInfo.clickThroughUrl`  => is now available in `adInfo.media`*/
    media: string;
}
export interface AdLoadedEvent {
    /** the event type = 'adLoaded' */
    type: string;
    /** adBreaksInfo */
    adBreakInfo: AdvertisementAdBreakInfo;
    /** adInfo */
    adInfo: AdvertisementInfo;
    /** @deprecated id, as retrieved from the VAST response of the started advertisment => is now available in `adInfo.id` */
    id: string;
    /** @deprecated duration, in seconds, as retrieved from the VAST response of the started advertisment => is now available in `adInfo.duration` */
    duration: number;
    /** @deprecated Type of ad according to vast (non linear or linear) => is now available in `adInfo.type`  */
    adType: AdvertisementType;
}
export interface AdTimeUpdatedEvent {
    /** the event type = 'adTimeUpdated' */
    type: string;
    /** position, in seconds, of the ad */
    currentTime: number;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdVolumeChangeEvent {
    /** the event type = 'advolumechange' */
    type: string;
    /** id, as retrieved from the VAST response of the started advertisment */
    id: string;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdPlayEvent {
    /** the event type = 'adplay' */
    type: string;
    /** id, as retrieved from the VAST response of the started advertisment */
    id: string;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdPauseEvent {
    /** the event type = 'adpause' */
    type: string;
    /** id, as retrieved from the VAST response of the started advertisment */
    id: string;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdEndedEvent {
    /** the event type = 'adended' */
    type: string;
    /** adBreaksInfo */
    adBreakInfo: AdvertisementAdBreakInfo;
    /** adInfo */
    adInfo: AdvertisementInfo;
    /** @deprecated id, as retrieved from the VAST response of the ended advertisment => is now available in `adInfo.id` */
    id: string;
    /** @deprecated Type of ad according to vast (non linear or linear) => is now available in `adInfo.type`  */
    adType: AdvertisementType;
}
export interface SkipAdEvent {
    /** the event type = 'skipAd' */
    type: string;
    /** id, as retrieved from the VAST response of the started advertisment */
    id: string;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdActivatedEvent {
    /** the event type = 'adActivated' */
    type: string;
    /** id, as retrieved from the VAST response of the started advertisment */
    id: string;
    /** clickThroughUrl as retrieved from the VAST response of the started advertisment */
    clickThroughUrl: string;
    /** Type of ad according to vast (non linear or linear)   */
    adType: AdvertisementType;
}
export interface AdSeekingEvent {
    /** the event type = 'adSeeking' */
    type: string;
}
export interface AdSeekedEvent {
    /** the event type = 'adSeeked' */
    type: string;
}
export interface DashEventEvent {
    /** the event type = 'dashevent' */
    type: string;
}
export declare type Scte35CueDetectedEvent = Scte35ScheduledEvent;
export declare type Scte35CueStartEvent = Scte35ScheduledEvent;
export declare type Scte35CueEndEvent = Scte35ScheduledEvent;
export interface ExitVRImmersiveEvent {
    /** the event type = 'exitvrimmersive' */
    type: string;
}
export interface EnterVRImmersiveEvent {
    /** the event type = 'entervrimmersive' */
    type: string;
}
export interface ReadyEvent {
    /** the event type = 'ready' */
    type: string;
}
export interface SectionStartEvent {
    /** the event type = 'sectionstart' */
    type: string;
    /** the section object */
    section: Section;
}
export interface SectionEndEvent {
    /** the event type = 'sectionend' */
    type: string;
    /** the section object */
    section: Section;
}
export interface SectionInEvent {
    /** the event type = 'sectionin' */
    type: string;
    /** the section object */
    section: Section;
}
export interface SectionOutEvent {
    /** the event type = 'sectionout' */
    type: string;
    /** the section object */
    section: Section;
}
