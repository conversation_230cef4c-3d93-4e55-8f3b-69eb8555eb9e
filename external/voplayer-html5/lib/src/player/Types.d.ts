/**
 * @module Types
 */
import { ISpliceInfoSectionExtended } from './ads/AdManager';
/**
 * Enum to depict the type of core player used to play the content.
 */
export declare enum PlayerType {
    NONE = -1,
    HLS = 0,
    SAFARI_NATIVE_APPLEOS = 1,
    TIZEN_NATIVE = 2,
    DASHJS = 3,
    VIDEO_SRC = 4,
    IMG_SRC = 5,
    WEBOS_NATIVE = 6
}
export declare enum WidevineRobustnessLevel {
    /** Secure decryption in software is required. This typically corresponds to Widevine L3, which is a software-only solution that offers content protection but can be more vulnerable to piracy. */
    swSecureCrypto = "SW_SECURE_CRYPTO",
    /** Media data is to be decoded securely in software. This typically corresponds to Widevine L3, where both decryption and decoding happen in software, with no hardware security. */
    swSecureDecode = "SW_SECURE_DECODE",
    /** Secure decryption in hardware is required. This corresponds to Widevine L2, providing better protection through hardware. */
    hwSecureCrypto = "HW_SECURE_CRYPTO",
    /**  Media data is to be decoded securely in hardware. This corresponds to Widevine L1, which ensures maximum security by performing operations in a secure hardware environment. */
    hwSecureDecode = "HW_SECURE_DECODE",
    /** The media pipeline must be decrypted and decoded securely in hardware. This  corresponds to Widevine L1, where both decryption and decoding are handled securely by hardware. */
    hwSecureAll = "HW_SECURE_ALL"
}
export declare enum PlayreadyRobustnessLevel {
    /** Secure decryption and playback in software, providing a lower level of protection compared to hardware solutions. */
    software = "2000",
    /** Secure decryption and playback in hardware, offering strong content protection. */
    hardware = "3000"
}
export declare enum InitDataType {
    /** Common Encryption, standard for most DRM systems like Widevine and PlayReady. */
    cenc = "cenc",
    /** Used for WebM content, often with VP8/VP9 codecs in DRM systems. */
    webm = "webm",
    /** Specifies explicit key IDs without full encryption data, common in WebM or some specific encryption schemes.  */
    keyids = "keyids"
}
export declare enum EncryptionScheme {
    /** Common Encryption (CENC): The standard encryption scheme for many DRM systems, ensuring compatibility across various platforms. */
    cenc = "cenc",
    /** Cipher Block Chaining with Subsample Encryption (CBCS): Often used by FairPlay and supported by PlayReady, especially in HLS and CMAF for additional content protection. */
    cbcs = "cbcs"
}
export declare enum KeySystem {
    /** Used for PlayReady's modern recommendation, often in newer platforms and environments. */
    playReadyRecommendation = "com.microsoft.playready.recommendation",
    /** The original PlayReady key system for backward compatibility with older devices. */
    playReadyLegacy = "com.microsoft.playready",
    /** it is Google's DRM for protected content.  */
    widevine = "com.widevine.alpha"
}
export declare enum VideoCodecs {
    AVC1 = "video/mp4; codecs=\"avc1.42E01E\"",
    AVC1_Main = "video/mp4; codecs=\"avc1.4D401E\"",
    HEVC_HVC1 = "video/mp4; codecs=\"hvc1.1.6.L93.B0\"",
    HEVC_HEV1 = "video/mp4; codecs=\"hev1.1.6.L93.B0\"",
    H264_High = "video/mp4; codecs=\"avc1.64001F\"",
    AVC3_42E01E = "video/mp4; codecs=\"avc3.42E01E\"",
    HVC1_1_6_L93_90 = "video/mp4; codecs=\"hvc1.1.6.L93.90\"",
    AVC1_66_13 = "video/mp4; codecs=\"avc1.66.13\"",
    AVC1_66_30 = "video/mp4; codecs=\"avc1.66.30\"",
    AVC1_42001E = "video/mp4; codecs=\"avc1.42001e\"",
    AVC1_42001F = "video/mp4; codecs=\"avc1.42001f\"",
    AVC1_42801F = "video/mp4; codecs=\"avc1.42801f\"",
    AVC1_77_30 = "video/mp4; codecs=\"avc1.77.30\"",
    AVC1_4D001E = "video/mp4; codecs=\"avc1.4d001e\"",
    AVC1_4D001F = "video/mp4; codecs=\"avc1.4d001f\"",
    AVC1_640029 = "video/mp4; codecs=\"avc1.640029\"",
    DolbyVision_Profile4_DVH1 = "video/mp4; codecs=\"dvh1.04.07\"",
    DolbyVision_Profile4_DVHE = "video/mp4; codecs=\"dvhe.04.07\"",
    DolbyVision_Profile5_DVH1 = "video/mp4; codecs=\"dvh1.05.07\"",
    DolbyVision_Profile5_DVHE = "video/mp4; codecs=\"dvhe.05.07\"",
    DolbyVision_Profile8_DVH1 = "video/mp4; codecs=\"dvh1.08.07\"",
    DolbyVision_Profile8_DVHE = "video/mp4; codecs=\"dvhe.08.07\"",
    DolbyVision_Profile9_DVAV = "video/mp4; codecs=\"dvav.09.05\"",
    DolbyVision_Profile9_DVA1 = "video/mp4; codecs=\"dva1.09.05\""
}
export declare enum AudioCodecs {
    AAC = "audio/mp4; codecs=\"mp4a.40.2\"",
    MP3 = "audio/mpeg",
    HEAAC = "audio/mp4; codecs=\"mp4a.40.5\"",
    HEAACv2 = "audio/mp4; codecs=\"mp4a.40.29\"",
    xHEAAC = "audio/mp4; codecs=\"mp4a.40.42\"",
    DolbyAC3 = "audio/mp4; codecs=\"ac-3\"",
    DolbyEC3 = "audio/mp4; codecs=\"ec-3\"",
    DolbyAC4 = "audio/mp4; codecs=\"ac-4\"",
    AACMain = "audio/mp4; codecs=\"mp4a.40.1\"",
    ALAC = "audio/mp4; codecs=\"alac\""
}
/**
 * Enum depicting the accessibility level related to an {@link AudioTrack} or {@link TextTrack}
 */
export declare enum Accessibility {
    hardOfHearing = "hardOfHearing",
    visuallyImpaired = "visuallyImpaired"
}
/**
 * Interface depicting an audio track
 */
export interface AudioTrack {
    /** index of the tracks among all tracks */
    index: number;
    /** unique ID of the track */
    id: number;
    /** true if active */
    active: boolean;
    /** language of the track */
    language: string;
    /** kind of the track, ex "main-desc" */
    kind?: string;
    /** roles of the audio track, e.g. 'main' or 'commentary'. Will be empty for text tracks or variant tracks without audio (dash) */
    roles?: Array<string>;
    /** array of {@link Accessibility} of the audio track (dash) */
    accessibility?: Array<Accessibility>;
    /** mimeType of the audio track (dash) */
    mimeType?: string;
    /** codec of the audio track (dash) */
    codec?: string;
    /** label for the audio track (dash) */
    labels?: Array<string>;
    /** name for the audio track (hls) */
    name?: string;
}
/**
 * Interface depicting a TextTrack
 */
export interface TextTrack {
    /** index of the tracks among all tracks */
    index: number;
    /** true if active */
    active: boolean;
    /** language of the track */
    language: string;
    /** name of the track (HLS only) */
    name: string;
    /** kind of the track, either "captions" or "subtitles" */
    kind: string;
    /** roles of text track, e.g. "main", "caption" or "commentary" */
    roles: Array<string>;
    /** array of {@link Accessibility} of the text track */
    accessibility: Array<Accessibility>;
}
/**
 * Interface depicting the quality of a video representation
 */
export interface Quality {
    /** The unique ID of the track (internal player use only) */
    id: number;
    /** If true, this is the quality being streamed (another quality may temporarily be visible in the buffer) */
    active: boolean;
    /** The bandwidth required to play the track, in bits/sec */
    bandwidth: number;
    /** The width of the track in pixels */
    width?: number;
    /** The height of the track in pixels */
    height?: number;
    /** codec of the video track */
    codec?: string;
}
/** @hidden */
export interface Restrictions {
    /**  */
    maxBandwidth: number;
    /** */
    minWidth: number;
    /** */
    maxWidth: number;
    /** */
    minHeight: number;
    /** */
    maxHeight: number;
    /** */
    minPixels: number;
    /** */
    maxPixels: number;
}
/**
 * Interface depicting the preferred kind information of a track (audio or text)
 */
export interface PreferredTrackKind {
    /** The preferred kind of the track */
    kind?: string;
    /** The preferred role of the track */
    role?: string;
    /** The preferred accessibility of the track */
    accessibility?: Accessibility | string;
}
/**
* Interface depicting some application analytics set by the application
*/
export interface ApplicationAnalytics {
    /** Name of the Application set by the application */
    mdaApplicationName?: string;
    /** Version of the Application set by the application */
    mdaApplicationVersion?: string;
    /** Language used by the Application set by the application */
    mdaApplicationLanguage?: string;
    /** Name of the operator set by the application */
    mdaDeviceOperator?: string;
    /** CDN ID set by the application */
    sesCdnSessionId?: string;
    /** ID of the content or channel whatched set by the application */
    sesContentId?: string;
    /** Name of the content or channel watched set by the application */
    sesContentName?: string;
    /** Account/Household ID set by the application */
    sesSessionAccountId?: string;
    /** The serial number id of the device set by the application */
    sesSessionSerialId?: string;
    /** Type of the session set by the application (eg.: catchup, npvr, catchup-ad, live, VoD) */
    sesSessionType?: string;
    /** User profile id set by the application (to uniquely identify a user) */
    sesSessionUserId?: string;
    /** User profile type set by the application (eg.: classic, kid...) */
    sesSessionProfileType?: string;
    /** Genres associated to content, if several available should be concatenated and separated with coma  */
    sesContentGenre?: string;
    /** Type of content */
    sesContentType?: string;
    /** Season Number for a Series */
    sesContentSeasonNb?: string;
    /** Episode Number with a Season of a Series */
    sesContentEpisodeNb?: string;
    /** Latest playback audio language set by the end user */
    sesContentLanguage?: string;
    /** String representing the parental rating (PR) of the content */
    sesContentParentalRating?: string;
    /** String representing the provider of the content */
    sesContentProvider?: string;
    /** Id of the program watched */
    sesContentProgramId?: string;
    /** Name of the program watched */
    sesContentProgramName?: string;
    /** Type or tier of subscription associated to account */
    sesSessionSubscriptionType?: string;
    /** The duration in milliseconds between the user input to start a playback and the first frame displayed callback */
    sesPlaybackStartupTime?: number;
    sesContentBdcastStart?: number;
    sesContentBdcastStop?: number;
}
/**
 * Interface depicting player configuration parameters
 */
export interface PlayerConfiguration {
    /** true if the player should start automatically */
    autostart?: boolean;
    /** Specifies if an HLS clear content (non protected by Fairplay DRM) shall be loaded with Native Player */
    forceAppleNative?: boolean;
    /** Specifies if the video willl be played inline on Safari iOS (when false, video will start in fullscreen), defaults to false */
    iosPlaysInline?: boolean;
    /** ABR configuration and settings */
    abr?: AbrConfiguration;
    /** DRM configuraton and settings (valid only for DASH streams) */
    drm?: DrmConfiguration;
    /** Streaming configuration */
    streaming?: StreamingConfiguration;
    /** Subtitles configuration (for HLS only) */
    subtitles?: SubtitleConfiguration;
    /** Advertisement configuration */
    ads?: AdvertisementConfiguration;
    /** @hidden Restrictions for alternates selection by ABR algorithm */
    restrictions?: Restrictions;
    /** Object holding specific configuration for HLS core engine (hls.js configuration type, not applied in Safari) */
    hlsConfiguration?: Record<string, any>;
    /** Object holding specific configuration for DASH core engine */
    dashConfiguration?: Record<string, any>;
    /** Configuration specific for Tizen native implementation */
    tizenNativeConfiguration?: TizenNativeConfiguration;
    /** Configuration specific for WebOS native implementation */
    webosNativeConfiguration?: WebosNativeConfiguration;
    /** Configuration specific for Viaccess-Orca watermarking feature */
    vowmk?: VOWatermarkingConfiguration;
    /** preferredTextLanguage */
    preferredTextLanguage?: string;
    /** preferredTextKind */
    preferredTextKind?: PreferredTrackKind;
    /** preferredAudioLanguage */
    preferredAudioLanguage?: string;
    /** preferredAudioKind */
    preferredAudioKind?: PreferredTrackKind;
    /** anayltics set by the application and send with PSR */
    applicationAnalytics?: ApplicationAnalytics;
    /** Object holding specific configuration for the extenal Thumbnails implementation */
    externalThumbnails?: ExternalThumbnails;
    /** Determines if the stream type (Event/StartOver) is identified automatically. If 'true', the player computes it internally using 'seekRange().start' as an offset to set the start time to 0. On DASH streams, at least one manifest update is required for detection. If 'false' the integrator can specify this via 'isEventStream'. */
    isEventAutomatic?: boolean;
    /** Indicates if the stream is an Event/StartOver stream. Considered only if 'isEventAutomatic' is 'false'. If 'true' 'seekRange().start' is used as the offset to set the start time to 0.*/
    isEventStream?: boolean;
    /** Specifies the configuration for the Broadpeak SmartLib advertisement support. If set to 'true', the player will enable the advertisement support with Broadpeak SmartLib. */
    enableSmartLibAdvertisement?: boolean;
    /** schemeId to be supported. array of schemeID and callback */
    events?: Map<string, (event: any) => void> | null;
}
/** Inteface depicting media object to be provided when calling player load method */
export interface Media {
    /** URL of the main media to be played */
    url: string;
    /** The media mime type, if not provided media type is automatically detected */
    mimeType?: string;
    /** URL of a VAST manifest to be downloaded and used during the playback session */
    vastUrl?: string;
    /** URL of a VMAP manifest to be downloaded and used during the playback session */
    vmapUrl?: string;
    /** URL of a MAST manifest to be downloaded and used during the playback session */
    mastUrl?: string;
    /** Protocol agnostic ad request URL of a MAST/VAST/VMAP manifest to be downloaded and used during the playback */
    adRequestUrl?: string;
    /** time to start the media */
    startTime?: number;
    /** duration of the media when the media is an image. Not applicable to video */
    imageDuration?: number;
    /** indicate the player that it shall preserve the natural aspect ratio of an image when it is played */
    preserveImageAspectRatio?: boolean;
    /** indicate the player that is shall use a sepcific aspect ration when an image is played */
    imageAspectRatio?: number;
}
/** Interface to configuration when VMAP autrefresh should be performed on Live content */
export interface VmapAutrefreshConfig {
    /** VMAP url to be called periodically in a VMAP Live auto refresh use case */
    requestURL: string;
    /** Period duration in seconds between two calls to the VMAP url */
    duration: number;
}
/** Interface to configuration when Tizen native core player is used */
export interface TizenNativeConfiguration {
    /** If true, enables the use of the  native Tizen implementation on all Tizen platforms. */
    useFullNativePlayer: boolean;
    /** @deprecatedIf this property is deprecated. By default the generic player is used on all Tizen version. The `useFullNativePlayer` configuration property shall be used to handle specific cases. */
    forceUseGenericPlayer: boolean;
    /** If true, enables the use of the PREBUFFER_MODE for native Tizen implementation. Defaults to false. */
    usePrebufferMode: boolean;
    /** If true, the player will jump to live edge on live streams at loading time instead of trailing behind at ~48s behind (default player behavior). Defaults to false. */
    jumpToliveEdgeAtStart: boolean;
    /** If true, the player will recompute the live edge at loading time for native Tizen implementation. Defaults to false. */
    recomputeLiveEdge: boolean;
    /** If true, the player will play 4K resolution using the native Tizen player. Defaults to false. */
    enable4K?: boolean;
    /** If true, the player will play 8K resolution using the native Tizen player. Defaults to false. */
    enable8K?: boolean;
}
/** Interface to configuration when Webos native core player is used */
export interface WebosNativeConfiguration {
    /** If true, enables the use of the  native Webos implementation on all webos platfroms. */
    useFullNativePlayer: boolean;
}
/** Interface to BufferSensitivity used for {@page Adaptive Bitrate Control} */
export declare enum BufferSensitivity {
    none = "none",
    low = "low",
    medium = "medium",
    high = "high"
}
/** Interface for Adaptive Bitrate Configuration */
export interface AbrConfiguration {
    /** If true, enable adaptation by the current ABR manager. Defaults to true. */
    enabled: boolean;
    bufferSensitivity?: BufferSensitivity;
}
/** Interface for subtitle configuration */
export interface SubtitleConfiguration {
    /** If true, enable deocding of ID3 tags looking for SMPTE tracks. Defaults to false.
     * Applicable to HLS player.
     */
    enableSMPTE: boolean;
}
/** type of advertisement played. Used in AdBreak or Ad events to discriminate between linear and non linear ads */
export declare enum AdvertisementType {
    NONE = "none",
    LINEAR = "linear",
    NONLINEAR = "nonlinear",
    MIXED = "mixed",
    COMPANION = "companion"
}
/** position of advertisement adBreak within main content */
export declare enum AdvertisementPosition {
    NONE = "none",
    PREROLL = "preroll",
    MIDROLL = "midroll",
    POSTROLL = "postroll"
}
/** type to depict ad breaks. Used in Abreak and Ad loaded, started and ended events */
export interface AdvertisementAdBreakInfo {
    /** type of adBreak either linear, non linear or mixed if the adbreak is a combination of linear and non linear.
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    type: AdvertisementType;
    /** position of the adBreak within the main content: preroll, midroll, postroll */
    position: AdvertisementPosition;
    /**  number of ads in adBreak
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    nbAds: number;
    /** duration of the overall adBreak. Based on the information of the different VAST response.
     * Note: This value may not be the actual duration of the adBreak, if actual media durations differ from the durations indicated in the VAST responses.
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    duration: number;
    /** start time of the ad break in seconds. Will be set to -1 for postroll adBreaks.*/
    start: number;
    /** id of the ad break as depicted in the VMAP or MAST response */
    id: string;
    /** indicate the index of the active advertisement within the ad break over the number of advertisement.
     * Min value will be 1 and max value will be `nbAds`.
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    activeAdIdx: number;
    /** list of advertiemsent ids (as retrieved from VAST) within the ad break in order of appearance in the adBreak.
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    adIds: string[];
    /** list of advertiemsent infos (as retrieved from VAST) within the ad break in order of appearance in the adBreak.
     * Will be `null` on `adBreaksLoaded` and will be set on `adBreakStarted` event.
    */
    adInfos: AdvertisementInfo[];
    /**
     * VMAP or MAST url at the source of the adBreak.
     */
    url: string;
}
export interface AdvertisementInfo {
    /** duration of the playing advertisement as reported by the player*/
    duration: number;
    /** start time of the advertismement based on VMAP scheduled start time and player duration of previous played advertisments in case of adPods*/
    start: number;
    /** id of the advertisement as depicted in the VAST response */
    id: string;
    /** mime type of the playing advertisement ad indicated in the VAST response */
    mimeType: string;
    /** Type of ad according to VAST (non linear or linear) */
    type: AdvertisementType;
    /** url of the advertisement media that will be played */
    media: string;
    /** clickThroughUrl as retrieved from the VAST response of the started advertisement */
    clickThroughUrl: string;
    /** skipDelay in seconds indicating the time to wait before the advertisement could be skipped */
    skipDelay: number;
    /** VAST url at the source of the advertisement*/
    url: string;
}
/** Interface for advertisment configuration */
export interface AdvertisementConfiguration {
    /** Timeout value in seconds for any VAST calls (default 5s) */
    vastTimeout?: number;
    /** Timeout value in seconds for any VMAP calls (default 5s) */
    vmapTimeout?: number;
    /** Timeout value in seconds for any agnostic ad calls (default 5s) */
    adRequestTimeout?: number;
    /** Indicate if the video element provided to the main content player shall be used also for the advertisment player
     * Use case: low end platforms where only one video element could be instantiated (e.g. some SmartTvs or USB Dongles).
     * Impact: less smooth transitions between main content and advertisment.
     * Default value: false.
     */
    useMainPlayerVideoElement?: boolean;
    /** Callback to the application to resume the playback of the main content after the playback of an advertisement.
     * Only applicable in combination with useMainPlayerVideoElement set to true.
     * If not provided the player will resume the main content based on the url and configuration previously provided.
     * If provided the application will be in charge to resume the main content.
     * Use case: it is in particular required when manifest url or license url depends on token that may expire.
     */
    mainPlayerResumeCallback?: ((manifestUrl: string, configuration: PlayerConfiguration, resumeTime: number) => Promise<void>);
    /** Callback to the application to reload the content after a postroll advertisment.
     * Only applicable in combination with useMainPlayerVideoElement set to true.
     * If not provided the player will reload the main content based on the url and configuration previously provided.
     * If provided the application will be in charge to reload the main content.
     * Use case: it is in particular required when manifest url or license url depends on token that may expire.
     */
    mainPlayerReloadCallback?: ((manifestUrl: string, configuration: PlayerConfiguration) => Promise<void>);
    /** By default when parsing a MAST or VMAP response, all included VAST will be loaded to build the varions tunnels expected during the playback of the content
     * This might be problematic when a content will include a lot of ad breaks with a lot of advertisement in each ad breaks.
     * Consequently you may set the boolean `loadVastWithVmap` to false so that the VAST are requested at the time when the adBreak occurs.
     * Default value: true
     */
    loadVastWithVmap?: boolean;
    /** Defines the maximum value in kbps to limit an ad bitrate. If not provided, there will be no limitation on the ad bitrate.
     * Default value: -1
     */
    maxAdBitrate: number;
    /** Defines the maximum value in pixels to limit an ad resolution. If not provided, there will be no limitation on the ad resolution.
     * Default value: -1
    */
    maxAdResolutionHeight: number;
    /** This API indicates to VO Player to discard all the tunnel if an ad is unavailable in a tunnel
     * Default value: false
     */
    disableTunnelIfAdUnavailable?: boolean;
    /**
     * Configures the VO Player to wait for a response from the application before proceeding with the ad tunnel playback.
     * This can be used to control whether an ad tunnel should be skipped when preloaded.
     *
     * When the VO Player calls the callback adCallbackForTunnelSkipping, the application should respond
     * by calling the skipAdTunnel method to indicate whether the ad tunnel should be skipped or played.
     *
    */
    adCallbackForTunnelSkipping?: ((adBreaksInfo: {
        [key: number]: AdvertisementAdBreakInfo;
    }) => Promise<boolean>);
    /**
     * Boolean indicating if Mid roll advertisements before start position can be played when the argument is set to true.
     * By default, the value is false and only preroll will be played if present in VMAP or MAST response
     */
    enableVmapUpToPoliciesForStartAt?: boolean;
    /** Defines the duration of all non-linear ads that are received via a VAST request.
     * Default value: -1, in this case the duration of the non-linear ad is the one provided in the VAST response.
    */
    nonLinearDurationOverride?: number;
    /**
     * Configuration for VMAP skip offset overriding.
     */
    vmapSkipOffsetOverride?: VmapSkipOffset;
    /** Indicates whether the non-linear advertisement is closable by the user.
     * Default value: false
    */
    nonLinearClosable?: boolean;
    /** Configures whether seeking during advertisement playback is allowed.
     * By default, seeking during advertisements is forbidden both via the UI and the API.
     * When seeking is attempted during an ad with seeking disabled, a warning event is triggered with the error code ADS_LINEAR_SEEK_FORBIDDEN.
     * This method allows you to change this behavior by specifying whether seeking should be permitted during ads.
     * Default value: false
    */
    seekToAllowed?: boolean;
    /** This API indicates to VO Player to allow or forbid skipping of VAST v2.0 ads
     * Default value: true
     */
    vastV20SkipAllowed?: boolean;
    /** This API indicates if the SDK will or not report only the last VAST progress tracking event.
     * Default value: false
    */
    vastReportLastProgressEvent?: boolean;
    /** Provides an array of strings representing the HTTP custom headers to be set when making an advertisement call.
    * If not provided, no custom headers will be set.
    * Example value: { "headerKey1": "headerValue1", "headerKey2": "headerValue2" }
    * Default value: {}
    */
    adHttpCustomHeaders: Record<string, string>;
    /**
     * vmapSeekBackwardPlayUpTo: Configures the maximum number of ads to replay when seeking backward in the content.
     * int vmapSeekBackwardPlayUpTo; // integer varying from -1 (all ads will be replayed) to N defined by the application/integrator. playback position remains the one scheduled by the vmap. default value 0 (ads already played are not replayed)
     */
    vmapSeekBackwardPlayUpTo: number;
    /**
    * Configuration option defined by the application/integrator to control the number of ads played when seeking forward in the content.
    *
    * Values:
    * - `-1` : All scheduled ads will be played (default behavior).
    * - `0`  : No ads will be played after the seek position.
    * - `N`  : Only the first `N` ads after the seek position will be played.
    *
    * The playback position will start at the seek position, and all ads to be played are stacked.
    */
    vmapSeekForwardPlayUpTo: number;
    /**
     * Specifies the maximum time, in milliseconds, allowed for parsing an ad request.
     * If the ad request parsing time exceeds this limit, the process will be interrupted,
     * triggering an internal 'reset' event to halt the `loadAdRequest` function and avoid unnecessary delays.
     * Setting this parameter allows finer control over ad-loading performance,
     * ensuring a quick timeout when ad request parsing takes longer than expected.
     *
     * By default, this parameter is undefined, meaning there is no timeout restriction on ad request parsing.
     *
     * Example:
     * To set a 100 ms maximum parsing time, use `maxAdRequestParsingTime: 100`.
     */
    maxAdRequestParsingTime?: number;
    /**
     * Specifies if event infoEvent should be sent or not
     */
    sendInfoEvent?: boolean;
}
/** Enum depicting the required DRM agent */
export declare enum DrmAgentType {
    none = "",
    widevine = "widevine",
    playready = "playready"
}
/** Interface to configuration for DRM */
export interface DrmConfiguration {
    /** identification of the DRM agent (either widevine or playready) */
    drmAgentType?: DrmAgentType;
    /** A dictionary which maps key system IDs to their license servers.  */
    servers?: {
        [keySystemId: string]: string;
    };
    /** Forces the use of the Clear Key CDM. A map of key IDs (hex) to keys (hex) */
    clearKeys?: Record<string, unknown>;
    /** A dictionary which maps key system IDs to advanced DRM configuration for those key systems */
    advanced?: {
        [keySystemId: string]: AdvancedDrmConfiguration;
    };
}
/** Interface for streaming configuration */
export interface StreamingConfiguration {
    /** Sets whether player should jump large gaps (discontinuities) in the buffer (default false). */
    jumpLargeGaps?: boolean;
    /** When an error occurs on the download of a segment the player will try to seek forward to skip the potential missing segment.
     * `maxNbSeekOnDownloadError` defines the maximum number of occurence the player will try to seek forward.
     * If this fails then the player will try to reload the manifest.
     * Robustness use case: segment missing on CDN or origin server.
     * Only applicable to Dash and MSS VOD streams.
     * Default value: 3.
     */
    maxNbSeekOnDownloadError?: number;
    /** When an error occurs the player may try to reload the manifest in order to recover the error.
     * `maxNbManifestReloadOnError` defined the maximum number of occurence the player will try to reload the manifest.
     * If this fails the player will issue an error with information relevant for a reload from the application.
     * Robustness use case: handling small network interuptions.
     * Applicable to all Dash and MSS streams.
     * Default value: 5.
     */
    maxNbManifestReloadOnError?: number;
}
/** Interface for VO watermarking configuration */
export interface VOWatermarkingConfiguration {
    /** The backend URL the player will call to retrieve the watermarking information (in production environment, this URL should be a signed one). */
    url?: string;
    /** A callback function the player will call to get a new backend URL after the current watermarking information has expired. This function must return a Promise that resolves with the new URL to use. */
    updateUrlCb?: (() => Promise<string>);
    /** */
    'x-api-key'?: string;
    /** @hidden For test purposes: only enable watermaking on clear stream (default true) */
    'apply-drm'?: boolean;
    /** @hidden For test purposes only: enable watermarking algo 2 on browser (default false) */
    'apply-algo2-onBrowsers'?: boolean;
}
/** Interface for Advanced DRM configuration */
export interface AdvancedDrmConfiguration {
    /** True if the application requires the key system to support distinctive identifiers. */
    distinctiveIdentifierRequired?: boolean;
    /** True if the application requires the key system to support persistent state, e.g., for persistent license storage. */
    persistentStateRequired?: boolean;
    /** IDs of persistent sessions to be loaded */
    sessionIds?: string[];
    /** A key-system-specific string that specifies a required security level for video. If the security level requested is not available on the platform, playback will fail. Defaults to '', i.e., no specific robustness required. */
    videoRobustness?: string;
    /** A key-system-specific string that specifies a required security level for audio. If the security level requested is not available on the platform, playback will fail. Defaults to '', i.e., no specific robustness required. */
    audioRobustness?: string;
    /** A key-system-specific server certificate used to encrypt license requests. Its use is optional and is meant as an optimization to avoid a round-trip to request a certificate. */
    serverCertificate?: Uint8Array;
    /** The URL to download a key-system-specific server certificate used to encrypt license requests. Its use is optional. */
    serverCertificateUrl?: string;
    /** httpRequestHeaders contain header to pass for encrypted video request x-dt-custom-data */
    httpRequestHeaders?: {
        'X-AxDRM-Message'?: string;
        'x-dt-custom-data'?: string;
    };
    /** license server url */
    serverURL?: string;
}
export interface TimeRange {
    /** range start time */
    start: number;
    /** range start time */
    end: number;
}
export interface VideoSize {
    /** width of the video */
    width: number;
    /** height of the video */
    height: number;
}
export interface RequestFilterData {
    /** Body of the request, type can differ according to DRM */
    body: any;
    /** Selected Key sysyem string (e.g. com.widevine.alpha) that may be used to select your actions in your filter implementation */
    drm?: string;
    /** */
    mode?: string;
    /** HTTP headers of the request */
    headers: Record<string, string>;
    /** Type of the license requesr */
    licenseRequestType?: string;
    /** HTTP method of the request */
    method: string;
    /** Type of the response expected */
    responseType?: string;
    /** If provided, sessionId of the request */
    sessionId?: string;
    /** If provided, keyId for the session */
    keyIds?: Array<string>;
    /** license request uri */
    uri: string;
    /** Indicates if cross site credentials are allowed */
    allowCrossSiteCredentials?: boolean;
}
export interface ResponseFilterData {
    /** License response data */
    data: any;
    /** uri of the original license request */
    uri: string;
    /** Selected Key sysyem string (e.g. com.widevine.alpha) that may be used to select your actions in your filter implementation */
    drm?: string;
    /** HTTP headers of the response */
    headers: Record<string, string>;
}
export interface BufferLevel {
    /** buffer level */
    level: number;
    /** buffer target */
    target?: number;
}
export interface BufferLevels {
    [type: string]: BufferLevel;
}
export interface Thumbnail {
    /** The url of thumbnail tiles file */
    url: string;
    /** Width of thumbnail tile */
    width: number;
    /** Height of thumbnail tile */
    height: number;
    /** Position x of thumbnail tile */
    x: number;
    /** Position y of thumbnail tile */
    y: number;
}
/**
 * @hidden
 */
export interface BufferingWatchDog {
    timer: ReturnType<typeof setInterval>;
    checkActive: boolean;
    checkInterval: number;
}
/**
 * @hidden
 */
export interface FilteredUrl {
    /** manifest URL where vovmapurl or vovasturl query could have been filtered out */
    sanitizedUrl: string;
    /** VAST url that may be present within manifest URL under vovasturl query parameter */
    voVASTUrl: string;
    /** VMAP url that may be present within manifest URL under vovmapurl query parameter */
    voVMAPUrl: string;
}
/**
 * @hidden
 */
export interface ComposeAdBreakAndAdInfos {
    adBreakInfo: AdvertisementAdBreakInfo;
    adInfo: AdvertisementInfo;
}
export interface Scte35ScheduledEvent {
    /** id of the SCTE-35 event as retrived from the manifest */
    id: string;
    /** presentation time of the SCTE-35 event */
    presentationTime: number;
    /** duration of the SCTE-35 event  */
    duration?: number;
    /** boolean indicating if the SCTE-35 cue is active */
    active: boolean;
    /** Splice Info Section of the SCTE-35 event */
    scte35Data: ISpliceInfoSectionExtended;
}
/** Interface for External Thumbnails configuration */
export interface ExternalThumbnails {
    /** List of External Thumbnails Format supported (currently limited to Thumbnails carried over Web VTT protocol) */
    type?: ExternalThumbnailsFormatType;
    /** The backend URL the player will call to retrieve the thumbnails description. */
    url?: string;
}
/** Formated Data of the thumbnail */
export interface ThumbnailData {
    /** Starting time to display the thumbnail */
    startingTime: number;
    /** End time to display the thumbnail */
    endTime: number;
    /** The backend URL the player will call to retrieve the thumbnails tiles file */
    url: string;
    /** Width of thumbnail tile */
    width: number;
    /** Height of thumbnail tile */
    height: number;
    /** Position x of thumbnail tile */
    x: number;
    /** Position x of the retrieved tile */
    y: number;
}
/** List of Formated Data of thumbnails obtained from an url */
export interface ParsedThumbnails {
    /** The backend URL the player will call to retrieve the thumbnails description. */
    url: string;
    /** List of all thumbnail data obtained from the url */
    thumbnailData: ThumbnailData[];
}
/**
 * Enum list of the different format for thumbnails carried externally {@link externalThumbnails}
 */
export declare enum ExternalThumbnailsFormatType {
    vtt = "vtt"
}
/** interface for Sections as retrived from vodata provided via manifest URL */
export interface Section {
    /** type of the section */
    type: string;
    /** start time of the section in seconds */
    startTime: number;
    /** end time of the section in seconds */
    endTime: number;
    /** name of the section */
    name: string;
}
export interface VoDataSection {
    type: string;
    startTime: string;
    endTime: string;
    name: string;
}
export interface InternalAdBreak {
    url: string;
    time: number;
    skipOffsetFormat: string;
    skipOffsetValue: number;
    skipStatus: number;
    vmapBreak: InternalVmapBreak;
    lastPlayed?: number;
    seekDisabled: boolean;
}
export interface InternalVmapBreak {
    adSource: any;
    adSources?: any[];
    breakType: string;
    breakId: string;
    extensions: any[];
    repeatAfter: number;
    timeOffset: string;
    trackingEvents: any[];
    vastData: any;
}
/**
 * Interface representing VMAP skip offset override settings.
 */
export declare enum VmapSkipStatus {
    NO_OVERRIDE = -1,
    CANCEL_SKIP = 0,
    USE_SKIP_OFFSET = 1,
    OVERRIDE_SKIP_OFFSET = 2
}
export declare enum VmapSkipOffsetFormat {
    PERCENT = "percent",
    SECONDS = "seconds"
}
export interface VmapSkipOffset {
    /**
     * Indicates how the skip offset overriding will be applied.
     * -1 (default): No overriding.
     *  0: Any skip offset value defined for an advertisement will be canceled (advertisement will become non-skippable).
     *  1: If a skip offset is defined for an advertisement, it will be kept,
     *     else the one provided via `vmapSkipOffsetValue` will be used.
     *  2: All advertisements will have their skip offset overridden by `vmapSkipOffsetValue`.
     */
    vmapSkipStatus: VmapSkipStatus;
    /**
     * The skip offset value to be used on an advertisement depending on the `vmapSkipStatus`.
     * Skip offset value represents the time before the advertisement would be skippable and
     * can be expressed in percentage or time.
     * For percentage use format: 'x%' e.g. '10%'.
     * For time use format: 'HH:MM:SS' e.g. '00:00:15'.
     */
    vmapSkipOffsetValue: number;
    /**
     * Specifies the format of the `vmapSkipOffsetValue`.
     * 'percent': Indicates that the `vmapSkipOffsetValue` is in percentage format.
     * 'second': Indicates that the `vmapSkipOffsetValue` is in time format (HH:MM:SS).
     * Default value: seconds
     */
    vmapSkipOffsetFormat: VmapSkipOffsetFormat;
}
