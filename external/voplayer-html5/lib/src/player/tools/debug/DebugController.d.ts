import { Player } from '../../Player';
import { DebugPanel } from './DebugPanel';
export declare class DebugController {
    private player;
    debugPanel: DebugPanel;
    private show;
    private onKeyPressedListener;
    private version;
    /**
     * Constructor.
     */
    constructor(player: Player, video: HTMLElement);
    reset(): void;
    start(): void;
    stop(): void;
    showDebugPanel(show: boolean, debugRenderingDiv?: HTMLDivElement): void;
    enableDebugLogs(enable: boolean): void;
    private onKeyPressed;
}
