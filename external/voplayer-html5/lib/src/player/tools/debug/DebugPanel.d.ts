import { Player } from '../../Player';
declare global {
    interface Window {
        Chartist: any;
    }
}
export declare class DebugPanel {
    private videoElement;
    private player;
    private containerDiv;
    private debugDiv;
    private stateChangedListener;
    private pauseListener;
    private qualityRenderedListener;
    private keySystemSelectedListener;
    private needKeyListener;
    version: string;
    private protocol;
    private drm;
    private streamType;
    private playbackState;
    private sessionTime;
    private loadingTime;
    private playbackTime;
    private duration;
    private dvrSize;
    private latency;
    private playbackQualityIndex;
    private lastVideoPlaybackQuality;
    private videoFps;
    private videoDroppedFrames;
    private videoBuffer;
    private audioBuffer;
    private bufferTarget;
    private videoBitrate;
    private videoEncodingBitrate;
    private connectionSpeed;
    private aVideoFps;
    private aVideoDroppedFrames;
    private aVideoBuffer;
    private aAudioBuffer;
    private aVideoQuality;
    private aVideoQualityRatio;
    private aConnectionSpeed;
    private aEstimatedBandwidth;
    private videoFpsChart;
    private videoDroppedFrames<PERSON>hart;
    private videoBuffer<PERSON>hart;
    private audioBuffer<PERSON>hart;
    private videoBitrateChart;
    private connectionSpeedChart;
    private estimatedBandwidthChart;
    private pollInterval;
    private loadTime;
    private startTime;
    /**
     * Constructor.
     */
    constructor(player: Player, video: HTMLElement, version: string);
    reset(): void;
    start(): void;
    stop(): void;
    show(show: boolean, debugRenderingDiv?: HTMLDivElement): void;
    private registerEvents;
    private unregisterEvents;
    private init;
    private onStateChanged;
    private onPause;
    private onQualityRendered;
    private onKeySystemSelected;
    private onNeedKey;
    private startMetricsPolling;
    private stopMetricsPolling;
    private updateMetrics;
    private updateVideoFps;
    private updateBufferLevels;
    private updateVideoBitrate;
    private updateConnectionSpeed;
    private getEncodingBitrate;
    private appendChartValue;
    private render;
    private formatDuration;
    private formatBitrate;
    private formatEncodingBitrate;
    private getPlaybackQualityInfos;
    private loadChartist;
    private onChartistLoaded;
    private showDebug;
    private updateUI;
    private createDebugPanel;
    private addStyles;
    private getHeadSection;
    private getCurrentDateTime;
}
