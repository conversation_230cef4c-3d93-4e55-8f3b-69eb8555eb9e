/**
 * @module HandleExternalThumbnails
 */
import { Player } from '../Player';
import { ParsedThumbnails, Thumbnail } from '../Types';
export declare class HandleExternalThumbnails {
    private playerConfiguration_;
    private player_;
    private parsedFiles_;
    private currentFile_;
    private vttParser_;
    /**
    * Constructor.
    */
    constructor(player: Player);
    reset(): void;
    init: (url: string) => Promise<void>;
    setThumbnail: (thumbnail: ParsedThumbnails) => void;
    hasThumbnail: () => boolean;
    getAllThumbnail: () => Promise<ParsedThumbnails | null>;
    getThumbnail: (time: number) => Promise<Thumbnail | null>;
    private loadRequest;
    private readVttFile;
    private parseVTT;
    private checkIfExist_;
}
