/**
 * @module Errors
 */
export declare enum ErrorCategories {
    /** Errors from the network stack. */
    NETWORK_CATEGORY = 1,
    /** Errors parsing text streams. */
    TEXT_CATEGORY = 2,
    /** Errors parsing or processing audio or video streams. */
    MEDIA_CATEGORY = 3,
    /** Errors parsing the Manifest. */
    MANIFEST_CATEGORY = 4,
    /** Errors related to streaming. */
    STREAMING_CATEGORY = 5,
    /** Errors related to DRM. */
    DRM_CATEGORY = 6,
    /** Miscellaneous errors from the player. */
    PLAYER_CATEGORY = 7,
    /** Errors triggered internally, in principle should not be raised to the integrator */
    INTERNAL_CATEGORY = 8,
    /** Errors triggered on API change, used to trigger depreciation of API */
    API_CATEGORY = 9,
    /** Errors triggered with no specific mapping category */
    UNMAPPED_CATEGORY = 10,
    /** Errors triggered from Advertisement module */
    ADVERTISEMENT_CATEGORY = 20,
    /** Errors triggered from FMA module */
    FMA_CATEGORY = 12
}
export declare enum ErrorCodes {
    /**
     * Some error raised do not have a specific error code, in that case a code 0 will be issued.
     */
    NO_ERROR_CODE = 0,
    /**
     * A network request was made using an unsupported URI scheme.
     * <br> error.data[0] is the URI.
     */
    UNSUPPORTED_SCHEME = 1000,
    /**
     * An HTTP network request returned an HTTP status that indicated a failure.<br/>
     * The `data` object returned within the error will provide information on the type of error and information to potentially restart a session.
     * <ul>
     * <li> `data.type` : the type of content that failed to download, either 'MANIFEST' or 'CONTENT'</li>
     * <li> `data.uri` : string representing the uri where the error occurred.</li>
     * <li> `data.status` : HTTTP error code.</li>
     * <li> `data.currentTime` : time of playback when the error occurred, can be used to reload the manifest and resume at the time of playback.</li>
     * <li> `data.seekRange` : seekRange of playback when the error occurred.</li>
     * <li> `data.audioTrackId` : id of the selected audio track when the error occurred. May be used when reloading the manifest and resume with the proper audio track.</li>
     * <li> `data.textTrackId` : id of the selected text track when the error occurred. May be used when reloading the manifest and resume with the proper text track.<:li>
     * </ul>
     */
    BAD_HTTP_STATUS = 1001,
    /**
     * An HTTP network request failed with an error, but not from the server.
     * <br> error.data[0] is the URI.
     */
    HTTP_ERROR = 1002,
    /**
     * A network request timed out.
     * <br> error.data[0] is the URI.
     */
    TIMEOUT = 1003,
    /**
     * A network request was made with a malformed data URI.
     * <br> error.data[0] is the URI.
     */
    MALFORMED_DATA_URI = 1004,
    /**
     * A request filter threw an error.
     * <br> error.data[0] is the original error.
     */
    REQUEST_FILTER_ERROR = 1006,
    /**
     * A response filter threw an error.
     * <br> error.data[0] is the original error.
     */
    RESPONSE_FILTER_ERROR = 1007,
    /** */
    MALFORMED_TEST_URI = 1008,
    /** */
    UNEXPECTED_TEST_REQUEST = 1009,
    /** The text parser failed to parse a text stream due to an invalid header. */
    INVALID_TEXT_HEADER = 2000,
    /** The text parser failed to parse a text stream due to an invalid cue. */
    INVALID_TEXT_CUE = 2001,
    /**
     * Was unable to detect the encoding of the response text.  Suggest adding
     * byte-order-markings to the response data.
     */
    UNABLE_TO_DETECT_ENCODING = 2003,
    /** The response data contains invalid Unicode character encoding. */
    BAD_ENCODING = 2004,
    /**
     * The XML parser failed to parse an xml stream, or the XML lacks mandatory
     * elements for TTML.
     */
    INVALID_XML = 2005,
    /**
     * MP4 segment does not contain TTML.
     */
    INVALID_MP4_TTML = 2007,
    /**
     * MP4 segment does not contain VTT.
     */
    INVALID_MP4_VTT = 2008,
    /** */
    UNABLE_TO_EXTRACT_CUE_START_TIME = 2009,
    /**
     * Some component tried to read past the end of a buffer.  The segment index,
     * init segment, or PSSH may be malformed.
     */
    BUFFER_READ_OUT_OF_BOUNDS = 3000,
    /**
     * Some component tried to parse an integer that was too large to fit in a
     * JavaScript number without rounding error.  JavaScript can only natively
     * represent integers up to 53 bits.
     */
    JS_INTEGER_OVERFLOW = 3001,
    /**
     * The MP4 SIDX parser found the wrong box type.
     * Either the segment index range is incorrect or the data is corrupt.
     */
    MP4_SIDX_WRONG_BOX_TYPE = 3004,
    /**
     * The MP4 SIDX parser encountered an invalid timescale.
     * The segment index data may be corrupt.
     */
    MP4_SIDX_INVALID_TIMESCALE = 3005,
    /** The MP4 SIDX parser encountered a type of SIDX that is not supported. */
    MP4_SIDX_TYPE_NOT_SUPPORTED = 3006,
    /**
     * A MediaSource operation failed.
     * <br> error.data[0] is a MediaError code from the video element.
     */
    MEDIA_SOURCE_OPERATION_FAILED = 3014,
    /**
     * A MediaSource operation threw an exception.
     * <br> error.data[0] is the exception that was thrown.
     */
    MEDIA_SOURCE_OPERATION_THREW = 3015,
    /**
     * The video element reported an error.
     * The `data` object returned within the error provide information on video element error.
     * <ul>
     * <li> `data.mesage` : the video element error message</li>
     * <li> `data.code` : the video element error code</li>
     * <li> `data.extCode` : the video element error extended code (Microsoft extendend error code available on IE)</li>
     * </ul>
     */
    VIDEO_ERROR = 3016,
    /**
     * A MediaSource operation threw QuotaExceededError and recovery failed. The
     * content cannot be played correctly because the segments are too large for
     * the browser/platform. This may occur when attempting to play very high
     * quality, very high bitrate content on low-end devices.
     * <br> error.data[0] is the type of content which caused the error.
     */
    QUOTA_EXCEEDED_ERROR = 3017,
    /** */
    TRANSMUXING_FAILED = 3018,
    /** */
    HLS_FRAG_DECRYPT_ERROR = 3019,
    /** */
    HLS_FRAG_PARSING_ERROR = 3020,
    /**
     * Error raised when trying to play an image but no duration was provided for its playback
     */
    IMG_ERROR_NO_DURATION = 3021,
    /**
     * Generic Image error
     */
    IMG_ERROR_GENERIC = 3022,
    /**
     * The Player was unable to guess the manifest type based on file extension
     * or MIME type.  To fix, try one of the following:
     * <br><ul>
     *   <li>Rename the manifest so that the URI ends in a well-known extension.
     *   <li>Configure the server to send a recognizable Content-Type header.
     *   <li>Configure the server to accept a HEAD request for the manifest.
     * </ul>
     * <br> error.data[0] is the manifest URI.
     */
    UNABLE_TO_GUESS_MANIFEST_TYPE = 4000,
    /** The DASH Manifest contained invalid XML markup. */
    DASH_INVALID_XML = 4001,
    /**
     * The DASH Manifest contained a Representation with insufficient segment
     * information.
     */
    DASH_NO_SEGMENT_INFO = 4002,
    /** The DASH Manifest contained an AdaptationSet with no Representations. */
    DASH_EMPTY_ADAPTATION_SET = 4003,
    /** The DASH Manifest contained an Period with no AdaptationSets. */
    DASH_EMPTY_PERIOD = 4004,
    /** The DASH Manifest contained an unsupported container format. */
    DASH_UNSUPPORTED_CONTAINER = 4006,
    /** The embedded PSSH data has invalid encoding. */
    DASH_PSSH_BAD_ENCODING = 4007,
    /**
     * There is an AdaptationSet whose Representations do not have any common
     * key-systems.
     */
    DASH_NO_COMMON_KEY_SYSTEM = 4008,
    /** Having multiple key IDs per Representation is not supported. */
    DASH_MULTIPLE_KEY_IDS_NOT_SUPPORTED = 4009,
    /** The DASH Manifest specifies conflicting key IDs. */
    DASH_CONFLICTING_KEY_IDS = 4010,
    /**
     * The manifest contains a period with no playable streams.
     * Either the period was originally empty, or the streams within cannot be
     * played on this browser or platform.
     */
    UNPLAYABLE_PERIOD = 4011,
    /**
     * There exist some streams that could be decoded, but restrictions imposed
     * by the application or the key system prevent us from playing.  This may
     * happen under the following conditions:
     * <ul>
     *   <li>The application has given restrictions to the Player that restrict
     *       at least one content type completely (e.g. no playable audio).
     *   <li>The key system has imposed output restrictions that cannot be met
     *       (such as HDCP) and there are no unrestricted alternatives.
     * </ul>
     */
    RESTRICTIONS_CANNOT_BE_MET = 4012,
    /**
     * There was an error while parsing the manifest.
     */
    MANIFEST_PARSING_FAILURE = 4013,
    /**
     * No valid periods were found in the manifest.  Please check that your
     * manifest is correct and free of typos.
     */
    NO_PERIODS = 4014,
    /**
     * An error was raised while updating some data.
     */
    DATA_UPDATE_FAILURE = 4015,
    /**
     * HLS tag has an invalid name that doesn't start with '#EXT'
     */
    /**
     * HLS playlist has both Master and Media/Segment tags.
     */
    /**
     * A Representation has an id that is the same as another Representation in
     * the same Period.  This makes manifest updates impossible since we cannot
     * map the updated Representation to the old one.
     */
    DASH_DUPLICATE_REPRESENTATION_ID = 4018,
    /**
     * HLS manifest has several #EXT-X-MAP tags. We can only
     * support one at the moment.
     */
    /**
     * HLS parser was unable to guess mime type of a stream.
     * <br> error.data[0] is the stream file's extension.
     */
    /**
     * No Master Playlist has been provided. Master playlist provides
     * vital information about the streams (like codecs) that is
     * required for MediaSource. We don't support directly providing
     * a Media Playlist.
     */
    /**
     * One of the required attributes was not provided.
     * HLS manifest is invalid.
     * <br> error.data[0] is the missing attribute's name.
     */
    /**
     * One of the required tags was not provided.
     * HLS manifest is invalid.
     * <br> error.data[0] is the missing tag's name.
     */
    /**
     * HLS parser was unable to guess codecs of a stream.
     * <br> error.data[0] is the list of all codecs for the variant.
     */
    /**
     * HLS parser has encountered encrypted content with unsupported
     * KEYFORMAT attributes.
     */
    /** */
    DASH_XLINK_DEPTH_LIMIT = 4028,
    /** */
    CONTENT_UNSUPPORTED_BY_BROWSER = 4032,
    /** */
    CANNOT_ADD_EXTERNAL_TEXT_TO_LIVE_STREAM = 4033,
    /** */
    INVALID_STREAMS_CHOSEN = 5005,
    /**
     * The manifest indicated protected content, but the manifest parser was
     * unable to determine what key systems should be used.
     */
    NO_RECOGNIZED_KEY_SYSTEMS = 6000,
    /**
     * None of the requested key system configurations are available.  This may
     * happen under the following conditions:
     * <ul>
     *   <li> The key system is not supported.
     *   <li> The key system does not support the features requested (e.g. persistent state).
     *   <li> A user prompt was shown and the user denied access.
     *   <li> The key system is not available from unsecure contexts. (ie. requires HTTPS) See https://goo.gl/EEhZqT.
     * </ul>
     */
    REQUESTED_KEY_SYSTEM_CONFIG_UNAVAILABLE = 6001,
    /**
     * The browser found one of the requested key systems, but it failed to
     * create an instance of the CDM for some unknown reason.
     * <br> error.data[0] is an error message string from the browser.
     */
    FAILED_TO_CREATE_CDM = 6002,
    /**
     * The browser found one of the requested key systems and created an instance
     * of the CDM, but it failed to attach the CDM to the video for some unknown
     * reason.
     * <br> error.data[0] is an error message string from the browser.
     */
    FAILED_TO_ATTACH_TO_VIDEO = 6003,
    /**
     * The CDM rejected the server certificate supplied by the application.
     * The certificate may be malformed or in an unsupported format.
     * <br> error.data[0] is an error message string from the browser.
     */
    INVALID_SERVER_CERTIFICATE = 6004,
    /**
     * The CDM refused to create a session for some unknown reason.
     * <br> error.data[0] is an error message string from the browser.
     */
    FAILED_TO_CREATE_SESSION = 6005,
    /**
     * The CDM was unable to generate a license request for the init data it was
     * given.  The init data may be malformed or in an unsupported format.
     * <br> error.data[0] is an error message string from the browser.
     */
    FAILED_TO_GENERATE_LICENSE_REQUEST = 6006,
    /**
     * The license request failed.  This could be a timeout, a network failure, or
     * a rejection by the server.
     * <br> error.data[0] is a shaka.util.Error from the networking engine.
     */
    LICENSE_REQUEST_FAILED = 6007,
    /**
     * The license response was rejected by the CDM.  The server's response may be
     * invalid or malformed for this CDM.
     * <br> error.data[0] is an error message string from the browser.
     */
    LICENSE_RESPONSE_REJECTED = 6008,
    /**
     * The manifest does not specify any DRM info, but the content is encrypted.
     * Either the manifest or the manifest parser are broken.
     */
    ENCRYPTED_CONTENT_WITHOUT_DRM_INFO = 6010,
    /**
     * An error was raised when the DRM status expired.
     */
    STATUS_CHANGED_EXPIRED = 6011,
    /**
     * No license server was given for the key system signaled by the manifest.
     * A license server URI is required for every key system.
     */
    NO_LICENSE_SERVER_GIVEN = 6012,
    /**
     * A required offline session was removed.  The content is not playable.
     */
    OFFLINE_SESSION_REMOVED = 6013,
    /**
     * The license has expired.  This is triggered when playback is stalled on a
     * 'waitingforkeys' event and there are any expired keys in the key status map
     * of any active session.
     */
    EXPIRED = 6014,
    /**
     * An error indicating that a server certificate is required to properly perform a license request.
     */
    SERVER_CERTIFICATE_REQUIRED = 6015,
    /** */
    INIT_DATA_TRANSFORM_ERROR = 6016,
    /** */
    DRM_ERROR = 6055,
    /**
     * An error was issued by the key message system.
     */
    KEY_MESSAGE_ERROR = 6017,
    /**
     * An error was raised when the video output security does not meet the DRM restrictions requirements (e.g. HDCP security level)
     */
    STATUS_CHANGED_OUTPUT_RESTRICTED = 6018,
    /**
     * An error was raised when the key is not currently usable for decryption because of an error in the CDM
     */
    STATUS_CHANGED_INTERNAL_ERROR = 6019,
    /**
    * An error raised when a timemout occurs while loading the Widevine certificate
    */
    WIDEVINE_CERTIFICATE_LOADING_TIMEOUT = 6020,
    /**
     * An error raised when an error occur while loading the Widevine certificate
     */
    WIDEVINE_CERTIFICATE_LOADING_ERROR = 6021,
    LOAD_INTERRUPTED = 7000,
    /** */
    OPERATION_ABORTED = 7001,
    /** */
    NO_VIDEO_ELEMENT = 7002,
    /** */
    INVALID_OPERATION = 7003,
    /** */
    INVALID_STATE = 7004,
    /** */
    INVALID_PLAYER_LICENSE = 7005,
    /**
     * A warning issued when the play promise is rejected.
     * This can occur due to:
     * 1. The play request being interrupted (e.g., by a pause or a new load request).
     * 2. The browser blocking autoplay (e.g., due to autoplay policy restrictions).
   */
    PLAY_PROMISE_INTERRUPTED = 7006,
    /**
     * A non documented internal error.
     */
    INTERNAL_ERROR = 8000,
    /**
     * A waring or an error related to API dpreciation
     */
    API_DEPRECATED = 9000,
    /**
     * A waring or an error related to API not supported
     */
    API_NOT_SUPPORTED = 9001,
    /**
     * An error emited by a core player which is not mapped to a high level player error.
     */
    UNMAPPED_ERROR = 10000,
    /**
     * An error related to an exception raised by the core player.
     */
    EXCEPTION_ERROR = 10001,
    /**
     * A warning when we try to use the FMA before being initialized
     */
    FMA_OTHER_ERROR = 12000,
    /**
     * A warning when we try to use the FMA before being initialized
     */
    FMA_NOT_INITILIZED = 12001,
    /**
     * A warning when we try to call an FMA API with wrong parameters
     */
    FMA_BAD_ARGUMENTS = 12002,
    /**
     * A warning when the Player License does not have the Ads feature enabled
     */
    ADS_PLAYER_LICENSE_NO_SUPPORT = 20000,
    /**
     * A warning when the Ads extension library is missing
     */
    ADS_PLAYER_EXTENSION_MISSING = 20001,
    /**
     * An error issued when advertisment media asset could not be retrieved.
     */
    ADS_REQUEST_FAILED = 20002,
    /**
     * An error issued when VAST ad response is empty or its body is empty.
    */
    ADS_VAST_EMPTY_RESPONSE = 20003,
    /**
     * An error issued when VMAP or MAST ad response is empty or its body is empty.
    */
    ADS_VMAP_MAST_EMPTY_RESPONSE = 20004,
    /**
     * An error issued when an ad request is interrupted due to exceeding `maxAdRequestParsingTime`.
     */
    ADS_AD_REQUEST_INTERRUPTED = 20006,
    /**
     * VAST XML parsing error according to VAST 3.0 specifications.
     */
    ADS_VAST_XML_PARSING_ERROR = 20100,
    /**
     * VAST schema validation error.
     */
    ADS_VAST_SCHEMA_VALIDATION_ERROR = 20101,
    /**
     * VAST version of response not supported.
     */
    ADS_VAST_REPONSE_NOT_SUPPORTED = 20102,
    /**
     * Trafficking error. Video player received an Ad type that it was not expecting and/or cannot  display.
     */
    ADS_VAST_AD_TYPE_NOT_EXPECTED = 20200,
    /**
     * Video player expecting different linearity
     */
    ADS_VAST_LINEARITY_NOT_EXPECTED = 20201,
    /**
     * Video player expecting different duration.
     */
    ADS_VAST_DURATION_NOT_EXPECTED = 20202,
    /**
     * Video player expecting different size.
     */
    ADS_VAST_SIZE_NOT_EXPECTED = 20203,
    /**
     * General Wrapper error.
     */
    ADS_VAST_GENERAL_WRAPPER_ERROR = 20300,
    /**
     * Timeout of VAST URI provided in Wrapper element, or of VAST URI provided in a subsequent  Wrapper element. (URI was either unavailable or reached a timeout as defined by the video player.)
     */
    ADS_VAST_TIMEOUT_VAST_URI = 20301,
    /**
     * Wrapper limit reached, as defined by the video player. Too many Wrapper responses have been received with no InLine response.
     */
    ADS_VAST_TOO_MANY_WRAPPER_RESPONSES_RECEIVED = 20302,
    /**
     * No Ads VAST response after one or more Wrappers.
     */
    ADS_VAST_NO_RESPONSE = 20303,
    /**
     * General Linear error. Video player is unable to display the Linear Ad.
     */
    ADS_VAST_GENERAL_LINEAR_ERROR = 20400,
    /**
     * File not found. Unable to find Linear/MediaFile from URI.
     */
    ADS_VAST_FILE_NOT_FOUND = 20401,
    /**
     * Timeout of MediaFile URI.
     */
    ADS_VAST_TIMEOUT_MEDIAFILE_URI = 20402,
    /**
     * Couldn’t find MediaFile that is supported by this video player, based on the attributes of the MediaFile element
     */
    ADS_VAST_MEDIAFILE_NOT_SUPPORTED = 20403,
    /**
     * Problem displaying MediaFile. Video player found a MediaFile with supported type but couldn’t display it. MediaFile may include: unsupported codecs, different MIME type than MediaFile@type, unsupported delivery method, etc.
     */
    ADS_VAST_PROBLEM_DISPLAYING_MEDIAFILE = 20405,
    /**
     * General NonLinearAds error.
     */
    ADS_VAST_GENERAL_NONLINEAR_ERROR = 20500,
    /**
     * Unable to display NonLinear Ad because creative dimensions do not align with creative display area (i.e. creative dimension too large).
     */
    ADS_VAST_NONLINEAR_CREATIVE_DIMENSION_TOO_LARGE = 20501,
    /**
     * Unable to fetch NonLinearAds/NonLinear resource.
     */
    ADS_VAST_UNABLE_TO_FETCH_RESSOURCE = 20502,
    /**
     * Couldn’t find NonLinear resource with supported type.
     */
    ADS_VAST_NONLINEAR_RESSOURCE_NOT_SUPPORTED = 20503,
    /**
     * General CompanionAds error.
     */
    ADS_VAST_GENERAL_COMPANIONSADS_ERROR = 20600,
    /**
     * Unable to display Companion because creative dimensions do not fit within Companion display area (i.e., no available space).
     */
    ADS_VAST_COMPANION_CREATIVE_DIMENSION_TOO_LARGE = 20601,
    /**
     * Unable to display Required Companion.
     */
    ADS_VAST_UNABLE_TO_DISPLAY_COMPANION = 20602,
    /**
     * Unable to fetch CompanionAds/Companion resource.
     */
    ADS_VAST_UNABLE_TO_FETCH_COMPANION_RESSOURCE = 20603,
    /**
     * Couldn’t find Companion resource with supported type.
     */
    ADS_VAST_COMPANION_RESSOURCE_TYPE_NOT_SUPPORTED = 20604,
    /**
     * Undefined Error
     */
    ADS_VAST_UNDEFINED_ERROR = 20900,
    /**
     * General VPAID error
     */
    ADS_VAST_GENERAL_VPAID_ERROR = 20901,
    /**
     * VMAP schema error
     */
    ADS_VMAP_SCHEMA_ERROR = 21000,
    /**
     * VMAP version of response not supported
     */
    ADS_VMAP_RESPONSE_NOT_SUPPORTED = 21001,
    /**
     * VMAP parsing error
     */
    ADS_VMAP_PARSING_ERROR = 21002,
    /**
     * AdBreak type not supported
     */
    ADS_VMAP_ADBREAK_TYPE_NOT_SUPPORTED = 21003,
    /**
     * General ad response document error
     */
    ADS_VMAP_GENERAL_AD_RESPONSE_DOCUMENT_ERROR = 21004,
    /**
     * Ad response template type not supported
     */
    ADS_VMAP_AD_TEMPLATE_NOT_SUPPORTED = 21005,
    /**
     * Ad response document extraction or parsing error
     */
    ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR = 21006,
    /**
     * Ad response document retrieval timeout
     */
    ADS_VMAP_AD_DOCUMENT_RETRIEVAL_TIMEOUT = 21007,
    /**
     * Ad response document retrieval error (e.g., Http server responded with error code)
     */
    ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR = 21008,
    /**
     * MAST Parsing error
     */
    ADS_MAST_PARSING_ERROR = 21100,
    /**
     * Ad MAST response document extraction or parsing error
     */
    ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR = 21106,
    /**
     * Ad linear seek forbiddem
     */
    ADS_LINEAR_SEEK_FORBIDDEN = 20005
}
export interface Error {
    /** error category as depicted in {@link ErrorCategories} */
    category: number;
    /** error code as depicted in {@link ErrorCodes} */
    code: number;
    /** string message depicting the error */
    message: string;
    /** data information, could be a string, a blob or a stacktrace, provided by the core player */
    data: any;
}
