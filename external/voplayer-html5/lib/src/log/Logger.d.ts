/**
 * Log levels.
 * @enum {number}
 * @exportDoc
 */
export declare enum LogLevel {
    NONE = 0,
    ERROR = 1,
    WARNING = 2,
    LOG = 3,
    INFO = 4,
    DEBUG = 5
}
/**
 * @summary
 * A console logging mechanism
 * @exportDoc
 */
export declare class Logger {
    /**
     * Log levels.
     * @enum {number}
     * @exportDoc
     */
    static LogLevel: typeof LogLevel;
    static level: LogLevel;
    static coreLevel: LogLevel;
    /**
     * This log is for when an error occurs.  This should always be accompanied
     * with an error event, thrown exception, or rejected Promise.
     *
     * @param {...*} args
     */
    static error(...args: any[]): void;
    /**
     * This log is for possible errors or things that may be surprising to a user.
     * For example, if we work around unusual or bad content, we should warn that
     * they should fix their content.  Deprecation messages and messages the app
     * shouldn't ignore should use alwaysWarn instead.
     *
     * @param {...*} args
     */
    static warning(...args: any[]): void;
    /**
     * This log is for general output of logging information
     *
     * @param {...*} args
     */
    static log(...args: any[]): void;
    /**
     * This log is for messages to the user about what is happening.  For example,
     * when we update a manifest or install a polyfill.
     *
     * @param {...*} args
     */
    static info(...args: any[]): void;
    /**
     * This log is to aid *users* in debugging their content.  This should be for
     * logs about the content and what we do with it.  For example, when we change
     * streams or what we are choosing.
     *
     * @param {...*} args
     */
    static debug(...args: any[]): void;
    /**
     * This log is for warning messages that should always be displayed
     *
     * @param {...*} args
     */
    static alwaysWarn(...args: any[]): void;
    static setLevel(level: number): void;
    static setCoreLevel(level: number): void;
    static getLevel(): number;
    static getCoreLevel(): number;
}
