import { Player } from '../player/Player';
declare global {
    interface Element {
        mozRequestFullScreen?: () => Promise<void>;
        msRequestFullscreen?: () => Promise<void>;
        webkitRequestFullscreen?: () => Promise<void>;
    }
}
declare global {
    interface Document {
        mozFullScreenElement?: () => Promise<void>;
        msFullscreenElement?: () => Promise<void>;
        webkitCurrentFullScreenElement?: () => Promise<void>;
        webkitFullscreenElement?: () => Promise<void>;
        mozFullScreenEnabled?: () => Promise<void>;
        msFullscreenEnabled?: () => Promise<void>;
        webkitFullscreenEnabled?: () => Promise<void>;
        msExitFullscreen?: () => Promise<void>;
        mozCancelFullScreen?: () => Promise<void>;
        webkitCancelFullScreen?: () => Promise<void>;
    }
}
/**
  * Player UI Controller
  * @namespace PlaybackController
  */
/**
  * @typedef {{
   *   license: string,
   *   jquery: string,
   *   qualityLevelFormater: function,
   *   audioTrackDisplayFormater: function,
   *   textTrackDisplayFormater: function
   * }} PlaybackUIConfiguration
   *
   * @description
   * An object describing the configuration to use for the creation and drive of
   * the Player's playback controls and UI.<br>
   * read-only as changing any values does not have any effect.
   *
   * @property {!string} license
   *   License to use the player, as issued by Viaccess-Orca. This parameter is *MANDATORY* to allow playback.
   * @property {string} jquery
   *   The url of the jQuery script that will be injected and used for the controls.<br>
   *   When used, the injection is done in complete isolation using jQuery.noConflict(true); feature
   *   If not defined, the controller will try to use an already existing instance of jQuery.
   * @property {PlaybackController.UIFormaters#QualityLevelFormaterFunction} qualityLevelFormater
   *   function used instead of the inner default one to create the textual display of quality levels in the playback UI.
   * @property {PlaybackController.UIFormaters#AudioTrackTextFormaterFunction} audioTrackDisplayFormater
   *   function used instead of the inner default one to create the textual display of an audio track in the playback UI.
   * @property {PlaybackController.UIFormaters#TextTrackTextFormaterFunction} textTrackDisplayFormater
   *   function used instead of the inner default one to create the textual display of a text track in the playback UI.
   * @exportDoc
   * @memberof PlaybackController
   */
/**
 * VOPlayer video player controller class (UI)
 * @param videoContextRootElement {!string} id of the HTML DOM element containing all the player controls (controls outside this element will NOT be observed and driven)
 * @param video {!HTMLMediaElement} video element linked to the controls
 * @param player {!voplayer.Player} VOPlayer player element to control
 * @param config {Object} configuration objet for the controller
 */
export declare class VOPlayerController {
    private externalProgramInfoInSeconds;
    private config_;
    private window;
    private _jQuery;
    private rootElement_;
    private videoElement_;
    private player_;
    private prevMouseMoveX;
    private prevMouseMoveY;
    private progressTouchEventIdentifier;
    private disableProgressInteraction_;
    private lastFastSeekTime;
    private toolbarDisplayAllowed;
    private lastFullscreenToggleTime;
    private customerOverlayHidden;
    private controlsDisplayMode;
    private _playerClickTogglePlayPauseActionTimeoutId;
    private client;
    private touchTime;
    private inAdBreak_;
    private controlsEnabled_;
    private controlsHidden_;
    private controlsHiding_;
    private hideOverlayAllowed_;
    private combinedLiveStartOverTransition_;
    private shallResumeOnStartOver_;
    private livePausedTime_;
    private timeUpdateOnPauseInterval;
    private wasDragging_;
    private playerSubtitleLayer;
    private $volumeLevelIcons_;
    private $volumeLevel_;
    constructor(videoContextRootElement: any, video: any, player: Player, config: any);
    initController_: () => void;
    getDefaultConfig_: () => {
        useQualityOnlyButton: boolean;
        togglePlayPauseOnClick: boolean;
        toggleFullscreenOnDblClick: boolean;
        displayRelativePositionOnLive: boolean;
        preventControlsAutoBehavior: {
            playPause: boolean;
            progressBar: boolean;
            time: boolean;
        };
        controlsHiddingDelay: {
            mouseIn: number;
            mouseOut: number;
        };
        customerOverlay: {
            display: string;
            htmlContent: string;
        };
        qualityLevelFormater: any;
        textTrackDisplayFormater: any;
        audioTrackDisplayFormater: any;
        externalProgramInfo: any;
        minimumTimeShiftWindow: number;
        onStartOver: any;
        onBackToLive: any;
        onEndProgramReached: any;
    };
    fullScreenPolyfill_: () => void;
    /**
     * Proxy fullscreen events after changing their name.
     * @param {!Event} event
     * @private
     */
    proxyEvent_: (event: any) => void;
    mergeConfig_: (destination: any, source: any, template: any, path: any) => void;
    updateConfiguration: (config: any) => void;
    getMSEpochTimeFromISODate: (dateStr: any) => number;
    extractExternalProgramInfoFromConfig: () => void;
    applyLocalization: () => void;
    registerClickEvent: (jqe: any, listener: any) => void;
    loadUI: () => void;
    onProgressTouchStart: (event: any) => void;
    onProgressTouchMove: (event: any) => void;
    onProgressTouchEnd: (event: any) => void;
    onProgressMouseDown: (event: any) => void;
    onKeyDown: (event: any) => void;
    onWinMouseUp: (event: any) => void;
    onProgressHoverIn: (event: any) => void;
    onProgressHoverOut: (event: any) => void;
    onLiveButtonHoverIn: (event: any) => void;
    onLiveButtonHoverOut: (event: any) => void;
    onSurfacePreventingOverlayHideIn: (event: any) => void;
    onSurfacePreventingOverlayHideOut: (event: any) => void;
    restartHideControlsOverlayTimeout_: (timeout: any) => void;
    hideControlsOverlay: () => void;
    onVideoMouseMove: (event: any) => void;
    _showUIControlsIfNeeded: () => void;
    onWinMouseMove: (event: any, fromVideoMove: any) => void;
    backToGlobalMenu: (event: any) => void;
    showAudioOptMenu: (event: any) => void;
    showQualityOptMenu: (event: any) => void;
    showSubtitlesOptMenu: (event: any) => void;
    toggleOptionMenu: (event: any) => void;
    hideOptionMenu: (event: any) => void;
    closeQualityMenu: (event: any) => void;
    toggleQualityMenu: (event: any) => void;
    onVideoVolumeChanged: (event: any) => void;
    updateVolumeIcon: (volPercent: any) => void;
    toggleMute: (event: any) => void;
    onMouseMoveOverProgress: (event: any) => void;
    updateLiveButtonPosition: () => void;
    updateToolTipPosition: (event: any) => void;
    onManifestLoaded: (event: any) => void;
    onQualitySelected: (event: any) => void;
    onAudioTrackSelected: (event: any) => void;
    onAdaptation: (event: any) => void;
    formatQualityLevel: (level: any, forPrimaryDisplay: any, abrEnabled: any) => any;
    formatBitrateText: (bitrate: any) => any;
    formatAudioTrackText: (track: any) => any;
    onTrackChanged: (event: any) => void;
    adjustSubtitleArea: () => void;
    onAdjustSubtitleRenderingNode: () => void;
    formatSubtitleTrackText: (track: any) => any;
    onSubtitleTrackSelected: (event: any) => void;
    onPresentationModeChanged: (event: any) => void;
    onFullScreenChanged: (event: any) => void;
    onVideoEvent: (event: any) => void;
    onPlayerReset: () => void;
    onPlayerError: (event: any) => void;
    onPlayerBuffering: (event: any) => void;
    displayBuffering: (display: any, percentage: any) => void;
    setFullscreen: (fullscreen: any, only: any) => void;
    toggleFullscreen: () => void;
    isSeekable: () => boolean;
    onControlsClick: (event: any) => void;
    onPlayClick: (event: any) => void;
    onPauseClick: (event: any) => void;
    onPlayEvent: (event: any) => void;
    onPauseEvent: (event: any) => void;
    onDurationChanged: (event: any) => void;
    displayExternalStartTime: (newTime: any) => void;
    displayExternalEndTime: (forCatchUp: any) => void;
    onTimeUpdate: (event: any) => void;
    hideProgressBarIfNeeded: () => void;
    displayProgressBarAndTimeIndicators: () => void;
    onEndOfLiveEvent: (event: any) => void;
    isCombinedLiveStov: () => any;
    getExternalProgramLength: () => number;
    updateProgressBarNotSeekableLeft: () => void;
    isPlayingAd_: () => boolean;
    updateProgressBarNotSeekableRight: () => void;
    getSeekRangeStartEpochInSeconds: () => number;
    updateProgressPosition: (newTime: any) => void;
    computeProgressTimeForEvent: (event: any, forTooltipDisplay: any) => number;
    onProgressClick: (event: any) => void;
    deferedOnProgressClick: (event: any) => void;
    showVideoContextMenu: (event: any) => boolean;
    getProgramInfoSeekRange: () => number;
    getSeekRangeWidth: () => number;
    onPlayerClick: (event: any) => void;
    onPlayerClickTogglePlayPauseAction: () => void;
    onDocumentClick: (event: any) => void;
    hideVideoContextMenu: () => void;
    mouseX: (evt: any) => any;
    mouseY: (evt: any) => any;
    /**
       * Formats Date to HH:MM:SS text
       * @param date {Date} date to convert
       * @return {string} time text formated as HH:MM:SS
       */
    getTextFromDate: (date: any) => string;
    /**
       * Formats time in seconds to [HH:]MM:SS text
       * @param time {number} time in seconds
       * @param forceHour {number} force the use of the HH: part of the time even if less than 1 hour
       * @return {string} time text formated as [HH:]MM:SS (HH: part is included only if time >= 3600 s)
       */
    getTextFromTime(t: any, forceHour: any): string;
    onLiveButtonClick: (event: any) => void;
    setCustomerOverlay: (customerOverlay: any) => void;
    hideProgressBar: () => void;
    showProgressBar: () => void;
    hidePlayPause: () => void;
    showPlayPause: () => void;
    hideTimeInternal_: () => void;
    hideTime: () => void;
    showTimeInternal_: () => void;
    showTime: () => void;
    updateLiveButtonState: (forced: any) => void;
    onAdBreakStarted: (e: any) => void;
    onAdBreakEnded: (e: any) => void;
    onAdStarted: (e: any) => void;
    onAdEnded: (e: any) => void;
    onAdTimeUpdated: (e: any) => void;
    onSectionStart: (e: any) => void;
    onSectionEnd: (e: any) => void;
    updateSkipButton: () => void;
    onAdSkipButtonClick: (e: any) => void;
}
