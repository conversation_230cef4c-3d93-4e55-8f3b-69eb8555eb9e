/**
 * Creates a Player with its UI and controls in the div with the given id<br>
 * Playback controls, video tag and UI elements are injected in the supporting web page.<br>
 * <b>Important: </b>This function is available only after file <i>voplayer-ui.min.js</i> is present in the webpage
 * @param divElementId id of the element to use to create and inject the player, without the '#' character
 * @param config {Object} configuration object for the controller, this object MUST contain a valid *license* property
 * @return a new player instance
 */
export declare function createPlayerWithControls(parentDOMElementName: string, config: object): any;
/**
 * @hidden
 */
declare class VOPlayerPlus {
    parentDOMElementName: any;
    parentDOMElement: any;
    uiHTML_: any;
    videoDOMId_: any;
    config: any;
    constructor(parentDOMElementName: any, config: any);
    injectPlayerElementInDOM_: () => void;
    loadAdditionalJS_: () => void;
    onSVGLoaded: (event: any) => void;
    onSVGLoadError: (event: any) => void;
    loadSVGIcon: ($img: any, imgURL: any) => Promise<unknown>;
    updateSVGUI_: () => Promise<any[]>;
    finalizeSetup: (config: any) => void;
    initController: () => void;
    initPlayer: () => void;
    injectScriptHelper_: (scriptId: any, scriptSrc: any, loadDoneCB: any) => void;
    loadJQuery_: () => void;
    isolateJQuery_: () => void;
    preloadDone_: () => void;
}
/**
 * @hidden
 */
export declare var PlayerPlus: typeof VOPlayerPlus;
export {};
