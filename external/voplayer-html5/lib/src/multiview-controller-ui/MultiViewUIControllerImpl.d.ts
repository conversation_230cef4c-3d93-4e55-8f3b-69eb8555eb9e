/**
 * @module MultiViewUIController
 */
import { Player } from '../player/Player';
import { Media } from '../player/Types';
import MultiViewController from '../player/multiview/MultiViewController';
import { IMultiViewPlayerConfig } from '../player/multiview/MultiviewTypes';
export default class MultiViewUIControllerImpl {
    private _multiviewController;
    private _mainContainerElement;
    private _leaderContainer;
    private _rightContainer;
    private _playPauseContainer;
    private _nbPlayers;
    private _onClickMap;
    private _isDebug;
    private videoElement_;
    constructor(mainContainerId: string, multiviewController: MultiViewController, isDebug?: boolean);
    /**
     * Remove all the UI created by the MultiviewUIController and reset the MultiviewController
     * @return {Promise<void>}
     */
    destroy: () => Promise<void>;
    private handleEmptiedEvent;
    /**
     * Add a new player to the MultiviewController
     * Emits an error if the load() was already called or if the maximum number of player has been reached
     * @param mediaObj (string | Media) media object to pass to the player
     * @param config (IMultiViewPlayerConfig) synchronisation configuration
     * @return {Promise<Player>}
     */
    registerPlayer: (mediaObj: string | Media, config: IMultiViewPlayerConfig) => Promise<Player>;
    private _initMainContainer;
    private _applyMainContainerStyle;
    private _createLeaderContainer;
    private _createRightContainer;
    private _onSwapVideoElement;
    private _onCreateVideoElement;
    private _createLeaderVideoElement;
    private _createSecondaryVideoElement;
    private _addVRControlLayer;
    private _generateVideoElement;
    private _registerOnClickForVideoElement;
    private _disableVRControls;
    private _enableVRControls;
    private _getVideoContainerId;
    private _applyLeaderVideoContainerStyle;
    private _applySecondaryVideoContainerStyle;
    private _addSecondaryVideoContainerOnClick;
    private _generateDebugLayer;
    private _onLeaderPlayerPlay;
    private _onLeaderPlayerPause;
    private _createPlayPauseContainer;
    private _onDebug;
    private _onDebugAction;
    private _getNextVideoId;
}
