import MultiViewController from './src/player/multiview/MultiViewController';
import { MultiViewControllerConfig, MultiViewDebug, MultiViewDebugAction, MultiViewError, MultiViewEvent, MultiViewSwapLeaderPlayer, MultiViewSyncConfig } from './src/player/multiview/MultiviewTypes';
declare let voplayer: any;
export default voplayer;
export { MultiViewController, MultiViewControllerConfig, MultiViewSyncConfig, MultiViewEvent, MultiViewError, MultiViewSwapLeaderPlayer, MultiViewDebug, MultiViewDebugAction };
