/*! For license information please see voplayer-ui.min.js.LICENSE.txt */
!function(A,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var i=t();for(var e in i)("object"==typeof exports?exports:A)[e]=i[e]}}(self,(function(){return(self.webpackChunkvoplayer_html5=self.webpackChunkvoplayer_html5||[]).push([[870],{1163:function(A,t,i){"use strict";var e=i(8081),n=i.n(e),s=i(3645),o=i.n(s),r=i(1667),g=i.n(r),a=new URL(i(3886),i.b),l=new URL(i(947),i.b),u=o()(n()),h=g()(a),c=g()(l);u.push([A.id,"@font-face {\n    font-family: 'SourceSans';\n    src: url("+h+");\n}\n\n/* MAIN COLOR of the UI, used for all progress bars */\n.sqp-ui-main-bgcolor {\n    background: red;\n}\n\n.sqp-ui-ad-bgcolor {\n    background: yellow;\n}\n\n.sqp-ad-skip-button.allowed {\n    color: white;\n    width: 60px;\n    background-color: rgba(125, 125, 125, 0.85);\n    cursor: pointer;\n}\n\n.sqp-ad-skip-button {\n    position: absolute;\n    bottom: 54px;\n    right: 5px;\n    width: 190px;\n    height: 30px;\n    padding: 5px;\n    background-color: rgba(125, 125, 125, 0.7);\n    color: rgb(200, 200, 200);\n    cursor: default;\n}\n\n.sqp-ads-counter {\n    position: absolute;\n    left: 10px;\n    bottom: 54px;\n    padding: 5px;\n    background-color: rgba(125, 125, 125, 0.7);\n    color: rgb(200, 200, 200);\n    font-family: 'SourceSans', sans-serif;\n    cursor: default;\n}\n\n.sqp-ad-skip-button-text {\n    /* transform: translate(0 ,-50%); */\n    top: 50%;\n    text-align: center;\n    right: 0;\n    /* padding-right: 10px; */\n    font-family: 'SourceSans', sans-serif;\n}\n\n/* disable selection on elements */\n.noselect {\n    -webkit-touch-callout: none;\n    /* iOS Safari */\n    -webkit-user-select: none;\n    /* Chrome/Safari/Opera */\n    -khtml-user-select: none;\n    /* Konqueror */\n    -moz-user-select: none;\n    /* Firefox */\n    -ms-user-select: none;\n    /* Internet Explorer/Edge */\n    user-select: none;\n    /* Non-prefixed version, currently\n                                  not supported by any browser */\n}\n\n.sqp-customer-overlay {\n    position: absolute;\n    left: 15px;\n    top: 15px;\n    right: 15px;\n    bottom: 50px;\n}\n\n/* generic 'centering' class on X/Y axes */\n.sqp-center {\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n}\n\n.sqp-controls-box {\n    display: inline;\n}\n\n/*  video surface filling the entirity of its parent surface */\n.sqp-video {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n}\n\n/* overlay area on top of the video element, covering its whole surface */\n.sqp-video-overlay {\n    width: 100%;\n    height: 100%;\n    font-family: 'SourceSans', sans-serif;\n    font-size: 16px;\n    color: #AAAAAA;\n    position: absolute;\n    top: 0px;\n    pointer-events: none;\n}\n\n/* area of the player overlay in which the notifiation of a buffering event is displayed */\n.sqp-buffering {\n    position: relative;\n    width: 60px;\n    height: 60px;\n    background-color: rgba(0, 0, 0, 0.9);\n    color: #AAAAAA;\n    border-radius: 4px;\n    padding: 4px;\n}\n\n.sqp-buffering-value {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    text-align: center;\n    line-height: 60px;\n    margin-left: -4px;\n    font-family: 'SourceSans', sans-serif;\n}\n\n/* Animation keyframes for the buffering image */\n@keyframes sqp-buffering-img-anim {\n    from {\n        transform: rotate(0deg);\n    }\n\n    to {\n        transform: rotate(359deg);\n    }\n}\n\n/*\n* Animation class for the buffering image\n* This class is automatically added to the buffering image, if any, when a buffering event occurs\n*/\n.sqp-buffering-img-anim {\n    animation: sqp-buffering-img-anim 2s infinite;\n    animation-timing-function: linear;\n}\n\n/*\n* buffering image\n*/\n.sqp-buffering-img {\n    position: relative;\n    width: inherit;\n    height: inherit;\n}\n\n/* area containing video playback controls (by default at the bottom of the video) */\n.sqp-video-controls {\n    position: absolute;\n    bottom: 0px;\n    width: 100%;\n    pointer-events: all;\n    background: linear-gradient(to top, rgba(0, 0, 0, 0.65) 0%, rgba(0, 0, 0, 0) 100%);\n    /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */\n}\n\n.pointer-cursor {\n    cursor: pointer;\n}\n\n/* progress bar zone */\n.sqp-progressbar {\n    position: relative;\n    height: 16px;\n    margin-left: 12px;\n    margin-right: 12px;\n    display: flex;\n    flex-direction: row;\n    z-index:7;\n}\n\n/* progress bar zone */\n.sqp-progressbar-background {\n    position: relative;\n    height: 4px;\n    top: 6px;\n    background-color: rgba(255, 255, 255, 0.5);\n    border-radius: 1px;\n    flex: auto;\n}\n\n.sqp-progressbar-background-not-seekable {\n    position: relative;\n    height: 4px;\n    top: 6px;\n    background-color: rgba(80, 80, 80, 0.8);\n    width: 0%;\n    border-radius: 1px;\n}\n\n.sqp-progressbar-background:hover {\n    height: 6px;\n    top: 5px;\n    background-color: rgba(255, 255, 255, 0.7);\n}\n\n.sqp-progressbar-background:hover>.sqp-chapters {\n    top: 1px;\n}\n\n/*\n* Progress bar 'progress' zone\n* its width will be controlled by the javascript code and given in '%'\n*/\n.sqp-progressbar-progress {\n    position: absolute;\n    top: 0px;\n    height: 100%;\n    width: 50%;\n    /* default width at openning, will be overriden with values in % by javascript code */\n    border-radius: 1px;\n}\n\n.sqp-progressbar-progress-2nd {\n    background-color: yellow;\n}\n\n/*\n* playback progress bar endpoint\n* hidden by default (display: node), this element is showed only on mouse hovering,\n* using the animation classes below\n*/\n.sqp-progressbar-progress-endpoint {\n    position: absolute;\n    display: none;\n    top: -3px;\n    right: -6px;\n    width: 12px;\n    height: 12px;\n    border-radius: 6px;\n}\n\n.sqp-progressbar-progress-live-endpoint {\n    position: absolute;\n    top: -6px;\n    right: -15px;\n    border-radius: 6px;\n    background-color: #CCCCCC;\n    color: #000000;\n    border-radius: 1px;\n    font-size: 13px;\n    padding-right: 2px;\n    padding-left: 3px;\n    z-index: 7;\n}\n\n/* animation for the 'bullet' at the end of the progress bar (makes it appear) */\n@keyframes endpoint-anim {\n    from {\n        width: 0px;\n        height: 0px;\n        top: 4px;\n        right: 0px;\n    }\n\n    to {\n        top: -3px;\n        width: 12px;\n        height: 12px;\n        right: -6px;\n    }\n}\n\n/* activation of the animation for the progress bar endpoint on hovering of the progres bar itself */\n.sqp-progressbar-background:hover>.sqp-progressbar-progress-play>.sqp-progressbar-progress-endpoint {\n    display: inherit;\n    -webkit-animation-fill-mode: forwards;\n    animation-fill-mode: forwards;\n    animation-name: endpoint-anim;\n    animation-duration: 0.05s;\n}\n\n.sqp-program-pure-live {\n    position: absolute;\n    display: inline-block;\n    margin-left: 12px;\n    margin-right: 12px;\n    padding-top: 6px;\n}\n\n.sqp-program-pure-live-label {\n    background-color: red;\n    color: white;\n    border-radius: 1px;\n    font-size: 13px;\n    padding-right: 2px;\n    padding-left: 3px;\n    z-index: 7;\n}\n\n.sqp-chapters {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n}\n\n.sqp-chapter {\n    display: inline-block;\n    position: absolute;\n    background: red;\n    height: 16px;\n    width: 16px;\n    margin-left: -8px;\n    margin-top: -6px;\n    -webkit-transform: scaleX(0.7) rotate(45deg);\n}\n\n.sqp-controls-toolbar {\n    width: calc(100% - 8px);\n    padding: 4px;\n}\n\n/* Live Button that appears when playing a Live+TS or (Event+KnowDuration) media */\n.sqp-chapter-live-button {\n    background-color: #CCCCCC;\n    color: #000000;\n    /*position: absolute;*/\n    position: relative;\n    border-radius: 1px;\n    margin-top: -7px;\n    /* margin-left: 10px; */\n    font-size: 13px;\n    padding-right: 2px;\n    padding-left: 3px;\n    z-index: 7;\n    /*left: 100%;*/\n    float: right;\n    margin-right: -6px;\n    /*fixing issue on Safari with Chinese character*/\n    width: -webkit-max-content;\n}\n\n.sqp-chapter-live-button:hover {\n    color: #FFFFFF;\n    -webkit-transform: scale(1.1);\n}\n\n/* generic class for all buttons in video controller UI */\n.sqp-button {\n    display: inline-block;\n    width: 50px;\n    height: 32px;\n    background-color: transparent;\n    border: 0;\n    color: #AAAAAA;\n    cursor: pointer;\n}\n\n/* hover color for all buttons */\n.sqp-button:hover {\n    color: #FFFFFF;\n}\n\n.sqp-quality-btn {\n    width: auto;\n    height: auto;\n}\n\n/* icon size for all UI buttons */\n.sqp-button .icon {\n    width: 24px;\n    height: 24px;\n    padding: 2px;\n}\n\n/* color for disabled button */\n.sqp-button:disabled {\n    color: gray;\n}\n\n/* color for inactive button */\n.sqp-button-inactive {\n    color: #7b7b7b;\n}\n\n/* remove dotted outline on buttons */\nbutton.sqp-button {\n    outline: none;\n}\n\n/* remove dotted outline on buttons (Firefox) */\nbutton.sqp-button::-moz-focus-inner {\n    border: 0;\n}\n\n.sqp-right {\n    float: right;\n}\n\n.sqp-player-controls-text {\n    display: inline-block;\n    line-height: 28px;\n    margin-left: 10px;\n    margin-right: 10px;\n}\n\n.sqp-player-controls-times-zone {\n    position: absolute;\n    cursor: default;\n}\n\n.sqp-player-controls-times-zone div {\n    display: table-cell;\n    padding-left: 1px;\n    padding-right: 1px;\n}\n\n/* class applied to time indications in the UI */\n.sqp-time {\n    display: inline-block;\n    text-align: center;\n    position: relative;\n    vertical-align: top;\n}\n\n.sqp-volume-btn {\n    padding-right: 0px;\n}\n\n/* specificity for the icon in the volume button */\n.sqp-volume-btn .icon {\n    padding-right: 0px;\n}\n\n/* volume bar */\n.sqp-volume-bar {\n    display: inline-block;\n    width: 120px;\n    height: 28px;\n    margin-left: -4px;\n    cursor: pointer;\n    overflow-x: hidden;\n}\n\n/* background of the volume bar */\n.sqp-volume-bar-bg {\n    position: relative;\n    height: 4px;\n    width: inherit;\n    top: 11px;\n    left: 0px;\n    background-color: rgba(255, 255, 255, 0.5);\n    border-radius: 1px;\n}\n\n.sqp-volume-bar-bg:hover {\n    background-color: rgba(255, 255, 255, 0.7);\n}\n\n/* volume 'progress' on top the background (width is controlled by javascript and given in %) */\n.sqp-volume-bar-level {\n    position: relative;\n    height: inherit;\n    top: 0px;\n    left: 0px;\n    width: 50%;\n    background-color: white;\n    border-radius: 1px;\n    min-width: 12px;\n}\n\n.sqp-volume-bar-level-endpoint {\n    position: absolute;\n    display: inherit;\n    top: -4px;\n    right: 0px;\n    width: 12px;\n    height: 12px;\n    background-color: white;\n    border-radius: 6px;\n}\n\n/*\n-- Classes related to the 'Option' menu (Qualities, Audio and subtitles tracks) --\n*/\n.sqp-optmenu {\n    background-color: rgba(30, 30, 30, 0.9);\n    position: absolute;\n    right: 10px;\n    bottom: 54px;\n    padding-top: 4px;\n    padding-bottom: 4px;\n    overflow: hidden;\n    border-radius: 2px;\n    pointer-events: all;\n    z-index:7;\n}\n\n.sqp-optmenu-1st {\n    max-width: 340px;\n}\n\n.sqp-opt-item {\n    display: table-row;\n    vertical-align: middle;\n}\n\n.sqp-opt-title {\n    display: block;\n    cursor: pointer;\n    border-bottom: 1px solid #8a8a8a;\n    padding-bottom: 4px;\n    min-width: 150px;\n}\n\n.sqp-opt-title-2nd {\n    position: absolute;\n    top: 2px;\n    right: 0px;\n}\n\n.sqp-opt-title-back {\n    width: 18px;\n    height: 18px;\n    padding-left: 6px;\n    padding-right: 10px;\n    padding-top: 4px;\n}\n\n.sqp-opt-item div {\n    display: table-cell;\n    vertical-align: middle;\n}\n\n.sqp-opt-name {\n    display: table-cell;\n    vertical-align: middle;\n    width: 60px;\n    padding-left: 6px;\n    padding-right: 6px;\n    padding-top: 4px;\n    padding-bottom: 4px;\n}\n\n.sqp-opt-value {\n    display: table-cell;\n    text-align: right;\n    padding-left: 6px;\n    padding-right: 6px;\n    padding-top: 4px;\n    padding-bottom: 4px;\n}\n\n.sqp-optmenu-2nd .sqp-opt-list-container {\n    max-height: 300px;\n    overflow-y: auto;\n}\n\n.sqp-opt-items-list {\n    display: table;\n    overflow-y: auto;\n    width: 100%;\n}\n\n.sqp-opt-value-pick:hover {\n    background-color: rgba(70, 70, 70, 0.9);\n    cursor: pointer;\n}\n\n.sqp-opt-value-pick .pick-arrow {\n    display: table-cell;\n    width: 16px;\n    height: 16px;\n    vertical-align: middle;\n    padding-top: 2px;\n    padding-bottom: 4px;\n    padding-right: 4px;\n}\n\n.sqp-opt-value-2nd {\n    display: table-cell;\n    text-align: right;\n    padding-left: 6px;\n    padding-right: 6px;\n    padding-top: 4px;\n    padding-bottom: 4px;\n}\n\n.opt-item-select {\n    display: table-cell;\n    width: 14px;\n    height: 20px;\n    vertical-align: middle;\n    padding-left: 6px;\n    padding-top: 4px;\n}\n\n.sqp-quality-selected {\n    color: white;\n}\n\n.sqp-opt-select:hover {\n    background-color: rgba(70, 70, 70, 0.9);\n    cursor: pointer;\n}\n\n/*\n -- Classes related to the 'Tooltip' (hover UI above the playback progress bar) --\n*/\n/* tooltip area (its position and content is driven by javascript) */\n.sqp-tooltip {\n    background-color: rgba(30, 30, 30, 0.9);\n    font-size: 10pt;\n    position: absolute;\n    text-align: center;\n    bottom: 54px;\n    padding: 4px;\n    overflow: hidden;\n    border-radius: 2px;\n}\n\n.sqp-tooltip-thumbnail {\n  max-height: 100px;\n  width: 150px;\n  margin-bottom: 2px;\n}\n\n.sqp-thumbnail-canvas {\n  width: 100%;\n  height: auto;\n}\n\n.sqp-banner-img {\n    width: 100%;\n}\n\n.sqp-banner-a {\n    position: absolute;\n    left: 0;\n    bottom: 0;\n}\n\n.sqp-banner-close {\n    position: absolute;\n    right: 0;\n    top: 0;\n    width: 15px;\n    height: 15px;\n    background-image: url("+c+");\n    background-size: contain;\n    background-repeat: no-repeat;\n    background-position-x: right;\n}\n\n.importantDisplayNone {\n    display: none !important;\n}\n\n.importantCursorDefault {\n    cursor: default !important;\n}\n\n.sqp-section-overlay {\n    position: relative;\n    top: 50%;\n    width: 50%;\n    margin: auto;\n    text-align: center;\n    color: black;\n    background: white;\n}\n",""]),t.Z=u},1667:function(A){"use strict";A.exports=function(A,t){return t||(t={}),A?(A=String(A.__esModule?A.default:A),/^['"].*['"]$/.test(A)&&(A=A.slice(1,-1)),t.hash&&(A+=t.hash),/["'() \t\n]|(%20)/.test(A)||t.needQuotes?'"'.concat(A.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):A):A}},9023:function(A,t,i){var e=i(7091),n=i(8397),s=i(8387),o=i(4087),r=i(8446),g=i(7477),a=i(3828),l=i(5731),u=i(770),h=i(3018),c=i(5563),I=i(2901),d=i(6439),C=i(1225),p=e(n),M=e(s),B=e(o),E=e(r),m='<div class="sqp-optitem-selected-content" style="display:none"><img class="svg-icon opt-item-cur-pointer" src="'+p+'"></div> <video id="VIDEO_ID_PH" class="sqp-video" width="100%" height="100%"></video> <div class="sqp-all-controls"> <div class="sqp-buffering sqp-center"> <img class="sqp-buffering-img sqp-buffering-rotation-anim" src="'+M+'"> </div> <div class="sqp-vr-controls"></div> <div class="sqp-video-overlay" style="display:none"> <div class="sqp-optmenu sqp-optmenu-1st"> <div class="sqp-optmenu-global"> <div class="sqp-opt-item sqp-opt-item-audio sqp-opt-value-pick"> <div class="sqp-opt-name sqp-audio-txt">Audio</div> <div class="sqp-opt-value sqp-opt-audio-text">?</div> <div class="sqp-opt-sub-select-arrow"><img class="svg-icon pick-arrow" src="'+B+'"> </div> </div> <div class="sqp-opt-item sqp-opt-item-subtitles sqp-opt-value-pick"> <div class="sqp-opt-name sqp-subtitle-txt">Subtitles/CC</div> <div class="sqp-opt-value sqp-opt-subtitles-text">?</div> <div class="sqp-opt-sub-select-arrow"><img class="svg-icon pick-arrow" src="'+B+'"> </div> </div> <div class="sqp-opt-item sqp-opt-item-quality sqp-opt-value-pick"> <div class="sqp-opt-name sqp-quality-txt">Quality</div> <div class="sqp-opt-value sqp-opt-quality-text">Auto ?</div> <div class="sqp-opt-sub-select-arrow"><img class="svg-icon pick-arrow" src="'+B+'"> </div> </div> </div> <div class="sqp-optmenu-audio sqp-optmenu-2nd" style="display:none"> <div class="sqp-opt-title sqp-opt-item"> <div class="sqp-opt-title-back"><img class="svg-icon" src="'+E+'"></div> <div class="sqp-opt-value-2nd sqp-audio-txt">Audio</div> </div> <div class="sqp-opt-list-container"> <div class="sqp-opt-items-list"> </div> </div> </div> <div class="sqp-optmenu-quality sqp-optmenu-2nd" style="display:none"> <div class="sqp-opt-title sqp-opt-item"> <div class="sqp-opt-title-back"><img class="svg-icon" src="'+E+'"></div> <div class="sqp-opt-value-2nd sqp-quality-txt">Quality</div> </div> <div class="sqp-opt-list-container"> <div class="sqp-opt-items-list"> </div> </div> </div> <div class="sqp-optmenu-subtitles sqp-optmenu-2nd" style="display:none"> <div class="sqp-opt-title sqp-opt-item"> <div class="sqp-opt-title-back"><img class="svg-icon" src="'+E+'"></div> <div class="sqp-opt-value-2nd sqp-subtitle-txt">Subtitles / CC</div> </div> <div class="sqp-opt-list-container"> <div class="sqp-opt-items-list"> </div> </div> </div> </div> <div class="sqp-video-controls"> <div class="sqp-overlay-noautohide sqp-progressbar"> <div class="sqp-progressbar-background-not-seekable sqp-progressbar-background-not-seekable-left"></div> <div class="sqp-progressbar-background pointer-cursor"> <div class="sqp-chapters"> <div class="sqp-chapter-live-button sqp-live-txt">LIVE</div> </div> <div class="sqp-progressbar-progress sqp-progressbar-progress-play sqp-ui-main-bgcolor"> <div class="sqp-progressbar-progress-endpoint sqp-ui-main-bgcolor"></div> </div> </div> <div class="sqp-progressbar-background-not-seekable sqp-progressbar-background-not-seekable-right"></div> </div> <div class="sqp-ad-skip-button"> <div class="sqp-ad-skip-button-text"></div> </div> <div class="sqp-ads-counter"> </div> <div class="sqp-controls-toolbar"> <div class="sqp-tooltip"> <div class="sqp-tooltip-thumbnail"><canvas class="sqp-thumbnail-canvas"></canvas></div> <div class="sqp-tooltip-time">00:00</div> </div> <div class="sqp-overlay-noautohide sqp-controls-box"> <button type="button" class="sqp-play-btn sqp-button sqp-left"><img class="svg-icon icon" src="'+e(g)+'"></button> <button type="button" class="sqp-pause-btn sqp-button sqp-left"><img class="svg-icon icon" src="'+e(a)+'"></button> <button type="button" class="sqp-volume-btn sqp-button sqp-left"> <img class="svg-icon icon volume-icon-off" src="'+e(l)+'"> <img class="svg-icon icon volume-icon-l1" src="'+e(u)+'"> <img class="svg-icon icon volume-icon-l2" src="'+e(h)+'"> <img class="svg-icon icon volume-icon-l3" src="'+e(c)+'"> </button> <div class="sqp-volume-bar"> <div class="sqp-volume-bar-bg"> <div class="sqp-volume-bar-level"> <div class="sqp-volume-bar-level-endpoint"></div> </div> </div> </div> <div class="sqp-player-controls-text sqp-player-controls-times-zone"> <div class="sqp-time sqp-player-time">00:00</div> <div class="sqp-time-separator">/</div> <div class="sqp-time sqp-media-duration">00:00</div> </div> <div class="sqp-program-pure-live" style="display:none"> <div class="sqp-program-pure-live-label sqp-live-txt">LIVE</div> </div> </div> <div class="sqp-overlay-noautohide sqp-right sqp-controls-box"> <button type="button" class="sqp-fullscreen-btn sqp-button sqp-right"><img class="svg-icon icon" src="'+e(I)+'"></button> <button type="button" class="sqp-minimize-btn sqp-button sqp-right"><img class="svg-icon icon" src="'+e(d)+'"></button> <button type="button" class="sqp-button sqp-right sqp-options-btn"><img class="svg-icon icon" src="'+e(C)+'"></button> <button type="button" class="sqp-button sqp-right sqp-quality-btn sqp-player-controls-text"><div class="sqp-opt-quality-text">Auto ?</div></button> </div> </div> </div> </div> <div class="sqp-section-overlay"></div> <div class="sqp-customer-overlay"></div> </div> ';A.exports=m},7091:function(A){"use strict";A.exports=function(A,t){return t||(t={}),"string"!=typeof(A=A&&A.__esModule?A.default:A)?A:(t.hash&&(A+=t.hash),t.maybeNeedQuotes&&/[\t\n\f\r "'=<>`]/.test(A)?'"'.concat(A,'"'):A)}},3941:function(A,t,i){"use strict";function e(A){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},e(A)}function n(A,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(A,e.key,e)}}Object.defineProperty(t,"__esModule",{value:!0}),t.VOPlayerController=void 0;var s=i(7875),o=i(4695),r=function(){function A(t,i,n,r){var g=this;if(function(A,t){if(!(A instanceof t))throw new TypeError("Cannot call a class as a function")}(this,A),this.initController_=function(){g.controlsEnabled_=!1,g.controlsHidden_=!0,g.controlsHiding_=!1,g.hideOverlayAllowed_=!0,g.combinedLiveStartOverTransition_=!1,g.shallResumeOnStartOver_=!1,g.livePausedTime_=0,g.timeUpdateOnPauseInterval=void 0,g.loadUI(),g.videoElement_.addEventListener("webkitpresentationmodechanged",g.onPresentationModeChanged.bind(g)),g.videoElement_.addEventListener("loadeddata",g.onVideoEvent.bind(g)),g.player_.on("loaded",g.onManifestLoaded.bind(g)),g.player_.on("qualitychanged",g.onAdaptation.bind(g)),g.player_.on("trackschanged",g.onTrackChanged.bind(g)),g.player_.on("reset",g.onPlayerReset.bind(g)),g.player_.on("buffering",g.onPlayerBuffering.bind(g)),g.player_.on("error",g.onPlayerError.bind(g)),g.player_.on("timeupdate",g.onTimeUpdate.bind(g)),g.player_.on("durationchange",g.onDurationChanged.bind(g)),g.player_.on("volumechange",g.onVideoVolumeChanged.bind(g)),g.player_.on("play",g.onPlayEvent.bind(g)),g.player_.on("pause",g.onPauseEvent.bind(g)),g.player_.on("endoflivereached",g.onEndOfLiveEvent.bind(g)),g.player_.on("adBreakStarted",g.onAdBreakStarted.bind(g)),g.player_.on("adBreakEnded",g.onAdBreakEnded.bind(g)),g.player_.on("adStarted",g.onAdStarted.bind(g)),g.player_.on("adEnded",g.onAdEnded.bind(g)),g.player_.on("adTimeUpdated",g.onAdTimeUpdated.bind(g)),g.player_.on("adplay",g.onPlayEvent.bind(g)),g.player_.on("adpause",g.onPauseEvent.bind(g)),g.player_.on("advolumechange",g.onVideoVolumeChanged.bind(g)),g.player_.on("subtitlerenderingnodeadjusted",g.onAdjustSubtitleRenderingNode.bind(g)),g.player_.on("sectionstart",g.onSectionStart.bind(g)),g.player_.on("sectionend",g.onSectionEnd.bind(g)),g.player_.on("sectionin",g.onSectionStart.bind(g)),g.player_.on("sectionout",g.onSectionEnd.bind(g)),document.addEventListener("fullscreenchange",g.onFullScreenChanged.bind(g)),document.addEventListener("click",g.onDocumentClick.bind(g),!1),g.onPlayerReset()},this.getDefaultConfig_=function(){return{useQualityOnlyButton:!0,togglePlayPauseOnClick:!0,toggleFullscreenOnDblClick:!0,displayRelativePositionOnLive:!1,preventControlsAutoBehavior:{playPause:!1,progressBar:!1,time:!1},controlsHiddingDelay:{mouseIn:5e3,mouseOut:0},customerOverlay:{display:"hidden",htmlContent:""},qualityLevelFormater:void 0,textTrackDisplayFormater:void 0,audioTrackDisplayFormater:void 0,externalProgramInfo:void 0,minimumTimeShiftWindow:0,onStartOver:void 0,onBackToLive:void 0,onEndProgramReached:void 0}},this.fullScreenPolyfill_=function(){if(window.Document){var A=Element.prototype;A.requestFullscreen=A.requestFullscreen||A.mozRequestFullScreen||A.msRequestFullscreen||A.webkitRequestFullscreen;var t=Document.prototype;t.exitFullscreen=t.exitFullscreen||t.mozCancelFullScreen||t.msExitFullscreen||t.webkitCancelFullScreen,"fullscreenElement"in document||(Object.defineProperty(document,"fullscreenElement",{get:function(){return document.mozFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement||document.webkitFullscreenElement}}),Object.defineProperty(document,"fullscreenEnabled",{get:function(){return document.mozFullScreenEnabled||document.msFullscreenEnabled||document.webkitFullscreenEnabled}}));var i=this.proxyEvent_.bind(this);document.addEventListener("webkitfullscreenchange",i),document.addEventListener("webkitfullscreenerror",i),document.addEventListener("mozfullscreenchange",i),document.addEventListener("mozfullscreenerror",i),document.addEventListener("MSFullscreenChange",i),document.addEventListener("MSFullscreenError",i)}},this.proxyEvent_=function(A){var t,i=A.type.replace(/^(webkit|moz|MS)/,"").toLowerCase();"function"==typeof Event?t=new Event(i,A):(t=document.createEvent("Event")).initEvent(i,A.bubbles,A.cancelable),A.target.dispatchEvent(t)},this.mergeConfig_=function(A,t,i,n){var s={".localization":"object",".controlsHiddingDelay":"object",".jquery":"ignore",".qualityLevelFormater":"function",".textTrackDisplayFormater":"function",".audioTrackDisplayFormater":"function",".externalProgramInfo":"object",".onStartOver":"function",".onBackToLive":"function",".onEndProgramReached":"function"};for(var o in t){var r=n+"."+o,g=i[o];r in s?"function"===s[r]?"function"!=typeof t[o]&&void 0!==t[o]||(A[o]=t[o]):"object"===s[r]&&("object"!==e(t[o])&&void 0!==t[o]||(A[o]=t[o])):void 0===t[o]?void 0===g?delete A[o]:A[o]=g:"object"===e(A[o])&&"object"===e(t[o])?this.mergeConfig_(A[o],t[o],g,r):e(t[o])!==e(g)?voplayer.Log.error("[VOPlayerController] mergeConfig_: formatQualityLevel  ","Invalid config, wrong type for "+r,e(t[o]),e(g)):"function"==typeof A[o]&&A[o].length!==t[o].length?(voplayer.Log.warning("[VOPlayerController] mergeConfig_: formatQualityLevel  ","Invalid config, wrong number of arguments for "+r),A[o]=t[o]):A[o]=t[o]}},this.updateConfiguration=function(A){this.mergeConfig_(this.config_,A,this.getDefaultConfig_(),""),this.extractExternalProgramInfoFromConfig(),this.applyLocalization(),this.player_&&(this.onDurationChanged(),this.onTrackChanged(),this.config_.customerOverlay&&this.setCustomerOverlay(A.customerOverlay))},this.getMSEpochTimeFromISODate=function(A){var t=A.match(/([-0-9]+T[0-9:.]+)(Z?|(?:(-|\+)([0-9]{2})([0-9]{2})))+$/);if(t){var i=t[2],e=6e4*(new Date).getTimezoneOffset();if("Z"===i)return new Date(A).getTime();if(i.length>0){var n=new Date(t[1]+"Z").getTime(),s=1e3*(3600*parseInt(t[4],10)+60*parseInt(t[5],10));return"-"===t[3]?n+=s:n-=s,n}return new Date(A+"Z").getTime()+e}return Number.NaN},this.extractExternalProgramInfoFromConfig=function(){if(this.player_)if(this.config_.externalProgramInfo){var A=this.getMSEpochTimeFromISODate(this.config_.externalProgramInfo.startTime),t=this.getMSEpochTimeFromISODate(this.config_.externalProgramInfo.endTime);!isNaN(A)&&!isNaN(t)&&t>A&&(this.hasValidExternalProgramInfo=!0,this.externalProgramInfoEpochInSeconds={start:A/1e3,end:t/1e3})}else this.hasValidExternalProgramInfo=!1,this.externalProgramInfoEpochInSeconds=void 0},this.applyLocalization=function(){"object"===e(this.config_.localization)&&("string"==typeof this.config_.localization.qualityTxt&&this.$controlsOverlay_.find(".sqp-quality-txt").html(this.config_.localization.qualityTxt),"string"==typeof this.config_.localization.audioTxt&&this.$controlsOverlay_.find(".sqp-audio-txt").html(this.config_.localization.audioTxt),"string"==typeof this.config_.localization.subtitleTxt&&this.$controlsOverlay_.find(".sqp-subtitle-txt").html(this.config_.localization.subtitleTxt),"string"==typeof this.config_.localization.liveBtnText&&this.$controlsOverlay_.find(".sqp-live-txt").html(this.config_.localization.liveBtnText))},this.registerClickEvent=function(A,t){A.click(t.bind(this)),A.on("touchstart",t.bind(this))},this.loadUI=function(){voplayer.Log.log("[VOPlayerController] loadUI"),this._uiEventTrapper=function(A){A.stopPropagation()},this.$sectionOverlay_=this.rootElement_.find(".sqp-section-overlay"),this.$sectionOverlay_.hide(0),this.$customerOverlay=this.rootElement_.find(".sqp-customer-overlay"),this.config_.customerOverlay?this.setCustomerOverlay(this.config_.customerOverlay):this.config_.customerOverlay={display:"hidden"},this.rootElement_.click(this.onPlayerClick.bind(this)),this.rootElement_.on("touchstart",function(A){this._showUIControlsIfNeeded()}.bind(this)),this.$controlsOverlay_=this.rootElement_.find(".sqp-video-overlay"),this.$controls_=this.rootElement_.find(".sqp-video-controls"),this.registerClickEvent(this.$controls_,this.onControlsClick),this.$playBtn_=this.rootElement_.find(".sqp-play-btn"),this.$pauseBtn_=this.rootElement_.find(".sqp-pause-btn"),this.$bufferingInfo_=this.rootElement_.find(".sqp-buffering"),this.$bufferingValue_=this.rootElement_.find(".sqp-buffering-value"),this.$bufferingImg_=this.rootElement_.find(".sqp-buffering-img"),this.$bufferingInfo_.hide(0),this.$bufferingValue_.hide(0),this.$playbackTimesContainer_=this.rootElement_.find(".sqp-player-controls-times-zone"),this.$playbackTimes_=this.rootElement_.find(".sqp-player-time"),this.$timeIndicationSeparator=this.rootElement_.find(".sqp-time-separator"),this.$mediaDurations_=this.rootElement_.find(".sqp-media-duration"),this.$pureLive=this.rootElement_.find(".sqp-program-pure-live"),this.$progressbarNotSeekableLeft_=this.rootElement_.find(".sqp-progressbar-background-not-seekable-left"),this.$progressbarNotSeekableRight_=this.rootElement_.find(".sqp-progressbar-background-not-seekable-right"),this.$optionMenu_=this.rootElement_.find(".sqp-optmenu"),this.$optionMenu_.hide(0);var A=this.rootElement_.find(".sqp-optitem-selected-content");this.optionItemSelectedContent_=A[0].innerHTML,this.$optItemSelectAudio_=this.$optionMenu_.find(".sqp-opt-item-audio"),this.registerClickEvent(this.$optItemSelectAudio_,this.showAudioOptMenu),this.$optMenuGlobalAudioIndication_=this.$optionMenu_.find(".sqp-opt-audio-text"),this.$optItemSelectQuality_=this.$optionMenu_.find(".sqp-opt-item-quality"),this.$currentQualityIndications_=this.rootElement_.find(".sqp-opt-quality-text"),this.player_.isFeatureEnabled("qualityselection")?this.registerClickEvent(this.$optItemSelectQuality_,this.showQualityOptMenu):this.$optItemSelectQuality_.find(".sqp-opt-sub-select-arrow").hide(0),this.$optItemSelectSubtitles_=this.$optionMenu_.find(".sqp-opt-item-subtitles"),this.registerClickEvent(this.$optItemSelectSubtitles_,this.showSubtitlesOptMenu),this.$optMenuGlobalSubtitlesIndication_=this.$optionMenu_.find(".sqp-opt-subtitles-text"),this.$globOptMenu_=this.$optionMenu_.find(".sqp-optmenu-global"),this.$qualityOptMenu_=this.$optionMenu_.find(".sqp-optmenu-quality"),this.registerClickEvent(this.$qualityOptMenu_.find(".sqp-opt-title"),this.closeQualityMenu),this.$audioOptMenu_=this.$optionMenu_.find(".sqp-optmenu-audio"),this.registerClickEvent(this.$audioOptMenu_.find(".sqp-opt-title"),this.backToGlobalMenu),this.$subtitlesOptMenu_=this.$optionMenu_.find(".sqp-optmenu-subtitles"),this.registerClickEvent(this.$subtitlesOptMenu_.find(".sqp-opt-title"),this.backToGlobalMenu),this.$optionMenuBtn_=this.rootElement_.find(".sqp-options-btn"),this.registerClickEvent(this.$optionMenuBtn_,this.toggleOptionMenu),this.$optionMenuBtn_.dblclick(this._uiEventTrapper),this.$qualitySelectBtn_=this.rootElement_.find(".sqp-quality-btn"),this.registerClickEvent(this.$qualitySelectBtn_,this.toggleQualityMenu),this.$qualitySelectBtn_.hide(0),this.$tooltip_=this.rootElement_.find(".sqp-tooltip"),this.$tooltip_.hide(0),this.$tooltipTime_=this.$tooltip_.find(".sqp-tooltip-time"),this.$scrubbingThumbnail_=this.$tooltip_.find(".sqp-tooltip-thumbnail"),this.$scrubbingThumbnail_.hide(0),this.scrubbingCanvas=this.$tooltip_.find(".sqp-thumbnail-canvas")[0],this.thumbnailsImg=null,this.thumbnailsImgLoaded=!1,this.registerClickEvent(this.$playBtn_,this.onPlayClick),this.registerClickEvent(this.$pauseBtn_,this.onPauseClick),this.$pauseBtn_.hide(0),this.$playBtn_.dblclick(this._uiEventTrapper),this.$pauseBtn_.dblclick(this._uiEventTrapper),this.$fullscreenBtn_=this.rootElement_.find(".sqp-fullscreen-btn"),this.registerClickEvent(this.$fullscreenBtn_,(function(A){this.setFullscreen(!0),A.stopPropagation(),A.preventDefault()})),this.$fullscreenBtn_.dblclick(this._uiEventTrapper),this.$minimizeBtn_=this.rootElement_.find(".sqp-minimize-btn"),this.registerClickEvent(this.$minimizeBtn_,(function(A){this.setFullscreen(!1),A.stopPropagation(),A.preventDefault()})),this.$minimizeBtn_.dblclick(this._uiEventTrapper),this.$minimizeBtn_.hide(0),this.$progressbarProgress_=this.rootElement_.find(".sqp-progressbar-progress-play"),this.$progressbarProgress_.css("width","0%"),this.$progressbarProgressEndPoint_=this.rootElement_.find(".sqp-progressbar-progress-endpoint"),this.$progressBar_=this.rootElement_.find(".sqp-progressbar-background"),this.$progressBar_.click(this.onProgressClick.bind(this)),this.$progressBar_.dblclick(this._uiEventTrapper),this.$progressBar_.hover(this.onProgressHoverIn.bind(this),this.onProgressHoverOut.bind(this)),this.$progressBar_.mousedown(this.onProgressMouseDown.bind(this)),this.$progressBarForTouch_=this.rootElement_.find(".sqp-progressbar"),this.$progressBarForTouch_.on("touchmove",this.onProgressTouchMove.bind(this)),this.$progressBarForTouch_.on("touchstart",this.onProgressTouchStart.bind(this)),this.$progressBarForTouch_.on("touchend",this.onProgressTouchEnd.bind(this)),this.$liveButton_=this.rootElement_.find(".sqp-chapter-live-button"),this.$liveButton_.hide(0),this.registerClickEvent(this.$liveButton_,this.onLiveButtonClick),this.$liveButton_.dblclick(this._uiEventTrapper),this.$liveButton_.hover(this.onLiveButtonHoverIn.bind(this),this.onLiveButtonHoverOut.bind(this)),this.$volumeBtn_=this.rootElement_.find(".sqp-volume-btn"),this.$volumeZone_=this.rootElement_.find(".sqp-volume-bar"),this.$volumeZone_.data("originalWidth",this.$volumeZone_.width()),this.$volumeZone_.data("originalHeight",this.$volumeZone_.height()),this.isDraggingVolume_=!1,this.wasDragging_=!1,this.$volumeLevel_=this.$volumeZone_.find(".sqp-volume-bar-level"),this.$volumeLevelIcons_=[];var t=this.$volumeBtn_.find(".volume-icon-off");t.length>0&&(this.$volumeLevelIcons_.push(t),t.hide(0));for(var i=1;i<=3;i++)t=this.$volumeBtn_.find(".volume-icon-l"+i),this.$volumeLevelIcons_.push(t),t.hide(0);this.$adCounter_=this.rootElement_.find(".sqp-ads-counter"),this.$adCounter_.hide(0),this.$adSkipButton_=this.rootElement_.find(".sqp-ad-skip-button"),this.$adSkipButtonText_=this.rootElement_.find(".sqp-ad-skip-button-text"),this.$adSkipButton_.hide(0),this.registerClickEvent(this.$adSkipButton_,this.onAdSkipButtonClick),this.onVideoVolumeChanged(),this.$volumeBtn_.click(this.toggleMute),this.$volumeBtn_.dblclick(this._uiEventTrapper),this.$volumeZone_.attr("alwayson")||(this.$volumeZone_.width(0),this.$volumeBtn_.hover(function(A){this.inAdBreak_||(this.volumeHoverTimeout_&&clearTimeout(this.volumeHoverTimeout_),this.$volumeZone_.animate({width:this.$volumeZone_.data("originalWidth")},150))}.bind(this),function(A){this.inAdBreak_||(this.volumeHoverTimeout_=setTimeout(function(){this.rootElement_.find(".sqp-volume-bar:hover").length>0||this.$volumeZone_.stop(!0).animate({width:0},150)}.bind(this),30))}.bind(this)),this.$volumeZone_.hover((function(){}),function(A){setTimeout(function(){this.rootElement_.find(".sqp-volume-btn:hover").length>0||this.isDraggingVolume_||this.$volumeZone_.stop(!0).animate({width:0},150)}.bind(this),30)}.bind(this))),this.$volumeZone_.mousedown(function(A){this.isDraggingVolume_=!0}.bind(this)),this.$volumeZone_.click(function(A){var t=this.$volumeZone_[0].getBoundingClientRect(),i=document.documentElement,e=(window.pageXOffset||i.scrollLeft)-(i.clientLeft||0),n=(A.pageX-e-t.left)/(t.right-t.left);this.inAdBreak_?(this.player_.adManager.adVolume=n,this.player_.adManager.adUnmute()):(this.player_.volume=n,this.player_.muted=!1),A.stopPropagation()}.bind(this));var e=this._jQuery(window);e.on("mouseup",this.onWinMouseUp.bind(this)),e.on("mousemove",this.onWinMouseMove.bind(this)),e.on("keydown",this.onKeyDown.bind(this)),this._lastMouseXPosForMouseMove=-1,this._lastMouseYPosForMouseMove=-1,this.rootElement_.on("mousemove",this.onVideoMouseMove.bind(this)),this.rootElement_.find(".sqp-overlay-noautohide").hover(this.onSurfacePreventingOverlayHideIn.bind(this),this.onSurfacePreventingOverlayHideOut.bind(this)),this.playerSubtitleLayer=this.rootElement_.find(".sqp-subtitle-rendering")},this.onProgressTouchStart=function(A){if((A=A.originalEvent).preventDefault(),A.stopPropagation(),void 0===this.progressTouchEventIdentifier){var t=A.changedTouches;this.progressTouchEventIdentifier=t[0].identifier,this.onProgressMouseDown(t[0]),this.onProgressHoverIn()}},this.onProgressTouchMove=function(A){if((A=A.originalEvent).preventDefault(),void 0!==this.progressTouchEventIdentifier){var t=A.changedTouches;for(var i in t)if(t[i].identifier===this.progressTouchEventIdentifier){this.onWinMouseMove(t[i]);break}}},this.onProgressTouchEnd=function(A){if((A=A.originalEvent).preventDefault(),void 0!==this.progressTouchEventIdentifier){var t=A.changedTouches;for(var i in t)if(t[i].identifier===this.progressTouchEventIdentifier){this.progressTouchEventIdentifier=void 0,this.onWinMouseUp(t[i]),this.onProgressHoverOut();break}}},this.onProgressMouseDown=function(A){this.disableProgressInteraction_||(this.player_.playing&&this.player_.scrubbingAvailable&&(this.playerWasPlayingBeforeProgress=!0,this.player_.pause()),this.isDraggingProgress_=!0)},this.onKeyDown=function(A){if(this.mouseOverPlayer_){var t=Date.now();switch(A.which){case 70:t>this.lastFullscreenToggleTime+250&&(this.lastFullscreenToggleTime=t,this.toggleFullscreen());break;case 37:!this.controlsEnabled_||this.player_.ended||this.isPlayingAd_()||(this.player_.currentTime-=5);break;case 39:!this.controlsEnabled_||this.player_.ended||this.isPlayingAd_()||(this.player_.currentTime+=5);break;case 32:this.controlsEnabled_&&!this.player_.ended&&(this.player_.playing?this.player_.pause():this.player_.play(),A.stopPropagation(),A.preventDefault())}}},this.onWinMouseUp=function(A){var t=!1;this.isDraggingVolume_?(this.isDraggingVolume_=!1,this.wasDragging_=!0,setTimeout(function(){this.wasDragging_=!1}.bind(this),500),this.$volumeZone_.attr("alwayson")||setTimeout(function(){this.rootElement_.find(".sqp-volume-btn:hover").length>0||this.rootElement_.find(".sqp-volume-bar:hover").length>0||this.$volumeZone_.animate({width:0},150)}.bind(this),500),t=!0):this.isDraggingProgress_&&(this.isDraggingProgress_=!1,this.wasDragging_=!0,setTimeout(function(){this.wasDragging_=!1}.bind(this),500),(this.player_.seekRange()||this.isCombinedLiveStov())&&(this.hoverInLiveButton_||this.onProgressClick(A),this.hoverIn$progressBar_||this.$tooltip_.hide(0),this.playerWasPlayingBeforeProgress=!1),t=!0),t&&this.restartHideControlsOverlayTimeout_(this.config_.controlsHiddingDelay.mouseIn)},this.onProgressHoverIn=function(A){this.hoverIn$progressBar_=!0,!this.controlsEnabled_||this.optionMenuVisible_||this.hoverInLiveButton_||this.$tooltip_.show(0)},this.onProgressHoverOut=function(A){this.hoverIn$progressBar_=!1,this.isDraggingProgress_||this.$tooltip_.hide(0)},this.onLiveButtonHoverIn=function(A){this.hoverInLiveButton_=!0,this.$tooltip_.hide(0)},this.onLiveButtonHoverOut=function(A){this.hoverInLiveButton_=!1,this.controlsEnabled_&&!this.optionMenuVisible_&&this.$tooltip_.show(0)},this.onSurfacePreventingOverlayHideIn=function(A){this.hideOverlayAllowed_=!1},this.onSurfacePreventingOverlayHideOut=function(A){this.hideOverlayAllowed_=!0},this.restartHideControlsOverlayTimeout_=function(A){0===this.controlsDisplayMode&&(void 0!==this.overlayHideTimeout_&&clearTimeout(this.overlayHideTimeout_),this.overlayHideTimeout_=setTimeout(this.hideControlsOverlay.bind(this),A))},this.hideControlsOverlay=function(){if(!this.controlsHidden_&&!this.controlsHiding_&&this.hideOverlayAllowed_&&!this.optionMenuVisible_&&!this.isDraggingVolume_&&!this.isDraggingProgress_&&1!=this.controlsDisplayMode){var A=this;this.controlsHiding_=!0,this.$controlsOverlay_.fadeOut(400,(function(){A.controlsHidden_=!0,A.controlsHiding_=!1,A.adjustSubtitleArea()})),this.playerSubtitleLayer.clearQueue();var t=this.rootElement_;this.overlayHideTimeout_=void 0,t.css("cursor","none"),this.config_&&"function"==typeof this.config_.onOverlayHide&&this.config_.onOverlayHide(event),void 0!==this.config_.customerOverlay&&"default"===this.config_.customerOverlay.display&&(this.$customerOverlay.fadeOut(400),this.customerOverlayHidden=!0)}},this.onVideoMouseMove=function(A){A.preventDefault(),A.stopPropagation(),this._lastMouseXPosForMouseMove!==A.clientX||this._lastMouseYPosForMouseMove!==A.clientY?(this._lastMouseXPosForMouseMove=A.clientX,this._lastMouseYPosForMouseMove=A.clientY,this.onWinMouseMove(A,!0),this._showUIControlsIfNeeded()):voplayer.Log.log("[VOPlayerController] onVideoMouseMove: False mouse move event > discard it")},this._showUIControlsIfNeeded=function(){this.player_.videoElement&&(this.controlsHidden_&&2!==this.controlsDisplayMode&&(this.$controlsOverlay_.show(0),this.controlsHidden_=!1,this.adjustSubtitleArea(),this.rootElement_.css("cursor","inherit"),this.rootElement_.addClass("ui-controls"),"function"==typeof this.config_.onOverlayShow&&this.config_.onOverlayShow(event)),this.customerOverlayHidden&&"hidden"!==this.config_.customerOverlay.display&&(this.$customerOverlay.show(0),this.customerOverlayHidden=!1),this.restartHideControlsOverlayTimeout_(this.config_.controlsHiddingDelay.mouseIn))},this.onWinMouseMove=function(A,t){if(A.clientX!==this.prevMouseMoveX||A.clientY!==this.prevMouseMoveY){this.prevMouseMoveX=A.clientX,this.prevMouseMoveY=A.clientY,t?this.mouseOverPlayer_=!0:(this.mouseOverPlayer_=!1,this.restartHideControlsOverlayTimeout_(this.config_.controlsHiddingDelay.mouseOut));var i=document.documentElement;if(this.isDraggingVolume_){var e=this.$volumeZone_[0].getBoundingClientRect(),n=(window.pageXOffset||i.scrollLeft)-(i.clientLeft||0),s=A.pageX-n,o=0;o=s>=e.right?1:s<=e.left?0:(s-e.left)/(e.right-e.left),this.inAdBreak_?(this.player_.adManager.adVolume=o,this.player_.adManager.adUnmute()):(this.player_.volume=o,this.player_.muted=!1),this.updateVolumeIcon(o)}(this.isDraggingProgress_||this.hoverIn$progressBar_)&&this.onMouseMoveOverProgress(A)}},this.backToGlobalMenu=function(A){this.$audioOptMenu_.hide(0),this.$qualityOptMenu_.hide(0),this.$subtitlesOptMenu_.hide(0),this.$optionMenu_.removeClass("sqp-optmenu-2nd").addClass("sqp-optmenu-1st"),this.$globOptMenu_.show(0),A&&(A.stopPropagation(),A.preventDefault())},this.showAudioOptMenu=function(A){this.$globOptMenu_.hide(0),this.$optionMenu_.addClass("sqp-optmenu-2nd").removeClass("sqp-optmenu-1st"),this.$audioOptMenu_.show(0),A.stopPropagation(),A.preventDefault()},this.showQualityOptMenu=function(A){this.$globOptMenu_.hide(0),this.$optionMenu_.addClass("sqp-optmenu-2nd").removeClass("sqp-optmenu-1st"),this.$qualityOptMenu_.show(0),A.stopPropagation(),A.preventDefault()},this.showSubtitlesOptMenu=function(A){this.$globOptMenu_.hide(0),this.$optionMenu_.addClass("sqp-optmenu-2nd").removeClass("sqp-optmenu-1st"),this.$subtitlesOptMenu_.show(0),A.stopPropagation(),A.preventDefault()},this.toggleOptionMenu=function(A){!this.optionMenuVisible_&&this.controlsEnabled_?(this.optionMenuVisible_=!0,this.backToGlobalMenu(A),this.$optionMenu_.fadeIn(250)):(this.optionMenuVisible_=!1,this.$optionMenu_.hide(0)),A.stopPropagation(),A.preventDefault()},this.hideOptionMenu=function(A){this.optionMenuVisible_=!1,this.$optionMenu_.hide(0),A&&(A.stopPropagation(),A.preventDefault())},this.closeQualityMenu=function(A){this.showingQualityBtnOnly?this.$qualityOptMenu_.hide(0):this.backToGlobalMenu(A),A.stopPropagation(),A.preventDefault()},this.toggleQualityMenu=function(A){this.controlsEnabled_&&("yes"===this.$qualityOptMenu_.attr("displayed")?(this.$qualityOptMenu_.hide(0),this.$qualityOptMenu_.attr("displayed")):(this.$optionMenu_.addClass("sqp-optmenu-2nd").removeClass("sqp-optmenu-1st"),this.$qualityOptMenu_.show(0),this.$globOptMenu_.hide(0),this.$optionMenu_.fadeIn(250),this.$qualityOptMenu_.attr("displayed"))),A.stopPropagation(),A.preventDefault()},this.onVideoVolumeChanged=function(A){if(g.inAdBreak_)if(g.player_.adManager.adMuted)g.updateVolumeIcon(-1),g.$volumeLevel_.css("width","0%");else{var t=g.player_.adManager.adVolume;g.updateVolumeIcon(t),g.$volumeLevel_.css("width",100*t+"%")}else if(g.player_.muted)g.updateVolumeIcon(-1),g.$volumeLevel_.css("width","0%");else{var i=g.player_.volume;g.updateVolumeIcon(i),g.$volumeLevel_.css("width",100*i+"%")}},this.updateVolumeIcon=function(A){var t=1;A>.66?t=3:A>.33?t=2:A<0&&(t=0);for(var i=0;i<=3;i++)g.$volumeLevelIcons_[i].hide(0);g.$volumeLevelIcons_[t].show(0)},this.toggleMute=function(A){g.inAdBreak_?g.player_.adManager.adMuted?g.player_.adManager.adUnmute():g.player_.adManager.adMute():g.player_.muted=!g.player_.muted,A.stopPropagation(),A.preventDefault()},this.onMouseMoveOverProgress=function(A){var t=this;if(this.isSeekable()){var i=this.computeProgressTimeForEvent(A);if(!isNaN(i)){var e=this.computeProgressTimeForEvent(A,!0);if(this.isCombinedLiveStov()||!this.player_.isLive()&&!this.player_.wasLive()&&this.hasValidExternalProgramInfo){var n=this.getSeekRangeStartEpochInSeconds();this.config_.onStartOver&&(n=this.externalProgramInfoEpochInSeconds.start);var s=n+e;if(this.externalProgramInfoEpochInSeconds.start>n){var o=this.getProgramInfoSeekRange();s=this.player_.seekRange().end-this.player_.currentTime>o?n+this.player_.currentTime-this.player_.seekRange().start+e:this.externalProgramInfoEpochInSeconds.start+e}this.$tooltipTime_.html(this.getTextFromDate(new Date(1e3*s)))}else if((this.player_.isLive()||this.player_.wasLive())&&this.hasValidExternalProgramInfo){var r=this.getSeekRangeStartEpochInSeconds(),g=r+e;if(this.externalProgramInfoEpochInSeconds.start>r){var a=this.getProgramInfoSeekRange();g=this.player_.seekRange().end-this.player_.currentTime>a?r+this.player_.currentTime-this.player_.seekRange().start+e:this.externalProgramInfoEpochInSeconds.start+e}this.$tooltipTime_.html(this.getTextFromDate(new Date(1e3*g)))}else if(this.config_.displayRelativePositionOnLive){var l=new Date;l.setSeconds(l.getSeconds()+e),this.$tooltipTime_.html(this.getTextFromDate(l))}else this.$tooltipTime_.html(""+this.getTextFromTime(e));if(this.player_.hasThumbnails&&this.player_.getThumbnail(i).then((function(i){if(i){t.eventForThumbnailsLoad=A;var e=t.scrubbingCanvas.getContext("2d");null!==t.thumbnailsImg&&t.thumbnailsImg.src===i.url&&!0===t.thumbnailsImgLoaded?(e.drawImage(t.thumbnailsImg,i.x,i.y,i.width,i.height,0,0,t.scrubbingCanvas.width,t.scrubbingCanvas.height),t.$scrubbingThumbnail_.show(0)):(null===t.thumbnailsImg&&(t.thumbnailsImg=new Image,t.thumbnailsImg.onload=function(){e.drawImage(this.thumbnailsImg,i.x,i.y,i.width,i.height,0,0,this.scrubbingCanvas.width,this.scrubbingCanvas.height),this.$scrubbingThumbnail_.show(0),this.updateToolTipPosition(this.eventForThumbnailsLoad),this.thumbnailsImgLoaded=!0}.bind(t),t.thumbnailsImg.onerror=function(){voplayer.Log.warning("[VOPlayerController] onMouseMoveOverProgress: Unable to load thumbnail image at "+i.url),this.thumbnailsImg&&(this.thumbnailsImg=null)}.bind(t)),t.thumbnailsImg.src=i.url,t.thumbnailsImgLoaded=!1)}})),this.updateToolTipPosition(A),this.isDraggingProgress_){this.updateProgressPosition(i);var u=i-this.lastFastSeekTime;(u>1||u<-1)&&(this.lastFastSeekTime=i,this.inAdBreak_?this.player_.adManager.adPosition=i:this.player_.currentTime=i)}}}},this.updateLiveButtonPosition=function(){var A=this.$liveButton_[0].getBoundingClientRect().width,t=this.$progressbarNotSeekableLeft_[0].getBoundingClientRect().x,i=this.$progressbarNotSeekableLeft_[0].getBoundingClientRect().width,e=this.$progressbarProgress_[0].getBoundingClientRect().width,n=0;n=i+e+6<A?t-(i+e):0,this.$liveButton_.css("left",n+"px")},this.updateToolTipPosition=function(A){var t=document.documentElement,i=this.$tooltip_[0].getBoundingClientRect().width,e=(window.pageXOffset||t.scrollLeft)-(t.clientLeft||0),n=A.pageX-e-i/2,s=this.rootElement_[0].getBoundingClientRect(),o=2;o=n<s.left+2?2:n>s.right-i-2?s.width-i-2:n-s.left,this.$tooltip_.css("left",o+"px")},this.onManifestLoaded=function(A){this.player_.isLive()||this.isCombinedLiveStov()?(voplayer.Log.log("[VOPlayerController] onManifestLoaded: LIVE stream"),this.hasValidExternalProgramInfo&&!this.combinedLiveStartOverTransition_&&(this.displayExternalStartTime(0),this.displayExternalEndTime()),this.combinedLiveStartOverTransition_=!1):(voplayer.Log.log("[VOPlayerController] onManifestLoaded: VOD stream"),this.showProgressBar(),this.showTimeInternal_(),this.hasValidExternalProgramInfo&&(this.displayExternalStartTime(0),this.displayExternalEndTime(!0))),this.hideProgressBarIfNeeded(),this.controlsEnabled_=!0},this.onQualitySelected=function(A){A.currentTarget.hasAttribute("levelid")?(voplayer.Log.log("[VOPlayerController] onQualitySelected: setting current level: "+A.currentTarget.getAttribute("levelid")),this.player_.quality=parseInt(A.currentTarget.getAttribute("levelid"),10)):(voplayer.Log.log("[VOPlayerController] v: setting auto level"),this.player_.quality=-1),this.$qualityOptMenu_.hide(0),this.hideOptionMenu(A)},this.onAudioTrackSelected=function(A){if(A.currentTarget.hasAttribute("trackid")){var t=A.currentTarget.getAttribute("trackid");voplayer.Log.log("[VOPlayerController] onAudioTrackSelected: setting current audio: "+t),t!==this.player_.audioTrack&&(this.player_.audioTrack=parseInt(t,10))}this.$audioOptMenu_.hide(0),this.hideOptionMenu(A)},this.onAdaptation=function(A){var t=this.player_.getConfiguration().abr.enabled,i=this.formatQualityLevel(A.currentLevel,!0,t),e=-1;this.$currentQualityIndications_.html(i.text),i.isHD?(this.$currentQualityIndications_.addClass("sqp-hd-res"),this.$optionMenuBtn_.addClass("sqp-hd-res")):(this.$currentQualityIndications_.removeClass("sqp-hd-res"),this.$optionMenuBtn_.removeClass("sqp-hd-res")),t||(e=this.player_.quality);for(var n=this.$qualityOptMenu_.find(".opt-item-select"),s=this.$qualityOptMenu_.find(".sqp-opt-select"),o=n.length,r=0;r<o;r++){var g=parseInt(s[r].getAttribute("levelid"),10),a=this._jQuery(s[r]).find(".opt-item-select")[0];a.innerHTML=g===e?this.optionItemSelectedContent_:""}},this.formatQualityLevel=function(A,t,i){voplayer.Log.debug("[VOPlayerController] formatQualityLevel: level, forPrimaryDisplay, abrEnabled  ",A,t,i);var e={text:"",isHD:!1};return"function"==typeof this.config_.qualityLevelFormater?this.config_.qualityLevelFormater(A,t,i):(A.height&&A.height>0&&(e.text+=A.height+"p",A.height>=720&&(e.isHD=!0)),t?e.text=i?"Auto "+e.text:this.formatBitrateText(A.bandwidth):(e.text.length>0&&(e.text+=" - "),e.text+=this.formatBitrateText(A.bandwidth)),e)},this.formatBitrateText=function(A){var t;A>1e6?t=Math.round(A/1e5)/10+" Mbs":t=Math.round(A/1e3)+" Kbs";return t},this.formatAudioTrackText=function(A){return"function"==typeof this.config_.audioTrackDisplayFormater?this.config_.audioTrackDisplayFormater(A):A.name?A.name:A.language},this.onTrackChanged=function(A){if(voplayer.Log.log("[VOPlayerController] onTrackChanged: event",A),!this.inAdBreak_){this.$audioOptMenu_.hide(0),this.$qualityOptMenu_.hide(0),this.$subtitlesOptMenu_.hide(0),this.hideOptionMenu();var t=this.player_.qualities,i="",e=void 0,n=0,s=!0;if(t.length>1&&this.player_.isFeatureEnabled("qualityselection")){t.forEach(function(A){i+='<div class="sqp-opt-item sqp-opt-select" levelid='+n+">",i+='<div class="opt-item-select" levelid='+n+"></div>",A.active&&(e=A);var t=this.formatQualityLevel(A),s=t.isHD?" sqp-hd-res":"";i+='<div class="sqp-opt-value-2nd'+s+'">'+t.text+"</div></div>",n++}.bind(this)),i+='<div auto=true class="sqp-opt-item sqp-opt-select" levelid=-1><div class="opt-item-select" levelid=-1></div><div class="sqp-opt-value-2nd">Auto</div></div>';var o=this.$qualityOptMenu_.find(".sqp-opt-items-list");o.html(i),this.registerClickEvent(o.find(".sqp-opt-item"),this.onQualitySelected),this.$optItemSelectQuality_.show(0)}else s=!1,this.$optItemSelectQuality_.hide(0);var r=this.player_.audioTracks,g="";voplayer.Log.log("[VOPlayerController] onTrackChanged: audioTracks before if ",r);var a="",l=!0;if(r.length>1){var u=0;voplayer.Log.log("[VOPlayerController] onTrackChanged: audioTracks ",r),r.forEach(function(A){var t=this.formatAudioTrackText(A);a+='<div class="sqp-opt-item sqp-opt-select" trackid='+u+">",a+='<div class="opt-item-select" trackid='+u+">",A.active&&(g=t,u,a+=this.optionItemSelectedContent_),a+='</div><div class="sqp-opt-value-2nd">'+t+"</div></div>",u++}.bind(this));var h=this.$audioOptMenu_.find(".sqp-opt-items-list");h.html(a),this.registerClickEvent(h.find(".sqp-opt-item"),this.onAudioTrackSelected),this.$optMenuGlobalAudioIndication_.html(g),this.$optItemSelectAudio_.show(0)}else l=!1,this.$optItemSelectAudio_.hide(0);var c=this.player_.textTracks,I=this.player_.textTrack,d="Off";this.config_.localization&&"string"==typeof this.config_.localization.offTrackChoice&&(d=this.config_.localization.offTrackChoice);var C=!0;if(c.length>0){var p="",M=0;voplayer.Log.log("[VOPlayerController] onTrackChanged: subtitles tracks: ",c),p+='<div class="sqp-opt-item sqp-opt-select" trackid=-1><div class="opt-item-select" trackid=-1>',-1===I&&(p+=this.optionItemSelectedContent_);var B="Off";this.config_.localization&&"string"==typeof this.config_.localization.offTrackChoice&&(B=this.config_.localization.offTrackChoice),p+='</div><div class="sqp-opt-value-2nd">'+B+"</div></div>",c.forEach(function(A){var t=this.formatSubtitleTrackText(A);p+='<div class="sqp-opt-item sqp-opt-select" trackid='+M+">",p+='<div class="opt-item-select" trackid='+M+">",M===I&&A.active&&(d=t,p+=this.optionItemSelectedContent_),p+='</div><div class="sqp-opt-value-2nd">'+t+"</div></div>",M++}.bind(this));var E=this.$subtitlesOptMenu_.find(".sqp-opt-items-list");E.html(p),this.registerClickEvent(E.find(".sqp-opt-item"),this.onSubtitleTrackSelected),this.$optMenuGlobalSubtitlesIndication_.html(d),this.$optItemSelectSubtitles_.show(0)}else C=!1,this.$optItemSelectSubtitles_.hide(0);void 0!==e&&this.onAdaptation({currentLevel:e}),this.showingQualityBtnOnly=!1,s?C||l||!this.config_.useQualityOnlyButton?(this.$optionMenuBtn_.show(0),this.$qualitySelectBtn_.hide(0)):(this.$optionMenuBtn_.hide(0),this.$qualitySelectBtn_.show(0),this.showingQualityBtnOnly=!0):(this.$qualitySelectBtn_.hide(0),C||l?this.$optionMenuBtn_.show(0):this.$optionMenuBtn_.hide(0)),this.adjustSubtitleArea()}},this.adjustSubtitleArea=function(){if(g.controlsHidden_){var A=g.rootElement_;g.playerSubtitleLayer.animate({bottom:(g.player_.videoElement.clientHeight-g.player_.videoContainerHeight)/2+"px"},450,(function(){A.removeClass("ui-controls")}))}else{60-(g.player_.videoElement.clientHeight-g.player_.videoContainerHeight)/2>0&&g.playerSubtitleLayer.animate({bottom:"60px"},450)}},this.onAdjustSubtitleRenderingNode=function(){g.adjustSubtitleArea()},this.formatSubtitleTrackText=function(A){return"function"==typeof this.config_.textTrackDisplayFormater?this.config_.textTrackDisplayFormater(A):A.name?A.language?A.name+" / "+A.language:A.name:A.language},this.onSubtitleTrackSelected=function(A){if(A.currentTarget.hasAttribute("trackid")){var t=A.currentTarget.getAttribute("trackid");t=parseInt(t,10),voplayer.Log.log("[VOPlayerController] onSubtitleTrackSelected: setting current subtitle "+t),t!==this.player_.textTrack&&(this.player_.textTrack=t)}this.$subtitlesOptMenu_.hide(0),this.hideOptionMenu(A)},this.onPresentationModeChanged=function(A){"picture-in-picture"===this.videoElement_.webkitPresentationMode?this.$controlsOverlay_.hide(0):this.$controlsOverlay_.fadeIn(250),voplayer.Log.log("[VOPlayerController] onPresentationModeChanged: webkitPresentationMode ",this.videoElement_.webkitPresentationMode)},this.onFullScreenChanged=function(A){null!==document.fullscreenElement?(this.$fullscreenBtn_.hide(0),this.$minimizeBtn_.show(0)):(this.$fullscreenBtn_.show(0),this.$minimizeBtn_.hide(0)),this.player_.updateVideoArea()},this.onVideoEvent=function(A){switch(voplayer.Log.log("[VOPlayerController] onVideoEvent: eventType "+A.type),A.type){case"playing":this.displayBuffering(!1);break;case"waiting":this.displayBufferingTimeout_=setTimeout(this.displayBuffering.bind(this),200,!0);break;case"loadeddata":this.videoElement_.setAttribute("poster","")}},this.onPlayerReset=function(){this.combinedLiveStartOverTransition_||(this.controlsEnabled_=!1,this.disableProgressInteraction_=!1,this.hideOptionMenu(),this.showTimeInternal_(),this.$pureLive.hide(0),this.$optionMenuBtn_.hide(0),this.config_.preventControlsAutoBehavior.playPause||(this.$pauseBtn_.hide(0),this.$playBtn_.show(0)),this.$playbackTimes_.html("00:00"),this.$mediaDurations_.html("00:00"),this.$bufferingInfo_.hide(0),this.showProgressBar(),this.$progressbarProgress_.css("width","0%"),this.$liveButton_.hide(0),this.$progressbarNotSeekableRight_.hide(0),this.$progressbarNotSeekableLeft_.hide(0),this.$qualitySelectBtn_.hide(0),this.backToGlobalMenu(),this.rootElement_&&this.rootElement_.find(".sqp-poster-img").hide(0),this.playerWasPlayingBeforeProgress=!1,this.lastFastSeekTime=-1,this.thumbnailsImg=null,this.thumbnailsImgLoaded=!1,this.$scrubbingThumbnail_.hide(0))},this.onPlayerError=function(A){this.onPlayerReset()},this.onPlayerBuffering=function(A){this.inAdBreak_?this.displayBuffering(!1):A.buffering?this.displayBuffering(!0,A.percent):this.displayBuffering(!1)},this.displayBuffering=function(A,t){A?(this.$bufferingImg_.removeClass("sqp-buffering-img-anim").addClass("sqp-buffering-img-anim"),this.$bufferingInfo_.show(0),void 0!==t&&(this.$bufferingValue_.html(t+"%"),this.$bufferingValue_.show(0))):(this.displayBufferingTimeout_&&(clearTimeout(this.displayBufferingTimeout_),this.displayBufferingTimeout_=void 0),this.$bufferingInfo_.hide(0),this.$bufferingValue_.hide(0))},this.setFullscreen=function(A,t){A?voplayer.Player.isBrowserSafariiOS()||voplayer.Player.isBrowserSafariiPadOS()?this.player_.video_.webkitEnterFullscreen():this.rootElement_[0].requestFullscreen():voplayer.Player.isBrowserSafariiOS()||voplayer.Player.isBrowserSafariiPadOS()||window.document.fullscreenElement&&document.exitFullscreen(),this.player_.signalFullscreen(A)},this.toggleFullscreen=function(){window.document.fullscreenElement?this.setFullscreen(!1):this.setFullscreen(!0)},this.isSeekable=function(){var A=this.player_.seekRange();return!(!A||!((this.player_.isLive()||this.player_.wasLive())&&A.end-A.start>this.config_.minimumTimeShiftWindow||!this.player_.isLive()&&!this.player_.wasLive()))},this.onControlsClick=function(A){A.preventDefault(),A.stopPropagation()},this.onPlayClick=function(A){this.isCombinedLiveStov()&&this.config_.onStartOver&&this.shallResumeOnStartOver_?(this.shallResumeOnStartOver_=!1,this.config_.onStartOver(this.livePausedTime_)):this.controlsEnabled_&&(this.player_.isLive()&&!this.isSeekable()&&(this.player_.currentTime=this.player_.seekRange().end),this.player_.play().then((function(){voplayer.Log.log("[VOPlayerController] onPlayClick: PLAY OK")})).catch((function(A){voplayer.Log.log("[VOPlayerController] onPlayClick: PLAY KO",A)}))),A.preventDefault(),A.stopPropagation()},this.onPauseClick=function(A){this.controlsEnabled_&&this.player_.pause(),A.preventDefault(),A.stopPropagation()},this.onPlayEvent=function(A){this.displayBuffering(!1),this.config_.preventControlsAutoBehavior.playPause||(this.$playBtn_.hide(0),this.$pauseBtn_.show(0)),this.rootElement_.find(".sqp-poster-img").hide(0),clearInterval(this.timeUpdateOnPauseInterval),this.timeUpdateOnPauseInterval=void 0},this.onPauseEvent=function(A){this.config_.preventControlsAutoBehavior.playPause||(this.$pauseBtn_.hide(0),this.$playBtn_.show(0)),this.isCombinedLiveStov()&&this.config_.onStartOver&&(this.shallResumeOnStartOver_=!0,this.livePausedTime_=this.getProgramInfoSeekRange()),this.timeUpdateOnPauseInterval=setInterval(function(){this.onTimeUpdate()}.bind(this),1e3)},this.onDurationChanged=function(A){if(this.isCombinedLiveStov())this.displayExternalEndTime(),this.hideProgressBarIfNeeded();else if(this.player_.isLive()||this.player_.wasLive()){if(this.hasValidExternalProgramInfo)this.displayExternalEndTime();else if(this.isSeekable()){if(!this.config_.displayRelativePositionOnLive){var t=this.getTextFromTime(this.getSeekRangeWidth());this.$mediaDurations_.html(t)}}else this.$mediaDurations_.html("Live");this.hideProgressBarIfNeeded()}else if(this.hasValidExternalProgramInfo)this.displayExternalEndTime(!0);else{var i=this.getTextFromTime(this.player_.duration);this.$mediaDurations_.html(i)}},this.displayExternalStartTime=function(A){if(this.hasValidExternalProgramInfo){var t=this.player_.seekRange(),i=this.externalProgramInfoEpochInSeconds.start+A;(this.player_.isLive()||this.player_.wasLive())&&this.hasValidExternalProgramInfo&&this.isSeekable()&&(i+=this.getSeekRangeStartEpochInSeconds()-this.externalProgramInfoEpochInSeconds.start-t.start),this.$playbackTimes_.html(this.getTextFromDate(new Date(1e3*i)))}},this.displayExternalEndTime=function(A){if(void 0===A&&(A=!1),this.hasValidExternalProgramInfo)if(A&&this.isSeekable()){var t=new Date(this.config_.externalProgramInfo.startTime).getTime()+1e3*this.player_.duration;this.$mediaDurations_.html(this.getTextFromDate(new Date(t)))}else this.$mediaDurations_.html(this.getTextFromDate(new Date(1e3*this.externalProgramInfoEpochInSeconds.end)))},this.onTimeUpdate=function(A){this.isDraggingProgress_||this.inAdBreak_||this.updateProgressPosition(this.player_.currentTime)},this.hideProgressBarIfNeeded=function(){!this.config_.preventControlsAutoBehavior.progressBar&&this.player_&&(this.isCombinedLiveStov()?this.hasValidExternalProgramInfo&&(this.player_.isLive()&&!this.isPlayingAd_()?this.$liveButton_.show(0):this.$liveButton_.hide(0),this.displayProgressBarAndTimeIndicators(),this.showTimeInternal_(),this.$progressbarNotSeekableLeft_.hide(0),this.$progressbarNotSeekableRight_.show(0)):this.player_.isLive()||this.player_.wasLive()?this.hasValidExternalProgramInfo&&!this.isPlayingAd_()?(this.$liveButton_.show(0),this.displayProgressBarAndTimeIndicators(),this.showTimeInternal_(),this.$progressbarNotSeekableLeft_.show(0),this.$progressbarNotSeekableRight_.show(0)):(this.$progressbarNotSeekableLeft_.hide(0),this.$progressbarNotSeekableRight_.hide(0),this.isSeekable()?(this.displayProgressBarAndTimeIndicators(),this.player_.isLive()&&!this.isPlayingAd_()?this.$liveButton_.show(0):this.$liveButton_.hide(0)):(this.hideProgressBar(),this.$pureLive.show(0),this.hideTimeInternal_(),this.$liveButton_.hide(0))):this.displayProgressBarAndTimeIndicators())},this.displayProgressBarAndTimeIndicators=function(){this.showProgressBar(),this.showTimeInternal_(),this.$pureLive.hide(0)},this.onEndOfLiveEvent=function(A){this.$liveButton_.hide(0)},this.isCombinedLiveStov=function(){return(this.config_.onStartOver||this.config_.onBackToLive)&&this.hasValidExternalProgramInfo},this.getExternalProgramLength=function(){return this.externalProgramInfoEpochInSeconds.end-this.externalProgramInfoEpochInSeconds.start},this.updateProgressBarNotSeekableLeft=function(){if(this.hasValidExternalProgramInfo){var A=this.getSeekRangeStartEpochInSeconds()-this.externalProgramInfoEpochInSeconds.start,t=Math.min(A/this.getExternalProgramLength()*100,100);this.$progressbarNotSeekableLeft_.css("width",+t+"%"),this.updateLiveButtonPosition()}},this.isPlayingAd_=function(){return this.player_.adManager&&this.player_.adManager.currentAd_.type===o.AdvertisementType.LINEAR&&this.player_.adManager.isPlayingAd},this.updateProgressBarNotSeekableRight=function(){if(this.hasValidExternalProgramInfo){var A=(new Date).getTime()/1e3,t=this.externalProgramInfoEpochInSeconds.end-A,i=Math.min(t/this.getExternalProgramLength()*100,100),e=this.getProgramInfoSeekRange();this.player_.seekRange().end-this.player_.currentTime>e&&(i=Math.min(t/(t+this.player_.seekRange().end-this.player_.currentTime)*100,100)),this.$progressbarNotSeekableRight_.css("width",+i+"%"),i<0&&this.config_.onEndProgramReached&&this.config_.onEndProgramReached()}},this.getSeekRangeStartEpochInSeconds=function(){return this.player_.isLive()||this.player_.wasLive()?(new Date).getTime()/1e3-this.getSeekRangeWidth():this.externalProgramInfoEpochInSeconds.end-this.getSeekRangeWidth()},this.updateProgressPosition=function(A){if(void 0===A&&(A=0),this.isCombinedLiveStov()&&!this.inAdBreak_){if(!this.combinedLiveStartOverTransition_){var t=this.player_.seekRange();this.displayExternalStartTime(A);var i=A-t.start,e=this.getProgramInfoSeekRange();(this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()||this.config_.onStartOver&&!this.isDraggingProgress_)&&(t.end-A>e?(e=t.end-A,i=0):i=e-(t.end-A));var n=Math.min(i/e*100,100);this.$progressbarProgress_.css("width",n+"%"),this.updateLiveButtonState(),this.isDraggingProgress_||this.updateProgressBarNotSeekableRight()}}else if(!this.player_.isLive()&&!this.player_.wasLive()||this.inAdBreak_){if(this.hasValidExternalProgramInfo)this.displayExternalStartTime(A);else{var s=this.getTextFromTime(A);this.$playbackTimes_.html(s)}var o=null;if((o=this.inAdBreak_?this.player_.adManager.adDuration:this.player_.duration)>0){var r=(A/o*100).toString();this.$progressbarProgress_.css("width",r+"%")}}else if(this.isSeekable()){var g=this.player_.seekRange();if(this.hasValidExternalProgramInfo){this.displayExternalStartTime(A);var a=this.getSeekRangeWidth(),l=A-g.start;this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()&&(a=this.getProgramInfoSeekRange(),g.end-A>a?(a=g.end-A,l=0):l=a-(g.end-A));var u=Math.min(l/a*100,100);this.$progressbarProgress_.css("width",u+"%"),this.isDraggingProgress_||(this.updateProgressBarNotSeekableRight(),this.updateProgressBarNotSeekableLeft()),this.updateLiveButtonState()}else{var h,c=this.getSeekRangeWidth(),I=A-g.start,d=g.end-A,C="";if(this.config_.displayRelativePositionOnLive){var p=new Date;this.$mediaDurations_.html(this.getTextFromDate(p)),p.setSeconds(p.getSeconds()-d),this.$playbackTimes_.html(this.getTextFromDate(p))}else I<0&&(I=0),C=this.getTextFromTime(I),this.$playbackTimes_.html(C),this.$mediaDurations_.html(this.getTextFromTime(c));h=Math.min(I/c*100,100).toString(),this.$progressbarProgress_.css("width",h+"%"),this.updateLiveButtonState()}}else if(this.hasValidExternalProgramInfo){var M=this.player_.seekRange(),B=0;M&&(B=M.end-A);var E=(new Date).getTime()-1e3*B;this.$playbackTimes_.html(this.getTextFromDate(new Date(E))),this.$progressbarProgress_.css("width","0%"),this.updateLiveButtonState(!0),this.updateProgressBarNotSeekableRight(),this.updateProgressBarNotSeekableLeft()}},this.computeProgressTimeForEvent=function(A,t){var i=this.$progressBar_[0].getBoundingClientRect(),e=A.clientX,n=0,s=NaN;if(n=e<i.left?0:e>i.right?1:(e-i.left)/i.width,(this.player_.isLive()||this.player_.wasLive()||this.config_.onStartOver)&&!this.inAdBreak_){var o=this.player_.seekRange(),r=this.getSeekRangeWidth();this.hasValidExternalProgramInfo&&(this.isCombinedLiveStov()||this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds())&&(r=this.getProgramInfoSeekRange(),o.end-this.player_.currentTime>r&&(r=o.end-this.player_.currentTime)),s=t?this.config_.displayRelativePositionOnLive&&!this.hasValidExternalProgramInfo?-r*(1-n):r*n:this.hasValidExternalProgramInfo&&this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()?r*n+o.end-r:r*n+o.start}else{var g=0;g=this.inAdBreak_?this.player_.adManager.adDuration:this.player_.duration,this.isCombinedLiveStov()&&(g=this.getProgramInfoSeekRange()),g>0&&(s=g*n)}return s},this.onProgressClick=function(A){!(window.navigator.userAgent.indexOf("Edge/")>-1||this.client.isIE()||this.client.isFirefox()||this.client.isSafari()&&this.client.isMac())&&A instanceof Touch||A.stopPropagation();var t=function(){this.timeout&&clearTimeout(this.timeout),this.deferedOnProgressClick(A)}.bind(this);this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout(t,50)},this.deferedOnProgressClick=function(A){if(this.controlsEnabled_&&!this.isDraggingProgress_&&!this.disableProgressInteraction_&&(this.isSeekable()||this.config_.onBackToLive&&this.isCombinedLiveStov())){var t=this.$progressBar_[0].getBoundingClientRect(),i=(A.clientX-t.left)/t.width;if(this.isCombinedLiveStov()){if(!1===this.combinedLiveStartOverTransition_)if(this.config_.onStartOver){var e=i*this.getProgramInfoSeekRange();this.combinedLiveStartOverTransition_=!0,this.config_.onStartOver(e),this.updateLiveButtonState(!1)}else{var n=this.player_.seekRange().start+this.getProgramInfoSeekRange()*i;if(this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()){var s=0;this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()+this.player_.currentTime-this.player_.seekRange().start?(s=this.player_.seekRange().end-this.player_.currentTime,n=this.player_.currentTime+s*i):(s=this.getProgramInfoSeekRange(),n=this.player_.seekRange().start+(this.externalProgramInfoEpochInSeconds.start-this.getSeekRangeStartEpochInSeconds())+s*i)}this.inAdBreak_?this.player_.adManager.adPosition=n:this.player_.currentTime=n}}else if(this.player_.isLive()||this.player_.wasLive()){var r=this.player_.seekRange(),g=this.getSeekRangeWidth(),a=r.start+g*i;this.hasValidExternalProgramInfo&&this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()&&(this.externalProgramInfoEpochInSeconds.start>this.getSeekRangeStartEpochInSeconds()+this.player_.currentTime-r.start?(g=r.end-this.player_.currentTime,a=this.player_.currentTime+g*i):(g=this.getProgramInfoSeekRange(),a=r.start+(this.externalProgramInfoEpochInSeconds.start-this.getSeekRangeStartEpochInSeconds())+g*i)),this.inAdBreak_?this.player_.adManager.adPosition=a:this.player_.currentTime=a}else{var l=this.player_.duration;if(this.player_.adManager&&this.player_.adManager.isPlayingAdBreak&&this.player_.adManager.currentAdBreakInformation.adInfo.type===o.AdvertisementType.LINEAR&&(l=this.player_.adManager.currentAd_.duration),l>0){var u=l*i;this.inAdBreak_?this.player_.adManager.adPosition=u:this.player_.currentTime=u,this.player_.ended&&this.player_.play()}}}this.wasDragging_||this.player_.play()},this.showVideoContextMenu=function(A){if(A.target.id===this.videoElement_.id){A.preventDefault();var t=$(".sqp-ctx-menu-video");return 0===t.length&&(t=$('<div class="sqp-ctx-menu-video">This is a context menu</div>'),$("body").append(t)),t.css("top",this.mouseY(A)+"px"),t.css("left",this.mouseX(A)+"px"),t.fadeIn(500),A.stopPropagation(),!1}this.hideVideoContextMenu()},this.getProgramInfoSeekRange=function(){return this.hasValidExternalProgramInfo?this.player_.isLive()||this.player_.wasLive()?(new Date).getTime()/1e3-this.externalProgramInfoEpochInSeconds.start:this.externalProgramInfoEpochInSeconds.end-this.externalProgramInfoEpochInSeconds.start:0},this.getSeekRangeWidth=function(){if(!this.isSeekable())return 0;var A=this.player_.seekRange();return A.end-A.start},this.onPlayerClick=function(A){if("secondary"===this.player_.contentType)this.player_.vastClickThroughUrl;else{var t=this.controlsEnabled_;t=(t=t&&void 0===this._playerClickTogglePlayPauseActionTimeoutId)&&this.config_.togglePlayPauseOnClick,void 0!==this.player_.vrController&&(t=t&&!this.player_.vrController.vrInUse),t&&(this._playerClickTogglePlayPauseActionTimeoutId=setTimeout(this.onPlayerClickTogglePlayPauseAction.bind(this),400))}0===this.touchTime?this.touchTime=(new Date).getTime():(new Date).getTime()-this.touchTime<400?(void 0!==this._playerClickTogglePlayPauseActionTimeoutId&&(clearTimeout(this._playerClickTogglePlayPauseActionTimeoutId),this._playerClickTogglePlayPauseActionTimeoutId=void 0),this.config_.toggleFullscreenOnDblClick&&this.toggleFullscreen(),this.touchTime=0):this.touchTime=(new Date).getTime()},this.onPlayerClickTogglePlayPauseAction=function(){g._playerClickTogglePlayPauseActionTimeoutId=void 0,g.wasDragging_||(g.inAdBreak_?g.player_.adManager.adActivated():g.player_.playing?g.player_.pause():g.player_.play())},this.onDocumentClick=function(A){1===(A.which||A.button)&&this.hideVideoContextMenu(),this.hideOptionMenu()},this.hideVideoContextMenu=function(){this._jQuery(".sqp-ctx-menu-video").hide(0)},this.mouseX=function(A){return A.pageX?A.pageX:A.clientX?A.clientX+(document.documentElement.scrollLeft?document.documentElement.scrollLeft:document.body.scrollLeft):null},this.mouseY=function(A){return A.pageY?A.pageY:A.clientY?A.clientY+(document.documentElement.scrollTop?document.documentElement.scrollTop:document.body.scrollTop):null},this.getTextFromDate=function(A){return("0"+A.getHours()).slice(-2)+":"+("0"+A.getMinutes()).slice(-2)+":"+("0"+A.getSeconds()).slice(-2)},this.onLiveButtonClick=function(A){if(A.stopPropagation(),A.preventDefault(),this.isCombinedLiveStov()&&this.config_.onBackToLive)return this.combinedLiveStartOverTransition_=!0,void this.config_.onBackToLive();if(this.isSeekable()){var t=this.player_.seekRange();this.player_.currentTime=t.end}},this.setCustomerOverlay=function(A){if(this.config_.customerOverlay=A,A){if(A.htmlContent&&(this.config_.customerOverlay.htmlContent=A.htmlContent,this.$customerOverlay.html(A.htmlContent)),A.display)switch(this.config_.customerOverlay.display=A.display,A.display){case"hidden":this.$customerOverlay.stop().hide(0),this.customerOverlayHidden=!0;break;case"alwaysOn":this.$customerOverlay.show(0),this.customerOverlayHidden=!1;break;case"default":this.controlsHidden_?(this.$customerOverlay.stop().hide(0),this.customerOverlayHidden=!0):(this.$customerOverlay.show(0),this.customerOverlayHidden=!1)}}else this.config_.customerOverlay={display:"hidden"},this.$customerOverlay.hide(0)},this.hideProgressBar=function(){this.config_.preventControlsAutoBehavior.progressBar||this.$progressBarForTouch_.hide(0)},this.showProgressBar=function(){this.config_.preventControlsAutoBehavior.progressBar||this.$progressBarForTouch_.show(0)},this.hidePlayPause=function(){this.$playBtn_.hide(0),this.$pauseBtn_.hide(0)},this.showPlayPause=function(){this.player_.playing?this.$pauseBtn_.show(0):this.$playBtn_.show(0)},this.hideTimeInternal_=function(){this.config_.preventControlsAutoBehavior.time||(this.$playbackTimes_.hide(0),this.$timeIndicationSeparator.hide(0),this.$mediaDurations_.hide(0))},this.hideTime=function(){this.$playbackTimes_.hide(0),this.$timeIndicationSeparator.hide(0),this.$mediaDurations_.hide(0)},this.showTimeInternal_=function(){this.config_.preventControlsAutoBehavior.time||(this.$playbackTimes_.show(0),this.$timeIndicationSeparator.show(0),this.$mediaDurations_.show(0))},this.showTime=function(){this.$playbackTimes_.show(0),this.$timeIndicationSeparator.show(0),this.$mediaDurations_.show(0)},this.updateLiveButtonState=function(A){void 0!==A&&!1===A?(this.$liveButton_.css("backgroundColor",""),this.$liveButton_.css("color","")):void 0!==A&&!0===A||this.player_.seekRange().end-this.player_.currentTime<30?(this.$liveButton_.css("backgroundColor","red"),this.$liveButton_.css("color","white")):(this.$liveButton_.css("backgroundColor",""),this.$liveButton_.css("color",""))},this.onAdBreakStarted=function(A){A&&A.breakType&&"linear"===A.breakType&&(this.inAdBreak_=!0,this.$progressbarProgress_.removeClass("sqp-ui-main-bgcolor").addClass("sqp-ui-ad-bgcolor"),this.$playbackTimesContainer_.hide(0),this.$qualitySelectBtn_.hide(0),this.$optionMenuBtn_.hide(0),this.$progressbarProgressEndPoint_.removeClass("sqp-ui-main-bgcolor").addClass("sqp-ui-ad-bgcolor"),this.$volumeZone_.hide(0),this.$adCounter_.show(0),this.$scrubbingThumbnail_.hide(0),this.controlsEnabled_=!0)},this.onAdBreakEnded=function(A){A&&A.breakType&&"linear"===A.breakType&&(this.inAdBreak_=!1,this.$progressbarProgress_.removeClass("sqp-ui-ad-bgcolor").addClass("sqp-ui-main-bgcolor"),this.$playbackTimesContainer_.show(0),this.onTrackChanged(),this.$progressbarProgressEndPoint_.removeClass("sqp-ui-ad-bgcolor").addClass("sqp-ui-main-bgcolor"),this.disableProgressInteraction_=!1,this.$volumeZone_.show(0),this.$adCounter_.hide(0),this.$adSkipButton_.hide(0))},this.onAdStarted=function(A){if(A&&A.adType&&"linear"===A.adType){var t=this.player_.adManager.currentAdBreakInfo;if(null!==t){var i=t.currentAd.indexInAdBreak,e=t.adCount;this.$adCounter_.html("Ad ".concat(i,"/").concat(e))}this.updateSkipButton()}},this.onAdEnded=function(A){A&&A.adType&&"linear"===A.adType&&(this.config_.preventControlsAutoBehavior.playPause||(this.$pauseBtn_.hide(0),this.$playBtn_.show(0)))},this.onAdTimeUpdated=function(A){A&&A.adType&&"nonlinear"!==A.adType&&this.updateProgressPosition(A.currentTime),this.updateSkipButton()},this.onSectionStart=function(A){this.$sectionOverlay_.html("<div><span>Section</span><br/><span>name:"+A.name+"</span><br/><span>type:"+A.type+"</span><br/><span>start:"+A.startTime+"</span><br/><span>end:"+A.endTime+"</span></div>"),this.$sectionOverlay_.show(0)},this.onSectionEnd=function(A){this.$sectionOverlay_.hide(0)},this.updateSkipButton=function(){var A=null;null===this.player_.adManager.adSkipOffset||this.player_.adManager.adSkipOffset>this.player_.adManager.adDuration?(A=1/0,this.$adSkipButton_.hide(0)):(A=this.player_.adManager.adSkipOffset-this.player_.adManager.adPosition,this.$adSkipButton_.show(0)),A<=0?(this.$adSkipButtonText_.html("Skip ad"),this.$adSkipButton_.addClass("allowed")):A===1/0?(this.$adSkipButtonText_.html("Watch ad until the end"),this.$adSkipButton_.removeClass("allowed")):(this.$adSkipButtonText_.html("You can skip in ".concat(Math.ceil(A)," seconds.")),this.$adSkipButton_.removeClass("allowed"))},this.onAdSkipButtonClick=function(A){null!==this.player_.adManager.adSkipOffset&&this.player_.adManager.adSkipOffset<=this.player_.adManager.adPosition&&(this.player_.adManager.skipAd(),this.$adSkipButton_.hide(0)),A.stopPropagation()},this.externalProgramInfoInSeconds=void 0,this.fullScreenPolyfill_(),this.config_=this.getDefaultConfig_(),r&&this.updateConfiguration(r),this.window=window,void 0!==r.jquery)voplayer.Log.log("[VOPlayerController] constructor: Working with version of jQuery given as parameter: "+r.jquery.fn.jquery),this._jQuery=r.jquery;else{if("undefined"==typeof jQuery)throw"Impossible to find a workable version of jQuery";voplayer.Log.log("[VOPlayerController] constructor: No jQuery instance given as parameter, working with globally available one: "+jQuery.fn.jquery),this._jQuery=jQuery}this.rootElement_=this._jQuery("#"+t),this.rootElement_.css("touch-action","none"),this.videoElement_=i,this.player_=n,this.prevMouseMoveX=-1,this.prevMouseMoveY=-1,this.progressTouchEventIdentifier=void 0,this.disableProgressInteraction_=!1,this.lastFastSeekTime=-1,this.toolbarDisplayAllowed=!1,this.lastFullscreenToggleTime=0,this.customerOverlayHidden=!0,this.controlsDisplayMode=0,this._playerClickTogglePlayPauseActionTimeoutId=void 0,this.initController_(),this.player_.uiControls=this,this.client=new s.ClientJS,this.touchTime=0,this.wasDragging_=!1}var t,i,r;return t=A,(i=[{key:"getTextFromTime",value:function(A,t){var i=A<0;i&&(A*=-1);var e=Math.floor(A/3600),n=("0"+e).slice(-2),s=("0"+Math.floor(A/60)%60).slice(-2),o=("0"+Math.floor(A)%60).slice(-2),r="";return r=e>=1||!0===t?n+":"+s+":"+o:s+":"+o,i&&(r="- "+r),r}}])&&n(t.prototype,i),r&&n(t,r),Object.defineProperty(t,"prototype",{writable:!1}),A}();t.VOPlayerController=r},4843:function(A,t,i){"use strict";function e(A){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},e(A)}function n(A,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(A,e.key,e)}}function s(A,t,i){return t&&n(A.prototype,t),i&&n(A,i),Object.defineProperty(A,"prototype",{writable:!1}),A}Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerPlus=t.createPlayerWithControls=void 0;var o=i(3941),r=i(9023);t.createPlayerWithControls=function(A,t){return new voplayer.PlayerPlus(A,t).player};var g=s((function A(t,i){!function(A,t){if(!(A instanceof t))throw new TypeError("Cannot call a class as a function")}(this,A),this.injectPlayerElementInDOM_=function(){voplayer.Log.info("injectPlayerElementInDOM"),this.parentDOMElementRestoreContent=this.parentDOMElement.innerHTML;var A=this.uiHTML_.replace("VIDEO_ID_PH",this.videoDOMId_);this.parentDOMElement.innerHTML=A},this.loadAdditionalJS_=function(){if("string"==typeof this.config.jquery&&"existing"!==this.config.jquery)this.loadJQuery_();else{if(!jQuery)throw"Squadeo Ui Controller requires jQuery to operate";this._jQuery=jQuery,this.preloadDone_()}},this.onSVGLoaded=function(A){var t=A.target,i=t.$img,e=i.attr("class"),n=i.attr("id"),s=this._jQuery(t.responseXML).find("svg");void 0!==n&&(s=s.attr("id",n)),void 0!==e&&(s=s.attr("class",e+" replaced-svg")),s=s.removeAttr("xmlns:a"),i.replaceWith(s),t.resolvePromiseCB()},this.onSVGLoadError=function(A){var t=A.target;voplayer.Log.error("Error loading resource",t),t.rejectPromiseCB()},this.loadSVGIcon=function(A,t){return new Promise(function(i,e){var n=new XMLHttpRequest;n.addEventListener("load",this.onSVGLoaded.bind(this),!1),n.addEventListener("error",this.onSVGLoadError.bind(this),!1),n.addEventListener("timeout",this.onSVGLoadError.bind(this),!1),n.resolvePromiseCB=i,n.rejectPromiseCB=e,n.$img=A,n.open("GET",t,!0),n.send()}.bind(this))},this.updateSVGUI_=function(){for(var A=this._jQuery("#"+this.parentDOMElementName).find(".svg-icon"),t=A.length,i=[],e=0;e<t;e++){var n=A[e].getAttribute("src");i.push(this.loadSVGIcon(this._jQuery(A[e]),n))}return Promise.all(i)},this.finalizeSetup=function(A){"object"===e(A)?this.config=A:(this.config={},this.config.useSVGIcons=!0),this.loadAdditionalJS_()},this.initController=function(){var A={jquery:this._jQuery,qualityLevelFormater:this.config.qualityLevelFormater,audioTrackDisplayFormater:this.config.audioTrackDisplayFormater,textTrackDisplayFormater:this.config.textTrackDisplayFormater,localization:this.config.localization},t=["useQualityOnlyButton","togglePlayPauseOnClick","controlsHiddingDelay","displayRelativePositionOnLive","toggleFullscreenOnDblClick"];for(var i in t){var e=t[i];this.config.hasOwnProperty(e)&&(A[e]=this.config[e])}this.playerControls=new o.VOPlayerController(this.parentDOMElementName,this.videoElement,this.player,A),this.player.uiController=this.playerControls},this.initPlayer=function(){if(voplayer.Log.info("initPlayer"),this.videoElement=document.getElementById(this.videoDOMId_),!voplayer.Player.isBrowserSupported())throw"Browser is not supported by Squadeo HTML5 player";voplayer.smartlib?this.player=new voplayer.smartlib.PlayerBPKExtended(this.videoElement):this.player=new voplayer.Player(this.videoElement),this.player.license=this.config.license,this.player.parentLoader_=this,this.player.uiController=this.playerControls,this.player.once("destroyed",function(){this.parentDOMElement.innerHTML=this.parentDOMElementRestoreContent,this.player=void 0}.bind(this)),window.addEventListener("unload",function(){this.player&&this.player.destroy()}.bind(this))},this.injectScriptHelper_=function(A,t,i){var e,n=document.getElementsByTagName("script")[0];document.getElementById(A)||((e=document.createElement("script")).id=A,e.onload=function(){i()},e.src=t,n.parentNode.insertBefore(e,n))},this.loadJQuery_=function(){this.injectScriptHelper_("voplayer-jquery",this.config.jquery,this.isolateJQuery_.bind(this))},this.isolateJQuery_=function(){this._jQuery=jQuery.noConflict(!0),this.preloadDone_()},this.preloadDone_=function(){this.updateSVGUI_().then(function(){this.initController(),this.player&&this.player.emitEvent("ready")}.bind(this))},this.parentDOMElementName=t,this.parentDOMElement=document.getElementById(t),this.parentDOMElement?(this.uiHTML_=r,this.videoDOMId_=voplayer.findNextFreeVideoElementId(),"object"===e(i)?this.config=i:(this.config={},this.config.useSVGIcons=!0),this.injectPlayerElementInDOM_(),this.initPlayer(),this.finalizeSetup(i)):voplayer.Log.error("injectPlayerElementInDOM_ failed because this.parentDOMElement : "+this.parentDOMElement)}));t.PlayerPlus=g},818:function(A,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createPlayerWithControls=void 0,i(4192);var e=i(4843);Object.defineProperty(t,"createPlayerWithControls",{enumerable:!0,get:function(){return e.createPlayerWithControls}});var n="undefined"!=typeof window&&window||i.g,s=n.voplayer;s||(s=n.voplayer={}),s.PlayerPlus=e.PlayerPlus,s.createPlayerWithControls=e.createPlayerWithControls,t.default=s},4192:function(A,t,i){"use strict";i.r(t);var e=i(3379),n=i.n(e),s=i(1163),o={insert:"head",singleton:!1};n()(s.Z,o);t.default=s.Z.locals||{}},3886:function(A){"use strict";A.exports="data:font/ttf;base64,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"},8446:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgLTQ4MCAyNDAwIDI0MDAiPgogIDxnIHRyYW5zZm9ybT0ibWF0cml4KDEgMCAwIC0xIDAgMTkyMCkiPgogICA8cGF0aCBmaWxsPSJjdXJyZW50Q29sb3IiCmQ9Ik0xMTU4IDM3OS42M2wtMTA2Ny43NCA3OTAuOTgxYy0xMS4wMzAzIDguMjE5NzMgLTE2LjU0OTggMTkgLTE2LjU0OTggMjkuNzU5OHM1LjUxOTUzIDIxLjQ5MDIgMTYuNTQ5OCAyOS42MjAxbDEwNjkuNSA3ODguODhjNy40Mjk2OSA1LjUzMDI3IDE0LjQxOTkgOC4xNjAxNiAyMC40MTk5IDguMTYwMTZjMTEuODMwMSAwIDE5LjgzMDEgLTEwLjIwMDIgMTkuODMwMSAtMjguNDgwNWwtMC4zNzk4ODMgLTQ1MS4zNwpjMjI0IC0xNy41NzAzIDExMzYuNSAtMTQ3LjI1IDExNTAuMzggLTEwOTcuNWMtMTc3LjQ4IDQyMi4yOCAtNzQyLjU1MSA0NTAuODggLTEwNjguMTUgNDUwLjg4Yy0yOS44NjA0IDAgLTU3LjcwMDIgLTAuMjQwMjM0IC04My4wNDk4IC0wLjQ0MDQzbC0wLjU1OTU3IC01MDAuMTljMCAtMTguMjUgLTcuOTI5NjkgLTI4LjQyOTcgLTE5Ljc2OTUgLTI4LjQyOTdjLTYgMCAtMTMgMi42MjAxMiAtMjAuNDgwNSA4LjEyOTg4eiIgLz4KICA8L2c+Cgo8L3N2Zz4K"},8387:function(A){"use strict";A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QEeCR8kYuK6WwAABS5JREFUaN7tml1sFFUUx393umX3YTM2iiIpoPjxAE1TaEqBxA9KtEmBMQb1yZdG2J0QEq3EB3koDhA/ogE0YNLZNPjxoKaJYjptHzRhjbQGyypiwEo08iVfJgRSN4HQdI8P3U1a2J0Z6O66bPaf7MPOOXP3/vacuffcewcqKpxExNe1QkorIqwClIjsEZGfROSkiPQAs8oysiJyj+TWwmJHuhjQf7kAny032Frx1qJyeoYbffjUlRPw2Tz5/L9Tyi3e/7dLOl+bTtuGYfj2VX6cRkdH0XWd0dHRXcAGIAhcAHbpuv5uxu7xZ90PnAJm5Ejn35RSfuDWA+8BNelLX2ma1n7p0qV/BwcH8wOchv4RaM5iOqTrerOfDFFKISIWsBYIAweADqXUZZ+R3Ay8lfXZ1LTaVCp1znGcvET4ZeADF5fXdF3fcbuPis/IzktnSC4dcRxnUb4Grec97Otu9/nzA5v2e87DpaGtrS2UL+BaD/tdRRg07/XyCQQCd+cL+LiHfaQIE8XvXg6O45ybNnAymQTY7OH25vnz5wtK6zjOp8AVl5R/Py+FRzgcRtf1I0BnDpdNuq7HZ8+eXVBg0zQBHgeyFQRf9/b2vmpZVv6mpfRo3Zieh+8DjgK7dV2/UKzip7W1lVAoFBKRDmABcFEp9UVvb+/Pq1evpr+/P78/mE7vkpKfqFZUUUUVVXRHamhoCIBEIlGXSCSeSSQSjQAjIyMl3/dIJJIpUB6ORqOrIpHIctfCY//+/dTU1CxIpVIDwIOTfC4Cy4LB4Kn6+vqS3Es1TRMRmQU4wJJJpmvAU7FYbGgK8OHDhxkbG9M1Tbuco9wcSyaToRUrVqRKNcLRaPQCOTb1RWSeiJzp7u6egFu8eDGapu10qa2rw+HwG6UKa5pmBy4nGEqpHd3d3TctHlq9StlSBRaRNr99nwwc9LgpVMJjllffgtmAD3jcNFjCwF59/2EK8PDwMCKy0eWGcaVUZzweL1XgrW6bA5qmvZJeT08ANzc3k0wmL1ZVVT0EnLzB/x8ReTQQCFxpaWkpVeAxYD4wfMP168Dyrq6uo7ZtT03plpYWUqnUiaampvnAAhFZKyLLmpqaZmmadqKhoaFk8zkWiyEiV2Kx2FIRmaOUelZEVsZiseDVq1cPVkrKiiqqqKKy1L59+6Z87+npueMYAj6Lc5RSVFdXP+Y4zkZgJhMb8TuBM8XoqGVZmc9GYBPwAPAnsNuyrA8zdi/5PnlwHMcCsi0RTcMwYkWC/ghoz2I6ZFlWs582lJ/o9vX1PQ184+KzVNO04TVr1hQEdPv27YyPj68C+t3/D2urV1ueh2npA+sOD5+thYIF6OzsBHjRw22Dn7b8ng97pcv8ImT0Ix72mfkEPuVhL8Y7Vn9M035LwJ94pPRnfhrp6uq67RE6veZ10668DFqTRuljwMIspm8Nw2j1CVynlPocqM+stYF1pmn2+QR/HXg7W0Asy2rPW4Qdx8EwjDomXl3K7CwcB9YbhtE6MDDgev/evXuxbXuJUuroJFiYOFh3bNt+wSfwO8ATwJfAwXTmLbcsq93vObHvCMfjcaaz42Hb9nWgOteOhWmaM/y0s23bNrZs2XJTQeJXqgiDDbZt1wO/erg9aZrm94XuS1HephWReT58aovRl6IAa5p2zMunqqpqpGyAo9HoSeA7tzk0Eon8UjbAtm0zd+7clUAiR9HSmNlGLQtg0zQ5ffq0mKa5RCnVAuwBPlZKvWSa5hylVDKzUV52ypziZVZBFRVY/wHx/kffzHU82QAAAABJRU5ErkJggg=="},8397:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9Ii0xOCAtNDgwIDI0MDAgMjQwMCI+CiAgPGcgdHJhbnNmb3JtPSJtYXRyaXgoMSAwIDAgLTEgMCAxOTIwKSI+CiAgIDxwYXRoIGZpbGw9ImN1cnJlbnRDb2xvciIKZD0iTTc4MyAyNTUuMTNsLTMyNC42MzIgMzI0LjYzMWMtMTkuNSAxOS40NDA0IC01MS4zNzk5IDUxLjI1IC03MC42ODk1IDcwLjY5MDRsLTM5MC42OSAzOTAuNzVjLTkuNSA5LjY2MDE2IC0xNC4yNSAyMi40NDA0IC0xNC4yNSAzNS4yNXM0Ljc1IDI1LjY0OTQgMTQuMjUgMzUuNDM5NWwzMjQuNjkgMzI0LjYyYzkuNzUgOS43MTk3MyAyMi41ODk4IDE0LjU4MDEgMzUuNDEwMiAxNC41ODAxczI1LjYyMDEgLTQuODYwMzUgMzUuMjgwMyAtMTQuNTgwMQpsMzkwLjYzIC0zOTAuNjJjOS43ODAyNyAtOS43MTk3MyAyMi42MjAxIC0xNC41ODAxIDM1LjQyOTcgLTE0LjU4MDFzMjUuNTg5OCA0Ljg2MDM1IDM1LjI1IDE0LjU4MDFsMTA0OS40MyAxMDQ5LjE4YzkuNzAwMiA5Ljc1OTc3IDIyLjYxMDQgMTQuNjQ5NCAzNS41MzAzIDE0LjY0OTRjMTIuODIwMyAwIDI1LjY1MDQgLTQuODA5NTcgMzUuMzYwNCAtMTQuNDZsMzI2Ljc1IC0zMjIuMjUKYzkuOTI5NjkgLTkuNzA5OTYgMTQuODcwMSAtMjIuNTQ5OCAxNC44NzAxIC0zNS4zOTk0YzAgLTEyLjcwMDIgLTQuODMwMDggLTI1LjQxMDIgLTE0LjQyOTcgLTM1LjEwMDZsLTE0NDcuNSAtMTQ0Ny4zOGMtOS43NSAtOS43MTk3MyAtMjIuNTgwMSAtMTQuNTgwMSAtMzUuMzg5NiAtMTQuNTgwMXMtMjUuNjA5NCA0Ljg2MDM1IC0zNS4yOTk4IDE0LjU4MDF6IiAvPgogIDwvZz4KCjwvc3ZnPgo="},947:function(A){"use strict";A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAASdAAAEnQB3mYfeAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEOSURBVEiJ7daxrcMgEAbgP1Eky5u48TjIrrwE4A2ovAjoxqG5CTyEC/tVkVIEzCFFfnov14L4BOI/uK3reuCCul+BfuF/DscYQUTVCxMRYoxymJmhtYb3Xox676G1BjMn5zxSA0op7PsOYwy2bcM0TUVoCAHWWjjnoJSSwwAwDAMAwBgDAKd4CAHGGDjnTudmYQkuQYvgElyKFsM5vAYVwe/wpmmqUDH8xI/jwDzPAIBlWTCOo3SZ6zqXeMevOX0etSTnVXDqIpXmvApOodImI4LPIlODn8KlOZXiWVjaHCR4EiYiWGvFOX3Nedu2yRfqlvrexhjBzNmnLVdEhK7r0Pe9DP50/b4/1xf+M/APIhayE1X2raoAAAAASUVORK5CYII="},2901:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzQ3MDAiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDcxMCI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQ3MDgiIC8+CiAgPGcKICAgICBpZD0iZzQ3MDIiCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS4wOTg4NzAxLDAsMCwtMS4wOTg4NzAxLC00OS40MzUwNSw3MzQuNjA0NTQpIj4KICAgIDxwYXRoCiAgICAgICBzdHlsZT0iZmlsbDpjdXJyZW50Q29sb3IiCiAgICAgICBpZD0icGF0aDQ3MDQiCiAgICAgICBkPSJtIDQ3LDYwOCAwLC0xMTcgNTUsMCBxIDM4LC0yIDQ3LDggOSwxMCA3LDU0IGwgMCw2MyA2MywwIHEgNDQsLTIgNTQsNyAxMCw5IDgsNDcgbCAwLDU1IC0yMzQsMCAwLC0xMTcgeiBtIDY3Miw2MiBxIC0yLC0zOCA4LC00NyAxMCwtOSA1NCwtNyBsIDYzLDAgMCwtNjMgcSAtMiwtNDQgNywtNTQgOSwtMTAgNDcsLTggbCA1NSwwIDAsMjM0IC0yMzQsMCAwLC01NSB6IE0gNDcsOTIgbCAwLC0xMTcgMjM0LDAgMCw1NSBxIDIsMzggLTgsNDcgLTEwLDkgLTU0LDcgbCAtNjMsMCAwLDYzIHEgMiw0NCAtNyw1NCAtOSwxMCAtNDcsOCBsIC01NSwwIDAsLTExNyB6IG0gNzk3LDU1IDAsLTYzIC02MywwIHEgLTQ0LDIgLTU0LC03IC0xMCwtOSAtOCwtNDcgbCAwLC01NSAyMzQsMCAwLDIzNCAtNTUsMCBxIC0zOCwyIC00NywtOCAtOSwtMTAgLTcsLTU0IHoiIC8+CiAgPC9nPgo8L3N2Zz4K"},6439:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzUyNTIiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNTI2MiI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczUyNjAiIC8+CiAgPGcKICAgICBpZD0iZzUyNTQiCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS4wOTgyODQ4LDAsMCwtMS4wOTgyODQ4LC00OS4xNDI0LDczNC4zOTk2OCkiPgogICAgPHBhdGgKICAgICAgIHN0eWxlPSJmaWxsOmN1cnJlbnRDb2xvciIKICAgICAgIGlkPSJwYXRoNTI1NiIKICAgICAgIGQ9Im0gMTcyLDY2MyAwLC02MyAtNjMsMCBRIDY1LDYwMiA1NSw1OTIuNSA0NSw1ODMgNDcsNTQ1IGwgMCwtNTQgMjM0LDAgMCwyMzQgLTU0LDAgcSAtMzksMiAtNDgsLTguNSAtOSwtMTAuNSAtNywtNTMuNSB6IG0gNTQ3LC01NSAwLC0xMTcgMjM0LDAgMCw1NCBxIDIsMzggLTgsNDcuNSAtMTAsOS41IC01NCw3LjUgbCAtNjMsMCAwLDYzIHEgMiw0MyAtNyw1My41IC05LDEwLjUgLTQ4LDguNSBsIC01NCwwIDAsLTExNyB6IE0gNDcsMTU1IFEgNDUsMTE3IDU1LDEwNy41IDY1LDk4IDEwOSwxMDAgbCA2MywwIDAsLTYyIHEgLTIsLTQ0IDcsLTU0LjUgOSwtMTAuNSA0OCwtOC41IGwgNTQsMCAwLDIzNCAtMjM0LDAgMCwtNTQgeiBtIDY3MiwtNjMgMCwtMTE3IDU0LDAgcSAzOSwtMiA0OCw4LjUgOSwxMC41IDcsNTQuNSBsIDAsNjIgNjMsMCBxIDQ0LC0yIDU0LDcuNSAxMCw5LjUgOCw0Ny41IGwgMCw1NCAtMjM0LDAgMCwtMTE3IHoiIC8+CiAgPC9nPgo8L3N2Zz4K"},1225:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzU4MDQiCiAgIHZpZXdCb3g9Ii0xIC0xNTAgMTAwMCAxMDAwIgogICB2ZXJzaW9uPSIxLjEiPgogIDxtZXRhZGF0YQogICAgIGlkPSJtZXRhZGF0YTU4MTQiPgogICAgPHJkZjpSREY+CiAgICAgIDxjYzpXb3JrCiAgICAgICAgIHJkZjphYm91dD0iIj4KICAgICAgICA8ZGM6Zm9ybWF0PmltYWdlL3N2Zyt4bWw8L2RjOmZvcm1hdD4KICAgICAgICA8ZGM6dHlwZQogICAgICAgICAgIHJkZjpyZXNvdXJjZT0iaHR0cDovL3B1cmwub3JnL2RjL2RjbWl0eXBlL1N0aWxsSW1hZ2UiIC8+CiAgICAgICAgPGRjOnRpdGxlPjwvZGM6dGl0bGU+CiAgICAgIDwvY2M6V29yaz4KICAgIDwvcmRmOlJERj4KICA8L21ldGFkYXRhPgogIDxkZWZzCiAgICAgaWQ9ImRlZnM1ODEyIiAvPgogIDxnCiAgICAgaWQ9Imc1ODA2IgogICAgIHRyYW5zZm9ybT0ibWF0cml4KDEuMjcwOTI2NCwwLDAsLTEuMjcwOTI2NCw0Mi43Mzc0MjIsNzk0LjgyNDI0KSI+CiAgICA8cGF0aAogICAgICAgc3R5bGU9ImZpbGw6Y3VycmVudENvbG9yIgogICAgICAgaWQ9InBhdGg1ODA4IgogICAgICAgZD0ibSAyNTAsODcgcSAtNSwyMCAtMjQsMzEgLTE5LDExIC00MCw1IEwgMTEwLDEwMSBRIDg5LDk0IDY2LDEwNCA0MywxMTQgMzQsMTMyIEwgNywxNzcgcSAtMTAsMTkgLTYsNDIgNCwyMyAxOSwzOSBsIDU2LDU1IHEgMTUsMTUgMTUsMzYgMCwyMSAtMTUsMzcgbCAtNTYsNTUgcSAtMTYsMTUgLTE5LDM4IC0zLDIzIDgsNDMgbCAyNSw0NiBxIDEwLDE5IDMyLDI4IDIyLDkgNDQsMiBsIDc2LC0yMSBxIDIxLC02IDQwLDUgMTksMTEgMjQsMzEgbCAxOSw3NiBxIDYsMjIgMjUsMzcgMTksMTUgNDAsMTUgbCA1MSwwIHEgMjEsMCA0MCwtMTUgMTksLTE1IDI1LC0zNyBsIDIwLC03NiBxIDYsLTIxIDI0LC0zMSAxOCwtMTAgMzgsLTUgbCA3NywyMSBxIDIxLDYgNDMsLTIgMjIsLTggMzMsLTI4IGwgMjYsLTQ2IHEgMTAsLTE5IDYsLTQzIC00LC0yNCAtMTgsLTM4IGwgLTU3LC01NSBxIC0xNiwtMTUgLTE1LC0zNyAxLC0yMiAxNSwtMzYgbCA1NywtNTUgcSAxNSwtMTUgMTgsLTM5IDMsLTI0IC02LC00MiBsIC0yNiwtNDUgcSAtMTAsLTE5IC0zMywtMjggLTIzLC05IC00MywtMyBsIC03NywyMiBxIC0yMCw1IC0zOCwtNSAtMTgsLTEwIC0yNCwtMzEgTCA0NTAsOSBxIC02LC0yMSAtMjUsLTM1IC0xOSwtMTQgLTQwLC0xNSBsIC01MSwwIHEgLTIyLDAgLTQwLDE1IC0xOCwxNSAtMjUsMzUgeiBtIDExMCwzNjYgcSAtNDQsMCAtNzUsLTMxIC0zMSwtMzEgLTMwLC03MyAxLC00MiAzMCwtNzMgMjksLTMxIDc1LC0zMCA0MiwwIDczLDMwIDMxLDMwIDMxLDczIDAsNDMgLTMxLDc0IC0zMSwzMSAtNzMsMzAgeiIgLz4KICA8L2c+Cjwvc3ZnPgo="},3828:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzQxNDgiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE1OCI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxNTYiIC8+CiAgPGcKICAgICBpZD0iZzQxNTAiCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS40MjU4OTA2LDAsMCwtMS40MjU4OTA2LDEyMi4xMzg5OSw4NDkuMDYxNzEpIj4KICAgIDxwYXRoCiAgICAgICBzdHlsZT0iZmlsbDpjdXJyZW50Q29sb3IiCiAgICAgICBpZD0icGF0aDQxNTIiCiAgICAgICBkPSJtIDQ0MCw3MDAgcSA5MCwwIDkwLC02NCBMIDUzMCw2NiBRIDUzMCwwIDQ0MCwwIDM1MCwwIDM1MCw2NiBsIDAsNTcwIHEgMCw2NCA5MCw2NCB6IG0gLTM1MCwwIHEgOTAsMCA5MCwtNjQgTCAxODAsNjYgUSAxODAsMCA5MCwwIDAsMCAwLDY2IGwgMCw1NzAgcSAwLDY0IDkwLDY0IHoiIC8+CiAgPC9nPgo8L3N2Zz4K"},7477:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzQxMzYiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE0NiI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxNDQiIC8+CiAgPGcKICAgICBpZD0iZzQxMzgiCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS42NjE2NTM3LDAsMCwtMS42NjE2NTM3LDg0LjU4NjU3NSw5MzMuMjQwNDUpIj4KICAgIDxwYXRoCiAgICAgICBzdHlsZT0iZmlsbDpjdXJyZW50Q29sb3IiCiAgICAgICBpZD0icGF0aDQxNDAiCiAgICAgICBkPSJtIDQ4NiwzNzYgcSAxNCwtMTAgMTQsLTI1IDAsLTE1IC0xNCwtMjUgTCA1OCw2MCBRIDM0LDQ0IDE3LDU0IDAsNjQgMCw5NCBsIDAsNTE0IHEgMCwzMCAxNyw0MCAxNywxMCA0MSwtNiB6IiAvPgogIDwvZz4KPC9zdmc+Cg=="},4087:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiID4KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgLTQ4MCAyNDAwIDI0MDAiPgogIDxnIHRyYW5zZm9ybT0ibWF0cml4KDEgMCAwIC0xIDAgMTkyMCkiPgogICA8cGF0aCBmaWxsPSJjdXJyZW50Q29sb3IiCmQ9Ik05NzMuNDQgMTUwLjc1bC0zNjcuNTcgMzY3LjU2bDY4MS42MyA2ODEuNjNsLTY4MS4yNSA2ODIuMTJsMzY3LjE5IDM2Ny4xOWwxMDQ5LjQ0IC0xMDQ5LjMxeiIgLz4KICA8L2c+Cgo8L3N2Zz4K"},770:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzMzNzUiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhMzM4NSI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczMzODMiIC8+CiAgPGcKICAgICBpZD0iZzMzNzciCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS4zMjE2OTEyLDAsMCwtMS4zMjE2OTEyLDQuMzY1OCw4MTIuNTkxOTIpIj4KICAgIDxwYXRoCiAgICAgICBzdHlsZT0iZmlsbDpjdXJyZW50Q29sb3IiCiAgICAgICBpZD0icGF0aDMzNzkiCiAgICAgICBkPSJNIDAsNDc1IDE2Niw0NzUgMzc1LDY5NCAzNzUsNiAxNjYsMjI1IDAsMjI1IFoiIC8+CiAgPC9nPgo8L3N2Zz4K"},3018:function(A){"use strict";A.exports="data:image/svg+xml;base64,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"},5563:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgaWQ9InN2ZzMzNzUiCiAgIHZpZXdCb3g9IjAgLTE1MCAxMDAwIDEwMDAiCiAgIHZlcnNpb249IjEuMSI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhMzM4NSI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczMzODMiIC8+CiAgPGcKICAgICBpZD0iZzMzNzciCiAgICAgdHJhbnNmb3JtPSJtYXRyaXgoMS4zMjE2OTEyLDAsMCwtMS4zMjE2OTEyLDQuMzY1OCw4MTIuNTkxOTIpIj4KICAgIDxwYXRoCiAgICAgICBzdHlsZT0iZmlsbDpjdXJyZW50Q29sb3IiCiAgICAgICBpZD0icGF0aDMzNzkiCiAgICAgICBkPSJNIDAsNDc1IDE2Niw0NzUgMzc1LDY5NCAzNzUsNiAxNjYsMjI1IDAsMjI1IDAsNDc1IFogTSA1NjMsMzUwIFEgNTYyLDI5MyA1MzQuNSwyNDcgNTA3LDIwMSA0NTksMTc4IGwgMCwzNDYgUSA1MDcsNTAwIDUzNC41LDQ1My41IDU2Miw0MDcgNTYzLDM1MCBaIE0gNDU5LDcyNSBRIDU4Niw2OTMgNjY2LjUsNTkxIDc0Nyw0ODkgNzUwLDM1MCA3NDcsMjExIDY2Ni41LDEwOSA1ODYsNyA0NTksLTI1IGwgMCw4OCBRIDU1MSw5MyA2MDcuNSwxNzAgNjY0LDI0NyA2NjYsMzUwIDY2NCw0NTMgNjA3LjUsNTMwIDU1MSw2MDcgNDU5LDYzNyBsIDAsODggeiIgLz4KICA8L2c+Cjwvc3ZnPgo="},5731:function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIC0xNTAgMTAwMCAxMDAwIgogICBpZD0ic3ZnMzM4NyI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhMzM5NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczMzOTUiIC8+CiAgPGcKICAgICB0cmFuc2Zvcm09Im1hdHJpeCgxLjMxNjM4NDIsMCwwLC0xLjMxNjM4NDIsNi4zNTU5MjUsODEwLjczNDQ3KSIKICAgICBpZD0iZzMzODkiPgogICAgPHBhdGgKICAgICAgIGQ9Ik0gNjY4LDM1MCBRIDY2Niw0NTMgNjA4LjUsNTMwIDU1MSw2MDcgNDU5LDYzNyBsIDAsODggUSA1ODYsNjkzIDY2Ni41LDU5MSA3NDcsNDg5IDc1MCwzNTAgNzUwLDI5OSA3MzgsMjUxLjUgNzI2LDIwNCA3MDMsMTYzIGwgLTYyLDYyIHEgMjYsNTcgMjcsMTI1IHogbSAtMjkzLDM0NCAwLC0yMDMgLTEwMCw5OSB6IG0gMzIyLC02MTkgMCwwIDQ3LC00NyAtNDcsLTQ3IC03OCw3OCBRIDU1MSwtMyA0NTksLTI1IGwgMCw4OCBxIDU2LDE3IDEwMiw1NSBMIDM3NSwzMDMgMzc1LDYgMTY2LDIyNSAwLDIyNSAwLDQ3NSAxNjYsNDc1IDE4NCw0OTUgNiw2NzIgNTMsNzE5IFogTSA1NjMsMzUwIHEgMSwtMjEgLTQsLTQzIGwgLTEwMCwxMDAgMCwxMTcgUSA1MDcsNTAwIDUzNC41LDQ1My41IDU2Miw0MDcgNTYzLDM1MCBaIgogICAgICAgaWQ9InBhdGgzMzkxIgogICAgICAgc3R5bGU9ImZpbGw6Y3VycmVudENvbG9yIiAvPgogIDwvZz4KPC9zdmc+Cg=="}},function(A){var t;return t=818,A(A.s=t)}])}));