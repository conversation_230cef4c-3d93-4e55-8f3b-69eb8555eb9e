/*! For license information please see voplayer-multiview-ui.min.js.LICENSE.txt */
"use strict";!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(self,(function(){return(self.webpackChunkvoplayer_html5=self.webpackChunkvoplayer_html5||[]).push([[426],{9427:function(e,t,n){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(){i=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,o=Object.create(i.prototype),a=new S(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return x()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var l=b(a,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var y={};function f(){}function p(){}function h(){}var v={};u(v,a,(function(){return this}));var _=Object.getPrototypeOf,m=_&&_(_(L([])));m&&m!==t&&n.call(m,a)&&(v=m);var g=h.prototype=f.prototype=Object.create(v);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(o,a,l,c){var u=d(e[o],e,a);if("throw"!==u.type){var s=u.arg,y=s.value;return y&&"object"==r(y)&&n.call(y,"__await")?t.resolve(y.__await).then((function(e){i("next",e,l,c)}),(function(e){i("throw",e,l,c)})):t.resolve(y).then((function(e){s.value=e,l(s)}),(function(e){return i("throw",e,l,c)}))}c(u.arg)}var o;this._invoke=function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}}function b(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return y;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var r=d(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,y;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,y):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function L(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,u(e,c,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},E(w.prototype),u(w.prototype,l,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new w(s(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(g),u(g,c,"Generator"),u(g,a,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=L,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;P(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),y}},e}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var l=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});var c=n(6865),u=n(8754),s="sqp-all-controls",d="multiview-vr-controls",y="CLICK TO PAUSE",f="debug-current-time",p="debug-video-current-time",h="debug-seek-range",v="debug-offset",_="debug-gap-with_leader",m="debug-playback-rate",g="debug-is-playing",E="debug-is-muted",w="debug-bandwidth",b="debug-latest-action",C=a((function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.destroy=function(){return l(r,void 0,void 0,i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._multiviewController){e.next=9;break}return this._multiviewController.off(u.MultiViewEvent.LEADER_PLAYER_PLAY,this._onLeaderPlayerPlay),this._multiviewController.off(u.MultiViewEvent.LEADER_PLAYER_PAUSE,this._onLeaderPlayerPause),this._multiviewController.off(u.MultiViewEvent.ON_SWAP_VIDEO_ELEMENT,this._onSwapVideoElement),this._multiviewController.off(u.MultiViewEvent.ON_DEBUG,this._onDebug),this._multiviewController.off(u.MultiViewEvent.ON_DEBUG_ACTION,this._onDebugAction),e.next=8,this._multiviewController.reset();case 8:this._multiviewController=null;case 9:this._nbPlayers=0,this._onClickMap=new Map([]),this._leaderContainer.remove(),this._rightContainer.remove(),this._isDebug=!1,this.videoElement_&&(this.videoElement_.addEventListener("emptied",this.handleEmptiedEvent),this.videoElement_.pause(),this.videoElement_.srcObject=null,this.videoElement_.removeAttribute("src"),this.videoElement_.load());case 15:case"end":return e.stop()}}),e,this)})))},this.handleEmptiedEvent=function(){r.videoElement_.removeEventListener("emptied",r.handleEmptiedEvent),r.videoElement_=null},this.registerPlayer=function(e,t){return l(r,void 0,void 0,i().mark((function n(){var r,o;return i().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!(this._multiviewController&&this._nbPlayers<c.default.MAX_NUMBER_OF_PLAYER_INSTANCES)){n.next=8;break}return r=this._getNextVideoId(),this._onCreateVideoElement(r,void 0!==t.vrConfig),n.next=5,this._multiviewController.registerPlayer(r,e,t);case 5:return o=n.sent,this._registerOnClickForVideoElement(o,r),n.abrupt("return",o);case 8:case"end":return n.stop()}}),n,this)})))},this._initMainContainer=function(e){r._mainContainerElement=document.getElementById(e),r._applyMainContainerStyle(),r._createLeaderContainer(),r._createRightContainer()},this._applyMainContainerStyle=function(){r._mainContainerElement.style.backgroundColor="black",r._mainContainerElement.style.fontSize="0",r._mainContainerElement.style.display="flex",r._mainContainerElement.style.flexDirection="row",r._mainContainerElement.style.width="100%",r._mainContainerElement.style.height="100%",r._mainContainerElement.style.position="relative"},this._createLeaderContainer=function(){r._leaderContainer=document.createElement("div"),r._leaderContainer.id="multiview-videoLeaderContainer",r._leaderContainer.style.display="flex",r._leaderContainer.style.height="100%",r._leaderContainer.style.width="65%",r._leaderContainer.style.overflow="hidden",r._leaderContainer.style.zIndex="1",r._leaderContainer.style.position="relative",r._leaderContainer.style.fontSize="12px",r._mainContainerElement.appendChild(r._leaderContainer)},this._createRightContainer=function(){r._rightContainer=document.createElement("div"),r._rightContainer.id="multiview-videoRightContainer",r._rightContainer.style.display="flex",r._rightContainer.style.flexDirection="column",r._rightContainer.style.justifyContent="start",r._rightContainer.style.overflow="hidden",r._rightContainer.style.width="35%",r._rightContainer.style.height="100%",r._rightContainer.style.zIndex="2",r._rightContainer.style.position="relative",r._rightContainer.style.fontSize="8px",r._mainContainerElement.appendChild(r._rightContainer)},this._onSwapVideoElement=function(e){var t=r._getVideoContainerId(e.newLeaderVideoElementId),n=document.getElementById(t);n&&r._applyLeaderVideoContainerStyle(n);var i=r._getVideoContainerId(e.oldLeaderVideoElementId),o=document.getElementById(i);if(o&&r._applySecondaryVideoContainerStyle(o),n&&o){for(var a=r._rightContainer.childNodes,l=0;l<a.length;l++)a[l].id===t&&(l===a.length-1?r._rightContainer.appendChild(o):r._rightContainer.insertBefore(o,a[l]));r._leaderContainer.appendChild(n)}e.shouldActivateVRControls&&r._enableVRControls(e.newLeaderVideoElementId),r._disableVRControls(e.oldLeaderVideoElementId)},this._onCreateVideoElement=function(e,t){if(0===r._nbPlayers?(r._createLeaderVideoElement(e,t),r._nbPlayers++):r._nbPlayers<c.default.MAX_NUMBER_OF_PLAYER_INSTANCES&&(r._createSecondaryVideoElement(e,t),r._disableVRControls(e),r._nbPlayers++),r._nbPlayers>=4){r._rightContainer.style.justifyContent="space-between";for(var n=r._rightContainer.childNodes,i=0;i<n.length;i++)n[i].style.marginBottom="0"}},this._createLeaderVideoElement=function(e,t){var n=document.createElement("div");n.id=r._getVideoContainerId(e),r._applyLeaderVideoContainerStyle(n);var i=document.createElement("div");i.className=s,i.id="".concat(s,"_").concat(e),t&&r._addVRControlLayer(i,e);var o=r._generateVideoElement(e);if(n.appendChild(o),n.appendChild(i),r._isDebug){var a=r._generateDebugLayer(e);n.appendChild(a)}r._leaderContainer.appendChild(n)},this._createSecondaryVideoElement=function(e,t){var n=document.createElement("div");n.id=r._getVideoContainerId(e),r._applySecondaryVideoContainerStyle(n);var i=document.createElement("div");i.className=s,i.id="".concat(s,"_").concat(e),t&&r._addVRControlLayer(i,e);var o=r._generateDebugLayer(e),a=r._generateVideoElement(e);n.appendChild(a),n.appendChild(i),n.appendChild(o),r._rightContainer.appendChild(n)},this._addVRControlLayer=function(e,t){var n=document.createElement("div");n.className="sqp-vr-controls"+t,n.id="".concat(d,"_").concat(t),e.appendChild(n)},this._generateVideoElement=function(e){var t=document.createElement("video");return t.id=e,t.setAttribute("webkit-playsinline",""),t.setAttribute("playsinline",""),t.style.height="100%",t.style.width="100%",r.videoElement_=t,t},this._registerOnClickForVideoElement=function(e,t){var n=r._getVideoContainerId(t);if(r._onClickMap[n]=function(){r._multiviewController.swapLeaderPlayer(e,!0)},r._nbPlayers>1){var i=document.getElementById(n);i&&r._addSecondaryVideoContainerOnClick(i)}},this._disableVRControls=function(e){var t=document.getElementById("".concat(d,"_").concat(e));t&&(t.style.display="none")},this._enableVRControls=function(e){var t=document.getElementById("".concat(d,"_").concat(e));t&&(t.style.display="block")},this._getVideoContainerId=function(e){return"container_".concat(e)},this._applyLeaderVideoContainerStyle=function(e){e.style.height="100%",e.style.width="100%",e.style.position="relative",e.style.border="none",e.style.marginBottom="0",e.onclick=null,e.onmouseover=null,e.onmouseout=null,e.style.cursor="auto"},this._applySecondaryVideoContainerStyle=function(e){e.style.height="calc(100% / 3 - 1.2vh)",e.style.width="calc(100% - 0.8vh)",e.style.border="solid white 0.4vh",e.style.position="relative",r._nbPlayers>=4?e.style.marginBottom="0":e.style.marginBottom="0.4vh",r._addSecondaryVideoContainerOnClick(e)},this._addSecondaryVideoContainerOnClick=function(e){var t=r._onClickMap[e.id];t&&(e.onclick=function(e){t(),e.stopPropagation(),e.preventDefault()},e.onmouseover=function(){e.style.cursor="pointer"},e.onmouseout=function(){e.style.cursor="auto"})},this._generateDebugLayer=function(e){var t=document.createElement("div");t.id="debug_".concat(e),t.style.position="absolute",t.style.bottom="0",t.style.left="0",t.style.backgroundColor="rgba(0, 0, 0, 0.3)",t.style.color="white",t.style.fontSize="1.6vh";var n=document.createElement("div");n.textContent=e,t.appendChild(n);var r=document.createElement("div");r.id="".concat(f,"_").concat(e),t.appendChild(r);var i=document.createElement("div");i.id="".concat(p,"_").concat(e),t.appendChild(i);var o=document.createElement("div");o.id="".concat(h,"_").concat(e),t.appendChild(o);var a=document.createElement("div");a.id="".concat(v,"_").concat(e),t.appendChild(a);var l=document.createElement("div");l.id="".concat(_,"_").concat(e),t.appendChild(l);var c=document.createElement("div");c.id="".concat(m,"_").concat(e),t.appendChild(c);var u=document.createElement("div");u.id="".concat(g,"_").concat(e),t.appendChild(u);var s=document.createElement("div");s.id="".concat(E,"_").concat(e),t.appendChild(s);var d=document.createElement("div");d.id="".concat(w,"_").concat(e),t.appendChild(d);var y=document.createElement("div");return y.id="".concat(b,"_").concat(e),t.appendChild(y),t},this._onLeaderPlayerPlay=function(){r._playPauseContainer||r._createPlayPauseContainer(),r._playPauseContainer.textContent=y},this._onLeaderPlayerPause=function(){r._playPauseContainer||r._createPlayPauseContainer(),r._playPauseContainer.textContent="CLICK TO PLAY"},this._createPlayPauseContainer=function(){r._playPauseContainer=document.createElement("div"),r._playPauseContainer.style.position="absolute",r._playPauseContainer.style.bottom="2vh",r._playPauseContainer.style.right="2vw",r._playPauseContainer.style.fontSize="3vh",r._playPauseContainer.style.color="white",r._playPauseContainer.style.zIndex="3",r._playPauseContainer.onclick=function(){r._multiviewController&&(r._playPauseContainer.textContent===y?r._multiviewController.pauseAllPlayers():r._multiviewController.startAllPlayers())},r._playPauseContainer.onmouseover=function(){r._playPauseContainer.style.cursor="pointer"},r._playPauseContainer.onmouseout=function(){r._playPauseContainer.style.cursor="auto"},r._leaderContainer.appendChild(r._playPauseContainer)},this._onDebug=function(e){var t=document.getElementById("".concat(f,"_").concat(e.videoElementId));t&&(t.textContent="current time: ".concat(Math.round(e.currentTime)));var n=document.getElementById("".concat(p,"_").concat(e.videoElementId)),r=document.getElementById(e.videoElementId);n&&r&&(n.textContent="video element current time: ".concat(Math.round(r.currentTime)));var i=document.getElementById("".concat(h,"_").concat(e.videoElementId));i&&(i.textContent="seek range start: ".concat(Math.round(e.seekRangeStart)));var o=document.getElementById("".concat(v,"_").concat(e.videoElementId));o&&(o.textContent="offset: ".concat(Math.round(e.offset)));var a=document.getElementById("".concat(_,"_").concat(e.videoElementId));a&&(a.textContent="gap with leader: ".concat(Math.round(-e.gapWithLeader)," ms"));var l=document.getElementById("".concat(m,"_").concat(e.videoElementId));l&&(l.textContent="playback rate: ".concat(e.playbackRate));var c=document.getElementById("".concat(g,"_").concat(e.videoElementId));c&&(c.textContent="is playing: ".concat(e.isPlaying));var u=document.getElementById("".concat(w,"_").concat(e.videoElementId));u&&(u.textContent="bandwidth: ".concat(e.bandwidth));var s=document.getElementById("".concat(E,"_").concat(e.videoElementId));s&&(s.textContent="is muted: ".concat(e.isMuted))},this._onDebugAction=function(e){var t=document.getElementById("".concat(b,"_").concat(e.videoElementId));t&&(t.textContent="last action: ".concat(e.action))},this._getNextVideoId=function(){return"multi-view-video-".concat(r._nbPlayers)},this._multiviewController=n,this._nbPlayers=0,this._onClickMap=new Map([]),this._initMainContainer(t),this._multiviewController.on(u.MultiViewEvent.LEADER_PLAYER_PLAY,this._onLeaderPlayerPlay),this._multiviewController.on(u.MultiViewEvent.LEADER_PLAYER_PAUSE,this._onLeaderPlayerPause),this._multiviewController.on(u.MultiViewEvent.ON_SWAP_VIDEO_ELEMENT,this._onSwapVideoElement),this._multiviewController.on(u.MultiViewEvent.ON_DEBUG,this._onDebug),this._multiviewController.on(u.MultiViewEvent.ON_DEBUG_ACTION,this._onDebugAction),this._isDebug=o,this._multiviewController.setDebugMode(o),this.videoElement_=null}));t.default=C},6865:function(e,t,n){function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function i(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(c)throw o}}}}function o(){o=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,o=Object.create(i.prototype),a=new S(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return x()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var l=b(a,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var y={};function f(){}function p(){}function h(){}var v={};u(v,a,(function(){return this}));var _=Object.getPrototypeOf,m=_&&_(_(L([])));m&&m!==t&&n.call(m,a)&&(v=m);var g=h.prototype=f.prototype=Object.create(v);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function i(o,a,l,c){var u=d(e[o],e,a);if("throw"!==u.type){var s=u.arg,y=s.value;return y&&"object"==r(y)&&n.call(y,"__await")?t.resolve(y.__await).then((function(e){i("next",e,l,c)}),(function(e){i("throw",e,l,c)})):t.resolve(y).then((function(e){s.value=e,l(s)}),(function(e){return i("throw",e,l,c)}))}c(u.arg)}var o;this._invoke=function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return o=o?o.then(r,r):r()}}function b(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return y;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var r=d(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,y;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,y):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function L(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:x}}function x(){return{value:void 0,done:!0}}return p.prototype=h,u(g,"constructor",h),u(h,"constructor",p),p.displayName=u(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,u(e,c,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},E(w.prototype),u(w.prototype,l,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new w(s(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(g),u(g,c,"Generator"),u(g,a,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=L,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;P(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),y}},e}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(e){l=!0,i=e}finally{try{a||null==n.return||n.return()}finally{if(l)throw i}}return o}(e,t)||l(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,i=f(e);if(t){var o=f(this).constructor;n=Reflect.construct(i,arguments,o)}else n=i.apply(this,arguments);return function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return y(e)}(this,n)}}function y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}var p=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function l(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),n(7875);var h=n(5417),v=n(7187),_=n(3742),m=n(8754),g=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}(c,e);var t,n,r,l=d(c);function c(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),(t=l.call(this,null)).setSyncConfig=function(e){if(t._multiViewConfig)for(var n=0,r=Object.entries(e);n<r.length;n++){var i=a(r[n],2),o=i[0],l=i[1];l&&(t._multiViewConfig.multiViewSyncConfig[o]=l)}},t.registerPlayer=function(e,n,r){return p(y(t),void 0,void 0,o().mark((function t(){var i,a,l,u,s,d,y,f,p,v=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this._loaded){t.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Cannot register new player after load() was called"}),t.abrupt("return");case 3:if(!(this._players.length>=c.MAX_NUMBER_OF_PLAYER_INSTANCES)){t.next=6;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Maximum number of player instances (".concat(c.MAX_NUMBER_OF_PLAYER_INSTANCES,") was reached")}),t.abrupt("return");case 6:return(a=null!==(i=r.playerConfig)&&void 0!==i?i:{}).iosPlaysInline=!0,a.hlsConfiguration||(a.hlsConfiguration={}),a.hlsConfiguration.capLevelToPlayerSize=!0,a.hlsConfiguration.capLevelOnFPSDrop=!0,a.hlsConfiguration.autoStartLoad=!1,l=this._isHlsJsSupported(),u=this._isHvcSupported(),s="string"==typeof n?n:n.url,t.next=17,this._isHvcStream(s);case 17:return d=t.sent,a.forceAppleNative=!l||!u&&d,y=(0,h.createPlayer)(e),f={player:y,mediaObj:n,playerConfig:a,videoElementId:e,vrConfig:r.vrConfig},y.license=this._multiViewConfig.license,y.configure(a),r.isLeader||(y.muted=!0),p=this._players.length,this._players.push(f),setTimeout((function(){(r.isLeader||0===p)&&v.swapLeaderPlayer(y,!0)}),20),t.abrupt("return",y);case 28:case"end":return t.stop()}}),t,this)})))},t.load=function(){return p(y(t),void 0,void 0,o().mark((function e(){var t=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was already called"}),e.abrupt("return");case 3:if(!(this._players.length<c.MIN_NUMBER_OF_PLAYER_INSTANCES)){e.next=6;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Minimum number of player instances (".concat(c.MIN_NUMBER_OF_PLAYER_INSTANCES,") was not reached")}),e.abrupt("return");case 6:return this._loaded=!0,e.next=9,Promise.all(this._players.map((function(e){return p(t,void 0,void 0,o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.vrConfig&&(e.player.vrController.prepareVR(),e.player.vrController.isMonoscopic=e.vrConfig.isMonoscopic,e.player.vrController.is180=e.vrConfig.is180,e.player.vrController.isHorizontalSplit=e.vrConfig.isHorizontalSplit),t.next=3,e.player.load(e.mediaObj);case 3:e!==this._leaderPlayer?e.player.quality=e.player.qualities.length-1:e.player.quality=-1;case 4:case"end":return t.stop()}}),t,this)})))})));case 9:return this._unsubscribeToLeaderEvents(),this._subscribeToLeaderEvents(),e.next=13,this.startAllPlayers();case 13:case"end":return e.stop()}}),e,this)})))},t.startAllPlayers=function(){return p(y(t),void 0,void 0,o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was not yet called"}),e.abrupt("return");case 3:return e.abrupt("return",this._startAllPlayers());case 4:case"end":return e.stop()}}),e,this)})))},t.pauseAllPlayers=function(){return p(y(t),void 0,void 0,o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was not yet called"}),e.abrupt("return");case 3:return e.abrupt("return",this._pauseAllPlayers());case 4:case"end":return e.stop()}}),e,this)})))},t.swapLeaderPlayer=function(e){var n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=t._leaderPlayer,a=i(t._players);try{for(a.s();!(n=a.n()).done;){var l=n.value;if(l.player===e){t._loaded?(t._unsubscribeToLeaderEvents(),t._leaderPlayer=l,t._subscribeToLeaderEvents()):t._leaderPlayer=l;break}}}catch(e){a.e(e)}finally{a.f()}if(!o||o===t._leaderPlayer)return t._loaded&&(t._leaderPlayer.player.quality=-1),void(t._audioSetOnSpecificPlayer||(t._leaderPlayer.player.muted=!1));var c=o.player.volume,u=o.player.muted;t._audioSetOnSpecificPlayer||(o.player.muted=!0),t._loaded&&(o.player.quality=o.player.qualities.length-1,t._leaderPlayer.player.quality=-1),u||t._audioSetOnSpecificPlayer||(t._leaderPlayer.player.muted=!1,t._leaderPlayer.player.volume=c),o.player.playing&&!t._isPlaying(t._leaderPlayer.player)&&t._leaderPlayer.player.play(),1!==t._leaderPlayer.player.playbackRate&&(t._leaderPlayer.player.playbackRate=1),r&&t._emitEvent(m.MultiViewEvent.ON_SWAP_VIDEO_ELEMENT,{oldLeaderVideoElementId:o.videoElementId,newLeaderVideoElementId:t._leaderPlayer.videoElementId,shouldActivateVRControls:void 0!==t._leaderPlayer.vrConfig})},t.setAudio=function(e){if(!e)return t._audioSetOnSpecificPlayer=!1,void t._muteAllPlayers(t._leaderPlayer.player);t._muteAllPlayers(e),t._audioSetOnSpecificPlayer=!0},t.setDebugMode=function(e){t._isDebug=e},t.reset=function(){return p(y(t),void 0,void 0,o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._unsubscribeToLeaderEvents(),this._stopPlayerSync(),e.next=4,Promise.all(this._players.map((function(e){return e.player.destroy()})));case 4:this._players=[],this._leaderPlayer=null,this._isInitialBuffering=0,this._isDebug=!1,this._audioSetOnSpecificPlayer=!1,this._loaded=!1,this._playersSyncPaused=!0;case 11:case"end":return e.stop()}}),e,this)})))},t.getPlayers=function(){return t._players.map((function(e){return e.player}))},t.getLeaderPlayer=function(){var e;return null===(e=t._leaderPlayer)||void 0===e?void 0:e.player},t._unsubscribeToLeaderEvents=function(){t._leaderPlayer&&(t._leaderPlayer.player.off(h.Player.BUFFERING,t._onPlayerBuffering),t._leaderPlayer.player.off(h.Player.PLAY,t._onPlayerPlay),t._leaderPlayer.player.off(h.Player.PAUSE,t._onPlayerPause))},t._subscribeToLeaderEvents=function(){t._leaderPlayer&&(t._leaderPlayer.player.on(h.Player.BUFFERING,t._onPlayerBuffering),t._leaderPlayer.player.on(h.Player.PLAY,t._onPlayerPlay),t._leaderPlayer.player.on(h.Player.PAUSE,t._onPlayerPause))},t._startAllPlayers=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return p(y(t),void 0,void 0,o().mark((function t(){var n=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._startPlayersSync(),t.next=3,Promise.all(this._players.filter((function(t){return e||t!==n._leaderPlayer})).map((function(e){if(!n._isPlaying(e.player))return e.player.play()})));case 3:case"end":return t.stop()}}),t,this)})))},t._pauseAllPlayers=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return p(y(t),void 0,void 0,o().mark((function t(){var n=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._pausePlayersSync(),t.next=3,Promise.all(this._players.filter((function(t){return e||t!==n._leaderPlayer})).map((function(e){if(n._isPlaying(e.player))return e.player.pause()})));case 3:case"end":return t.stop()}}),t,this)})))},t._muteAllPlayers=function(e){var n,r=i(t._players);try{for(r.s();!(n=r.n()).done;){var o=n.value;o.player===e?o.player.muted&&(o.player.muted=!1):o.player.muted||(o.player.muted=!0)}}catch(e){r.e(e)}finally{r.f()}},t._isPlaying=function(e){return e.videoElement.currentTime>0&&!e.videoElement.paused&&!e.videoElement.ended&&e.videoElement.readyState>e.videoElement.HAVE_CURRENT_DATA&&e.playing},t._startPlayersSync=function(){t._playersSyncTimer||(t._playersSyncTimer=setInterval(t._synchronisePlayers,t._multiViewConfig.multiViewSyncConfig.playerSyncFrequency)),t._playersSyncPaused=!1},t._pausePlayersSync=function(){t._playersSyncPaused=!0},t._stopPlayerSync=function(){t._pausePlayersSync(),t._playersSyncTimer||clearInterval(t._playersSyncTimer)},t._synchronisePlayers=function(){var e,n,r,o,a,l;if(t._playersSyncPaused){var c,u=i(t._players);try{for(u.s();!(c=u.n()).done;){var s=c.value;t._isPlaying(s.player)&&s.player.pause()}}catch(e){u.e(e)}finally{u.f()}}else{var d,y=t._leaderPlayer.player,f=!1,p=i(t._players);try{for(p.s();!(d=p.n()).done;){var h=d.value,v=null!==(n=null===(e=y.corePlayer.hlsSeekRange_)||void 0===e?void 0:e.start)&&void 0!==n?n:y.seekRange().start,_=y.videoElement.currentTime,g=_+v-y.currentTime;if(h!==t._leaderPlayer){var E=null!==(a=null===(o=h.player.corePlayer.hlsSeekRange_)||void 0===o?void 0:o.start)&&void 0!==a?a:h.player.seekRange().start,w=h.player.videoElement.currentTime,b=w+E-h.player.currentTime,C=1e3*(v+_-g-(E+w-b));C>=t._multiViewConfig.multiViewSyncConfig.thresholdJumpPlayback||C<=-t._multiViewConfig.multiViewSyncConfig.thresholdJumpPlayback?(1!==h.player.playbackRate&&(h.player.playbackRate=1),h.player.currentTime=h.player.currentTime+C/1e3,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"jumped to ".concat(Math.round(h.player.currentTime+C/1e3))})):C>=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback+t._multiViewConfig.multiViewSyncConfig.thresoldForceSynchronization?1.2!==h.player.playbackRate&&(h.player.playbackRate=1.2,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"increase playback rate to 1.2"})):C>=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback?1.1!==h.player.playbackRate&&(h.player.playbackRate=1.1,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"increase playback rate to 1.1"})):C<t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback&&C>t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback?1!==h.player.playbackRate&&(h.player.playbackRate=1,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"playback rate 1.0"})):C<=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback&&C>=t._multiViewConfig.multiViewSyncConfig.thresholdPausePlayback?.8!==h.player.playbackRate&&(h.player.playbackRate=.8,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"decrease playback rate to 0.8"})):C<=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback&&(f=!0,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"pause"})),!t._isPlaying(h.player)||!f&&t._isPlaying(y)?!t._isPlaying(h.player)&&t._isPlaying(y)&&h.player.play():h.player.pause(),t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG,{videoElementId:h.videoElementId,gapWithLeader:C,currentTime:h.player.currentTime,seekRangeStart:E,offset:b,playbackRate:h.player.playbackRate,isPlaying:t._isPlaying(h.player),isMuted:h.player.muted,bandwidth:null===(l=h.player.qualities[h.player.quality])||void 0===l?void 0:l.bandwidth})}else t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG,{videoElementId:h.videoElementId,gapWithLeader:0,currentTime:y.currentTime,seekRangeStart:v,offset:g,playbackRate:h.player.playbackRate,isPlaying:t._isPlaying(h.player),isMuted:h.player.muted,bandwidth:null===(r=h.player.qualities[h.player.quality])||void 0===r?void 0:r.bandwidth})}}catch(e){p.e(e)}finally{p.f()}}},t._onPlayerBuffering=function(e){t._isInitialBuffering<2?t._isInitialBuffering++:e.buffering?t._pauseAllPlayers(!1):t._startAllPlayers(!1)},t._onPlayerPlay=function(){t._emitEvent(m.MultiViewEvent.LEADER_PLAYER_PLAY)},t._onPlayerPause=function(){t._emitEvent(m.MultiViewEvent.LEADER_PLAYER_PAUSE)},t._isHlsJsSupported=function(){return _.default.isSupported()},t._isHvcSupported=function(){var e,t;return h.Player.isBrowserSafariiOS()||h.Player.isBrowserSafariiPadOS()||null!==(t=null===(e=window.MediaSource||window.WebKitMediaSource)||void 0===e?void 0:e.isTypeSupported('video/mp4; codecs="hvc1.1.4.L150.B0,mp4a.40.2"'))&&void 0!==t&&t},t._isHvcStream=function(e){return p(y(t),void 0,void 0,o().mark((function t(){var n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this._makeGetRequest(e);case 3:return n=t.sent,t.abrupt("return",n.includes('CODECS="hvc1'));case 7:return t.prev=7,t.t0=t.catch(0),t.abrupt("return",!1);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})))},t._makeGetRequest=function(e){return p(y(t),void 0,void 0,o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.onload=function(){return t(r.responseText)},r.onerror=function(){return n(r.statusText)};try{r.send()}catch(e){voplayer.Log.error("[MultiViewController] _makeGetRequest: fail to XMLHttpRequest",e)}})));case 1:case"end":return t.stop()}}),t)})))},t._multiViewConfig=e,t._players=[],t._loaded=!1,t._playersSyncPaused=!0,t._isInitialBuffering=0,t._isDebug=!1,t._audioSetOnSpecificPlayer=!1,t}return t=c,(n=[{key:"_emitEvent",value:function(e,t){try{var n={type:e};t&&(n=Object.assign(n,t)),this.emit(e,n)}catch(e){voplayer.Log.error("[MultiViewController] _emitEvent: error ",e)}}}])&&u(t.prototype,n),r&&u(t,r),Object.defineProperty(t,"prototype",{writable:!1}),c}(v.EventEmitter);t.default=g,g.MIN_NUMBER_OF_PLAYER_INSTANCES=2,g.MAX_NUMBER_OF_PLAYER_INSTANCES=4},8754:function(e,t){function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);a=!0);}catch(e){l=!0,i=e}finally{try{a||null==n.return||n.return()}finally{if(l)throw i}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.MultiViewEvent=t.MultiViewControllerConfig=t.MultiViewSyncConfig=void 0;var l=o((function e(t){var n=t.playerSyncFrequency,r=void 0===n?500:n,i=t.thresholdJumpPlayback,o=void 0===i?3e3:i,l=t.thresoldForceSynchronization,c=void 0===l?150:l,u=t.thresholdSpeedUpPlayback,s=void 0===u?50:u,d=t.thresholdSpeedDownPlayback,y=void 0===d?-50:d,f=t.thresholdPausePlayback,p=void 0===f?-3e3:f;a(this,e),this.playerSyncFrequency=r,this.thresholdJumpPlayback=o,this.thresoldForceSynchronization=c,this.thresholdSpeedUpPlayback=s,this.thresholdSpeedDownPlayback=y,this.thresholdPausePlayback=p}));t.MultiViewSyncConfig=l;var c=o((function e(t,r){a(this,e),this.license=t,this.multiViewSyncConfig=new l({});for(var i=0,o=Object.entries(r);i<o.length;i++){var c=n(o[i],2),u=c[0],s=c[1];s&&(this.multiViewSyncConfig[u]=s)}}));t.MultiViewControllerConfig=c;var u=o((function e(){a(this,e)}));t.MultiViewEvent=u,u.ERROR="error",u.LEADER_PLAYER_PLAY="leader_player_play",u.LEADER_PLAYER_PAUSE="leader_player_pause",u.ON_SWAP_VIDEO_ELEMENT="on_swap_video_element",u.ON_DEBUG="on_debug",u.ON_DEBUG_ACTION="on_debug_action"},9760:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.MultiViewUIControllerImpl=void 0;var r=n(9427);t.MultiViewUIControllerImpl=r.default;var i="undefined"!=typeof window&&window||n.g,o=i.voplayer;o||(o=i.voplayer={}),o.multiviewUI={},o.multiviewUI.MultiViewUIControllerImpl=r.default,t.default=o}},function(e){var t;return t=9760,e(e.s=t)}])}));