/*! For license information please see voplayer-multiview.min.js.LICENSE.txt */
"use strict";!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var r=t();for(var n in r)("object"==typeof exports?exports:e)[n]=r[n]}}(self,(function(){return(self.webpackChunkvoplayer_html5=self.webpackChunkvoplayer_html5||[]).push([[501],{6865:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=l(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(u)throw a}}}}function a(){a=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),o=new C(n||[]);return a._invoke=function(e,t,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return R()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var l=P(o,r);if(l){if(l===f)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=y(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===f)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function y(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var f={};function p(){}function d(){}function h(){}var v={};c(v,o,(function(){return this}));var _=Object.getPrototypeOf,m=_&&_(_(O([])));m&&m!==t&&r.call(m,o)&&(v=m);var g=h.prototype=p.prototype=Object.create(v);function w(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function i(a,o,l,u){var c=y(e[a],e,o);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==n(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){i("next",e,l,u)}),(function(e){i("throw",e,l,u)})):t.resolve(f).then((function(e){s.value=e,l(s)}),(function(e){return i("throw",e,l,u)}))}u(c.arg)}var a;this._invoke=function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}}function P(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,P(e,t),"throw"===t.method))return f;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return f}var n=y(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,f;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,f):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,f)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function O(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return d.prototype=h,c(g,"constructor",h),c(h,"constructor",d),d.displayName=c(h,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,u,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},w(b.prototype),c(b.prototype,l,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new b(s(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},w(g),c(g,u,"Generator"),c(g,o,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=O,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return o.type="throw",o.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,f):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),f},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),f}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;S(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},e}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,i,a=[],o=!0,l=!1;try{for(r=r.call(e);!(o=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==r.return||r.return()}finally{if(l)throw i}}return a}(e,t)||l(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){if(e){if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},s(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,i=p(e);if(t){var a=p(this).constructor;r=Reflect.construct(i,arguments,a)}else r=i.apply(this,arguments);return function(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return f(e)}(this,r)}}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}var d=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function l(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,l)}u((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),r(7875);var h=r(5417),v=r(7187),_=r(3742),m=r(8754),g=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s(e,t)}(u,e);var t,r,n,l=y(u);function u(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),(t=l.call(this,null)).setSyncConfig=function(e){if(t._multiViewConfig)for(var r=0,n=Object.entries(e);r<n.length;r++){var i=o(n[r],2),a=i[0],l=i[1];l&&(t._multiViewConfig.multiViewSyncConfig[a]=l)}},t.registerPlayer=function(e,r,n){return d(f(t),void 0,void 0,a().mark((function t(){var i,o,l,c,s,y,f,p,d,v=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this._loaded){t.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Cannot register new player after load() was called"}),t.abrupt("return");case 3:if(!(this._players.length>=u.MAX_NUMBER_OF_PLAYER_INSTANCES)){t.next=6;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Maximum number of player instances (".concat(u.MAX_NUMBER_OF_PLAYER_INSTANCES,") was reached")}),t.abrupt("return");case 6:return(o=null!==(i=n.playerConfig)&&void 0!==i?i:{}).iosPlaysInline=!0,o.hlsConfiguration||(o.hlsConfiguration={}),o.hlsConfiguration.capLevelToPlayerSize=!0,o.hlsConfiguration.capLevelOnFPSDrop=!0,o.hlsConfiguration.autoStartLoad=!1,l=this._isHlsJsSupported(),c=this._isHvcSupported(),s="string"==typeof r?r:r.url,t.next=17,this._isHvcStream(s);case 17:return y=t.sent,o.forceAppleNative=!l||!c&&y,f=(0,h.createPlayer)(e),p={player:f,mediaObj:r,playerConfig:o,videoElementId:e,vrConfig:n.vrConfig},f.license=this._multiViewConfig.license,f.configure(o),n.isLeader||(f.muted=!0),d=this._players.length,this._players.push(p),setTimeout((function(){(n.isLeader||0===d)&&v.swapLeaderPlayer(f,!0)}),20),t.abrupt("return",f);case 28:case"end":return t.stop()}}),t,this)})))},t.load=function(){return d(f(t),void 0,void 0,a().mark((function e(){var t=this;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was already called"}),e.abrupt("return");case 3:if(!(this._players.length<u.MIN_NUMBER_OF_PLAYER_INSTANCES)){e.next=6;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"Minimum number of player instances (".concat(u.MIN_NUMBER_OF_PLAYER_INSTANCES,") was not reached")}),e.abrupt("return");case 6:return this._loaded=!0,e.next=9,Promise.all(this._players.map((function(e){return d(t,void 0,void 0,a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.vrConfig&&(e.player.vrController.prepareVR(),e.player.vrController.isMonoscopic=e.vrConfig.isMonoscopic,e.player.vrController.is180=e.vrConfig.is180,e.player.vrController.isHorizontalSplit=e.vrConfig.isHorizontalSplit),t.next=3,e.player.load(e.mediaObj);case 3:e!==this._leaderPlayer?e.player.quality=e.player.qualities.length-1:e.player.quality=-1;case 4:case"end":return t.stop()}}),t,this)})))})));case 9:return this._unsubscribeToLeaderEvents(),this._subscribeToLeaderEvents(),e.next=13,this.startAllPlayers();case 13:case"end":return e.stop()}}),e,this)})))},t.startAllPlayers=function(){return d(f(t),void 0,void 0,a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was not yet called"}),e.abrupt("return");case 3:return e.abrupt("return",this._startAllPlayers());case 4:case"end":return e.stop()}}),e,this)})))},t.pauseAllPlayers=function(){return d(f(t),void 0,void 0,a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._loaded){e.next=3;break}return this._emitEvent(m.MultiViewEvent.ERROR,{message:"load() was not yet called"}),e.abrupt("return");case 3:return e.abrupt("return",this._pauseAllPlayers());case 4:case"end":return e.stop()}}),e,this)})))},t.swapLeaderPlayer=function(e){var r,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=t._leaderPlayer,o=i(t._players);try{for(o.s();!(r=o.n()).done;){var l=r.value;if(l.player===e){t._loaded?(t._unsubscribeToLeaderEvents(),t._leaderPlayer=l,t._subscribeToLeaderEvents()):t._leaderPlayer=l;break}}}catch(e){o.e(e)}finally{o.f()}if(!a||a===t._leaderPlayer)return t._loaded&&(t._leaderPlayer.player.quality=-1),void(t._audioSetOnSpecificPlayer||(t._leaderPlayer.player.muted=!1));var u=a.player.volume,c=a.player.muted;t._audioSetOnSpecificPlayer||(a.player.muted=!0),t._loaded&&(a.player.quality=a.player.qualities.length-1,t._leaderPlayer.player.quality=-1),c||t._audioSetOnSpecificPlayer||(t._leaderPlayer.player.muted=!1,t._leaderPlayer.player.volume=u),a.player.playing&&!t._isPlaying(t._leaderPlayer.player)&&t._leaderPlayer.player.play(),1!==t._leaderPlayer.player.playbackRate&&(t._leaderPlayer.player.playbackRate=1),n&&t._emitEvent(m.MultiViewEvent.ON_SWAP_VIDEO_ELEMENT,{oldLeaderVideoElementId:a.videoElementId,newLeaderVideoElementId:t._leaderPlayer.videoElementId,shouldActivateVRControls:void 0!==t._leaderPlayer.vrConfig})},t.setAudio=function(e){if(!e)return t._audioSetOnSpecificPlayer=!1,void t._muteAllPlayers(t._leaderPlayer.player);t._muteAllPlayers(e),t._audioSetOnSpecificPlayer=!0},t.setDebugMode=function(e){t._isDebug=e},t.reset=function(){return d(f(t),void 0,void 0,a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._unsubscribeToLeaderEvents(),this._stopPlayerSync(),e.next=4,Promise.all(this._players.map((function(e){return e.player.destroy()})));case 4:this._players=[],this._leaderPlayer=null,this._isInitialBuffering=0,this._isDebug=!1,this._audioSetOnSpecificPlayer=!1,this._loaded=!1,this._playersSyncPaused=!0;case 11:case"end":return e.stop()}}),e,this)})))},t.getPlayers=function(){return t._players.map((function(e){return e.player}))},t.getLeaderPlayer=function(){var e;return null===(e=t._leaderPlayer)||void 0===e?void 0:e.player},t._unsubscribeToLeaderEvents=function(){t._leaderPlayer&&(t._leaderPlayer.player.off(h.Player.BUFFERING,t._onPlayerBuffering),t._leaderPlayer.player.off(h.Player.PLAY,t._onPlayerPlay),t._leaderPlayer.player.off(h.Player.PAUSE,t._onPlayerPause))},t._subscribeToLeaderEvents=function(){t._leaderPlayer&&(t._leaderPlayer.player.on(h.Player.BUFFERING,t._onPlayerBuffering),t._leaderPlayer.player.on(h.Player.PLAY,t._onPlayerPlay),t._leaderPlayer.player.on(h.Player.PAUSE,t._onPlayerPause))},t._startAllPlayers=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return d(f(t),void 0,void 0,a().mark((function t(){var r=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._startPlayersSync(),t.next=3,Promise.all(this._players.filter((function(t){return e||t!==r._leaderPlayer})).map((function(e){if(!r._isPlaying(e.player))return e.player.play()})));case 3:case"end":return t.stop()}}),t,this)})))},t._pauseAllPlayers=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return d(f(t),void 0,void 0,a().mark((function t(){var r=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._pausePlayersSync(),t.next=3,Promise.all(this._players.filter((function(t){return e||t!==r._leaderPlayer})).map((function(e){if(r._isPlaying(e.player))return e.player.pause()})));case 3:case"end":return t.stop()}}),t,this)})))},t._muteAllPlayers=function(e){var r,n=i(t._players);try{for(n.s();!(r=n.n()).done;){var a=r.value;a.player===e?a.player.muted&&(a.player.muted=!1):a.player.muted||(a.player.muted=!0)}}catch(e){n.e(e)}finally{n.f()}},t._isPlaying=function(e){return e.videoElement.currentTime>0&&!e.videoElement.paused&&!e.videoElement.ended&&e.videoElement.readyState>e.videoElement.HAVE_CURRENT_DATA&&e.playing},t._startPlayersSync=function(){t._playersSyncTimer||(t._playersSyncTimer=setInterval(t._synchronisePlayers,t._multiViewConfig.multiViewSyncConfig.playerSyncFrequency)),t._playersSyncPaused=!1},t._pausePlayersSync=function(){t._playersSyncPaused=!0},t._stopPlayerSync=function(){t._pausePlayersSync(),t._playersSyncTimer||clearInterval(t._playersSyncTimer)},t._synchronisePlayers=function(){var e,r,n,a,o,l;if(t._playersSyncPaused){var u,c=i(t._players);try{for(c.s();!(u=c.n()).done;){var s=u.value;t._isPlaying(s.player)&&s.player.pause()}}catch(e){c.e(e)}finally{c.f()}}else{var y,f=t._leaderPlayer.player,p=!1,d=i(t._players);try{for(d.s();!(y=d.n()).done;){var h=y.value,v=null!==(r=null===(e=f.corePlayer.hlsSeekRange_)||void 0===e?void 0:e.start)&&void 0!==r?r:f.seekRange().start,_=f.videoElement.currentTime,g=_+v-f.currentTime;if(h!==t._leaderPlayer){var w=null!==(o=null===(a=h.player.corePlayer.hlsSeekRange_)||void 0===a?void 0:a.start)&&void 0!==o?o:h.player.seekRange().start,b=h.player.videoElement.currentTime,P=b+w-h.player.currentTime,E=1e3*(v+_-g-(w+b-P));E>=t._multiViewConfig.multiViewSyncConfig.thresholdJumpPlayback||E<=-t._multiViewConfig.multiViewSyncConfig.thresholdJumpPlayback?(1!==h.player.playbackRate&&(h.player.playbackRate=1),h.player.currentTime=h.player.currentTime+E/1e3,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"jumped to ".concat(Math.round(h.player.currentTime+E/1e3))})):E>=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback+t._multiViewConfig.multiViewSyncConfig.thresoldForceSynchronization?1.2!==h.player.playbackRate&&(h.player.playbackRate=1.2,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"increase playback rate to 1.2"})):E>=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback?1.1!==h.player.playbackRate&&(h.player.playbackRate=1.1,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"increase playback rate to 1.1"})):E<t._multiViewConfig.multiViewSyncConfig.thresholdSpeedUpPlayback&&E>t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback?1!==h.player.playbackRate&&(h.player.playbackRate=1,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"playback rate 1.0"})):E<=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback&&E>=t._multiViewConfig.multiViewSyncConfig.thresholdPausePlayback?.8!==h.player.playbackRate&&(h.player.playbackRate=.8,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"decrease playback rate to 0.8"})):E<=t._multiViewConfig.multiViewSyncConfig.thresholdSpeedDownPlayback&&(p=!0,t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG_ACTION,{videoElementId:h.videoElementId,action:"pause"})),!t._isPlaying(h.player)||!p&&t._isPlaying(f)?!t._isPlaying(h.player)&&t._isPlaying(f)&&h.player.play():h.player.pause(),t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG,{videoElementId:h.videoElementId,gapWithLeader:E,currentTime:h.player.currentTime,seekRangeStart:w,offset:P,playbackRate:h.player.playbackRate,isPlaying:t._isPlaying(h.player),isMuted:h.player.muted,bandwidth:null===(l=h.player.qualities[h.player.quality])||void 0===l?void 0:l.bandwidth})}else t._isDebug&&t._emitEvent(m.MultiViewEvent.ON_DEBUG,{videoElementId:h.videoElementId,gapWithLeader:0,currentTime:f.currentTime,seekRangeStart:v,offset:g,playbackRate:h.player.playbackRate,isPlaying:t._isPlaying(h.player),isMuted:h.player.muted,bandwidth:null===(n=h.player.qualities[h.player.quality])||void 0===n?void 0:n.bandwidth})}}catch(e){d.e(e)}finally{d.f()}}},t._onPlayerBuffering=function(e){t._isInitialBuffering<2?t._isInitialBuffering++:e.buffering?t._pauseAllPlayers(!1):t._startAllPlayers(!1)},t._onPlayerPlay=function(){t._emitEvent(m.MultiViewEvent.LEADER_PLAYER_PLAY)},t._onPlayerPause=function(){t._emitEvent(m.MultiViewEvent.LEADER_PLAYER_PAUSE)},t._isHlsJsSupported=function(){return _.default.isSupported()},t._isHvcSupported=function(){var e,t;return h.Player.isBrowserSafariiOS()||h.Player.isBrowserSafariiPadOS()||null!==(t=null===(e=window.MediaSource||window.WebKitMediaSource)||void 0===e?void 0:e.isTypeSupported('video/mp4; codecs="hvc1.1.4.L150.B0,mp4a.40.2"'))&&void 0!==t&&t},t._isHvcStream=function(e){return d(f(t),void 0,void 0,a().mark((function t(){var r;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this._makeGetRequest(e);case 3:return r=t.sent,t.abrupt("return",r.includes('CODECS="hvc1'));case 7:return t.prev=7,t.t0=t.catch(0),t.abrupt("return",!1);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})))},t._makeGetRequest=function(e){return d(f(t),void 0,void 0,a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,r){var n=new XMLHttpRequest;n.open("GET",e,!0),n.onload=function(){return t(n.responseText)},n.onerror=function(){return r(n.statusText)};try{n.send()}catch(e){voplayer.Log.error("[MultiViewController] _makeGetRequest: fail to XMLHttpRequest",e)}})));case 1:case"end":return t.stop()}}),t)})))},t._multiViewConfig=e,t._players=[],t._loaded=!1,t._playersSyncPaused=!0,t._isInitialBuffering=0,t._isDebug=!1,t._audioSetOnSpecificPlayer=!1,t}return t=u,(r=[{key:"_emitEvent",value:function(e,t){try{var r={type:e};t&&(r=Object.assign(r,t)),this.emit(e,r)}catch(e){voplayer.Log.error("[MultiViewController] _emitEvent: error ",e)}}}])&&c(t.prototype,r),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(v.EventEmitter);t.default=g,g.MIN_NUMBER_OF_PLAYER_INSTANCES=2,g.MAX_NUMBER_OF_PLAYER_INSTANCES=4},8754:function(e,t){function r(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,i,a=[],o=!0,l=!1;try{for(r=r.call(e);!(o=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==r.return||r.return()}finally{if(l)throw i}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0}),t.MultiViewEvent=t.MultiViewControllerConfig=t.MultiViewSyncConfig=void 0;var l=a((function e(t){var r=t.playerSyncFrequency,n=void 0===r?500:r,i=t.thresholdJumpPlayback,a=void 0===i?3e3:i,l=t.thresoldForceSynchronization,u=void 0===l?150:l,c=t.thresholdSpeedUpPlayback,s=void 0===c?50:c,y=t.thresholdSpeedDownPlayback,f=void 0===y?-50:y,p=t.thresholdPausePlayback,d=void 0===p?-3e3:p;o(this,e),this.playerSyncFrequency=n,this.thresholdJumpPlayback=a,this.thresoldForceSynchronization=u,this.thresholdSpeedUpPlayback=s,this.thresholdSpeedDownPlayback=f,this.thresholdPausePlayback=d}));t.MultiViewSyncConfig=l;var u=a((function e(t,n){o(this,e),this.license=t,this.multiViewSyncConfig=new l({});for(var i=0,a=Object.entries(n);i<a.length;i++){var u=r(a[i],2),c=u[0],s=u[1];s&&(this.multiViewSyncConfig[c]=s)}}));t.MultiViewControllerConfig=u;var c=a((function e(){o(this,e)}));t.MultiViewEvent=c,c.ERROR="error",c.LEADER_PLAYER_PLAY="leader_player_play",c.LEADER_PLAYER_PAUSE="leader_player_pause",c.ON_SWAP_VIDEO_ELEMENT="on_swap_video_element",c.ON_DEBUG="on_debug",c.ON_DEBUG_ACTION="on_debug_action"},1266:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.MultiViewEvent=t.MultiViewSyncConfig=t.MultiViewControllerConfig=t.MultiViewController=void 0;var n=r(6865);t.MultiViewController=n.default;var i=r(8754);Object.defineProperty(t,"MultiViewControllerConfig",{enumerable:!0,get:function(){return i.MultiViewControllerConfig}}),Object.defineProperty(t,"MultiViewEvent",{enumerable:!0,get:function(){return i.MultiViewEvent}}),Object.defineProperty(t,"MultiViewSyncConfig",{enumerable:!0,get:function(){return i.MultiViewSyncConfig}});var a="undefined"!=typeof window&&window||r.g,o=a.voplayer;o||(o=a.voplayer={}),o.multiview={},o.multiview.MultiViewController=n.default,o.multiview.MultiViewControllerConfig=i.MultiViewControllerConfig,o.multiview.MultiViewSyncConfig=i.MultiViewSyncConfig,o.multiview.MultiViewEvent=i.MultiViewEvent,t.default=o}},function(e){var t;return t=1266,e(e.s=t)}])}));