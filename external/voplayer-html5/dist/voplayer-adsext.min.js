/*! For license information please see voplayer-adsext.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var r=t();for(var n in r)("object"==typeof exports?exports:e)[n]=r[n]}}(self,(function(){return(self.webpackChunkvoplayer_html5=self.webpackChunkvoplayer_html5||[]).push([[809],{248:function(e,t){!function(e){"use strict";function t(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function r(e){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?t(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}function o(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}function d(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=l(e);if(t){var i=l(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return d(this,r)}}function p(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function y(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{id:e.id||null,adId:e.adId||null,sequence:e.sequence||null,apiFramework:e.apiFramework||null,universalAdIds:[],creativeExtensions:[]}}var m=["ADCATEGORIES","ADCOUNT","ADPLAYHEAD","ADSERVINGID","ADTYPE","APIFRAMEWORKS","APPBUNDLE","ASSETURI","BLOCKEDADCATEGORIES","BREAKMAXADLENGTH","BREAKMAXADS","BREAKMAXDURATION","BREAKMINADLENGTH","BREAKMINDURATION","BREAKPOSITION","CLICKPOS","CLICKTYPE","CLIENTUA","CONTENTID","CONTENTPLAYHEAD","CONTENTURI","DEVICEIP","DEVICEUA","DOMAIN","EXTENSIONS","GDPRCONSENT","IFA","IFATYPE","INVENTORYSTATE","LATLONG","LIMITADTRACKING","MEDIAMIME","MEDIAPLAYHEAD","OMIDPARTNER","PAGEURL","PLACEMENTTYPE","PLAYERCAPABILITIES","PLAYERSIZE","PLAYERSTATE","PODSEQUENCE","REGULATIONS","SERVERSIDE","SERVERUA","TRANSACTIONID","UNIVERSALADID","VASTVERSIONS","VERIFICATIONVENDORS"];function v(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=[],i=A(e);for(var a in!t.ERRORCODE||r.isCustomCode||/^[0-9]{3}$/.test(t.ERRORCODE)||(t.ERRORCODE=900),t.CACHEBUSTING=I(Math.round(1e8*Math.random())),t.TIMESTAMP=(new Date).toISOString(),t.RANDOM=t.random=t.CACHEBUSTING,t)t[a]=w(t[a]);for(var o in i){var s=i[o];"string"==typeof s&&n.push(_(s,t))}return n}function _(e,t){var r=(e=T(e,t)).match(/[^[\]]+(?=])/g);if(!r)return e;var n=r.filter((function(e){return m.indexOf(e)>-1}));return 0===n.length?e:T(e,n=n.reduce((function(e,t){return e[t]=-1,e}),{}))}function T(e,t){var r=e;for(var n in t){var i=t[n];r=r.replace(new RegExp("(?:\\[|%%)(".concat(n,")(?:\\]|%%)"),"g"),i)}return r}function A(e){return Array.isArray(e)?e.map((function(e){return e&&e.hasOwnProperty("url")?e.url:e})):e}function E(e){return/^(https?:\/\/|\/\/)/.test(e)}function b(e,t){for(var r=0;r<t.length;r++)if(k(t[r],e))return!0;return!1}function k(e,t){if(e&&t){var r=Object.getOwnPropertyNames(e),n=Object.getOwnPropertyNames(t);return r.length===n.length&&e.id===t.id&&e.url===t.url}return!1}function w(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%".concat(e.charCodeAt(0).toString(16))}))}function I(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return e.toString().padStart(t,"0")}var R={track:function(e,t,r){v(e,t,r).forEach((function(e){"undefined"!=typeof window&&null!==window&&((new Image).src=e)}))},resolveURLTemplates:v,extractURLsFromTemplates:A,filterValidUrlTemplates:function(e){return Array.isArray(e)?e.filter((function(e){return E(e.hasOwnProperty("url")?e.url:e)})):E(e)},containsTemplateObject:b,isTemplateObjectEqual:k,encodeURIComponentRFC3986:w,replaceUrlMacros:_,isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},flatten:function e(t){return t.reduce((function(t,r){return t.concat(Array.isArray(r)?e(r):r)}),[])},joinArrayOfUniqueTemplateObjs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=Array.isArray(e)?e:[],n=Array.isArray(t)?t:[];return r.concat(n).reduce((function(e,t){return b(t,e)||e.push(t),e}),[])},isValidTimeValue:function(e){return Number.isFinite(e)&&e>=-2},addLeadingZeros:I};function S(e){return-1!==["true","TRUE","True","1"].indexOf(e)}function P(e){if(null==e)return-1;if(R.isNumeric(e))return parseInt(e);var t=e.split(":");if(3!==t.length)return-1;var r=t[2].split("."),n=parseInt(r[0]);2===r.length&&(n+=parseFloat("0.".concat(r[1])));var i=parseInt(60*t[1]),a=parseInt(60*t[0]*60);return isNaN(a)||isNaN(i)||isNaN(n)||i>3600||n>60?-1:a+i+n}var N={childByName:function(e,t){var r=e.childNodes;for(var n in r){var i=r[n];if(i.nodeName===t)return i}},childrenByName:function(e,t){var r=[],n=e.childNodes;for(var i in n){var a=n[i];a.nodeName===t&&r.push(a)}return r},resolveVastAdTagURI:function(e,t){if(!t)return e;if(0===e.indexOf("//")){var r=location.protocol;return"".concat(r).concat(e)}if(-1===e.indexOf("://")){var n=t.slice(0,t.lastIndexOf("/"));return"".concat(n,"/").concat(e)}return e},parseBoolean:S,parseNodeText:function(e){return e&&(e.textContent||e.text||"").trim()},copyNodeAttribute:function(e,t,r){var n=t.getAttribute(e);n&&r.setAttribute(e,n)},parseAttributes:function(e){for(var t=e.attributes,r={},n=0;n<t.length;n++)r[t[n].nodeName]=t[n].nodeValue;return r},parseDuration:P,splitVAST:function(e){var t=[],r=null;return e.forEach((function(n,i){if(n.sequence&&(n.sequence=parseInt(n.sequence,10)),n.sequence>1){var a=e[i-1];if(a&&a.sequence===n.sequence-1)return void(r&&r.push(n));delete n.sequence}r=[n],t.push(r)})),t},assignAttributes:function(e,t){if(e)for(var r in e){var n=e[r];if(n.nodeName&&n.nodeValue&&t.hasOwnProperty(n.nodeName)){var i=n.nodeValue;"boolean"==typeof t[n.nodeName]&&(i=S(i)),t[n.nodeName]=i}}},mergeWrapperAdData:function(e,t){var r;e.errorURLTemplates=t.errorURLTemplates.concat(e.errorURLTemplates),e.impressionURLTemplates=t.impressionURLTemplates.concat(e.impressionURLTemplates),e.extensions=t.extensions.concat(e.extensions),t.viewableImpression.length>0&&(e.viewableImpression=[].concat(p(e.viewableImpression),p(t.viewableImpression))),e.followAdditionalWrappers=t.followAdditionalWrappers,e.allowMultipleAds=t.allowMultipleAds,e.fallbackOnNoAd=t.fallbackOnNoAd;var n=(t.creatives||[]).filter((function(e){return e&&"companion"===e.type})),i=n.reduce((function(e,t){return(t.variations||[]).forEach((function(t){(t.companionClickTrackingURLTemplates||[]).forEach((function(t){R.containsTemplateObject(t,e)||e.push(t)}))})),e}),[]);e.creatives=n.concat(e.creatives);var a=t.videoClickTrackingURLTemplates&&t.videoClickTrackingURLTemplates.length,o=t.videoCustomClickURLTemplates&&t.videoCustomClickURLTemplates.length;if(e.creatives.forEach((function(e){if(t.trackingEvents&&t.trackingEvents[e.type])for(var r in t.trackingEvents[e.type]){var n=t.trackingEvents[e.type][r];Array.isArray(e.trackingEvents[r])||(e.trackingEvents[r]=[]),e.trackingEvents[r]=e.trackingEvents[r].concat(n)}"linear"===e.type&&(a&&(e.videoClickTrackingURLTemplates=e.videoClickTrackingURLTemplates.concat(t.videoClickTrackingURLTemplates)),o&&(e.videoCustomClickURLTemplates=e.videoCustomClickURLTemplates.concat(t.videoCustomClickURLTemplates)),!t.videoClickThroughURLTemplate||null!==e.videoClickThroughURLTemplate&&void 0!==e.videoClickThroughURLTemplate||(e.videoClickThroughURLTemplate=t.videoClickThroughURLTemplate)),"companion"===e.type&&i.length&&(e.variations||[]).forEach((function(e){e.companionClickTrackingURLTemplates=R.joinArrayOfUniqueTemplateObjs(e.companionClickTrackingURLTemplates,i)}))})),t.adVerifications&&(e.adVerifications=e.adVerifications.concat(t.adVerifications)),t.blockedAdCategories&&(e.blockedAdCategories=e.blockedAdCategories.concat(t.blockedAdCategories)),null!==(r=t.creatives)&&void 0!==r&&r.length){var s=t.creatives.filter((function(e){var t;return(null===(t=e.icons)||void 0===t?void 0:t.length)&&!e.mediaFiles.length}));s.length&&(e.creatives=e.creatives.concat(s))}}};function O(e,t){var r=function(){var e=g(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return{id:e.id,adId:e.adId,sequence:e.sequence,apiFramework:e.apiFramework,type:"companion",required:null,variations:[]}}(t);return r.required=e.getAttribute("required")||null,r.variations=N.childrenByName(e,"Companion").map((function(e){var t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{id:e.id||null,adType:"companionAd",width:e.width||0,height:e.height||0,assetWidth:e.assetWidth||null,assetHeight:e.assetHeight||null,expandedWidth:e.expandedWidth||null,expandedHeight:e.expandedHeight||null,apiFramework:e.apiFramework||null,adSlotId:e.adSlotId||null,pxratio:e.pxratio||"1",renderingMode:e.renderingMode||"default",staticResources:[],htmlResources:[],iframeResources:[],adParameters:null,altText:null,companionClickThroughURLTemplate:null,companionClickTrackingURLTemplates:[],trackingEvents:{}}}(N.parseAttributes(e));t.htmlResources=N.childrenByName(e,"HTMLResource").reduce((function(e,t){var r=N.parseNodeText(t);return r?e.concat(r):e}),[]),t.iframeResources=N.childrenByName(e,"IFrameResource").reduce((function(e,t){var r=N.parseNodeText(t);return r?e.concat(r):e}),[]),t.staticResources=N.childrenByName(e,"StaticResource").reduce((function(e,t){var r=N.parseNodeText(t);return r?e.concat({url:r,creativeType:t.getAttribute("creativeType")||null}):e}),[]),t.altText=N.parseNodeText(N.childByName(e,"AltText"))||null;var r=N.childByName(e,"TrackingEvents");r&&N.childrenByName(r,"Tracking").forEach((function(e){var r=e.getAttribute("event"),n=N.parseNodeText(e);r&&n&&(Array.isArray(t.trackingEvents[r])||(t.trackingEvents[r]=[]),t.trackingEvents[r].push(n))})),t.companionClickTrackingURLTemplates=N.childrenByName(e,"CompanionClickTracking").map((function(e){return{id:e.getAttribute("id")||null,url:N.parseNodeText(e)}})),t.companionClickThroughURLTemplate=N.parseNodeText(N.childByName(e,"CompanionClickThrough"))||null;var n=N.childByName(e,"AdParameters");return n&&(t.adParameters={value:N.parseNodeText(n),xmlEncoded:n.getAttribute("xmlEncoded")||null}),t})),r}function D(e,t){var r,n=function(){var e=g(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return{id:e.id,adId:e.adId,sequence:e.sequence,apiFramework:e.apiFramework,type:"linear",duration:0,skipDelay:null,mediaFiles:[],mezzanine:null,interactiveCreativeFile:null,closedCaptionFiles:[],videoClickThroughURLTemplate:null,videoClickTrackingURLTemplates:[],videoCustomClickURLTemplates:[],adParameters:null,icons:[],trackingEvents:{}}}(t);n.duration=N.parseDuration(N.parseNodeText(N.childByName(e,"Duration")));var i=e.getAttribute("skipoffset");if(null==i)n.skipDelay=null;else if("%"===i.charAt(i.length-1)&&-1!==n.duration){var a=parseInt(i,10);n.skipDelay=n.duration*(a/100)}else n.skipDelay=N.parseDuration(i);var o=N.childByName(e,"VideoClicks");if(o){var s=N.childByName(o,"ClickThrough");n.videoClickThroughURLTemplate=s?{id:s.getAttribute("id")||null,url:N.parseNodeText(s)}:null,N.childrenByName(o,"ClickTracking").forEach((function(e){n.videoClickTrackingURLTemplates.push({id:e.getAttribute("id")||null,url:N.parseNodeText(e)})})),N.childrenByName(o,"CustomClick").forEach((function(e){n.videoCustomClickURLTemplates.push({id:e.getAttribute("id")||null,url:N.parseNodeText(e)})}))}var u=N.childByName(e,"AdParameters");u&&(n.adParameters={value:N.parseNodeText(u),xmlEncoded:u.getAttribute("xmlEncoded")||null}),N.childrenByName(e,"TrackingEvents").forEach((function(e){N.childrenByName(e,"Tracking").forEach((function(e){var t=e.getAttribute("event"),i=N.parseNodeText(e);if(t&&i){if("progress"===t){if(!(r=e.getAttribute("offset")))return;t="%"===r.charAt(r.length-1)?"progress-".concat(r):"progress-".concat(Math.round(N.parseDuration(r)))}Array.isArray(n.trackingEvents[t])||(n.trackingEvents[t]=[]),n.trackingEvents[t].push(i)}}))})),N.childrenByName(e,"MediaFiles").forEach((function(e){N.childrenByName(e,"MediaFile").forEach((function(e){n.mediaFiles.push(function(e){var t={id:null,fileURL:null,fileSize:0,deliveryType:"progressive",mimeType:null,mediaType:null,codec:null,bitrate:0,minBitrate:0,maxBitrate:0,width:0,height:0,apiFramework:null,scalable:null,maintainAspectRatio:null};t.id=e.getAttribute("id"),t.fileURL=N.parseNodeText(e),t.deliveryType=e.getAttribute("delivery"),t.codec=e.getAttribute("codec"),t.mimeType=e.getAttribute("type"),t.mediaType=e.getAttribute("mediaType")||"2D",t.apiFramework=e.getAttribute("apiFramework"),t.fileSize=parseInt(e.getAttribute("fileSize")||0),t.bitrate=parseInt(e.getAttribute("bitrate")||0),t.minBitrate=parseInt(e.getAttribute("minBitrate")||0),t.maxBitrate=parseInt(e.getAttribute("maxBitrate")||0),t.width=parseInt(e.getAttribute("width")||0),t.height=parseInt(e.getAttribute("height")||0);var r=e.getAttribute("scalable");r&&"string"==typeof r&&(t.scalable=N.parseBoolean(r));var n=e.getAttribute("maintainAspectRatio");return n&&"string"==typeof n&&(t.maintainAspectRatio=N.parseBoolean(n)),t}(e))}));var t=N.childByName(e,"InteractiveCreativeFile");t&&(n.interactiveCreativeFile=function(e){var t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{type:e.type||null,apiFramework:e.apiFramework||null,variableDuration:N.parseBoolean(e.variableDuration),fileURL:null}}(N.parseAttributes(e));return t.fileURL=N.parseNodeText(e),t}(t));var r=N.childByName(e,"ClosedCaptionFiles");r&&N.childrenByName(r,"ClosedCaptionFile").forEach((function(e){var t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{type:e.type||null,language:e.language||null,fileURL:null}}(N.parseAttributes(e));t.fileURL=N.parseNodeText(e),n.closedCaptionFiles.push(t)}));var i,a,o,s=N.childByName(e,"Mezzanine"),u=(i=s,a={},o=!1,["delivery","type","width","height"].forEach((function(e){i&&i.getAttribute(e)?a[e]=i.getAttribute(e):o=!0})),o?null:a);if(u){var l={id:null,fileURL:null,delivery:null,codec:null,type:null,width:0,height:0,fileSize:0,mediaType:"2D"};l.id=s.getAttribute("id"),l.fileURL=N.parseNodeText(s),l.delivery=u.delivery,l.codec=s.getAttribute("codec"),l.type=u.type,l.width=parseInt(u.width,10),l.height=parseInt(u.height,10),l.fileSize=parseInt(s.getAttribute("fileSize"),10),l.mediaType=s.getAttribute("mediaType")||"2D",n.mezzanine=l}}));var l=N.childByName(e,"Icons");return l&&N.childrenByName(l,"Icon").forEach((function(e){n.icons.push(function(e){var t={program:null,height:0,width:0,xPosition:0,yPosition:0,apiFramework:null,offset:null,duration:0,type:null,staticResource:null,htmlResource:null,iframeResource:null,pxratio:"1",iconClickThroughURLTemplate:null,iconClickTrackingURLTemplates:[],iconViewTrackingURLTemplate:null,iconClickFallbackImages:[]};t.program=e.getAttribute("program"),t.height=parseInt(e.getAttribute("height")||0),t.width=parseInt(e.getAttribute("width")||0),t.xPosition=function(e){return-1!==["left","right"].indexOf(e)?e:parseInt(e||0)}(e.getAttribute("xPosition")),t.yPosition=function(e){return-1!==["top","bottom"].indexOf(e)?e:parseInt(e||0)}(e.getAttribute("yPosition")),t.apiFramework=e.getAttribute("apiFramework"),t.pxratio=e.getAttribute("pxratio")||"1",t.offset=N.parseDuration(e.getAttribute("offset")),t.duration=N.parseDuration(e.getAttribute("duration")),N.childrenByName(e,"HTMLResource").forEach((function(e){t.type=e.getAttribute("creativeType")||"text/html",t.htmlResource=N.parseNodeText(e)})),N.childrenByName(e,"IFrameResource").forEach((function(e){t.type=e.getAttribute("creativeType")||0,t.iframeResource=N.parseNodeText(e)})),N.childrenByName(e,"StaticResource").forEach((function(e){t.type=e.getAttribute("creativeType")||0,t.staticResource=N.parseNodeText(e)}));var r=N.childByName(e,"IconClicks");if(r){t.iconClickThroughURLTemplate=N.parseNodeText(N.childByName(r,"IconClickThrough")),N.childrenByName(r,"IconClickTracking").forEach((function(e){t.iconClickTrackingURLTemplates.push({id:e.getAttribute("id")||null,url:N.parseNodeText(e)})}));var n=N.childByName(r,"IconClickFallbackImages");n&&N.childrenByName(n,"IconClickFallbackImage").forEach((function(e){t.iconClickFallbackImages.push({url:N.parseNodeText(e)||null,width:e.getAttribute("width")||null,height:e.getAttribute("height")||null})}))}return t.iconViewTrackingURLTemplate=N.parseNodeText(N.childByName(e,"IconViewTracking")),t}(e))})),n}function B(e,t){var r=function(){var e=g(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return{id:e.id,adId:e.adId,sequence:e.sequence,apiFramework:e.apiFramework,type:"nonlinear",variations:[],trackingEvents:{}}}(t);return N.childrenByName(e,"TrackingEvents").forEach((function(e){var t,n;N.childrenByName(e,"Tracking").forEach((function(e){t=e.getAttribute("event"),n=N.parseNodeText(e),t&&n&&(Array.isArray(r.trackingEvents[t])||(r.trackingEvents[t]=[]),r.trackingEvents[t].push(n))}))})),N.childrenByName(e,"NonLinear").forEach((function(e){var t={id:null,width:0,height:0,expandedWidth:0,expandedHeight:0,scalable:!0,maintainAspectRatio:!0,minSuggestedDuration:0,apiFramework:"static",adType:"nonLinearAd",type:null,staticResource:null,htmlResource:null,iframeResource:null,nonlinearClickThroughURLTemplate:null,nonlinearClickTrackingURLTemplates:[],adParameters:null};t.id=e.getAttribute("id")||null,t.width=e.getAttribute("width"),t.height=e.getAttribute("height"),t.expandedWidth=e.getAttribute("expandedWidth"),t.expandedHeight=e.getAttribute("expandedHeight"),t.scalable=N.parseBoolean(e.getAttribute("scalable")),t.maintainAspectRatio=N.parseBoolean(e.getAttribute("maintainAspectRatio")),t.minSuggestedDuration=N.parseDuration(e.getAttribute("minSuggestedDuration")),t.apiFramework=e.getAttribute("apiFramework"),N.childrenByName(e,"HTMLResource").forEach((function(e){t.type=e.getAttribute("creativeType")||"text/html",t.htmlResource=N.parseNodeText(e)})),N.childrenByName(e,"IFrameResource").forEach((function(e){t.type=e.getAttribute("creativeType")||0,t.iframeResource=N.parseNodeText(e)})),N.childrenByName(e,"StaticResource").forEach((function(e){t.type=e.getAttribute("creativeType")||0,t.staticResource=N.parseNodeText(e)}));var n=N.childByName(e,"AdParameters");n&&(t.adParameters={value:N.parseNodeText(n),xmlEncoded:n.getAttribute("xmlEncoded")||null}),t.nonlinearClickThroughURLTemplate=N.parseNodeText(N.childByName(e,"NonLinearClickThrough")),N.childrenByName(e,"NonLinearClickTracking").forEach((function(e){t.nonlinearClickTrackingURLTemplates.push({id:e.getAttribute("id")||null,url:N.parseNodeText(e)})})),r.variations.push(t)})),r}function C(e){var t=[];return e.forEach((function(e){var r=L(e);r&&t.push(r)})),t}function L(e){if("#comment"===e.nodeName)return null;var t,r={name:null,value:null,attributes:{},children:[]},n=e.attributes,i=e.childNodes;if(r.name=e.nodeName,e.attributes)for(var a in n)if(n.hasOwnProperty(a)){var o=n[a];o.nodeName&&o.nodeValue&&(r.attributes[o.nodeName]=o.nodeValue)}for(var s in i)if(i.hasOwnProperty(s)){var u=L(i[s]);u&&r.children.push(u)}if(0===r.children.length||1===r.children.length&&["#cdata-section","#text"].indexOf(r.children[0].name)>=0){var l=N.parseNodeText(e);""!==l&&(r.value=l),r.children=[]}return null===(t=r).value&&0===Object.keys(t.attributes).length&&0===t.children.length?null:r}function x(e){var t=[];return e.forEach((function(e){var r,n={id:e.getAttribute("id")||null,adId:U(e),sequence:e.getAttribute("sequence")||null,apiFramework:e.getAttribute("apiFramework")||null},i=[];N.childrenByName(e,"UniversalAdId").forEach((function(e){var t={idRegistry:e.getAttribute("idRegistry")||"unknown",value:N.parseNodeText(e)};i.push(t)}));var a=N.childByName(e,"CreativeExtensions");for(var o in a&&(r=C(N.childrenByName(a,"CreativeExtension"))),e.childNodes){var s=e.childNodes[o],u=void 0;switch(s.nodeName){case"Linear":u=D(s,n);break;case"NonLinearAds":u=B(s,n);break;case"CompanionAds":u=O(s,n)}u&&(i&&(u.universalAdIds=i),r&&(u.creativeExtensions=r),t.push(u))}})),t}function U(e){return e.getAttribute("AdID")||e.getAttribute("adID")||e.getAttribute("adId")||null}var M={Wrapper:{subElements:["VASTAdTagURI","Impression"]},BlockedAdCategories:{attributes:["authority"]},InLine:{subElements:["AdSystem","AdTitle","Impression","AdServingId","Creatives"]},Category:{attributes:["authority"]},Pricing:{attributes:["model","currency"]},Verification:{oneOfinLineResources:["JavaScriptResource","ExecutableResource"],attributes:["vendor"]},UniversalAdId:{attributes:["idRegistry"]},JavaScriptResource:{attributes:["apiFramework","browserOptional"]},ExecutableResource:{attributes:["apiFramework","type"]},Tracking:{attributes:["event"]},Creatives:{subElements:["Creative"]},Creative:{subElements:["UniversalAdId"]},Linear:{subElements:["MediaFiles","Duration"]},MediaFiles:{subElements:["MediaFile"]},MediaFile:{attributes:["delivery","type","width","height"]},Mezzanine:{attributes:["delivery","type","width","height"]},NonLinear:{oneOfinLineResources:["StaticResource","IFrameResource","HTMLResource"],attributes:["width","height"]},Companion:{oneOfinLineResources:["StaticResource","IFrameResource","HTMLResource"],attributes:["width","height"]},StaticResource:{attributes:["creativeType"]},Icons:{subElements:["Icon"]},Icon:{oneOfinLineResources:["StaticResource","IFrameResource","HTMLResource"]}};function V(e,t){if(M[e.nodeName]&&M[e.nodeName].attributes){var r=M[e.nodeName].attributes.filter((function(t){return!e.getAttribute(t)}));r.length>0&&q({name:e.nodeName,parentName:e.parentNode.nodeName,attributes:r},t)}}function F(e,t,r){var n=M[e.nodeName],i=!r&&"Wrapper"!==e.nodeName;if(n&&!i){if(n.subElements){var a=n.subElements.filter((function(t){return!N.childByName(e,t)}));a.length>0&&q({name:e.nodeName,parentName:e.parentNode.nodeName,subElements:a},t)}r&&n.oneOfinLineResources&&(n.oneOfinLineResources.some((function(t){return N.childByName(e,t)}))||q({name:e.nodeName,parentName:e.parentNode.nodeName,oneOfResources:n.oneOfinLineResources},t))}}function j(e){return e.children&&0!==e.children.length}function q(e,t){var r=e.name,n=e.parentName,i=e.attributes,a=e.subElements,o=e.oneOfResources,s="Element '".concat(r,"'");t("VAST-warning",{message:s+=i?" missing required attribute(s) '".concat(i.join(", "),"' "):a?" missing required sub element(s) '".concat(a.join(", "),"' "):o?" must provide one of the following '".concat(o.join(", "),"' "):" is empty",parentElement:n,specVersion:4.1})}var G={verifyRequiredValues:function e(t,r,n){if(t&&t.nodeName)if("InLine"===t.nodeName&&(n=!0),V(t,r),j(t)){F(t,r,n);for(var i=0;i<t.children.length;i++)e(t.children[i],r,n)}else 0===N.parseNodeText(t).length&&q({name:t.nodeName,parentName:t.parentNode.nodeName},r)},hasSubElements:j,emitMissingValueWarning:q,verifyRequiredAttributes:V,verifyRequiredSubElements:F};function H(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.allowMultipleAds,i=r.followAdditionalWrappers,a=e.childNodes;for(var o in a){var s=a[o];if(-1!==["Wrapper","InLine"].indexOf(s.nodeName)&&("Wrapper"!==s.nodeName||!1!==i)){if(N.copyNodeAttribute("id",e,s),N.copyNodeAttribute("sequence",e,s),N.copyNodeAttribute("adType",e,s),"Wrapper"===s.nodeName)return{ad:X(s,t),type:"WRAPPER"};if("InLine"===s.nodeName)return{ad:W(s,t,{allowMultipleAds:n}),type:"INLINE"}}}}function W(e,t){return!1===(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).allowMultipleAds&&e.getAttribute("sequence")?null:Y(e,t)}function Y(e,t){var r=[];t&&G.verifyRequiredValues(e,t);var n,i,a=e.childNodes,o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{id:e.id||null,sequence:e.sequence||null,adType:e.adType||null,adServingId:null,categories:[],expires:null,viewableImpression:[],system:null,title:null,description:null,advertiser:null,pricing:null,survey:null,errorURLTemplates:[],impressionURLTemplates:[],creatives:[],extensions:[],adVerifications:[],blockedAdCategories:[],followAdditionalWrappers:!0,allowMultipleAds:!1,fallbackOnNoAd:null}}(N.parseAttributes(e));for(var s in a){var u=a[s];switch(u.nodeName){case"Error":o.errorURLTemplates.push(N.parseNodeText(u));break;case"Impression":o.impressionURLTemplates.push({id:u.getAttribute("id")||null,url:N.parseNodeText(u)});break;case"Creatives":o.creatives=x(N.childrenByName(u,"Creative"));break;case"Extensions":var l=N.childrenByName(u,"Extension");o.extensions=C(l),o.adVerifications.length||(r=$(l));break;case"AdVerifications":o.adVerifications=z(N.childrenByName(u,"Verification"));break;case"AdSystem":o.system={value:N.parseNodeText(u),version:u.getAttribute("version")||null};break;case"AdTitle":o.title=N.parseNodeText(u);break;case"AdServingId":o.adServingId=N.parseNodeText(u);break;case"Category":o.categories.push({authority:u.getAttribute("authority")||null,value:N.parseNodeText(u)});break;case"Expires":o.expires=parseInt(N.parseNodeText(u),10);break;case"ViewableImpression":o.viewableImpression.push((i=void 0,i=function(e,t){var r=N.parseNodeText(t);return r&&e.push(r),e},{id:(n=u).getAttribute("id")||null,viewable:N.childrenByName(n,"Viewable").reduce(i,[]),notViewable:N.childrenByName(n,"NotViewable").reduce(i,[]),viewUndetermined:N.childrenByName(n,"ViewUndetermined").reduce(i,[])}));break;case"Description":o.description=N.parseNodeText(u);break;case"Advertiser":o.advertiser={id:u.getAttribute("id")||null,value:N.parseNodeText(u)};break;case"Pricing":o.pricing={value:N.parseNodeText(u),model:u.getAttribute("model")||null,currency:u.getAttribute("currency")||null};break;case"Survey":o.survey={value:N.parseNodeText(u),type:u.getAttribute("type")||null};break;case"BlockedAdCategories":o.blockedAdCategories.push({authority:u.getAttribute("authority")||null,value:N.parseNodeText(u)})}}return r.length&&(o.adVerifications=o.adVerifications.concat(r)),o}function X(e,t){var r=Y(e,t),n=e.getAttribute("followAdditionalWrappers"),i=e.getAttribute("allowMultipleAds"),a=e.getAttribute("fallbackOnNoAd");r.followAdditionalWrappers=!n||N.parseBoolean(n),r.allowMultipleAds=!!i&&N.parseBoolean(i),r.fallbackOnNoAd=a?N.parseBoolean(a):null;var o=N.childByName(e,"VASTAdTagURI");if(o?r.nextWrapperURL=N.parseNodeText(o):(o=N.childByName(e,"VASTAdTagURL"))&&(r.nextWrapperURL=N.parseNodeText(N.childByName(o,"URL"))),r.creatives.forEach((function(e){if(-1!==["linear","nonlinear"].indexOf(e.type)){if(e.trackingEvents){r.trackingEvents||(r.trackingEvents={}),r.trackingEvents[e.type]||(r.trackingEvents[e.type]={});var t=function(t){var n=e.trackingEvents[t];Array.isArray(r.trackingEvents[e.type][t])||(r.trackingEvents[e.type][t]=[]),n.forEach((function(n){r.trackingEvents[e.type][t].push(n)}))};for(var n in e.trackingEvents)t(n)}e.videoClickTrackingURLTemplates&&(Array.isArray(r.videoClickTrackingURLTemplates)||(r.videoClickTrackingURLTemplates=[]),e.videoClickTrackingURLTemplates.forEach((function(e){r.videoClickTrackingURLTemplates.push(e)}))),e.videoClickThroughURLTemplate&&(r.videoClickThroughURLTemplate=e.videoClickThroughURLTemplate),e.videoCustomClickURLTemplates&&(Array.isArray(r.videoCustomClickURLTemplates)||(r.videoCustomClickURLTemplates=[]),e.videoCustomClickURLTemplates.forEach((function(e){r.videoCustomClickURLTemplates.push(e)})))}})),r.nextWrapperURL)return r}function z(e){var t=[];return e.forEach((function(e){var r={resource:null,vendor:null,browserOptional:!1,apiFramework:null,type:null,parameters:null,trackingEvents:{}},n=e.childNodes;for(var i in N.assignAttributes(e.attributes,r),n){var a=n[i];switch(a.nodeName){case"JavaScriptResource":case"ExecutableResource":r.resource=N.parseNodeText(a),N.assignAttributes(a.attributes,r);break;case"VerificationParameters":r.parameters=N.parseNodeText(a)}}var o=N.childByName(e,"TrackingEvents");o&&N.childrenByName(o,"Tracking").forEach((function(e){var t=e.getAttribute("event"),n=N.parseNodeText(e);t&&n&&(Array.isArray(r.trackingEvents[t])||(r.trackingEvents[t]=[]),r.trackingEvents[t].push(n))})),t.push(r)})),t}function $(e){var t=null,r=[];return e.some((function(e){return t=N.childByName(e,"AdVerifications")})),t&&(r=z(N.childrenByName(t,"Verification"))),r}var K=function(){function e(){i(this,e),this._handlers=[]}return o(e,[{key:"on",value:function(e,t){if("function"!=typeof t)throw new TypeError("The handler argument must be of type Function. Received type ".concat(n(t)));if(!e)throw new TypeError("The event argument must be of type String. Received type ".concat(n(e)));return this._handlers.push({event:e,handler:t}),this}},{key:"once",value:function(e,t){return this.on(e,function(e,t,r){var n={fired:!1,wrapFn:void 0};function i(){n.fired||(e.off(t,n.wrapFn),n.fired=!0,r.bind(e).apply(void 0,arguments))}return n.wrapFn=i,i}(this,e,t))}},{key:"off",value:function(e,t){return this._handlers=this._handlers.filter((function(r){return r.event!==e||r.handler!==t})),this}},{key:"emit",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=!1;return this._handlers.forEach((function(t){"*"===t.event&&(i=!0,t.handler.apply(t,[e].concat(r))),t.event===e&&(i=!0,t.handler.apply(t,r))})),i}},{key:"removeAllListeners",value:function(e){return e?(this._handlers=this._handlers.filter((function(t){return t.event!==e})),this):(this._handlers=[],this)}},{key:"listenerCount",value:function(e){return this._handlers.filter((function(t){return t.event===e})).length}},{key:"listeners",value:function(e){return this._handlers.reduce((function(t,r){return r.event===e&&t.push(r.handler),t}),[])}},{key:"eventNames",value:function(){return this._handlers.map((function(e){return e.event}))}}]),e}(),Q={get:function(e,t,r){r(new Error("Please bundle the library for node to use the node urlHandler"))}},Z=12e4;function J(){try{var e=new window.XMLHttpRequest;return"withCredentials"in e?e:null}catch(e){return null}}function ee(e,t,r){var n=r?408:e.status,i=r?"XHRURLHandler: Request timed out after ".concat(e.timeout," ms (").concat(n,")"):"XHRURLHandler: ".concat(e.statusText," (").concat(n,")");t(new Error(i),null,{statusCode:n})}var te={get:function(e,t,r){if("https:"===window.location.protocol&&0===e.indexOf("http://"))return r(new Error("XHRURLHandler: Cannot go from HTTPS to HTTP."));try{var n=J();n.open("GET",e),n.timeout=t.timeout||Z,n.withCredentials=t.withCredentials||!1,n.overrideMimeType&&n.overrideMimeType("text/xml"),n.onload=function(){return function(e,t){200===e.status?t(null,e.responseXML,{byteLength:e.response.length,statusCode:e.status}):ee(e,t,!1)}(n,r)},n.onerror=function(){return ee(n,r,!1)},n.onabort=function(){return ee(n,r,!1)},n.ontimeout=function(){return ee(n,r,!0)},n.send()}catch(e){r(new Error("XHRURLHandler: Unexpected error"))}},supported:function(){return!!J()}},re={get:function(e,t,r){return r||("function"==typeof t&&(r=t),t={}),"undefined"==typeof window||null===window?Q.get(e,t,r):te.supported()?te.get(e,t,r):r(new Error("Current context is not supported by any of the default URLHandlers. Please provide a custom URLHandler"))}},ne=0,ie=0,ae=function(e,t){!e||!t||e<=0||t<=0||(ie=(ie*ne+8*e/t)/++ne)},oe={ERRORCODE:900,extensions:[]},se=function(e){u(r,e);var t=h(r);function r(){var e;return i(this,r),(e=t.call(this)).remainingAds=[],e.errorURLTemplates=[],e.rootErrorURLTemplates=[],e.maxWrapperDepth=null,e.URLTemplateFilters=[],e.fetchingOptions={},e.parsingOptions={},e}return o(r,[{key:"addURLTemplateFilter",value:function(e){"function"==typeof e&&this.URLTemplateFilters.push(e)}},{key:"removeURLTemplateFilter",value:function(){this.URLTemplateFilters.pop()}},{key:"countURLTemplateFilters",value:function(){return this.URLTemplateFilters.length}},{key:"clearURLTemplateFilters",value:function(){this.URLTemplateFilters=[]}},{key:"trackVastError",value:function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i];this.emit("VAST-error",Object.assign.apply(Object,[{},oe,t].concat(n))),R.track(e,t)}},{key:"getErrorURLTemplates",value:function(){return this.rootErrorURLTemplates.concat(this.errorURLTemplates)}},{key:"getEstimatedBitrate",value:function(){return ie}},{key:"fetchVAST",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return new Promise((function(a,o){t.URLTemplateFilters.forEach((function(t){e=t(e)}));var s=Date.now();t.emit("VAST-resolving",{url:e,previousUrl:n,wrapperDepth:r,maxWrapperDepth:t.maxWrapperDepth,timeout:t.fetchingOptions.timeout,wrapperAd:i}),t.urlHandler.get(e,t.fetchingOptions,(function(i,u){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=Math.round(Date.now()-s),d=Object.assign({url:e,previousUrl:n,wrapperDepth:r,error:i,duration:c},l);t.emit("VAST-resolved",d),ae(l.byteLength,c),i?o(i):a(u)}))}))}},{key:"initParsingStatus",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.errorURLTemplates=[],this.fetchingOptions={timeout:e.timeout||Z,withCredentials:e.withCredentials},this.maxWrapperDepth=e.wrapperLimit||10,this.parsingOptions={allowMultipleAds:e.allowMultipleAds},this.remainingAds=[],this.rootErrorURLTemplates=[],this.rootURL="",this.urlHandler=e.urlHandler||e.urlhandler||re,this.vastVersion=null,ae(e.byteLength,e.requestDuration)}},{key:"getRemainingAds",value:function(e){var t=this;if(0===this.remainingAds.length)return Promise.reject(new Error("No more ads are available for the given VAST"));var r=e?R.flatten(this.remainingAds):this.remainingAds.shift();return this.errorURLTemplates=[],this.resolveAds(r,{wrapperDepth:0,url:this.rootURL}).then((function(e){return t.buildVASTResponse(e)}))}},{key:"getAndParseVAST",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.initParsingStatus(r),this.URLTemplateFilters.forEach((function(t){e=t(e)})),this.rootURL=e,this.fetchVAST(e).then((function(n){return r.previousUrl=e,r.isRootVAST=!0,r.url=e,t.parse(n,r).then((function(e){return t.buildVASTResponse(e)}))}))}},{key:"parseVAST",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.initParsingStatus(r),r.isRootVAST=!0,this.parse(e,r).then((function(e){return t.buildVASTResponse(e)}))}},{key:"buildVASTResponse",value:function(e){var t,r={ads:(t={ads:e,errorURLTemplates:this.getErrorURLTemplates(),version:this.vastVersion}).ads||[],errorURLTemplates:t.errorURLTemplates||[],version:t.version||null};return this.completeWrapperResolving(r),r}},{key:"parseVastXml",value:function(e,t){var r=t.isRootVAST,n=void 0!==r&&r,i=t.url,a=void 0===i?null:i,o=t.wrapperDepth,s=void 0===o?0:o,u=t.allowMultipleAds,l=t.followAdditionalWrappers;if(!e||!e.documentElement||"VAST"!==e.documentElement.nodeName)throw this.emit("VAST-ad-parsed",{type:"ERROR",url:a,wrapperDepth:s}),new Error("Invalid VAST XMLDocument");var c=[],d=e.documentElement.childNodes,h=e.documentElement.getAttribute("version");for(var p in n&&h&&(this.vastVersion=h),d){var f=d[p];if("Error"===f.nodeName){var y=N.parseNodeText(f);n?this.rootErrorURLTemplates.push(y):this.errorURLTemplates.push(y)}else if("Ad"===f.nodeName){if(this.vastVersion&&parseFloat(this.vastVersion)<3)u=!0;else if(!1===u&&c.length>1)break;var g=H(f,this.emit.bind(this),{allowMultipleAds:u,followAdditionalWrappers:l});g.ad?(c.push(g.ad),this.emit("VAST-ad-parsed",{type:g.type,url:a,wrapperDepth:s,adIndex:c.length-1,vastVersion:h})):this.trackVastError(this.getErrorURLTemplates(),{ERRORCODE:101})}}return c}},{key:"parse",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.url,n=void 0===r?null:r,i=t.resolveAll,a=void 0===i||i,o=t.wrapperSequence,s=void 0===o?null:o,u=t.previousUrl,l=void 0===u?null:u,c=t.wrapperDepth,d=void 0===c?0:c,h=t.isRootVAST,p=void 0!==h&&h,f=t.followAdditionalWrappers,y=t.allowMultipleAds,g=[];this.vastVersion&&parseFloat(this.vastVersion)<3&&p&&(y=!0);try{g=this.parseVastXml(e,{isRootVAST:p,url:n,wrapperDepth:d,allowMultipleAds:y,followAdditionalWrappers:f})}catch(e){return Promise.reject(e)}return 1===g.length&&null!=s&&(g[0].sequence=s),!1===a&&(this.remainingAds=N.splitVAST(g),g=this.remainingAds.shift()),this.resolveAds(g,{wrapperDepth:d,previousUrl:l,url:n})}},{key:"resolveAds",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,n=r.wrapperDepth,i=r.previousUrl,a=r.url,o=[];return i=a,t.forEach((function(t){var r=e.resolveWrappers(t,n,i);o.push(r)})),Promise.all(o).then((function(t){var r=R.flatten(t);if(!r&&e.remainingAds.length>0){var o=e.remainingAds.shift();return e.resolveAds(o,{wrapperDepth:n,previousUrl:i,url:a})}return r}))}},{key:"resolveWrappers",value:function(e,t,r){var n=this;return new Promise((function(i){var a;if(t++,!e.nextWrapperURL)return delete e.nextWrapperURL,i(e);if(t>=n.maxWrapperDepth)return e.errorCode=302,delete e.nextWrapperURL,i(e);e.nextWrapperURL=N.resolveVastAdTagURI(e.nextWrapperURL,r),n.URLTemplateFilters.forEach((function(t){e.nextWrapperURL=t(e.nextWrapperURL)}));var o=null!==(a=n.parsingOptions.allowMultipleAds)&&void 0!==a?a:e.allowMultipleAds,s=e.sequence;n.fetchVAST(e.nextWrapperURL,t,r,e).then((function(a){return n.parse(a,{url:e.nextWrapperURL,previousUrl:r,wrapperSequence:s,wrapperDepth:t,followAdditionalWrappers:e.followAdditionalWrappers,allowMultipleAds:o}).then((function(t){if(delete e.nextWrapperURL,0===t.length)return e.creatives=[],i(e);t.forEach((function(t){t&&N.mergeWrapperAdData(t,e)})),i(t)}))})).catch((function(t){e.errorCode=301,e.errorMessage=t.message,i(e)}))}))}},{key:"completeWrapperResolving",value:function(e){if(0===e.ads.length)this.trackVastError(e.errorURLTemplates,{ERRORCODE:303});else for(var t=e.ads.length-1;t>=0;t--){var r=e.ads[t];(r.errorCode||0===r.creatives.length)&&(this.trackVastError(r.errorURLTemplates.concat(e.errorURLTemplates),{ERRORCODE:r.errorCode||303},{ERRORMESSAGE:r.errorMessage||""},{extensions:r.extensions},{system:r.system}),e.ads.splice(t,1))}}}]),r}(K),ue=null,le={data:{},length:0,getItem:function(e){return this.data[e]},setItem:function(e,t){this.data[e]=t,this.length=Object.keys(this.data).length},removeItem:function(e){delete this.data[e],this.length=Object.keys(this.data).length},clear:function(){this.data={},this.length=0}},ce=function(){function e(){i(this,e),this.storage=this.initStorage()}return o(e,[{key:"initStorage",value:function(){if(ue)return ue;try{ue="undefined"!=typeof window&&null!==window?window.localStorage||window.sessionStorage:null}catch(e){ue=null}return ue&&!this.isStorageDisabled(ue)||(ue=le).clear(),ue}},{key:"isStorageDisabled",value:function(e){var t="__VASTStorage__";try{if(e.setItem(t,t),e.getItem(t)!==t)return e.removeItem(t),!0}catch(e){return!0}return e.removeItem(t),!1}},{key:"getItem",value:function(e){return this.storage.getItem(e)}},{key:"setItem",value:function(e,t){return this.storage.setItem(e,t)}},{key:"removeItem",value:function(e){return this.storage.removeItem(e)}},{key:"clear",value:function(){return this.storage.clear()}}]),e}(),de=function(){function e(t,r,n){i(this,e),this.cappingFreeLunch=t||0,this.cappingMinimumTimeInterval=r||0,this.defaultOptions={withCredentials:!1,timeout:0},this.vastParser=new se,this.storage=n||new ce,void 0===this.lastSuccessfulAd&&(this.lastSuccessfulAd=0),void 0===this.totalCalls&&(this.totalCalls=0),void 0===this.totalCallsTimeout&&(this.totalCallsTimeout=0)}return o(e,[{key:"getParser",value:function(){return this.vastParser}},{key:"lastSuccessfulAd",get:function(){return this.storage.getItem("vast-client-last-successful-ad")},set:function(e){this.storage.setItem("vast-client-last-successful-ad",e)}},{key:"totalCalls",get:function(){return this.storage.getItem("vast-client-total-calls")},set:function(e){this.storage.setItem("vast-client-total-calls",e)}},{key:"totalCallsTimeout",get:function(){return this.storage.getItem("vast-client-total-calls-timeout")},set:function(e){this.storage.setItem("vast-client-total-calls-timeout",e)}},{key:"hasRemainingAds",value:function(){return this.vastParser.remainingAds.length>0}},{key:"getNextAds",value:function(e){return this.vastParser.getRemainingAds(e)}},{key:"get",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Date.now();return(r=Object.assign({},this.defaultOptions,r)).hasOwnProperty("resolveAll")||(r.resolveAll=!1),this.totalCallsTimeout<n?(this.totalCalls=1,this.totalCallsTimeout=n+36e5):this.totalCalls++,new Promise((function(i,a){if(t.cappingFreeLunch>=t.totalCalls)return a(new Error("VAST call canceled – FreeLunch capping not reached yet ".concat(t.totalCalls,"/").concat(t.cappingFreeLunch)));var o=n-t.lastSuccessfulAd;if(o<0)t.lastSuccessfulAd=0;else if(o<t.cappingMinimumTimeInterval)return a(new Error("VAST call canceled – (".concat(t.cappingMinimumTimeInterval,")ms minimum interval reached")));t.vastParser.getAndParseVAST(e,r).then((function(e){return i(e)})).catch((function(e){return a(e)}))}))}}]),e}(),he=function(e){u(a,e);var t=h(a);function a(e,r,n){var o,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,u=arguments.length>4&&void 0!==arguments[4]&&arguments[4];for(var l in i(this,a),(o=t.call(this)).ad=r,o.creative=n,o.variation=s,o.muted=u,o.impressed=!1,o.skippable=!1,o.trackingEvents={},o.lastPercentage=0,o._alreadyTriggeredQuartiles={},o.emitAlwaysEvents=["creativeView","start","firstQuartile","midpoint","thirdQuartile","complete","resume","pause","rewind","skip","closeLinear","close"],o.creative.trackingEvents){var c=o.creative.trackingEvents[l];o.trackingEvents[l]=c.slice(0)}return function(e){return"linear"===e.type}(o.creative)?o._initLinearTracking():o._initVariationTracking(),e&&o.on("start",(function(){e.lastSuccessfulAd=Date.now()})),o}return o(a,[{key:"_initLinearTracking",value:function(){this.linear=!0,this.skipDelay=this.creative.skipDelay,this.setDuration(this.creative.duration),this.clickThroughURLTemplate=this.creative.videoClickThroughURLTemplate,this.clickTrackingURLTemplates=this.creative.videoClickTrackingURLTemplates}},{key:"_initVariationTracking",value:function(){if(this.linear=!1,this.skipDelay=-1,this.variation){for(var e in this.variation.trackingEvents){var t=this.variation.trackingEvents[e];this.trackingEvents[e]?this.trackingEvents[e]=this.trackingEvents[e].concat(t.slice(0)):this.trackingEvents[e]=t.slice(0)}"nonLinearAd"===this.variation.adType?(this.clickThroughURLTemplate=this.variation.nonlinearClickThroughURLTemplate,this.clickTrackingURLTemplates=this.variation.nonlinearClickTrackingURLTemplates,this.setDuration(this.variation.minSuggestedDuration)):function(e){return"companionAd"===e.adType}(this.variation)&&(this.clickThroughURLTemplate=this.variation.companionClickThroughURLTemplate,this.clickTrackingURLTemplates=this.variation.companionClickTrackingURLTemplates)}}},{key:"setDuration",value:function(e){R.isValidTimeValue(e)&&(this.assetDuration=e,this.quartiles={firstQuartile:Math.round(25*this.assetDuration)/100,midpoint:Math.round(50*this.assetDuration)/100,thirdQuartile:Math.round(75*this.assetDuration)/100})}},{key:"setProgress",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(R.isValidTimeValue(e)&&"object"===n(r)){var i=this.skipDelay||-1;if(-1===i||this.skippable||(i>e?this.emit("skip-countdown",i-e):(this.skippable=!0,this.emit("skip-countdown",0))),this.assetDuration>0){var a=Math.round(e/this.assetDuration*100),o=[];if(e>0){o.push("start");for(var s=this.lastPercentage;s<a;s++)o.push("progress-".concat(s+1,"%"));for(var u in o.push("progress-".concat(Math.round(e))),this.quartiles)this.isQuartileReached(u,this.quartiles[u],e)&&(o.push(u),this._alreadyTriggeredQuartiles[u]=!0);this.lastPercentage=a}o.forEach((function(e){t.track(e,{macros:r,once:!0})})),e<this.progress&&this.track("rewind",{macros:r})}this.progress=e}}},{key:"isQuartileReached",value:function(e,t,r){var n=!1;return t<=r&&!this._alreadyTriggeredQuartiles[e]&&(n=!0),n}},{key:"setMuted",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&"object"===n(t)&&(this.muted!==e&&this.track(e?"mute":"unmute",{macros:t}),this.muted=e)}},{key:"setPaused",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&"object"===n(t)&&(this.paused!==e&&this.track(e?"pause":"resume",{macros:t}),this.paused=e)}},{key:"setFullscreen",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&"object"===n(t)&&(this.fullscreen!==e&&this.track(e?"fullscreen":"exitFullscreen",{macros:t}),this.fullscreen=e)}},{key:"setExpand",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"boolean"==typeof e&&"object"===n(t)&&(this.expanded!==e&&(this.track(e?"expand":"collapse",{macros:t}),this.track(e?"playerExpand":"playerCollapse",{macros:t})),this.expanded=e)}},{key:"setSkipDelay",value:function(e){R.isValidTimeValue(e)&&(this.skipDelay=e)}},{key:"trackImpression",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&(this.impressed||(this.impressed=!0,this.trackURLs(this.ad.impressionURLTemplates,e),this.track("creativeView",{macros:e})))}},{key:"trackViewableImpression",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(t)&&this.ad.viewableImpression.forEach((function(r){e.trackURLs(r.viewable,t)}))}},{key:"trackNotViewableImpression",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(t)&&this.ad.viewableImpression.forEach((function(r){e.trackURLs(r.notViewable,t)}))}},{key:"trackUndeterminedImpression",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(t)&&this.ad.viewableImpression.forEach((function(r){e.trackURLs(r.viewUndetermined,t)}))}},{key:"error",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];"object"===n(e)&&"boolean"==typeof t&&this.trackURLs(this.ad.errorURLTemplates,e,{isCustomCode:t})}},{key:"errorWithCode",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];"string"==typeof e&&"boolean"==typeof t&&(this.error({ERRORCODE:e},t),console.log("The method errorWithCode is deprecated, please use vast tracker error method instead"))}},{key:"complete",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("complete",{macros:e})}},{key:"notUsed",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&(this.track("notUsed",{macros:e}),this.trackingEvents=[])}},{key:"otherAdInteraction",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("otherAdInteraction",{macros:e})}},{key:"acceptInvitation",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("acceptInvitation",{macros:e})}},{key:"adExpand",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("adExpand",{macros:e})}},{key:"adCollapse",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("adCollapse",{macros:e})}},{key:"minimize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("minimize",{macros:e})}},{key:"verificationNotExecuted",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof e&&"object"===n(t)){if(!this.ad||!this.ad.adVerifications||!this.ad.adVerifications.length)throw new Error("No adVerifications provided");if(!e)throw new Error("No vendor provided, unable to find associated verificationNotExecuted");var r=this.ad.adVerifications.find((function(t){return t.vendor===e}));if(!r)throw new Error("No associated verification element found for vendor: ".concat(e));var i=r.trackingEvents;if(i&&i.verificationNotExecuted){var a=i.verificationNotExecuted;this.trackURLs(a,t),this.emit("verificationNotExecuted",{trackingURLTemplates:a})}}}},{key:"overlayViewDuration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"==typeof e&&"object"===n(t)&&(t.ADPLAYHEAD=e,this.track("overlayViewDuration",{macros:t}))}},{key:"close",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track(this.linear?"closeLinear":"close",{macros:e})}},{key:"skip",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("skip",{macros:e})}},{key:"load",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"object"===n(e)&&this.track("loaded",{macros:e})}},{key:"click",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((null===e||"string"==typeof e)&&"object"===n(t)){this.clickTrackingURLTemplates&&this.clickTrackingURLTemplates.length&&this.trackURLs(this.clickTrackingURLTemplates,t);var i=this.clickThroughURLTemplate||e,a=r({},t);if(i){this.progress&&(a.ADPLAYHEAD=this.progressFormatted());var o=R.resolveURLTemplates([i],a)[0];this.emit("clickthrough",o)}}}},{key:"track",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.macros,i=void 0===r?{}:r,a=t.once,o=void 0!==a&&a;if("object"===n(i)){"closeLinear"===e&&!this.trackingEvents[e]&&this.trackingEvents.close&&(e="close");var s=this.trackingEvents[e],u=this.emitAlwaysEvents.indexOf(e)>-1;s?(this.emit(e,{trackingURLTemplates:s}),this.trackURLs(s,i)):u&&this.emit(e,null),o&&(delete this.trackingEvents[e],u&&this.emitAlwaysEvents.splice(this.emitAlwaysEvents.indexOf(e),1))}}},{key:"trackURLs",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=R.filterValidUrlTemplates(e),o=r({},n);this.linear&&(this.creative&&this.creative.mediaFiles&&this.creative.mediaFiles[0]&&this.creative.mediaFiles[0].fileURL&&(o.ASSETURI=this.creative.mediaFiles[0].fileURL),this.progress&&(o.ADPLAYHEAD=this.progressFormatted())),null!==(t=this.creative)&&void 0!==t&&null!==(t=t.universalAdIds)&&void 0!==t&&t.length&&(o.UNIVERSALADID=this.creative.universalAdIds.map((function(e){return e.idRegistry.concat(" ",e.value)})).join(",")),this.ad&&(this.ad.sequence&&(o.PODSEQUENCE=this.ad.sequence),this.ad.adType&&(o.ADTYPE=this.ad.adType),this.ad.adServingId&&(o.ADSERVINGID=this.ad.adServingId),this.ad.categories&&this.ad.categories.length&&(o.ADCATEGORIES=this.ad.categories.map((function(e){return e.value})).join(",")),this.ad.blockedAdCategories&&this.ad.blockedAdCategories.length&&(o.BLOCKEDADCATEGORIES=this.ad.blockedAdCategories)),R.track(a,o,i)}},{key:"convertToTimecode",value:function(e){if(!R.isValidTimeValue(e))return"";var t=1e3*e,r=Math.floor(t/36e5),n=Math.floor(t/6e4%60),i=Math.floor(t/1e3%60),a=Math.floor(t%1e3);return"".concat(R.addLeadingZeros(r,2),":").concat(R.addLeadingZeros(n,2),":").concat(R.addLeadingZeros(i,2),".").concat(R.addLeadingZeros(a,3))}},{key:"progressFormatted",value:function(){return this.convertToTimecode(this.progress)}}]),a}(K);e.VASTClient=de,e.VASTParser=se,e.VASTTracker=he,e.parseDuration=P,Object.defineProperty(e,"__esModule",{value:!0})}(t)},228:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return s}});var n=class{constructor(e){this.id=e.getAttribute("id"),this.allowMultipleAds=e.getAttribute("allowMultipleAds"),this.followRedirects=e.getAttribute("followRedirects"),this.vastAdData=null,this.adTagURI=null,this.customData=null;for(const t in e.childNodes){const r=e.childNodes[t];switch(r.localName){case"AdTagURI":this.adTagURI={templateType:r.getAttribute("templateType"),uri:(r.textContent||r.text||"").trim()};break;case"VASTAdData":for(this.vastAdData=r.firstChild;this.vastAdData&&1!==this.vastAdData.nodeType;)this.vastAdData=this.vastAdData.nextSibling;break;case"CustomAdData":this.customData=r}}}};function i(e,t){const r=[];for(const n in e.childNodes){const i=e.childNodes[n];i.nodeName!==t&&t!==`vmap:${i.nodeName}`&&i.nodeName!==`vmap:${t}`||r.push(i)}return r}function a(e){const t={attributes:{},children:{},value:{}};t.value=function(e){if(!e||!e.childNodes)return{};const t=e.childNodes,r=[];for(const e in t){const n=t[e];"#cdata-section"===n.nodeName&&r.push(n)}if(r&&r.length>0)try{return JSON.parse(r[0].data)}catch(e){}let n="";for(const e in t){const r=t[e];switch(r.nodeName){case"#text":n+=r.textContent.trim();break;case"#cdata-section":n+=r.data}}return n}(e);const r=e.attributes;if(r)for(const e in r){const n=r[e];n.nodeName&&void 0!==n.nodeValue&&null!==n.nodeValue&&(t.attributes[n.nodeName]=n.nodeValue)}const n=e.childNodes;if(n)for(const e in n){const r=n[e];r.nodeName&&"#"!==r.nodeName.substring(0,1)&&(t.children[r.nodeName]=a(r))}return t}var o=class{constructor(e){this.timeOffset=e.getAttribute("timeOffset"),this.breakType=e.getAttribute("breakType"),this.breakId=e.getAttribute("breakId"),this.repeatAfter=e.getAttribute("repeatAfter"),this.adSource=null,this.trackingEvents=[],this.extensions=[];for(const t in e.childNodes){const r=e.childNodes[t];switch(r.localName){case"AdSource":this.adSource=new n(r);break;case"TrackingEvents":for(const e in r.childNodes){const t=r.childNodes[e];"Tracking"===t.localName&&this.trackingEvents.push({event:t.getAttribute("event"),uri:(t.textContent||t.text||"").trim()})}break;case"Extensions":this.extensions=i(r,"Extension").map((e=>a(e)))}}}track(e,t){for(const r in this.trackingEvents){const n=this.trackingEvents[r];if(n.event===e){let{uri:e}=n;"error"===n.event&&(e=e.replace("[ERRORCODE]",t)),this.tracker(e)}}}tracker(e){if("undefined"!=typeof window&&null!==window){(new Image).src=e}}};var s=class{constructor(e){if(!e||!e.documentElement||"VMAP"!==e.documentElement.localName)throw new Error("Not a VMAP document");this.version=e.documentElement.getAttribute("version"),this.adBreaks=[],this.extensions=[];for(const t in e.documentElement.childNodes){const r=e.documentElement.childNodes[t];switch(r.localName){case"AdBreak":this.adBreaks.push(new o(r));break;case"Extensions":this.extensions=i(r,"Extension").map((e=>a(e)))}}}}},9742:function(e,t){"use strict";t.byteLength=function(e){var t=s(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,a=s(e),o=a[0],u=a[1],l=new i(function(e,t,r){return 3*(t+r)/4-r}(0,o,u)),c=0,d=u>0?o-4:o;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],l[c++]=t>>16&255,l[c++]=t>>8&255,l[c++]=255&t;2===u&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,l[c++]=255&t);1===u&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,l[c++]=t>>8&255,l[c++]=255&t);return l},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=16383,s=0,l=n-i;s<l;s+=o)a.push(u(e,s,s+o>l?l:s+o));1===i?(t=e[n-1],a.push(r[t>>2]+r[t<<4&63]+"==")):2===i&&(t=(e[n-2]<<8)+e[n-1],a.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+"="));return a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0;o<64;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function s(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function u(e,t,n){for(var i,a,o=[],s=t;s<n;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),o.push(r[(a=i)>>18&63]+r[a>>12&63]+r[a>>6&63]+r[63&a]);return o.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},8764:function(e,t,r){"use strict";var n=r(9742),i=r(645);t.Buffer=s,t.SlowBuffer=function(e){+e!=e&&(e=0);return s.alloc(+e)},t.INSPECT_MAX_BYTES=50;var a=2147483647;function o(e){if(e>a)throw new RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return t.__proto__=s.prototype,t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return c(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!s.isEncoding(t))throw new TypeError("Unknown encoding: "+t);var r=0|p(e,t),n=o(r),i=n.write(e,t);i!==r&&(n=n.slice(0,i));return n}(e,t);if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(j(e,ArrayBuffer)||e&&j(e.buffer,ArrayBuffer))return function(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');var n;n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r);return n.__proto__=s.prototype,n}(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return s.from(n,t,r);var i=function(e){if(s.isBuffer(e)){var t=0|h(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}if(void 0!==e.length)return"number"!=typeof e.length||q(e.length)?o(0):d(e);if("Buffer"===e.type&&Array.isArray(e.data))return d(e.data)}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return l(e),o(e<0?0:0|h(e))}function d(e){for(var t=e.length<0?0:0|h(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function h(e){if(e>=a)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a.toString(16)+" bytes");return 0|e}function p(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||j(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return M(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(e).length;default:if(i)return n?-1:M(e).length;t=(""+t).toLowerCase(),i=!0}}function f(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return P(this,t,r);case"utf8":case"utf-8":return w(this,t,r);case"ascii":return R(this,t,r);case"latin1":case"binary":return S(this,t,r);case"base64":return k(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function y(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function g(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),q(r=+r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:m(e,t,r,n,i);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):m(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function m(e,t,r,n,i){var a,o=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;o=2,s/=2,u/=2,r/=2}function l(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var c=-1;for(a=r;a<s;a++)if(l(e,a)===l(t,-1===c?0:a-c)){if(-1===c&&(c=a),a-c+1===u)return c*o}else-1!==c&&(a-=a-c),c=-1}else for(r+u>s&&(r=s-u),a=r;a>=0;a--){for(var d=!0,h=0;h<u;h++)if(l(e,a+h)!==l(t,h)){d=!1;break}if(d)return a}return-1}function v(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s=parseInt(t.substr(2*o,2),16);if(q(s))return o;e[r+o]=s}return o}function _(e,t,r,n){return F(M(t,e.length-r),e,r,n)}function T(e,t,r,n){return F(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function A(e,t,r,n){return T(e,t,r,n)}function E(e,t,r,n){return F(V(t),e,r,n)}function b(e,t,r,n){return F(function(e,t){for(var r,n,i,a=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,i=r%256,a.push(i),a.push(n);return a}(t,e.length-r),e,r,n)}function k(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function w(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,s,u,l=e[i],c=null,d=l>239?4:l>223?3:l>191?2:1;if(i+d<=r)switch(d){case 1:l<128&&(c=l);break;case 2:128==(192&(a=e[i+1]))&&(u=(31&l)<<6|63&a)>127&&(c=u);break;case 3:a=e[i+1],o=e[i+2],128==(192&a)&&128==(192&o)&&(u=(15&l)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:a=e[i+1],o=e[i+2],s=e[i+3],128==(192&a)&&128==(192&o)&&128==(192&s)&&(u=(15&l)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&u<1114112&&(c=u)}null===c?(c=65533,d=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=d}return function(e){var t=e.length;if(t<=I)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=I));return r}(n)}t.kMaxLength=a,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),s.poolSize=8192,s.from=function(e,t,r){return u(e,t,r)},s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,s.alloc=function(e,t,r){return function(e,t,r){return l(e),e<=0?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)}(e,t,r)},s.allocUnsafe=function(e){return c(e)},s.allocUnsafeSlow=function(e){return c(e)},s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(j(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),j(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=s.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(j(a,Uint8Array)&&(a=s.from(a)),!s.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},s.byteLength=p,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?w(this,0,e):f.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},s.prototype.compare=function(e,t,r,n,i){if(j(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var a=(i>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0),u=Math.min(a,o),l=this.slice(n,i),c=e.slice(t,r),d=0;d<u;++d)if(l[d]!==c[d]){a=l[d],o=c[d];break}return a<o?-1:o<a?1:0},s.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return g(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return g(this,e,t,r,!1)},s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var a=!1;;)switch(n){case"hex":return v(this,e,t,r);case"utf8":case"utf-8":return _(this,e,t,r);case"ascii":return T(this,e,t,r);case"latin1":case"binary":return A(this,e,t,r);case"base64":return E(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return b(this,e,t,r);default:if(a)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),a=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var I=4096;function R(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function S(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function P(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=U(e[a]);return i}function N(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}function O(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function D(e,t,r,n,i,a){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function B(e,t,r,n,i,a){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function C(e,t,r,n,a){return t=+t,r>>>=0,a||B(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function L(e,t,r,n,a){return t=+t,r>>>=0,a||B(e,0,r,8),i.write(e,t,r,n,52,8),r+8}s.prototype.slice=function(e,t){var r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return n.__proto__=s.prototype,n},s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||O(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||O(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},s.prototype.readUInt8=function(e,t){return e>>>=0,t||O(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||O(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||O(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||O(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||O(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||O(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||O(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},s.prototype.readInt8=function(e,t){return e>>>=0,t||O(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||O(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||O(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||O(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||O(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||O(e,4,this.length),i.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||O(e,4,this.length),i.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||O(e,8,this.length),i.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||O(e,8,this.length),i.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||D(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||D(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);D(this,e,t,r,i-1,-i)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);D(this,e,t,r,i-1,-i)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||D(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return C(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return C(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return L(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return L(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var a=i-1;a>=0;--a)e[a+t]=this[a+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===e.length){var i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var a;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(a=t;a<r;++a)this[a]=e;else{var o=s.isBuffer(e)?e:s.from(e,n),u=o.length;if(0===u)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(a=0;a<r-t;++a)this[a+t]=o[a%u]}return this};var x=/[^+/0-9A-Za-z-_]/g;function U(e){return e<16?"0"+e.toString(16):e.toString(16)}function M(e,t){var r;t=t||1/0;for(var n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return a}function V(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(x,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function F(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function j(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function q(e){return e!=e}},624:function(e){function t(e){if(e)return function(e){for(var r in t.prototype)e[r]=t.prototype[r];return e}(e)}e.exports=t,t.prototype.on=t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks[e]=this._callbacks[e]||[]).push(t),this},t.prototype.once=function(e,t){var r=this;function n(){r.off(e,n),t.apply(this,arguments)}return this._callbacks=this._callbacks||{},n.fn=t,this.on(e,n),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks[e];if(!n)return this;if(1==arguments.length)return delete this._callbacks[e],this;for(var i=0;i<n.length;i++)if((r=n[i])===t||r.fn===t){n.splice(i,1);break}return this},t.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),r=this._callbacks[e];if(r)for(var n=0,i=(r=r.slice(0)).length;n<i;++n)r[n].apply(this,t);return this},t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks[e]||[]},t.prototype.hasListeners=function(e){return!!this.listeners(e).length}},645:function(e,t){t.read=function(e,t,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,l=u>>1,c=-7,d=r?i-1:0,h=r?-1:1,p=e[t+d];for(d+=h,a=p&(1<<-c)-1,p>>=-c,c+=s;c>0;a=256*a+e[t+d],d+=h,c-=8);for(o=a&(1<<-c)-1,a>>=-c,c+=n;c>0;o=256*o+e[t+d],d+=h,c-=8);if(0===a)a=1-l;else{if(a===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),a-=l}return(p?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,s,u,l=8*a-i-1,c=(1<<l)-1,d=c>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:a-1,f=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),(t+=o+d>=1?h/u:h*Math.pow(2,1-d))*u>=2&&(o++,u/=2),o+d>=c?(s=0,o=c):o+d>=1?(s=(t*u-1)*Math.pow(2,i),o+=d):(s=t*Math.pow(2,d-1)*Math.pow(2,i),o=0));i>=8;e[r+p]=255&s,p+=f,s/=256,i-=8);for(o=o<<i|s,l+=i;l>0;e[r+p]=255&o,p+=f,o/=256,l-=8);e[r+p-f]|=128*y}},9509:function(e,t,r){var n=r(8764),i=n.Buffer;function a(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(a(n,t),t.Buffer=o),o.prototype=Object.create(i.prototype),a(i,o),o.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},o.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},6099:function(e,t,r){!function(e){e.parser=function(e,t){return new i(e,t)},e.SAXParser=i,e.SAXStream=o,e.createStream=function(e,t){return new o(e,t)},e.MAX_BUFFER_LENGTH=65536;var t,n=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function i(t,r){if(!(this instanceof i))return new i(t,r);var a=this;!function(e){for(var t=0,r=n.length;t<r;t++)e[n[t]]=""}(a),a.q=a.c="",a.bufferCheckPosition=e.MAX_BUFFER_LENGTH,a.opt=r||{},a.opt.lowercase=a.opt.lowercase||a.opt.lowercasetags,a.looseCase=a.opt.lowercase?"toLowerCase":"toUpperCase",a.tags=[],a.closed=a.closedRoot=a.sawRoot=!1,a.tag=a.error=null,a.strict=!!t,a.noscript=!(!t&&!a.opt.noscript),a.state=R.BEGIN,a.strictEntities=a.opt.strictEntities,a.ENTITIES=a.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),a.attribList=[],a.opt.xmlns&&(a.ns=Object.create(g)),a.trackPosition=!1!==a.opt.position,a.trackPosition&&(a.position=a.line=a.column=0),P(a,"onready")}e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(e){function t(){}return t.prototype=e,new t}),Object.keys||(Object.keys=function(e){var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}),i.prototype={end:function(){C(this)},write:function(t){var r=this;if(this.error)throw this.error;if(r.closed)return B(r,"Cannot write after close. Assign an onready handler.");if(null===t)return C(r);"object"==typeof t&&(t=t.toString());var i=0,a="";for(;a=G(t,i++),r.c=a,a;)switch(r.trackPosition&&(r.position++,"\n"===a?(r.line++,r.column=0):r.column++),r.state){case R.BEGIN:if(r.state=R.BEGIN_WHITESPACE,"\ufeff"===a)continue;q(r,a);continue;case R.BEGIN_WHITESPACE:q(r,a);continue;case R.TEXT:if(r.sawRoot&&!r.closedRoot){for(var o=i-1;a&&"<"!==a&&"&"!==a;)(a=G(t,i++))&&r.trackPosition&&(r.position++,"\n"===a?(r.line++,r.column=0):r.column++);r.textNode+=t.substring(o,i-1)}"<"!==a||r.sawRoot&&r.closedRoot&&!r.strict?(!b(s,a)||r.sawRoot&&!r.closedRoot||L(r,"Text data outside of root node."),"&"===a?r.state=R.TEXT_ENTITY:r.textNode+=a):(r.state=R.OPEN_WAKA,r.startTagPosition=r.position);continue;case R.SCRIPT:"<"===a?r.state=R.SCRIPT_ENDING:r.script+=a;continue;case R.SCRIPT_ENDING:"/"===a?r.state=R.CLOSE_TAG:(r.script+="<"+a,r.state=R.SCRIPT);continue;case R.OPEN_WAKA:if("!"===a)r.state=R.SGML_DECL,r.sgmlDecl="";else if(E(s,a));else if(E(m,a))r.state=R.OPEN_TAG,r.tagName=a;else if("/"===a)r.state=R.CLOSE_TAG,r.tagName="";else if("?"===a)r.state=R.PROC_INST,r.procInstName=r.procInstBody="";else{if(L(r,"Unencoded <"),r.startTagPosition+1<r.position){var u=r.position-r.startTagPosition;a=new Array(u).join(" ")+a}r.textNode+="<"+a,r.state=R.TEXT}continue;case R.SGML_DECL:(r.sgmlDecl+a).toUpperCase()===h?(N(r,"onopencdata"),r.state=R.CDATA,r.sgmlDecl="",r.cdata=""):r.sgmlDecl+a==="--"?(r.state=R.COMMENT,r.comment="",r.sgmlDecl=""):(r.sgmlDecl+a).toUpperCase()===p?(r.state=R.DOCTYPE,(r.doctype||r.sawRoot)&&L(r,"Inappropriately located doctype declaration"),r.doctype="",r.sgmlDecl=""):">"===a?(N(r,"onsgmldeclaration",r.sgmlDecl),r.sgmlDecl="",r.state=R.TEXT):E(c,a)?(r.state=R.SGML_DECL_QUOTED,r.sgmlDecl+=a):r.sgmlDecl+=a;continue;case R.SGML_DECL_QUOTED:a===r.q&&(r.state=R.SGML_DECL,r.q=""),r.sgmlDecl+=a;continue;case R.DOCTYPE:">"===a?(r.state=R.TEXT,N(r,"ondoctype",r.doctype),r.doctype=!0):(r.doctype+=a,"["===a?r.state=R.DOCTYPE_DTD:E(c,a)&&(r.state=R.DOCTYPE_QUOTED,r.q=a));continue;case R.DOCTYPE_QUOTED:r.doctype+=a,a===r.q&&(r.q="",r.state=R.DOCTYPE);continue;case R.DOCTYPE_DTD:r.doctype+=a,"]"===a?r.state=R.DOCTYPE:E(c,a)&&(r.state=R.DOCTYPE_DTD_QUOTED,r.q=a);continue;case R.DOCTYPE_DTD_QUOTED:r.doctype+=a,a===r.q&&(r.state=R.DOCTYPE_DTD,r.q="");continue;case R.COMMENT:"-"===a?r.state=R.COMMENT_ENDING:r.comment+=a;continue;case R.COMMENT_ENDING:"-"===a?(r.state=R.COMMENT_ENDED,r.comment=D(r.opt,r.comment),r.comment&&N(r,"oncomment",r.comment),r.comment=""):(r.comment+="-"+a,r.state=R.COMMENT);continue;case R.COMMENT_ENDED:">"!==a?(L(r,"Malformed comment"),r.comment+="--"+a,r.state=R.COMMENT):r.state=R.TEXT;continue;case R.CDATA:"]"===a?r.state=R.CDATA_ENDING:r.cdata+=a;continue;case R.CDATA_ENDING:"]"===a?r.state=R.CDATA_ENDING_2:(r.cdata+="]"+a,r.state=R.CDATA);continue;case R.CDATA_ENDING_2:">"===a?(r.cdata&&N(r,"oncdata",r.cdata),N(r,"onclosecdata"),r.cdata="",r.state=R.TEXT):"]"===a?r.cdata+="]":(r.cdata+="]]"+a,r.state=R.CDATA);continue;case R.PROC_INST:"?"===a?r.state=R.PROC_INST_ENDING:E(s,a)?r.state=R.PROC_INST_BODY:r.procInstName+=a;continue;case R.PROC_INST_BODY:if(!r.procInstBody&&E(s,a))continue;"?"===a?r.state=R.PROC_INST_ENDING:r.procInstBody+=a;continue;case R.PROC_INST_ENDING:">"===a?(N(r,"onprocessinginstruction",{name:r.procInstName,body:r.procInstBody}),r.procInstName=r.procInstBody="",r.state=R.TEXT):(r.procInstBody+="?"+a,r.state=R.PROC_INST_BODY);continue;case R.OPEN_TAG:E(v,a)?r.tagName+=a:(x(r),">"===a?V(r):"/"===a?r.state=R.OPEN_TAG_SLASH:(b(s,a)&&L(r,"Invalid character in tag name"),r.state=R.ATTRIB));continue;case R.OPEN_TAG_SLASH:">"===a?(V(r,!0),F(r)):(L(r,"Forward-slash in opening tag not followed by >"),r.state=R.ATTRIB);continue;case R.ATTRIB:if(E(s,a))continue;">"===a?V(r):"/"===a?r.state=R.OPEN_TAG_SLASH:E(m,a)?(r.attribName=a,r.attribValue="",r.state=R.ATTRIB_NAME):L(r,"Invalid attribute name");continue;case R.ATTRIB_NAME:"="===a?r.state=R.ATTRIB_VALUE:">"===a?(L(r,"Attribute without value"),r.attribValue=r.attribName,M(r),V(r)):E(s,a)?r.state=R.ATTRIB_NAME_SAW_WHITE:E(v,a)?r.attribName+=a:L(r,"Invalid attribute name");continue;case R.ATTRIB_NAME_SAW_WHITE:if("="===a)r.state=R.ATTRIB_VALUE;else{if(E(s,a))continue;L(r,"Attribute without value"),r.tag.attributes[r.attribName]="",r.attribValue="",N(r,"onattribute",{name:r.attribName,value:""}),r.attribName="",">"===a?V(r):E(m,a)?(r.attribName=a,r.state=R.ATTRIB_NAME):(L(r,"Invalid attribute name"),r.state=R.ATTRIB)}continue;case R.ATTRIB_VALUE:if(E(s,a))continue;E(c,a)?(r.q=a,r.state=R.ATTRIB_VALUE_QUOTED):(L(r,"Unquoted attribute value"),r.state=R.ATTRIB_VALUE_UNQUOTED,r.attribValue=a);continue;case R.ATTRIB_VALUE_QUOTED:if(a!==r.q){"&"===a?r.state=R.ATTRIB_VALUE_ENTITY_Q:r.attribValue+=a;continue}M(r),r.q="",r.state=R.ATTRIB_VALUE_CLOSED;continue;case R.ATTRIB_VALUE_CLOSED:E(s,a)?r.state=R.ATTRIB:">"===a?V(r):"/"===a?r.state=R.OPEN_TAG_SLASH:E(m,a)?(L(r,"No whitespace between attributes"),r.attribName=a,r.attribValue="",r.state=R.ATTRIB_NAME):L(r,"Invalid attribute name");continue;case R.ATTRIB_VALUE_UNQUOTED:if(b(d,a)){"&"===a?r.state=R.ATTRIB_VALUE_ENTITY_U:r.attribValue+=a;continue}M(r),">"===a?V(r):r.state=R.ATTRIB;continue;case R.CLOSE_TAG:if(r.tagName)">"===a?F(r):E(v,a)?r.tagName+=a:r.script?(r.script+="</"+r.tagName,r.tagName="",r.state=R.SCRIPT):(b(s,a)&&L(r,"Invalid tagname in closing tag"),r.state=R.CLOSE_TAG_SAW_WHITE);else{if(E(s,a))continue;b(m,a)?r.script?(r.script+="</"+a,r.state=R.SCRIPT):L(r,"Invalid tagname in closing tag."):r.tagName=a}continue;case R.CLOSE_TAG_SAW_WHITE:if(E(s,a))continue;">"===a?F(r):L(r,"Invalid characters in closing tag");continue;case R.TEXT_ENTITY:case R.ATTRIB_VALUE_ENTITY_Q:case R.ATTRIB_VALUE_ENTITY_U:var l,f;switch(r.state){case R.TEXT_ENTITY:l=R.TEXT,f="textNode";break;case R.ATTRIB_VALUE_ENTITY_Q:l=R.ATTRIB_VALUE_QUOTED,f="attribValue";break;case R.ATTRIB_VALUE_ENTITY_U:l=R.ATTRIB_VALUE_UNQUOTED,f="attribValue"}";"===a?(r[f]+=j(r),r.entity="",r.state=l):E(r.entity.length?T:_,a)?r.entity+=a:(L(r,"Invalid character in entity name"),r[f]+="&"+r.entity+a,r.entity="",r.state=l);continue;default:throw new Error(r,"Unknown state: "+r.state)}r.position>=r.bufferCheckPosition&&function(t){for(var r=Math.max(e.MAX_BUFFER_LENGTH,10),i=0,a=0,o=n.length;a<o;a++){var s=t[n[a]].length;if(s>r)switch(n[a]){case"textNode":O(t);break;case"cdata":N(t,"oncdata",t.cdata),t.cdata="";break;case"script":N(t,"onscript",t.script),t.script="";break;default:B(t,"Max buffer length exceeded: "+n[a])}i=Math.max(i,s)}var u=e.MAX_BUFFER_LENGTH-i;t.bufferCheckPosition=u+t.position}(r);return r},resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){var e;O(e=this),""!==e.cdata&&(N(e,"oncdata",e.cdata),e.cdata=""),""!==e.script&&(N(e,"onscript",e.script),e.script="")}};try{t=r(3086).Stream}catch(e){t=function(){}}var a=e.EVENTS.filter((function(e){return"error"!==e&&"end"!==e}));function o(e,r){if(!(this instanceof o))return new o(e,r);t.apply(this),this._parser=new i(e,r),this.writable=!0,this.readable=!0;var n=this;this._parser.onend=function(){n.emit("end")},this._parser.onerror=function(e){n.emit("error",e),n._parser.error=null},this._decoder=null,a.forEach((function(e){Object.defineProperty(n,"on"+e,{get:function(){return n._parser["on"+e]},set:function(t){if(!t)return n.removeAllListeners(e),n._parser["on"+e]=t,t;n.on(e,t)},enumerable:!0,configurable:!1})}))}o.prototype=Object.create(t.prototype,{constructor:{value:o}}),o.prototype.write=function(e){if("function"==typeof Buffer&&"function"==typeof Buffer.isBuffer&&Buffer.isBuffer(e)){if(!this._decoder){var t=r(2553).s;this._decoder=new t("utf8")}e=this._decoder.write(e)}return this._parser.write(e.toString()),this.emit("data",e),!0},o.prototype.end=function(e){return e&&e.length&&this.write(e),this._parser.end(),!0},o.prototype.on=function(e,r){var n=this;return n._parser["on"+e]||-1===a.indexOf(e)||(n._parser["on"+e]=function(){var t=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);t.splice(0,0,e),n.emit.apply(n,t)}),t.prototype.on.call(n,e,r)};var s="\r\n\t ",u="0124356789",l="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",c="'\"",d=s+">",h="[CDATA[",p="DOCTYPE",f="http://www.w3.org/XML/1998/namespace",y="http://www.w3.org/2000/xmlns/",g={xml:f,xmlns:y};s=A(s),u=A(u),l=A(l);var m=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,v=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/,_=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,T=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040\.\d-]/;function A(e){return e.split("").reduce((function(e,t){return e[t]=!0,e}),{})}function E(e,t){return function(e){return"[object RegExp]"===Object.prototype.toString.call(e)}(e)?!!t.match(e):e[t]}function b(e,t){return!E(e,t)}c=A(c),d=A(d);var k,w,I,R=0;for(var S in e.STATE={BEGIN:R++,BEGIN_WHITESPACE:R++,TEXT:R++,TEXT_ENTITY:R++,OPEN_WAKA:R++,SGML_DECL:R++,SGML_DECL_QUOTED:R++,DOCTYPE:R++,DOCTYPE_QUOTED:R++,DOCTYPE_DTD:R++,DOCTYPE_DTD_QUOTED:R++,COMMENT_STARTING:R++,COMMENT:R++,COMMENT_ENDING:R++,COMMENT_ENDED:R++,CDATA:R++,CDATA_ENDING:R++,CDATA_ENDING_2:R++,PROC_INST:R++,PROC_INST_BODY:R++,PROC_INST_ENDING:R++,OPEN_TAG:R++,OPEN_TAG_SLASH:R++,ATTRIB:R++,ATTRIB_NAME:R++,ATTRIB_NAME_SAW_WHITE:R++,ATTRIB_VALUE:R++,ATTRIB_VALUE_QUOTED:R++,ATTRIB_VALUE_CLOSED:R++,ATTRIB_VALUE_UNQUOTED:R++,ATTRIB_VALUE_ENTITY_Q:R++,ATTRIB_VALUE_ENTITY_U:R++,CLOSE_TAG:R++,CLOSE_TAG_SAW_WHITE:R++,SCRIPT:R++,SCRIPT_ENDING:R++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach((function(t){var r=e.ENTITIES[t],n="number"==typeof r?String.fromCharCode(r):r;e.ENTITIES[t]=n})),e.STATE)e.STATE[e.STATE[S]]=S;function P(e,t,r){e[t]&&e[t](r)}function N(e,t,r){e.textNode&&O(e),P(e,t,r)}function O(e){e.textNode=D(e.opt,e.textNode),e.textNode&&P(e,"ontext",e.textNode),e.textNode=""}function D(e,t){return e.trim&&(t=t.trim()),e.normalize&&(t=t.replace(/\s+/g," ")),t}function B(e,t){return O(e),e.trackPosition&&(t+="\nLine: "+e.line+"\nColumn: "+e.column+"\nChar: "+e.c),t=new Error(t),e.error=t,P(e,"onerror",t),e}function C(e){return e.sawRoot&&!e.closedRoot&&L(e,"Unclosed root tag"),e.state!==R.BEGIN&&e.state!==R.BEGIN_WHITESPACE&&e.state!==R.TEXT&&B(e,"Unexpected end"),O(e),e.c="",e.closed=!0,P(e,"onend"),i.call(e,e.strict,e.opt),e}function L(e,t){if("object"!=typeof e||!(e instanceof i))throw new Error("bad call to strictFail");e.strict&&B(e,t)}function x(e){e.strict||(e.tagName=e.tagName[e.looseCase]());var t=e.tags[e.tags.length-1]||e,r=e.tag={name:e.tagName,attributes:{}};e.opt.xmlns&&(r.ns=t.ns),e.attribList.length=0,N(e,"onopentagstart",r)}function U(e,t){var r=e.indexOf(":")<0?["",e]:e.split(":"),n=r[0],i=r[1];return t&&"xmlns"===e&&(n="xmlns",i=""),{prefix:n,local:i}}function M(e){if(e.strict||(e.attribName=e.attribName[e.looseCase]()),-1!==e.attribList.indexOf(e.attribName)||e.tag.attributes.hasOwnProperty(e.attribName))e.attribName=e.attribValue="";else{if(e.opt.xmlns){var t=U(e.attribName,!0),r=t.prefix,n=t.local;if("xmlns"===r)if("xml"===n&&e.attribValue!==f)L(e,"xml: prefix must be bound to "+f+"\nActual: "+e.attribValue);else if("xmlns"===n&&e.attribValue!==y)L(e,"xmlns: prefix must be bound to "+y+"\nActual: "+e.attribValue);else{var i=e.tag,a=e.tags[e.tags.length-1]||e;i.ns===a.ns&&(i.ns=Object.create(a.ns)),i.ns[n]=e.attribValue}e.attribList.push([e.attribName,e.attribValue])}else e.tag.attributes[e.attribName]=e.attribValue,N(e,"onattribute",{name:e.attribName,value:e.attribValue});e.attribName=e.attribValue=""}}function V(e,t){if(e.opt.xmlns){var r=e.tag,n=U(e.tagName);r.prefix=n.prefix,r.local=n.local,r.uri=r.ns[n.prefix]||"",r.prefix&&!r.uri&&(L(e,"Unbound namespace prefix: "+JSON.stringify(e.tagName)),r.uri=n.prefix);var i=e.tags[e.tags.length-1]||e;r.ns&&i.ns!==r.ns&&Object.keys(r.ns).forEach((function(t){N(e,"onopennamespace",{prefix:t,uri:r.ns[t]})}));for(var a=0,o=e.attribList.length;a<o;a++){var s=e.attribList[a],u=s[0],l=s[1],c=U(u,!0),d=c.prefix,h=c.local,p=""===d?"":r.ns[d]||"",f={name:u,value:l,prefix:d,local:h,uri:p};d&&"xmlns"!==d&&!p&&(L(e,"Unbound namespace prefix: "+JSON.stringify(d)),f.uri=d),e.tag.attributes[u]=f,N(e,"onattribute",f)}e.attribList.length=0}e.tag.isSelfClosing=!!t,e.sawRoot=!0,e.tags.push(e.tag),N(e,"onopentag",e.tag),t||(e.noscript||"script"!==e.tagName.toLowerCase()?e.state=R.TEXT:e.state=R.SCRIPT,e.tag=null,e.tagName=""),e.attribName=e.attribValue="",e.attribList.length=0}function F(e){if(!e.tagName)return L(e,"Weird empty close tag."),e.textNode+="</>",void(e.state=R.TEXT);if(e.script){if("script"!==e.tagName)return e.script+="</"+e.tagName+">",e.tagName="",void(e.state=R.SCRIPT);N(e,"onscript",e.script),e.script=""}var t=e.tags.length,r=e.tagName;e.strict||(r=r[e.looseCase]());for(var n=r;t--;){if(e.tags[t].name===n)break;L(e,"Unexpected close tag")}if(t<0)return L(e,"Unmatched closing tag: "+e.tagName),e.textNode+="</"+e.tagName+">",void(e.state=R.TEXT);e.tagName=r;for(var i=e.tags.length;i-- >t;){var a=e.tag=e.tags.pop();e.tagName=e.tag.name,N(e,"onclosetag",e.tagName);var o={};for(var s in a.ns)o[s]=a.ns[s];var u=e.tags[e.tags.length-1]||e;e.opt.xmlns&&a.ns!==u.ns&&Object.keys(a.ns).forEach((function(t){var r=a.ns[t];N(e,"onclosenamespace",{prefix:t,uri:r})}))}0===t&&(e.closedRoot=!0),e.tagName=e.attribValue=e.attribName="",e.attribList.length=0,e.state=R.TEXT}function j(e){var t,r=e.entity,n=r.toLowerCase(),i="";return e.ENTITIES[r]?e.ENTITIES[r]:e.ENTITIES[n]?e.ENTITIES[n]:("#"===(r=n).charAt(0)&&("x"===r.charAt(1)?(r=r.slice(2),i=(t=parseInt(r,16)).toString(16)):(r=r.slice(1),i=(t=parseInt(r,10)).toString(10))),r=r.replace(/^0+/,""),i.toLowerCase()!==r?(L(e,"Invalid character entity"),"&"+e.entity+";"):String.fromCodePoint(t))}function q(e,t){"<"===t?(e.state=R.OPEN_WAKA,e.startTagPosition=e.position):b(s,t)&&(L(e,"Non-whitespace before first tag."),e.textNode=t,e.state=R.TEXT)}function G(e,t){var r="";return t<e.length&&(r=e.charAt(t)),r}R=e.STATE,String.fromCodePoint||(k=String.fromCharCode,w=Math.floor,I=function(){var e,t,r=[],n=-1,i=arguments.length;if(!i)return"";for(var a="";++n<i;){var o=Number(arguments[n]);if(!isFinite(o)||o<0||o>1114111||w(o)!==o)throw RangeError("Invalid code point: "+o);o<=65535?r.push(o):(e=55296+((o-=65536)>>10),t=o%1024+56320,r.push(e,t)),(n+1===i||r.length>16384)&&(a+=k.apply(null,r),r.length=0)}return a},Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:I,configurable:!0,writable:!0}):String.fromCodePoint=I)}(t)},4405:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseDescriptor=t.SegmentationMessage=void 0;const n=r(2607);!function(e){e[e.RESTRICT_GROUP_0=0]="RESTRICT_GROUP_0",e[e.RESTRICT_GROUP_1=1]="RESTRICT_GROUP_1",e[e.RESTRICT_GROUP_2=2]="RESTRICT_GROUP_2",e[e.NONE=3]="NONE"}(t.SegmentationMessage||(t.SegmentationMessage={}));t.parseDescriptor=e=>{const t=(e=>{const t={};let r=0;for(t.spliceDescriptorTag=e.getUint8(r++),t.descriptorLength=e.getUint8(r++),t.indentifier="";t.indentifier.length<4;)t.indentifier+=String.fromCharCode(e.getUint8(r++));return t})(e);let r=6;if(0===t.spliceDescriptorTag)r=t.descriptorLength+2,console.warn("scte35-js TODO: support spliceDescriptorTag: SpliceDescriptorTag.AVAIL_DESCRIPTOR");else if(1===t.spliceDescriptorTag)r=t.descriptorLength+2,console.warn("scte35-js TODO: support spliceDescriptorTag: SpliceDescriptorTag.DTMF_DESCRIPTOR");else if(2===t.spliceDescriptorTag){const i=t;if(i.segmentationEventId=e.getUint32(r),r+=4,i.segmentationEventCancelIndicator=!!(128&e.getUint8(r++)),!i.segmentationEventCancelIndicator){const a=e.getUint8(r++);i.programSegmentationFlag=!!(128&a),i.segmentationDurationFlag=!!(64&a),i.deliveryNotRestrictedFlag=!!(32&a),i.deliveryNotRestrictedFlag||(i.webDeliveryAllowedFlag=!!(16&a),i.noRegionalBlackoutFlag=!!(8&a),i.archiveAllowedFlag=!!(4&a),i.deviceResctrictions=3&a),i.programSegmentationFlag||(i.componentCount=e.getUint8(r++),console.warn("scte35-js TODO: segmentationDescriptor.componentCount: "+i.componentCount),r+=6*i.componentCount),i.segmentationDurationFlag&&(i.segmentationDuration=n.shiftThirtyTwoBits(e.getUint8(r++)),i.segmentationDuration+=e.getUint32(r),r+=4),i.segmentationUpidType=e.getUint8(r++),i.segmentationUpidLength=e.getUint8(r++);let o=i.segmentationUpidLength;for(i.segmentationUpid=new Uint8Array(o);o>=0;)o--,i.segmentationUpid[o]=e.getUint8(r+o);r+=i.segmentationUpidLength,i.segmentationTypeId=e.getUint8(r++),i.segmentNum=e.getUint8(r++),i.segmentsExpected=e.getUint8(r++),r<t.descriptorLength+2&&(52===i.segmentationTypeId||54===i.segmentationTypeId)&&(i.subSegmentNum=e.getUint8(r++),i.subSegmentsExpected=e.getUint8(r++))}}else 3===t.spliceDescriptorTag?(r=t.descriptorLength+2,console.warn("scte35-js TODO: support spliceDescriptorTag: SpliceDescriptorTag.TIME_DESCRIPTOR")):(console.error(`scte35-js Unrecognized spliceDescriptorTag ${t.spliceDescriptorTag}`),r=t.descriptorLength+2);return r!==t.descriptorLength+2&&console.error(`scte35-js Error reading descriptor offset @${r} of ${t.descriptorLength+2}`),t}},4125:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SCTE35=void 0;const n=r(4405),i=r(2607),a=r(5422);t.SCTE35=class{constructor(){this.buffer=a.Buffer}parseFromB64(e){const t=Uint8Array.from(this.parseBase64(e).split("").map((e=>e.charCodeAt(0))));return this.parseSCTE35Data(t)}parseFromHex(e){const t=e.match(/[a-f\d]{2}/gi)||[],r=Uint8Array.from(t.map((e=>parseInt(e,16))));return this.parseSCTE35Data(r)}parseBase64(e){return this.buffer.from(e,"base64").toString("binary")}spliceEvent(e,t,r){let n=0;if(e.spliceEventId=t.getUint32(n),n+=4,e.spliceEventCancelIndicator=!!(128&t.getUint8(n++)),e.spliceEventCancelIndicator)return n;let a=t.getUint8(n++);if(e.outOfNetworkIndicator=!!(128&a),e.programSpliceFlag=!!(64&a),e.durationFlag=!!(32&a),5===r&&(e.spliceImmediateFlag=!!(16&a)),e.programSpliceFlag)if(5!==r||e.spliceImmediateFlag)4===r&&(e.utcSpliceTime=t.getUint32(n),n+=4);else{const r=this.timeSignal(new DataView(t.buffer,t.byteOffset+n,5));e.spliceTime=r,n++,r.specified&&(n+=4)}else if(e.componentCount=t.getUint8(n++),4===r){const r=[];for(;r.length!==e.componentCount;)r.push({componentTag:t.getUint8(n++),utcSpliceTime:t.getUint32(n)}),n+=4;e.utcSpliceComponents=r}else console.warn("scte35-js TODO: support splice_insert");return e.durationFlag&&(a=t.getUint8(n++),e.breakDuration={autoReturn:!!(128&a),duration:(1&a?i.THIRTY_TWO_BIT_MULTIPLIER:0)+t.getUint32(n)},n+=4),e.uniqueProgramId=t.getUint16(n),n+=2,e.available=t.getUint8(n++),e.expected=t.getUint8(n++),n}spliceSchedule(e){const t={};t.spliceCount=e.getUint8(0),t.spliceEvents=[];let r=1;for(;t.spliceEvents.length!==t.spliceCount;){const n={};r+=this.spliceEvent(n,new DataView(e.buffer,e.byteOffset+r),4),t.spliceEvents.push(n)}return r!==e.byteLength&&console.error(`scte35-js Bad read splice_schedule actual: ${r} expected: ${e.byteLength}`),t}spliceInsert(e){const t={},r=this.spliceEvent(t,e,5);return r!==e.byteLength&&console.error(`scte35-js Bad read splice_insert actual: ${r} expected: ${e.byteLength}`),t}timeSignal(e){const t={},r=e.getUint8(0);return t.specified=!!(128&r),t.specified&&(t.pts=1&r?i.THIRTY_TWO_BIT_MULTIPLIER:0,t.pts+=e.getUint32(1)),t}parseSCTE35Data(e){const t={},r=new DataView(e.buffer);let a=0;t.tableId=r.getUint8(a++);let o=r.getUint8(a++);if(t.selectionSyntaxIndicator=!!(128&o),t.privateIndicator=!!(64&o),t.sectionLength=((15&o)<<8)+r.getUint8(a++),t.sectionLength+3!==e.byteLength)throw new Error(`Binary read error sectionLength: ${t.sectionLength} + 3 !== data.length: ${e.byteLength}`);if(t.protocolVersion=r.getUint8(a++),o=r.getUint8(a++),t.encryptedPacket=!!(128&o),t.encryptedAlgorithm=(126&o)>>1,t.encryptedPacket&&console.error(`scte35-js splice_info_section encrypted_packet ${t.encryptedAlgorithm} not supported`),t.ptsAdjustment=1&o?i.THIRTY_TWO_BIT_MULTIPLIER:0,t.ptsAdjustment+=r.getUint32(a),a+=4,t.cwIndex=r.getUint8(a++),t.tier=r.getUint8(a++)<<4,o=r.getUint8(a++),t.tier+=(240&o)>>4,t.spliceCommandLength=((15&o)<<8)+r.getUint8(a++),t.spliceCommandType=r.getUint8(a++),0!==t.spliceCommandType){const r=new DataView(e.buffer,a,t.spliceCommandLength);4===t.spliceCommandType?t.spliceCommand=this.spliceSchedule(r):5===t.spliceCommandType?t.spliceCommand=this.spliceInsert(r):6===t.spliceCommandType?t.spliceCommand=this.timeSignal(r):255===t.spliceCommandType&&console.error("scte35-js command_type private_command not supported.")}if(a+=t.spliceCommandLength,t.descriptorLoopLength=r.getUint16(a),a+=2,t.descriptorLoopLength){let r=t.descriptorLoopLength;t.descriptors=[];try{for(;r;){const i=new DataView(e.buffer,a,r),o=n.parseDescriptor(i);r-=o.descriptorLength+2,a+=o.descriptorLength+2,t.descriptors.push(o)}}catch(e){console.error(`scte35-js Error reading descriptor @ ${a}, ignoring remaing bytes: ${r} in loop.`),console.error(e),a+=r,r=0}}return t.crc=r.getUint32(a),a+=4,a!==r.byteLength&&console.error(`scte35-js Bad SCTE35 read - remaining data: ${e.slice(a).join(", ")}`),t}}},2607:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shiftThirtyTwoBits=t.THIRTY_TWO_BIT_MULTIPLIER=t.bytesToUUID=void 0;t.bytesToUUID=e=>{if(16!==e.length)throw new Error(`scte35-js Uint8Array uuid bad size: ${e.length}`);return[].map.call(e,((e,t)=>{const r=(e<=15?"0":"")+e.toString(16);return t>=4&&t<=10&&t%2==0?"-"+r:r})).join("")},t.THIRTY_TWO_BIT_MULTIPLIER=Math.pow(2,32);t.shiftThirtyTwoBits=e=>e*t.THIRTY_TWO_BIT_MULTIPLIER},5422:function(e,t,r){"use strict";const n=r(9742),i=r(645),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;t.Buffer=u,t.SlowBuffer=function(e){+e!=e&&(e=0);return u.alloc(+e)},t.INSPECT_MAX_BYTES=50;const o=2147483647;function s(e){if(e>o)throw new RangeError('The value "'+e+'" is invalid for option "size"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,u.prototype),t}function u(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return d(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e)return function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!u.isEncoding(t))throw new TypeError("Unknown encoding: "+t);const r=0|y(e,t);let n=s(r);const i=n.write(e,t);i!==r&&(n=n.slice(0,i));return n}(e,t);if(ArrayBuffer.isView(e))return function(e){if($(e,Uint8Array)){const t=new Uint8Array(e);return p(t.buffer,t.byteOffset,t.byteLength)}return h(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if($(e,ArrayBuffer)||e&&$(e.buffer,ArrayBuffer))return p(e,t,r);if("undefined"!=typeof SharedArrayBuffer&&($(e,SharedArrayBuffer)||e&&$(e.buffer,SharedArrayBuffer)))return p(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');const n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return u.from(n,t,r);const i=function(e){if(u.isBuffer(e)){const t=0|f(e.length),r=s(t);return 0===r.length||e.copy(r,0,0,t),r}if(void 0!==e.length)return"number"!=typeof e.length||K(e.length)?s(0):h(e);if("Buffer"===e.type&&Array.isArray(e.data))return h(e.data)}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return u.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function d(e){return c(e),s(e<0?0:0|f(e))}function h(e){const t=e.length<0?0:0|f(e.length),r=s(t);for(let n=0;n<t;n+=1)r[n]=255&e[n];return r}function p(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');let n;return n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),Object.setPrototypeOf(n,u.prototype),n}function f(e){if(e>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|e}function y(e,t){if(u.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||$(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let i=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return Y(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return X(e).length;default:if(i)return n?-1:Y(e).length;t=(""+t).toLowerCase(),i=!0}}function g(e,t,r){let n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return N(this,t,r);case"utf8":case"utf-8":return I(this,t,r);case"ascii":return S(this,t,r);case"latin1":case"binary":return P(this,t,r);case"base64":return w(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return O(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function m(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),K(r=+r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:_(e,t,r,n,i);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):_(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function _(e,t,r,n,i){let a,o=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;o=2,s/=2,u/=2,r/=2}function l(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){let n=-1;for(a=r;a<s;a++)if(l(e,a)===l(t,-1===n?0:a-n)){if(-1===n&&(n=a),a-n+1===u)return n*o}else-1!==n&&(a-=a-n),n=-1}else for(r+u>s&&(r=s-u),a=r;a>=0;a--){let r=!0;for(let n=0;n<u;n++)if(l(e,a+n)!==l(t,n)){r=!1;break}if(r)return a}return-1}function T(e,t,r,n){r=Number(r)||0;const i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;const a=t.length;let o;for(n>a/2&&(n=a/2),o=0;o<n;++o){const n=parseInt(t.substr(2*o,2),16);if(K(n))return o;e[r+o]=n}return o}function A(e,t,r,n){return z(Y(t,e.length-r),e,r,n)}function E(e,t,r,n){return z(function(e){const t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function b(e,t,r,n){return z(X(t),e,r,n)}function k(e,t,r,n){return z(function(e,t){let r,n,i;const a=[];for(let o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),n=r>>8,i=r%256,a.push(i),a.push(n);return a}(t,e.length-r),e,r,n)}function w(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function I(e,t,r){r=Math.min(e.length,r);const n=[];let i=t;for(;i<r;){const t=e[i];let a=null,o=t>239?4:t>223?3:t>191?2:1;if(i+o<=r){let r,n,s,u;switch(o){case 1:t<128&&(a=t);break;case 2:r=e[i+1],128==(192&r)&&(u=(31&t)<<6|63&r,u>127&&(a=u));break;case 3:r=e[i+1],n=e[i+2],128==(192&r)&&128==(192&n)&&(u=(15&t)<<12|(63&r)<<6|63&n,u>2047&&(u<55296||u>57343)&&(a=u));break;case 4:r=e[i+1],n=e[i+2],s=e[i+3],128==(192&r)&&128==(192&n)&&128==(192&s)&&(u=(15&t)<<18|(63&r)<<12|(63&n)<<6|63&s,u>65535&&u<1114112&&(a=u))}}null===a?(a=65533,o=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|1023&a),n.push(a),i+=o}return function(e){const t=e.length;if(t<=R)return String.fromCharCode.apply(String,e);let r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=R));return r}(n)}t.kMaxLength=o,u.TYPED_ARRAY_SUPPORT=function(){try{const e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(e,t,r){return function(e,t,r){return c(e),e<=0?s(e):void 0!==t?"string"==typeof r?s(e).fill(t,r):s(e).fill(t):s(e)}(e,t,r)},u.allocUnsafe=function(e){return d(e)},u.allocUnsafeSlow=function(e){return d(e)},u.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==u.prototype},u.compare=function(e,t){if($(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),$(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,n=t.length;for(let i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);let r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;const n=u.allocUnsafe(t);let i=0;for(r=0;r<e.length;++r){let t=e[r];if($(t,Uint8Array))i+t.length>n.length?(u.isBuffer(t)||(t=u.from(t)),t.copy(n,i)):Uint8Array.prototype.set.call(n,t,i);else{if(!u.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(n,i)}i+=t.length}return n},u.byteLength=y,u.prototype._isBuffer=!0,u.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)m(this,t,t+1);return this},u.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},u.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},u.prototype.toString=function(){const e=this.length;return 0===e?"":0===arguments.length?I(this,0,e):g.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){let e="";const r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},a&&(u.prototype[a]=u.prototype.inspect),u.prototype.compare=function(e,t,r,n,i){if($(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;let a=(i>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0);const s=Math.min(a,o),l=this.slice(n,i),c=e.slice(t,r);for(let e=0;e<s;++e)if(l[e]!==c[e]){a=l[e],o=c[e];break}return a<o?-1:o<a?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}const i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let a=!1;for(;;)switch(n){case"hex":return T(this,e,t,r);case"utf8":case"utf-8":return A(this,e,t,r);case"ascii":case"latin1":case"binary":return E(this,e,t,r);case"base64":return b(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,e,t,r);default:if(a)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),a=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const R=4096;function S(e,t,r){let n="";r=Math.min(e.length,r);for(let i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function P(e,t,r){let n="";r=Math.min(e.length,r);for(let i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function N(e,t,r){const n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);let i="";for(let n=t;n<r;++n)i+=Q[e[n]];return i}function O(e,t,r){const n=e.slice(t,r);let i="";for(let e=0;e<n.length-1;e+=2)i+=String.fromCharCode(n[e]+256*n[e+1]);return i}function D(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function B(e,t,r,n,i,a){if(!u.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function C(e,t,r,n,i){q(t,n,i,e,r,7);let a=Number(t&BigInt(4294967295));e[r++]=a,a>>=8,e[r++]=a,a>>=8,e[r++]=a,a>>=8,e[r++]=a;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,r}function L(e,t,r,n,i){q(t,n,i,e,r,7);let a=Number(t&BigInt(4294967295));e[r+7]=a,a>>=8,e[r+6]=a,a>>=8,e[r+5]=a,a>>=8,e[r+4]=a;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[r+3]=o,o>>=8,e[r+2]=o,o>>=8,e[r+1]=o,o>>=8,e[r]=o,r+8}function x(e,t,r,n,i,a){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function U(e,t,r,n,a){return t=+t,r>>>=0,a||x(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function M(e,t,r,n,a){return t=+t,r>>>=0,a||x(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){const r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);const n=this.subarray(e,t);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);let n=this[e],i=1,a=0;for(;++a<t&&(i*=256);)n+=this[e+a]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);let n=this[e+--t],i=1;for(;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(e,t){return e>>>=0,t||D(e,1,this.length),this[e]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(e,t){return e>>>=0,t||D(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(e,t){return e>>>=0,t||D(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(e,t){return e>>>=0,t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(e,t){return e>>>=0,t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readBigUInt64LE=Z((function(e){G(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||H(e,this.length-8);const n=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,i=this[++e]+256*this[++e]+65536*this[++e]+r*2**24;return BigInt(n)+(BigInt(i)<<BigInt(32))})),u.prototype.readBigUInt64BE=Z((function(e){G(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||H(e,this.length-8);const n=t*2**24+65536*this[++e]+256*this[++e]+this[++e],i=this[++e]*2**24+65536*this[++e]+256*this[++e]+r;return(BigInt(n)<<BigInt(32))+BigInt(i)})),u.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);let n=this[e],i=1,a=0;for(;++a<t&&(i*=256);)n+=this[e+a]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||D(e,t,this.length);let n=t,i=1,a=this[e+--n];for(;n>0&&(i*=256);)a+=this[e+--n]*i;return i*=128,a>=i&&(a-=Math.pow(2,8*t)),a},u.prototype.readInt8=function(e,t){return e>>>=0,t||D(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){e>>>=0,t||D(e,2,this.length);const r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){e>>>=0,t||D(e,2,this.length);const r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return e>>>=0,t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return e>>>=0,t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readBigInt64LE=Z((function(e){G(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||H(e,this.length-8);const n=this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),u.prototype.readBigInt64BE=Z((function(e){G(e>>>=0,"offset");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||H(e,this.length-8);const n=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(n)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+r)})),u.prototype.readFloatLE=function(e,t){return e>>>=0,t||D(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return e>>>=0,t||D(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return e>>>=0,t||D(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return e>>>=0,t||D(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){B(this,e,t,r,Math.pow(2,8*r)-1,0)}let i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){B(this,e,t,r,Math.pow(2,8*r)-1,0)}let i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,1,255,0),this[t]=255&e,t+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeBigUInt64LE=Z((function(e,t=0){return C(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),u.prototype.writeBigUInt64BE=Z((function(e,t=0){return L(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);B(this,e,t,r,n-1,-n)}let i=0,a=1,o=0;for(this[t]=255&e;++i<r&&(a*=256);)e<0&&0===o&&0!==this[t+i-1]&&(o=1),this[t+i]=(e/a>>0)-o&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);B(this,e,t,r,n-1,-n)}let i=r-1,a=1,o=0;for(this[t+i]=255&e;--i>=0&&(a*=256);)e<0&&0===o&&0!==this[t+i+1]&&(o=1),this[t+i]=(e/a>>0)-o&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||B(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeBigInt64LE=Z((function(e,t=0){return C(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),u.prototype.writeBigInt64BE=Z((function(e,t=0){return L(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),u.prototype.writeFloatLE=function(e,t,r){return U(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return U(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return M(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return M(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(!u.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);const i=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},u.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===e.length){const t=e.charCodeAt(0);("utf8"===n&&t<128||"latin1"===n)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;let i;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{const a=u.isBuffer(e)?e:u.from(e,n),o=a.length;if(0===o)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=a[i%o]}return this};const V={};function F(e,t,r){V[e]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function j(e){let t="",r=e.length;const n="-"===e[0]?1:0;for(;r>=n+4;r-=3)t=`_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function q(e,t,r,n,i,a){if(e>r||e<t){const n="bigint"==typeof t?"n":"";let i;throw i=a>3?0===t||t===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(a+1)}${n}`:`>= -(2${n} ** ${8*(a+1)-1}${n}) and < 2 ** ${8*(a+1)-1}${n}`:`>= ${t}${n} and <= ${r}${n}`,new V.ERR_OUT_OF_RANGE("value",i,e)}!function(e,t,r){G(t,"offset"),void 0!==e[t]&&void 0!==e[t+r]||H(t,e.length-(r+1))}(n,i,a)}function G(e,t){if("number"!=typeof e)throw new V.ERR_INVALID_ARG_TYPE(t,"number",e)}function H(e,t,r){if(Math.floor(e)!==e)throw G(e,r),new V.ERR_OUT_OF_RANGE(r||"offset","an integer",e);if(t<0)throw new V.ERR_BUFFER_OUT_OF_BOUNDS;throw new V.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${t}`,e)}F("ERR_BUFFER_OUT_OF_BOUNDS",(function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),F("ERR_INVALID_ARG_TYPE",(function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`}),TypeError),F("ERR_OUT_OF_RANGE",(function(e,t,r){let n=`The value of "${e}" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>2**32?i=j(String(r)):"bigint"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=j(i)),i+="n"),n+=` It must be ${t}. Received ${i}`,n}),RangeError);const W=/[^+/0-9A-Za-z-_]/g;function Y(e,t){let r;t=t||1/0;const n=e.length;let i=null;const a=[];for(let o=0;o<n;++o){if(r=e.charCodeAt(o),r>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&a.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return a}function X(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(W,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function z(e,t,r,n){let i;for(i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function $(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function K(e){return e!=e}const Q=function(){const e="0123456789abcdef",t=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let i=0;i<16;++i)t[n+i]=e[r]+e[i]}return t}();function Z(e){return"undefined"==typeof BigInt?J:e}function J(){throw new Error("BigInt not supported")}},3086:function(e,t,r){var n=r(624);function i(){n.call(this)}i.prototype=new n,e.exports=i,i.Stream=i,i.prototype.pipe=function(e,t){var r=this;function n(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function i(){r.readable&&r.resume&&r.resume()}r.on("data",n),e.on("drain",i),e._isStdio||t&&!1===t.end||(r.on("end",o),r.on("close",s));var a=!1;function o(){a||(a=!0,e.end())}function s(){a||(a=!0,"function"==typeof e.destroy&&e.destroy())}function u(e){if(l(),!this.hasListeners("error"))throw e}function l(){r.off("data",n),e.off("drain",i),r.off("end",o),r.off("close",s),r.off("error",u),e.off("error",u),r.off("end",l),r.off("close",l),e.off("end",l),e.off("close",l)}return r.on("error",u),e.on("error",u),r.on("end",l),r.on("close",l),e.on("end",l),e.on("close",l),e.emit("pipe",r),e}},489:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw o}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function o(){o=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function c(e,t,r,n){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),o=new w(n||[]);return a._invoke=function(e,t,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return R()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=E(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var h={};function p(){}function f(){}function y(){}var g={};l(g,a,(function(){return this}));var m=Object.getPrototypeOf,v=m&&m(m(I([])));v&&v!==t&&r.call(v,a)&&(g=v);var _=y.prototype=p.prototype=Object.create(g);function T(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function i(a,o,s,u){var l=d(e[a],e,o);if("throw"!==l.type){var c=l.arg,h=c.value;return h&&"object"==n(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,s,u)}),(function(e){i("throw",e,s,u)})):t.resolve(h).then((function(e){c.value=e,s(c)}),(function(e){return i("throw",e,s,u)}))}u(l.arg)}var a;this._invoke=function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}}function E(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=d(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function b(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function w(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(b,this),this.reset(!0)}function I(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return f.prototype=y,l(_,"constructor",y),l(y,"constructor",f),f.displayName=l(y,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,u,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},T(A.prototype),l(A.prototype,s,(function(){return this})),e.AsyncIterator=A,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new A(c(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},T(_),l(_,u,"Generator"),l(_,a,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=I,w.prototype={constructor:w,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return o.type="throw",o.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),k(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;k(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:I(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},e}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},u(e,t)}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=h(e);if(t){var i=h(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return c(this,r)}}function c(e,t){if(t&&("object"===n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return d(e)}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}var p=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}u((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.AdManager=void 0;var f=r(7187),y=r(248),g=r(228),m=r(9087),v=r(5417),_=r(4695),T=r(3806),A=r(5055),E=r(4125),b=r(2815),k=r(2066),w=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}(h,e);var t,r,n,a=l(h);function h(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),(t=a.call(this)).foundPreRoll=function(e){return p(d(t),void 0,void 0,o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(voplayer.Log.info("[AdManager] foundPreRoll:  currentTime,startTime ",this.player_.currentTime,e),e&&(this.lastValidPlayerTime=e),null===this.adBreaks_){t.next=6;break}return t.abrupt("return",this.checkPreroll_());case 6:this.player_.once("adBreaksLoaded",this.checkPreroll_.bind);case 7:return t.abrupt("return",Promise.resolve(!1));case 8:case"end":return t.stop()}}),t,this)})))},t.adEnded=function(e){if(t.adStarted_=!1,voplayer.Log.debug("[AdManager] adEnded: emitEvent ",e),e){var r=Object.assign({},t.adBreaksInfo_[t.currentAdBreakIdx_]),n=Object.assign({},t.adInfo_);t.currentAd_.duration&&null!==t.currentAd_.duration&&(t.adInfo_.start+=t.currentAd_.duration),t.vastReportLastProgressEvent_&&t.latestTrackingEventUri_&&t.callEventURI_(t.latestTrackingEventUri_,null),t.adTagStrategy_.adSkipped||t.adTagStrategy_.adError||t.findVASTEvent_("complete").forEach((function(e){t.player_.getConfiguration().ads.sendInfoEvent&&t.player_.emitEvent("infoEvent",{message:"ad Tracking Complete"}),t.callEventURI_(e,null)})),t.player_.emitEvent(v.Player.AD_ENDED,{adBreakInfo:r,adInfo:n,id:t.currentAd_.data.id,adType:t.currentAd_.type})}0===t.remainingAdsInBreak_.length||t.currentAd_.type===_.AdvertisementType.NONLINEAR&&t.mainContentEnded_?(voplayer.Log.debug("[AdManager] adEnded: adBreakEnded_"),t.adBreakEnded_(!0)):(voplayer.Log.debug("[AdManager] adEnded: startAd_"),t.startAd_(t.currentAd_.type))},t.updateAdsOnPlaying_=function(){voplayer.Log.debug("[AdManager] updateAdsOnPlaying_: liveHack "+t.liveHack),t.liveHack&&t.rewriteVMAPForLiveHack_()},t.updateAdsOnDurationChange_=function(){for(var e=0;e<t.adBreaks_.length;e++)if(null===t.adBreaks_[e].time){var r=t.parseOffset_(t.adBreaks_[e].vmapBreak.timeOffset);null!==r?t.adBreaks_[e].time=r:(t.adBreaks_.splice(e,1),e--)}for(var n=0;n<t.adBreaks_.length;n++)if(-1===t.adBreaks_[n].time){var i=!1,a=!1,o=0;if(void 0!==t.adBreaks_[n].vmapBreak.vastData){for(var s=0;s<t.adBreaks_[n].vmapBreak.vastData.ads.length;s++)for(var u=t.adBreaks_[n].vmapBreak.vastData.ads[s],l=0;l<u.creatives.length;l++){var c=u.creatives[l];if("linear"===c.type&&(i=!0),"nonlinear"===c.type){a=!0;var d=t.findNonLinearStaticResource_(c.variations,!1);null!==d&&(o+=t.nonLinearDuration_(d.minSuggestedDuration))}}a&&i&&(t.adBreaks_.splice(n,1),n--),a&&!i&&(t.adBreaks_[n].time=t.player_.duration-o-.5)}}},t.checkMidroll_=function(e){return p(d(t),void 0,void 0,o().mark((function t(){var r,n,i,a=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!0!==this.onGoingCheckMidroll_&&!0!==this.mainSeeking_&&!0!==this.adStarted_){t.next=4;break}return t.abrupt("return",Promise.resolve(!1));case 4:this.onGoingCheckMidroll_=!0;case 5:if(r=!1,!this.playingAdBreak_&&null!==this.adBreaks_&&0!==this.adBreaks_.length){t.next=9;break}return this.onGoingCheckMidroll_=!1,t.abrupt("return");case 9:voplayer.Log.debug("[AdManager] checkMidroll_:  event ",e),null!==e&&(this.lastValidPlayerTime=this.player_.currentTime),voplayer.Log.debug("[AdManager] checkMidroll_: this.lastValidPlayerTime,this.previousPlayerTime_ "+this.lastValidPlayerTime,this.previousPlayerTime_),n=o().mark((function e(t){var n;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.adBreaks_[t].time,voplayer.Log.debug("[AdManager] checkMidroll_:  adBreakTime= "+n),!(!1===a.adBreaks_[t].seekDisabled&&-1!==n&&n>=a.previousPlayerTime_&&n<=a.lastValidPlayerTime)||a.adBreaks_[t].lastPlayed){e.next=13;break}return voplayer.Log.debug("checkMidroll_: calling startAd_Break_"),e.next=6,a.updateAdBreak_(n);case 6:if(!a.disableTunnelIfAdUnavailable_){e.next=10;break}if(a.adBreaks_.find((function(e){return e.time===n}))){e.next=10;break}return e.abrupt("return","break");case 10:return r=a.startAd_Break_(t),voplayer.Log.debug("[AdManager] checkMidroll_: adBreakFound= "+r),e.abrupt("return","break");case 13:case"end":return e.stop()}}),e)})),i=0;case 14:if(!(i<this.adBreaks_.length)){t.next=22;break}return t.delegateYield(n(i),"t0",16);case 16:if("break"!==t.t0){t.next=19;break}return t.abrupt("break",22);case 19:i++,t.next=14;break;case 22:return r||(this.previousPlayerTime_=this.lastValidPlayerTime),this.onGoingCheckMidroll_=!1,r?this.unRegisterPlayerEvents_():this.registerPlayerEvents_(),t.abrupt("return",r);case 26:case"end":return t.stop()}}),t,this)})))},t.checkPreroll_=function(){return p(d(t),void 0,void 0,o().mark((function e(){var t,r,n;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(voplayer.Log.log("[AdManager] checkPreroll_"),t=!1,r=-1,voplayer.Log.log("[AdManager] checkPreroll_: currentTime ",this.player_.currentTime),!(this.player_.currentTime<1||this.player_.isLive())){e.next=17;break}n=0;case 6:if(!(n<this.adBreaks_.length)){e.next=16;break}if(0!==this.adBreaks_[n].time||this.adBreaks_[n].lastPlayed||!1!==this.adBreaks_[n].seekDisabled){e.next=13;break}return t=!0,e.next=11,this.updateAdBreak_(0);case 11:return r=n,e.abrupt("break",16);case 13:n++,e.next=6;break;case 16:-1!==r&&(this.disableTunnelIfAdUnavailable_?this.adBreaks_.find((function(e){return 0===e.time}))&&(t=this.startAd_Break_(r)):t=this.startAd_Break_(r));case 17:return voplayer.Log.debug("[AdManager] checkPreroll_: foundPreRoll ",t),t||this.currentAd_.type===_.AdvertisementType.NONLINEAR||this.adTagStrategy_.adBreakEnded(),this.adPrerollAdBreakAutostart=!t||(!this.player_.getConfiguration().ads.useMainPlayerVideoElement||this.player_.getConfiguration().autostart),t?this.unRegisterPlayerEvents_():this.registerPlayerEvents_(),e.abrupt("return",Promise.resolve(t));case 22:case"end":return e.stop()}}),e,this)})))},t.checkPostroll_=function(){return p(d(t),void 0,void 0,o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:voplayer.Log.log("[AdManager] checkPostroll_"),t=0;case 2:if(!(t<this.adBreaks_.length)){e.next=18;break}if(-1!==this.adBreaks_[t].time||this.adBreaks_[t].lastPlayed){e.next=14;break}return e.next=6,this.updateAdBreak_(-1);case 6:if(!this.disableTunnelIfAdUnavailable_){e.next=10;break}if(this.adBreaks_.find((function(e){return-1===e.time}))){e.next=10;break}return e.abrupt("break",18);case 10:return this.inPostRoll_=this.startAd_Break_(t),e.abrupt("break",18);case 14:this.inPostRoll_=!1;case 15:t++,e.next=2;break;case 18:return this.inPostRoll_?this.unRegisterPlayerEvents_():this.registerPlayerEvents_(),e.abrupt("return",Promise.resolve(this.inPostRoll_));case 20:case"end":return e.stop()}}),e,this)})))},t.isDash_=function(e){return"application/dash+xml"===e.mimeType},t.isHls_=function(e){return"application/x-mpegURL"===e.mimeType||"application/vnd.apple.mpegurl"},t.onPlay_=function(){t.mainContentEnded_=!1},t.onSeeking_=function(e){t.lastTimeSeeking_=e.time,t.mainSeeking_=!0},t.onSeeked_=function(e){t.applyVmapSeekLogic(e),t.mainSeeking_=!1},t.applyVmapSeekLogic=function(e){var r=e.time;if(r>t.lastTimeSeeking_)if(-1===t.vmapSeekForwardPlayUpTo_)for(var n=0;n<t.adBreaks_.length;n++)t.adBreaks_[n].time>=t.lastTimeSeeking_&&t.adBreaks_[n].time<=r&&(t.adBreaks_[n].vmapBreak.breakType===_.AdvertisementType.LINEAR?t.adBreaks_[n].seekDisabled=!1:t.adBreaks_[n].seekDisabled=!0);else for(var i=t.vmapSeekForwardPlayUpTo_,a=0;a<t.adBreaks_.length;a++)t.adBreaks_[a].time>=t.lastTimeSeeking_&&t.adBreaks_[a].time<=r&&(i>0&&t.adBreaks_[a].vmapBreak.breakType===_.AdvertisementType.LINEAR&&void 0===t.adBreaks_[a].lastPlayed?(t.adBreaks_[a].seekDisabled=!1,i--):t.adBreaks_[a].seekDisabled=!0);else if(-1===t.vmapSeekBackwardPlayUpTo_)for(var o=0;o<t.adBreaks_.length;o++)t.adBreaks_[o].time<=t.lastTimeSeeking_&&t.adBreaks_[o].time>=r&&(t.adBreaks_[o].seekDisabled=!1);else for(var s=t.vmapSeekBackwardPlayUpTo_,u=0;u<t.adBreaks_.length;u++)t.adBreaks_[u].time<=t.lastTimeSeeking_&&t.adBreaks_[u].time>=r&&(s>0&&void 0===t.adBreaks_[u].lastPlayed?(t.adBreaks_[u].seekDisabled=!1,s--):t.adBreaks_[u].seekDisabled=!0)},t.checkScte35_=function(){var e=t.player_.currentTime;if(-1!==t.scte35PreviousPlayerTime_){for(var r=0;r<t.scte35cues_.length;r++){(isNaN(t.scte35cues_[r].duration)?t.scte35cues_[r].presentationTime:t.scte35cues_[r].presentationTime+t.scte35cues_[r].duration)<t.player_.seekRange().start&&(t.scte35cues_.splice(r,1),r--)}for(var n=0;n<t.scte35cues_.length;n++){var i=t.scte35cues_[n].presentationTime,a=isNaN(t.scte35cues_[n].duration)?t.scte35cues_[n].presentationTime:t.scte35cues_[n].presentationTime+t.scte35cues_[n].duration;i>t.scte35PreviousPlayerTime_&&i<e&&!1===t.scte35cues_[n].active?(t.scte35cues_[n].active=!0,t.player_.emitEvent(v.Player.SCTE35_CUE_START,t.scte35cues_[n])):a<e&&a>t.scte35PreviousPlayerTime_&&!0===t.scte35cues_[n].active?(t.scte35cues_[n].active=!1,t.player_.emitEvent(v.Player.SCTE35_CUE_END,t.scte35cues_[n])):e<a&&e>i&&!1===t.scte35cues_[n].active?(t.scte35cues_[n].active=!0,t.player_.emitEvent(v.Player.SCTE35_CUE_START,t.scte35cues_[n])):(e>a||e<i)&&!0===t.scte35cues_[n].active&&(t.scte35cues_[n].active=!1,t.player_.emitEvent(v.Player.SCTE35_CUE_END,t.scte35cues_[n]))}t.scte35PreviousPlayerTime_=e}else t.scte35PreviousPlayerTime_=e},t.player_=e,t.player_.control.adsEnabled?(t.previousPlayerTime_=-1,t.pendingPlayPromise_=null,t.lastValidPlayerTime=-1,t.mainContentEnded_=!1,t.vmap_=null,t.adBreaks_=[],t.currentAdBreak_=null,t.currentAdBreakIdx_=-1,t.remainingAdsInBreak_=[],t.liveHack=!1,t.liveVmapRewriten_=!1,t.adTagStrategy_=null,t.createAdTagStrategy_(),t.currentAd_={data:null,url:null,mimeType:null,duration:null,timeBasedTrackingEvents:null,creative:null,type:_.AdvertisementType.NONE,previousTIme:-1},t.vmapAutoTimer_=null,t.vmapTimeout_=5,t.vastTimeout_=5,t.adRequestTimeout_=5,t.loadVastWithVmap_=!0,t.disableTunnelIfAdUnavailable_=!1,t.adCallbackForTunnelSkipping_=null,t.adHttpCustomHeaders_={},t.nonLinearDurationOverride_=-1,t.vastV20SkipAllowed_=!0,t.vastReportLastProgressEvent_=!1,t.vmapSeekBackwardPlayUpTo_=-1,t.vmapSeekForwardPlayUpTo_=-1,t.maxAdRequestParsingTime_=null,t.adRequestParsingTimer_=null,t.loadRequestInterrupted_=!1,t.voSkipGlobalStatus_=-1,t.voSkipGlobalOffsetValue_=null,t.voSkipGlobalOffsetFormat_=null,t.adRequestProtocol=null,t.adRequestUrl=null,t.detectedScte35EventsId_=[],t.scte35cues_=[],t.scte35PreviousPlayerTime_=-1,t.onGoingCheckMidroll_=!1,t.adBreaksInfo_=[],t.adInfo_={id:null,mimeType:null,start:null,duration:null,type:null,media:null,clickThroughUrl:null,skipDelay:null,url:null},t.wrapperErrorReceived_=!1,t.latestTrackingEventUri_=null,t.lastTimeSeeking_=-1,t.mainSeeking_=!1,t.timeoutSendBreakEndTrackingEvent_=null,t.adStarted_=!1,t.playerEventsRegistered_=!1,t):(voplayer.Log.warning("[AdManager] constructor: ads not enabled in license"),c(t))}return t=h,n=[{key:"dummyVMAP_",get:function(){return'<vmap:VMAP xmlns:vmap="http://www.iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="start" breakType="linear" breakId="preroll"><vmap:AdSource id="preroll-ad-1" allowMultipleAds="false" followRedirects="true"><vmap:AdTagURI templateType="vast3"><![CDATA[%%URL%%]]></vmap:AdTagURI></vmap:AdSource></vmap:AdBreak></vmap:VMAP>'}},{key:"dummyVMAP2_",get:function(){return'<vmap:VMAP xmlns:vmap="http://www.iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="start" breakType="linear" breakId="preroll"><vmap:AdSource id="preroll-ad-1" allowMultipleAds="false" followRedirects="true"><vmap:VASTAdData>%%VASTDATA%%</vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>'}}],(r=[{key:"destroy",value:function(){this.player_.removeListener("timeupdate",this.checkMidroll_),this.adTagStrategy_.destroy(),this.adTagStrategy_=null}},{key:"configure",value:function(e){voplayer.Log.debug("[AdManager] configure: config ",e),e&&(this.vastTimeout_=e.vastTimeout,this.vmapTimeout_=e.vmapTimeout,this.adRequestTimeout_=e.adRequestTimeout,this.loadVastWithVmap_=e.loadVastWithVmap,this.disableTunnelIfAdUnavailable_=e.disableTunnelIfAdUnavailable,this.adCallbackForTunnelSkipping_=e.adCallbackForTunnelSkipping,this.nonLinearDurationOverride_=e.nonLinearDurationOverride,this.vmapSkipOffsetOverride_=e.vmapSkipOffsetOverride,this.vastV20SkipAllowed_=e.vastV20SkipAllowed,this.enableVmapUpToPoliciesForStartAt_=e.enableVmapUpToPoliciesForStartAt,this.vastReportLastProgressEvent_=e.vastReportLastProgressEvent,this.adHttpCustomHeaders_=this.trimHeaders(e.adHttpCustomHeaders),this.vmapSeekBackwardPlayUpTo_=e.vmapSeekBackwardPlayUpTo,this.vmapSeekForwardPlayUpTo_=e.vmapSeekForwardPlayUpTo,this.maxAdRequestParsingTime_=e.maxAdRequestParsingTime||null,this.adTagStrategy_.configure(e))}},{key:"loadAdRequest",value:function(e,t){return p(this,void 0,void 0,o().mark((function r(){var n=this;return o().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return this.resetAdRequsestParsingTimer_(),this.loadRequestInterrupted_=!1,r.abrupt("return",new Promise((function(r,i){p(n,void 0,void 0,o().mark((function n(){var a,s,u,l,c,d,p,f,y,m,A,E=this;return o().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(voplayer.Log.debug("[AdManager] loadAdRequest: start url "+e+"adProtocol "+t),this.maxAdRequestParsingTime_&&this.maxAdRequestParsingTime_>0&&(this.adRequestParsingTimer_=setTimeout((function(){E.emit("reset"),E.loadRequestInterrupted_=!0;var e=new k.PlayerError({category:b.ErrorCategories.ADVERTISEMENT_CATEGORY,code:b.ErrorCodes.ADS_AD_REQUEST_INTERRUPTED,message:"Ad request interrupted due to timeout.",data:null});E.player_.emitEvent(v.Player.WARNING,e),E.resetAdRequsestParsingTimer_()}),this.maxAdRequestParsingTime_)),this.player_.control.adsEnabled){n.next=6;break}return voplayer.Log.warning("[AdManager] loadAdRequest: ads not enabled in license"),this.resetAdRequsestParsingTimer_(),n.abrupt("return",i());case 6:if(u={url:e,responseType:"text",headers:this.adHttpCustomHeaders_},l=null,voplayer.Log.info("[AdManager] loadAdRequest: url "+e+" adProtocol "+t),this.adRequestUrl=e,this.liveVmapRewriten_=!1,voplayer.Log.debug("[AdManager] loadAdRequest: adProtocol"+t),t&&(s="VMAP"===t||"MAST"===t?this.vmapTimeout_:"VAST"===t?this.vastTimeout_:this.adRequestTimeout_),c=null,d=null,"VAST"===t){n.next=38;break}return n.prev=16,n.next=19,T.XhrLoader.asyncLoad(u,{retryDelay:500,maxRetry:0,timeout:1e3*s});case 19:if(l=n.sent,!this.loadRequestInterrupted_){n.next=22;break}return n.abrupt("return",i());case 22:n.next=30;break;case 24:return n.prev=24,n.t0=n.catch(16),voplayer.Log.error("[AdManager] loadAdRequest: Failed to load VMAP or MAST "+n.t0),this.handleVmapMastError_(b.ErrorCodes.ADS_REQUEST_FAILED,"Failed to load "+(void 0===t?"ad request":t)+": "+n.t0,null),voplayer.Log.debug("AdManager loadAdRequest: loaderResponse reject ",n.t0),n.abrupt("return",i());case 30:if(voplayer.Log.debug("[AdManager] loadAdRequest: loaderResponse ",l),null!==l){n.next=36;break}return voplayer.Log.error("[AdManager] loadAdRequest: Failed to load VMAP or MAST "),this.handleVmapMastError_(b.ErrorCodes.ADS_REQUEST_FAILED,"Failed to load "+(void 0===t?"ad request":t),null),voplayer.Log.debug("[AdManager] loadAdRequest: loaderResponse null reject"),n.abrupt("return",i());case 36:"string"==typeof(d=l.data)&&(c=this.getAdProtocolFromUrl(d));case 38:if(voplayer.Log.debug("[AdManager] loadAdRequest:  detectedAdProtocol ",c),t&&void 0!==t){n.next=49;break}if(void 0!==c){n.next=46;break}return this.handleAdRequestError_(b.ErrorCodes.ADS_REQUEST_FAILED,"Unable to detect ad protocol"),voplayer.Log.debug("[AdManager] loadAdRequest: adProtocol 1 reject ",t),n.abrupt("return",i());case 46:this.adRequestProtocol=c;case 47:n.next=50;break;case 49:this.adRequestProtocol=t;case 50:if(voplayer.Log.debug("[AdManager] loadAdRequest: adRequestProtocol ",this.adRequestProtocol),"VAST"!==this.adRequestProtocol){n.next=67;break}if(t&&void 0!==t){n.next=64;break}if("string"!=typeof d){n.next=58;break}p=this.removeXmlTagFromVast(d),a=h.dummyVMAP2_.replace("%%VASTDATA%%",p),n.next=62;break;case 58:return voplayer.Log.error("[AdManager] loadAdRequest: Error parsing ad file content"),this.handleVastError(b.ErrorCodes.ADS_REQUEST_FAILED,"Error parsing ad file content",!1),voplayer.Log.debug("[AdManager] loadAdRequest: adProtocol 2 reject",t),n.abrupt("return",i());case 62:n.next=65;break;case 64:a=h.dummyVMAP_.replace("%%URL%%",e);case 65:n.next=94;break;case 67:if(n.prev=67,"string"!=typeof d||""===d){n.next=86;break}if("MAST"!==this.adRequestProtocol){n.next=85;break}return n.prev=70,n.next=73,this.convertMAST2VMAP_(d,e).then((function(e){a=e}));case 73:if(!this.loadRequestInterrupted_){n.next=75;break}return n.abrupt("return",i());case 75:n.next=83;break;case 77:return n.prev=77,n.t1=n.catch(70),voplayer.Log.warning("[AdManager] loadAdRequest: Error during MAST parsing: "+n.t1),this.handleVmapMastError_(b.ErrorCodes.ADS_MAST_PARSING_ERROR,"Error parsing MAST  "+JSON.stringify(n.t1),null),voplayer.Log.debug("[AdManager] loadAdRequest: convertMAST2VMAP_ reject",n.t1),n.abrupt("return",i());case 83:n.next=86;break;case 85:"VMAP"===this.adRequestProtocol&&(a=d);case 86:n.next=94;break;case 88:return n.prev=88,n.t2=n.catch(67),voplayer.Log.warning("[AdManager] loadAdRequest: Error during VMAP loading: "+n.t2),voplayer.Log.debug("[AdManager] loadAdRequest:  vmpa loading reject",n.t2),this.resetAdRequsestParsingTimer_(),n.abrupt("return",i());case 94:if(voplayer.Log.debug("[AdManager] loadAdRequest: text "+a),!a||""===a){n.next=111;break}n.prev=96,voplayer.Log.debug("[AdManager] loadAdRequest: create this.vmap_"),this.vmap_=new g.default((new window.DOMParser).parseFromString(a,"text/xml")),this.vmap_.url=e,voplayer.Log.debug("[AdManager] loadAdRequest: created this.vmap_",this.vmap_),n.next=109;break;case 103:return n.prev=103,n.t3=n.catch(96),voplayer.Log.error("[AdManager] loadAdRequest: Error parsing VMAP ",JSON.stringify(n.t3)),this.handleAdRequestError_(b.ErrorCodes.ADS_VMAP_PARSING_ERROR,"Error parsing VMAP  "+JSON.stringify(n.t3)),voplayer.Log.debug("[AdManager] loadAdRequest: VMAP reject ",JSON.stringify(n.t3)),n.abrupt("return",i());case 109:n.next=115;break;case 111:return voplayer.Log.error("[AdManager] loadAdRequest: Error loading VMAP"),this.handleAdRequestError_(b.ErrorCodes.ADS_VMAP_MAST_EMPTY_RESPONSE,"No ad breaks found in "+(void 0!==this.adRequestProtocol?this.adRequestProtocol:"ad Request")),voplayer.Log.debug("[AdManager] loadAdRequest: VMAP empty reject"),n.abrupt("return",i());case 115:if(!this.loadRequestInterrupted_){n.next=117;break}return n.abrupt("return",i());case 117:if(this.vmap_&&this.vmap_.extensions)for(f=this.parseExtensions_(this.vmap_.extensions),y=0;y<f.length;y++)"voskip"===(m=f[y]).type&&(this.vmapSkipOffsetOverride_&&this.vmapSkipOffsetOverride_.vmapSkipStatus!==_.VmapSkipStatus.NO_OVERRIDE&&(this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat===_.VmapSkipOffsetFormat.PERCENT&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue>=0&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue<=100||this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat===_.VmapSkipOffsetFormat.SECONDS&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue>=0)?(this.voSkipGlobalStatus_=this.vmapSkipOffsetOverride_.vmapSkipStatus,this.voSkipGlobalOffsetValue_=this.vmapSkipOffsetOverride_.vmapSkipOffsetValue,this.voSkipGlobalOffsetFormat_=this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat):(this.voSkipGlobalStatus_=m.status,this.voSkipGlobalOffsetValue_=m.offsetValue,this.voSkipGlobalOffsetFormat_=m.offsetFormat)),"vovmaprefresh"===m.type&&((A={duration:0,requestURL:null}).duration=m.vmapRefreshPeriod/1e3,A.requestURL=this.adRequestUrl,this.startVMAPAutoRefreshInternal_(A,!1));return voplayer.Log.debug("[AdManager] loadAdRequest: call updateAds_"),this.resetAdRequsestParsingTimer_(),n.prev=120,n.next=123,this.updateAds_();case 123:n.next=129;break;case 125:return n.prev=125,n.t4=n.catch(120),voplayer.Log.debug("[AdManager] loadAdRequest: updateAds_ reject",n.t4),n.abrupt("return",i(n.t4));case 129:return voplayer.Log.debug("[AdManager] loadAdRequest: return call updateAds_"),voplayer.Log.log("[AdManager] loadAdRequest: done"),n.abrupt("return",r());case 132:case"end":return n.stop()}}),n,this,[[16,24],[67,88],[70,77],[96,103],[120,125]])})))})));case 3:case"end":return r.stop()}}),r,this)})))}},{key:"getAdProtocolFromUrl",value:function(e){return e.includes("<vmap:VMAP")?"VMAP":e.includes("<MAST")?"MAST":e.includes("<VAST")?"VAST":void 0}},{key:"removeXmlTagFromVast",value:function(e){return e.replace(/^<\?xml[\s\S]*?>/,"")}},{key:"playinAdBreak",value:function(){return this.playingAdBreak_}},{key:"reset",value:function(){this.playingAdBreak_&&(this.sendBreakEndTrackingEvent_(),this.adTagStrategy_.adBreakEnded(),this.adTagStrategy_.stop()),this.unRegisterPlayerEvents_(),this.playingAdBreak_=!1,this.adBreaks_.splice(0,this.adBreaks_.length),this.vmap_=null,this.pendingPlayPromise_=null,this.clearVMAPAutoRefresh(),this.previousPlayerTime_=-1,this.lastValidPlayerTime=-1,this.currentAd_={data:null,url:null,mimeType:null,duration:null,timeBasedTrackingEvents:null,creative:null,type:_.AdvertisementType.NONE,previousTIme:-1},this.voSkipGlobalStatus_=-1,this.voSkipGlobalOffsetValue_=null,this.voSkipGlobalOffsetFormat_=null,this.liveHack=!1,this.liveVmapRewriten_=!1,this.adRequestProtocol=null,this.adRequestUrl=null,this.detectedScte35EventsId_=[],this.scte35cues_=[],this.scte35PreviousPlayerTime_=-1,this.onGoingCheckMidroll_=!1,this.adBreaksInfo_=[],this.adInfo_={id:null,mimeType:null,start:null,duration:null,type:null,media:null,clickThroughUrl:null,skipDelay:null,url:null},this.wrapperErrorReceived_=!1,this.adCallbackForTunnelSkipping_=null,this.adHttpCustomHeaders_={},null!==this.timeoutSendBreakEndTrackingEvent_&&(clearTimeout(this.timeoutSendBreakEndTrackingEvent_),this.timeoutSendBreakEndTrackingEvent_=null),this.disableTunnelIfAdUnavailable_=!1,this.lastTimeSeeking_=-1,this.mainSeeking_=!1,this.adStarted_=!1}},{key:"isPlayingAdBreak",get:function(){return this.playingAdBreak_}},{key:"isPlayingAd",get:function(){return!!this.adTagStrategy_&&this.adTagStrategy_.playing}},{key:"play",value:function(){return this.adTagStrategy_?this.adTagStrategy_.play():Promise.reject()}},{key:"pause",value:function(){this.adTagStrategy_&&this.adTagStrategy_.pause()}},{key:"adVolume",get:function(){return this.adTagStrategy_?this.adTagStrategy_.adVolume:0},set:function(e){this.adTagStrategy_&&this.currentAd_&&this.currentAd_.data&&(this.adTagStrategy_.adVolume=e,this.player_.emitEvent(v.Player.AD_VOLUME_CHANGE,{id:this.currentAd_.data.id,adType:this.currentAd_.type}))}},{key:"adMuted",get:function(){return!!this.adTagStrategy_&&this.adTagStrategy_.adMuted}},{key:"adMute",value:function(){var e=this;this.adTagStrategy_&&(this.adTagStrategy_.setAdMute(!0),this.player_.getConfiguration().ads.sendInfoEvent&&this.player_.emitEvent("infoEvent",{message:"ad Tracking  Mute"}),this.findVASTEvent_("mute").forEach((function(t){e.callEventURI_(t,null)})),this.player_.emitEvent(v.Player.AD_VOLUME_CHANGE,{id:this.currentAd_.data.id,adType:this.currentAd_.type}))}},{key:"adUnmute",value:function(){var e=this;this.adTagStrategy_&&(this.adTagStrategy_.setAdMute(!1),this.player_.getConfiguration().ads.sendInfoEvent&&this.player_.emitEvent("infoEvent",{message:"ad Tracking  Unmute"}),this.findVASTEvent_("unmute").forEach((function(t){e.callEventURI_(t,null)})),this.player_.emitEvent(v.Player.AD_VOLUME_CHANGE,{id:this.currentAd_.data.id,adType:this.currentAd_.type}))}},{key:"adSkipOffset",get:function(){var e,t,r,n,i,a,o,s,u=null!==(r=null===(t=null===(e=this.currentAd_)||void 0===e?void 0:e.creative)||void 0===t?void 0:t.skipDelay)&&void 0!==r?r:null;if(this.vastV20SkipAllowed_&&"2.0"===(null===(a=null===(i=null===(n=this.currentAdBreak_)||void 0===n?void 0:n.vmapBreak)||void 0===i?void 0:i.vastData)||void 0===a?void 0:a.version))return 0;if(this.currentAdBreak_&&void 0!==this.currentAdBreak_.skipStatus&&this.currentAdBreak_.skipStatus>=0)switch(this.currentAdBreak_.skipStatus){case 0:u=null;break;case 1:null===u&&(u="percent"===this.currentAdBreak_.skipOffsetFormat?Math.round(this.currentAd_.duration*this.currentAdBreak_.skipOffsetValue/100):this.currentAdBreak_.skipOffsetValue);break;case 2:u="percent"===this.currentAdBreak_.skipOffsetFormat?Math.round(this.currentAd_.duration*this.currentAdBreak_.skipOffsetValue/100):this.currentAdBreak_.skipOffsetValue}return(null===(o=this.currentAd_)||void 0===o?void 0:o.creative)&&u&&_.AdvertisementType.LINEAR===(null===(s=this.currentAd_)||void 0===s?void 0:s.type)?u:null}},{key:"skipAd",value:function(){var e=this;this.vastReportLastProgressEvent_&&this.latestTrackingEventUri_&&this.callEventURI_(this.latestTrackingEventUri_,null),this.adTagStrategy_&&(this.player_.getConfiguration().ads.sendInfoEvent&&this.player_.emitEvent("infoEvent",{message:"ad Tracking  Skip"}),this.findVASTEvent_("skip").forEach((function(t){e.callEventURI_(t,null)})),this.player_.emitEvent(v.Player.SKIP_AD,{id:this.currentAd_.data.id,adType:this.currentAd_.type}),this.adTagStrategy_.skipAd())}},{key:"adDuration",get:function(){return this.playingAdBreak_&&this.adTagStrategy_?this.adTagStrategy_.adDuration:0}},{key:"adPosition",get:function(){return this.playingAdBreak_&&this.adTagStrategy_?this.adTagStrategy_.adPosition:0},set:function(e){this.playingAdBreak_&&this.adTagStrategy_&&e>=0&&(this.adTagStrategy_.adPosition=e)}},{key:"currentAdBreakInfo",get:function(){var e,t,r;return this.playingAdBreak_?{currentAd:{skipDelay:null!==(r=null===(t=null===(e=this.currentAd_)||void 0===e?void 0:e.creative)||void 0===t?void 0:t.skipDelay)&&void 0!==r?r:null,duration:this.adTagStrategy_.adDuration,indexInAdBreak:this.currentAdBreak_.vmapBreak.vastData.ads.length-this.remainingAdsInBreak_.length},adCount:this.currentAdBreak_.vmapBreak.vastData.ads.length}:null}},{key:"currentAdBreakInformation",get:function(){return this.adBreaksInfo_&&this.adBreaksInfo_.length>0&&-1!==this.currentAdBreakIdx_&&this.adInfo_?{adBreakInfo:Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]),adInfo:Object.assign({},this.adInfo_)}:null}},{key:"adActivated",value:function(){var e,t,r,n,i,a=this;this.player_.control.adsEnabled?(null===(t=null===(e=this.currentAd_)||void 0===e?void 0:e.creative)||void 0===t?void 0:t.videoClickThroughURLTemplate)&&(this.player_.emitEvent(v.Player.AD_ACTIVATED,{id:this.currentAd_.data.id,clickThroughUrl:this.currentAd_.creative.videoClickThroughURLTemplate.url,adType:this.currentAd_.type}),this.adTagStrategy_.pause(),null!==(null!==(i=null===(n=null===(r=this.currentAd_)||void 0===r?void 0:r.creative)||void 0===n?void 0:n.videoClickTrackingURLTemplates)&&void 0!==i?i:null)&&this.currentAd_.creative.videoClickTrackingURLTemplates.forEach((function(e){a.callEventURI_(e.url,null)}))):voplayer.Log.warning("[AdManager] adActivated: ads not enabled in license")}},{key:"adPlay",value:function(){var e=this;this.player_.emitEvent(v.Player.AD_PLAY,{id:this.currentAd_.data.id,adType:this.currentAd_.type}),this.findVASTEvent_("start").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking Start"}),e.callEventURI_(t,null)}))}},{key:"adResume",value:function(){var e=this;this.player_.emitEvent(v.Player.AD_PLAY,{id:this.currentAd_.data.id,adType:this.currentAd_.type}),this.findVASTEvent_("resume").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking  Resume"}),e.callEventURI_(t,null)}))}},{key:"adPause",value:function(){var e=this;this.player_.emitEvent(v.Player.AD_PAUSE,{id:this.currentAd_.data.id,adType:this.currentAd_.type}),this.findVASTEvent_("pause").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking Pause"}),e.callEventURI_(t,null)}))}},{key:"adEnterFullscreen",value:function(){var e=this;this.findVASTEvent_("fullscreen").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking fullscreen"}),e.callEventURI_(t,null)})),this.findVASTEvent_("expand").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking Expand"}),e.callEventURI_(t,null)}))}},{key:"adExitFullscreen",value:function(){var e=this;this.findVASTEvent_("exitFullscreen").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking ExitFullscreen"}),e.callEventURI_(t,null)})),this.findVASTEvent_("collapse").forEach((function(t){e.player_.getConfiguration().ads.sendInfoEvent&&e.player_.emitEvent("infoEvent",{message:"ad Tracking Collapse"}),e.callEventURI_(t,null)}))}},{key:"adLoaded",value:function(e){var t,r,n=this;this.currentAd_.duration=e,this.adInfo_.duration=this.currentAd_.duration;var i=Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]),a=Object.assign({},this.adInfo_);this.player_.emitEvent(v.Player.AD_LOADED,{adBreakInfo:i,adInfo:a,id:this.currentAd_.data.id,duration:this.currentAd_.duration,adType:this.currentAd_.type}),voplayer.Log.log("[AdManager] adLoaded: current vast duration",this.currentAd_.duration),null!==(null!==(r=null===(t=this.currentAd_)||void 0===t?void 0:t.creative)&&void 0!==r?r:null)&&(this.currentAd_.timeBasedTrackingEvents=this.normaliseVASTProgressEvents_(this.currentAd_.creative.trackingEvents),this.findVASTEvent_("creativeView").forEach((function(e){n.player_.getConfiguration().ads.sendInfoEvent&&n.player_.emitEvent("infoEvent",{message:"ad Tracking CreativeView"}),n.callEventURI_(e,null)})))}},{key:"adTimeUpdated",value:function(e){var t=this;voplayer.Log.debug("[AdManager] adTimeUpdated:  position ",e),this.currentAd_.previousTIme<2&&e>=2&&this.sendImpression_(),this.currentAd_.timeBasedTrackingEvents&&null!==this.currentAd_.timeBasedTrackingEvents&&this.currentAd_.timeBasedTrackingEvents.forEach((function(r){t.currentAd_.previousTIme<r.time&&e>=r.time&&(t.vastReportLastProgressEvent_?t.latestTrackingEventUri_=r.uri:(t.player_.getConfiguration().ads.sendInfoEvent&&t.player_.emitEvent("infoEvent",{message:"ad Tracking "+r.eventName+("progress"===r.eventName?" "+r.position:"")}),t.callEventURI_(r.uri,null)))})),e<this.currentAd_.previousTIme&&this.findVASTEvent_("rewind").forEach((function(e){t.player_.getConfiguration().ads.sendInfoEvent&&t.player_.emitEvent("infoEvent",{message:"ad Tracking Rewind"}),t.callEventURI_(e,null)})),this.currentAd_.previousTIme=e,this.player_.emitEvent(v.Player.AD_TIMEUPDATED,{id:this.currentAd_.data.id,currentTime:e,adType:this.currentAd_.type}),voplayer.Log.debug("[AdManager] adTimeUpdated:  emit adTimeUpdated event")}},{key:"clearVMAPAutoRefresh",value:function(){voplayer.Log.debug("[AdManager] clearVMAPAutoRefresh"),null!==this.vmapAutoTimer_&&(clearInterval(this.vmapAutoTimer_),this.vmapAutoTimer_=null)}},{key:"startVMAPAutoRefresh",value:function(e){this.loadAdRequest(e.requestURL,"VMAP"),this.startVMAPAutoRefreshInternal_(e,!0)}},{key:"startVMAPAutoRefreshInternal_",value:function(e,t){var r=this;voplayer.Log.debug("[AdManager] startVMAPAutoRefreshInternal_: config",e),this.liveHack=!0,this.clearVMAPAutoRefresh(),this.vmapAutoTimer_=t?setInterval((function(){r.loadAdRequest(e.requestURL,"VMAP")}),1e3*e.duration):setTimeout((function(){r.loadAdRequest(e.requestURL,"VMAP")}),1e3*e.duration)}},{key:"notifyMainEnded",value:function(){this.playingAdBreak_&&this.currentAd_.type===_.AdvertisementType.NONLINEAR?(this.mainContentEnded_=!0,this.skipAd(),this.player_.emitEvent(v.Player.ENDED)):this.hasPostRolls_()?null!==this.adBreaks_&&this.checkPostroll_():this.player_.emitEvent(v.Player.ENDED)}},{key:"vmap",get:function(){return this.vmap_}},{key:"setPendingPlayPromise",value:function(e,t){voplayer.Log.info("[AdManager] setPendingPlayPromise"),this.pendingPlayPromise_=null===e&&null===t?null:{resolve:e,reject:t}}},{key:"sendVastErrorTrackingEvent_",value:function(e){return p(this,void 0,void 0,o().mark((function t(){var r,n;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(null!==this.currentAd_.data){t.next=2;break}return t.abrupt("return");case 2:for(r in this.currentAd_.data.errorURLTemplates)n=this.currentAd_.data.errorURLTemplates[r],e?this.callEventURI_(n,e):this.callEventURI_(n,"900");case 3:case"end":return t.stop()}}),t,this)})))}},{key:"handleAdRequestError_",value:function(e,t){this.resetAdRequsestParsingTimer_();var r=this.currentAdBreakIdx_>=0?Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]):{url:this.adRequestUrl},n=new k.PlayerError({category:b.ErrorCategories.ADVERTISEMENT_CATEGORY,code:e,message:t||"",data:{iabCode:void 0,adBreakInfo:r}});this.player_.emitEvent(v.Player.WARNING,n)}},{key:"handleVmapMastError_",value:function(e,t,r){this.resetAdRequsestParsingTimer_();var n=this.currentAdBreakIdx_>=0?Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]):{url:this.adRequestUrl};null===n.url&&(n.url=this.adRequestUrl);var i=e-1e3*b.ErrorCategories.ADVERTISEMENT_CATEGORY,a=new k.PlayerError({category:b.ErrorCategories.ADVERTISEMENT_CATEGORY,code:e,message:t||"",data:{iabCode:i>=900&&i<1100?i:void 0,adBreakInfo:n}});this.player_.emitEvent(v.Player.WARNING,a),r&&null!==r&&i>=900&&i<1100&&this.sendVmapErrorTrackingEvent_(i.toString(),r)}},{key:"handleVastError",value:function(e,t,r){this.resetAdRequsestParsingTimer_();var n=this.currentAdBreakIdx_>=0?Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]):null,i=null!==this.adInfo_?Object.assign({},this.adInfo_):{url:this.adRequestUrl};null===i.url&&(i.url=this.adRequestUrl);var a=e-1e3*b.ErrorCategories.ADVERTISEMENT_CATEGORY,o=new k.PlayerError({category:b.ErrorCategories.ADVERTISEMENT_CATEGORY,code:e,message:t||"",data:{iabCode:a>=100?a:void 0,adBreakInfo:n,adInfo:i}});this.player_.emitEvent(v.Player.WARNING,o),r&&!0===r&&a>=100&&(this.sendVastErrorTrackingEvent_(a.toString()),this.currentAdBreak_&&this.currentAdBreak_.vmapBreak&&this.sendVmapErrorTrackingEvent_(a.toString(),this.currentAdBreak_.vmapBreak))}},{key:"sendNotificationUrlRequest",value:function(e){if(voplayer.Log.debug("[AdManager] sendNotificationUrlRequest: url ",e),!this.player_.control.adsEnabled)return voplayer.Log.warning("[AdManager] sendNotificationUrlRequest: ads not enabled in license"),null;T.XhrLoader.asyncLoad({url:e,responseType:"text"},{retryDelay:500,maxRetry:0,timeout:1e3*this.vastTimeout_}).catch((function(e){voplayer.Log.warning("[AdManager] sendNotificationUrlRequest;  failed ",e)}))}},{key:"convertDashSpliceInfoSection_",value:function(e){var t={};if(e.tableId&&(t.tableId=e.tableId),e.selectionSyntaxIndicator&&(t.selectionSyntaxIndicator=e.selectionSyntaxIndicator),e.privateIndicator&&(t.privateIndicator=e.privateIndicator),e.sectionLength&&(t.sectionLength=e.sectionLength),e.protocolVersion&&(t.protocolVersion=e.protocolVersion),e.encryptedPacket&&(t.encryptedPacket=e.encryptedPacket),e.encryptedAlgorithm&&(t.encryptedAlgorithm=e.encryptedAlgorithm),e.ptsAdjustment&&(t.ptsAdjustment=e.ptsAdjustment),e.cwIndex&&(t.cwIndex=e.cwIndex),e.tier&&(t.tier=e.tier),e.spliceCommandLength&&(t.spliceCommandLength=e.spliceCommandLength),(null==e?void 0:e.spliceCommandType)&&(t.spliceCommandType=e.spliceCommandType),(null==e?void 0:e.spliceCommandType)&&(t.spliceCommandType=e.spliceCommandType),e.spliceCommand&&(t.spliceCommand=e.spliceCommand),e.descriptorLoopLength&&(t.descriptorLoopLength=e.descriptorLoopLength),e.crc&&(t.crc=e.crc),e.scte35Point&&(t.scte35Point=e.scte35Point),t.descriptors=[],e.SegmentationDescriptor_asArray&&e.SegmentationDescriptor_asArray.length>0)for(var r=0;r<e.SegmentationDescriptor_asArray.length;r++){var n=e.SegmentationDescriptor_asArray[r],i={segmentationEventId:0,segmentationEventCancelIndicator:!1,spliceDescriptorTag:2,descriptorLength:0,indentifier:""};n.descriptorLength&&(i.descriptorLength=n.descriptorLength),n.indentifier&&(i.indentifier=n.indentifier),n.segmentationEventId&&(i.segmentationEventId=n.segmentationEventId),n.segmentationEventCancelIndicator&&(i.segmentationEventCancelIndicator=n.segmentationEventCancelIndicator),n.programSegmentationFlag&&(i.programSegmentationFlag=n.programSegmentationFlag),n.segmentationDurationFlag&&(i.segmentationDurationFlag=n.segmentationDurationFlag),n.deliveryNotRestrictedFlag&&(i.deliveryNotRestrictedFlag=n.deliveryNotRestrictedFlag),n.webDeliveryAllowedFlag&&(i.webDeliveryAllowedFlag=n.webDeliveryAllowedFlag),n.noRegionalBlackoutFlag&&(i.noRegionalBlackoutFlag=n.noRegionalBlackoutFlag),n.archiveAllowedFlag&&(i.archiveAllowedFlag=n.archiveAllowedFlag),n.deviceResctrictions&&(i.deviceResctrictions=n.deviceResctrictions),n.componentCount&&(i.componentCount=n.componentCount),n.segmentationDuration&&1==n.segmentationDurationFlag&&(i.segmentationDuration=n.segmentationDuration),n.segmentationUpidType&&(i.segmentationUpidType=n.segmentationUpidType),n.segmentationUpidLength&&(i.segmentationUpidLength=n.segmentationUpidLength),n.segmentationUpid&&(i.segmentationUpid=n.segmentationUpid),n.segmentationTypeId&&(i.segmentationTypeId=n.segmentationTypeId),n.segmentNum&&(i.segmentNum=n.segmentNum),n.segmentsExpected&&(i.segmentsExpected=n.segmentsExpected),n.subSegmentNum&&(i.subSegmentNum=n.subSegmentNum),n.subSegmentsExpected&&(i.subSegmentsExpected=n.subSegmentsExpected),n.__text&&(i.private_bytes=n.__text),n.segmentationUpidContent&&(i.segmentationUpidContent=n.segmentationUpidContent),t.descriptors.push(i)}return t}},{key:"setScte35Event",value:function(e){if(-1===this.detectedScte35EventsId_.indexOf(e.id)){var t={};if("urn:scte:scte35:2013:xml"===e.eventStream.schemeIdUri)t=this.convertDashSpliceInfoSection_(e.messageData);else{if("urn:scte:scte35:2014:xml+bin"!==e.eventStream.schemeIdUri)return;try{for(var r=new E.SCTE35,n="",i=e.messageData.byteLength,a=0;a<i;a++)n+=String.fromCharCode(e.messageData[a]);var o=window.btoa(n);t=r.parseFromB64(o)}catch(e){return void voplayer.Log.warning("[AdManager] setScte35Event: error",e)}}var s=Object.assign(Object.assign({id:e.id,presentationTime:e.calculatedPresentationTime},Number.isNaN(e.duration)?{}:{duration:e.duration}),{scte35Data:t,active:!1});this.detectedScte35EventsId_.push(e.id),this.scte35cues_.push(s),this.registerPlayerEvents_(),this.player_.emitEvent(v.Player.SCTE35_CUE_DETECTED,s)}}},{key:"skipAdTunnel",value:function(e,t){e&&(this.adBreaks_=this.adBreaks_.filter((function(e){return e.time!==t})))}},{key:"resetAdRequsestParsingTimer_",value:function(){null!==this.adRequestParsingTimer_&&(clearTimeout(this.adRequestParsingTimer_),this.adRequestParsingTimer_=null)}},{key:"registerPlayerEvents_",value:function(){!1===this.playerEventsRegistered_&&(this.player_.on(v.Player.TIMEUPDATE,this.checkMidroll_),this.player_.on(v.Player.DURATION_CHANGE,this.updateAdsOnDurationChange_),this.player_.on(v.Player.PLAY,this.onPlay_),this.player_.videoElement.addEventListener("playing",this.updateAdsOnPlaying_),this.player_.on(v.Player.SEEKING,this.onSeeking_),this.player_.on(v.Player.TIMEUPDATE,this.checkScte35_),this.player_.on(v.Player.SEEKED,this.onSeeked_),this.playerEventsRegistered_=!0)}},{key:"unRegisterPlayerEvents_",value:function(){this.player_.off(v.Player.TIMEUPDATE,this.checkMidroll_),this.player_.off(v.Player.DURATION_CHANGE,this.updateAdsOnDurationChange_),this.player_.off(v.Player.PLAY,this.onPlay_),this.player_.videoElement&&this.player_.videoElement.removeEventListener("playing",this.updateAdsOnPlaying_),this.player_.off(v.Player.SEEKING,this.onSeeking_),this.player_.off(v.Player.TIMEUPDATE,this.checkScte35_),this.player_.off(v.Player.SEEKED,this.onSeeked_),this.playerEventsRegistered_=!1}},{key:"convertMAST2VMAP_",value:function(e,t){return p(this,void 0,void 0,o().mark((function r(){var n,i;return o().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=e.replace(/ns2:/g,""),r.next=3,new Promise((function(e,r){n.indexOf("http://openvideoplayer.sf.net/mast")>-1||r("not mast"),new A.Parser({explicitArray:!0}).parseString(n,(function(n,i){if(n)r(n);else{var a,o,s,u=[],l=void 0;if(i&&i.MAST&&i.MAST.triggers&&i.MAST.triggers.length>0)for(a=0;a<i.MAST.triggers.length;a++){var c=i.MAST.triggers[a];if(c.trigger&&c.trigger.length>0)for(o=0;o<c.trigger.length;o++){var d=c.trigger[o],h=d.$.id,p=void 0;if("Position"===d.startConditions[0].condition[0].$.name?p=d.startConditions[0].condition[0].$.value:"OnItemStart"===d.startConditions[0].condition[0].$.name?p="start":"OnItemEnd"===d.startConditions[0].condition[0].$.name&&(p="end"),d.sources[0].source&&d.sources[0].source.length>0){var f=void 0;for(f=0;f<d.sources[0].source.length;f++){var y=d.sources[0].source[f],g="";if("vast"===y.$.format.toLowerCase()&&y.$.uri&&""!==y.$.uri){if(y.$.uri.indexOf("http")>-1)g=y.$.uri;else if(t){var m=t.split("/");m.pop(),g=m.join("/");var v=0!==y.$.uri.indexOf("/")?"/"+y.$.uri:y.$.uri;g=g.concat(v)}var _={timeOffset:p,vastUri:g,breakId:h};u.push(_)}}}}}if(l='<?xml version="1.0" encoding="UTF-8"?><vmap:VMAP xmlns:vmap="http://www.iab.net/videosuite/vmap" version="1.0">',u.length>0)for(s=0;s<u.length;s++){var T='<vmap:AdBreak breakType="linear" breakId="__BREAK_ID__" timeOffset="__TIME_OFFSET__"><vmap:AdSource allowMultipleAds="true" id="1"><vmap:AdTagURI templateType="vast3"><![CDATA[__URI__]]></vmap:AdTagURI></vmap:AdSource></vmap:AdBreak>';switch(T=(T=T.replace("__URI__",u[s].vastUri)).replace("__BREAK_ID__",u[s].breakId),u[s].timeOffset){case"start":T=T.replace("__TIME_OFFSET__","start");break;case"end":T=T.replace("__TIME_OFFSET__","end");break;default:T=T.replace("__TIME_OFFSET__",u[s].timeOffset)}l+=T}e(l+="</vmap:VMAP>")}}))}));case 3:return i=r.sent,r.abrupt("return",i);case 5:case"end":return r.stop()}}),r)})))}},{key:"sendImpression_",value:function(){return p(this,void 0,void 0,o().mark((function e(){var t,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==this.currentAd_.data){e.next=2;break}return e.abrupt("return");case 2:for(t in this.currentAd_.data.impressionURLTemplates)r=this.currentAd_.data.impressionURLTemplates[t].url,this.callEventURI_(r,null);case 3:case"end":return e.stop()}}),e,this)})))}},{key:"createAdTagStrategy_",value:function(){null===this.adTagStrategy_&&(this.adTagStrategy_=new m.AdTagStrategy(this.player_))}},{key:"rewriteVMAPForLiveHack_",value:function(){if(voplayer.Log.debug("[AdManager] rewriteVMAPForLiveHack_: liveVmapRewriten_ ",this.liveVmapRewriten_),this.player_.isLive()){if(!1===this.liveVmapRewriten_){var e=this.player_.currentTime;voplayer.Log.debug("[AdManager] rewriteVMAPForLiveHack_: startTime ",e),this.adBreaks_.forEach((function(t){0===t.time?t.time=e+.5:t.time+=e})),this.liveVmapRewriten_=!0}}else voplayer.Log.debug("[AdManager] rewriteVMAPForLiveHack_: NOT LIVE return")}},{key:"updateAdBreak_",value:function(e){return p(this,void 0,void 0,o().mark((function t(){var r,n,i,a,s;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this.loadVastWithVmap_){t.next=36;break}r=0;case 2:if(!(r<this.adBreaks_.length)){t.next=34;break}if(this.adBreaks_[r].time!==e){t.next=31;break}t.prev=4,n=0;case 6:if(!(n<this.adBreaks_[r].vmapBreak.adSources.length)){t.next=20;break}if(void 0!==this.adBreaks_[r].vmapBreak.vastData){t.next=13;break}return t.next=10,this.getVASTDataFromAdSource_(this.adBreaks_[r].vmapBreak.adSources[n],this.adBreaks_[r]);case 10:this.adBreaks_[r].vmapBreak.vastData=t.sent,t.next=17;break;case 13:return t.next=15,this.getVASTDataFromAdSource_(this.adBreaks_[r].vmapBreak.adSources[n],this.adBreaks_[r]);case 15:for(i=t.sent,a=0;a<i.ads.length;a++)this.adBreaks_[r].vmapBreak.vastData.ads.push(i.ads[a]);case 17:n++,t.next=6;break;case 20:if(void 0!==this.adBreaks_[r].vmapBreak.vastData){t.next=24;break}return t.next=23,this.getVASTDataFromAdSource_(this.adBreaks_[r].vmapBreak.adSource,this.adBreaks_[r]);case 23:this.adBreaks_[r].vmapBreak.vastData=t.sent;case 24:t.next=31;break;case 26:t.prev=26,t.t0=t.catch(4),voplayer.Log.debug("[AdManager] updateAdBreak_: adBreak"+r+" FAILED getvast data",t.t0),"VMAP"===this.adRequestProtocol&&("Timeout"===t.t0?this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_DOCUMENT_RETRIEVAL_TIMEOUT,"VAST request timeout",this.adBreaks_[r].vmapBreak):void 0!==t.t0&&this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR,"VAST request failed",this.adBreaks_[r].vmapBreak)),this.disableTunnelIfAdUnavailable_&&(s=this.adBreaks_.filter((function(t){return t.time!==e})),this.adBreaks_=s);case 31:r++,t.next=2;break;case 34:this.checkBreakTypes_(),this.generateAdInfos_();case 36:return t.abrupt("return",Promise.resolve());case 37:case"end":return t.stop()}}),t,this,[[4,26]])})))}},{key:"applyStartTimePolicyForAds",value:function(){if(this.enableVmapUpToPoliciesForStartAt_)for(var e=0;e<this.adBreaks_.length;e++){var t=this.player_.getStartTime();null!==this.adBreaks_[e].time&&0!==this.adBreaks_[e].time&&null!==t&&t>0&&this.adBreaks_[e].time<t&&(this.adBreaks_.splice(e,1),e--)}}},{key:"rearrangeAds",value:function(){for(var e=this,t=-2,r=null,n=0;n<this.adBreaks_.length;n++)if(null!==this.adBreaks_[n].time)if(this.adBreaks_[n].time!==t)t=this.adBreaks_[n].time,r=n,this.loadVastWithVmap_||(this.adBreaks_[r].vmapBreak.adSources=[],this.adBreaks_[r].vmapBreak.adSources.push(this.adBreaks_[n].vmapBreak.adSource));else{if(void 0!==this.adBreaks_[n].vmapBreak.vastData||!this.loadVastWithVmap_)if(this.adBreaks_[n].vmapBreak.trackingEvents.forEach((function(t){e.adBreaks_[r].vmapBreak.trackingEvents.push(t)})),this.adBreaks_[r].vmapBreak.breakId!==this.adBreaks_[n].vmapBreak.breakId&&(this.adBreaks_[r].vmapBreak.breakId+=",".concat(this.adBreaks_[n].vmapBreak.breakId)),this.loadVastWithVmap_)for(var i=0;i<this.adBreaks_[n].vmapBreak.vastData.ads.length;i++)this.adBreaks_[r].vmapBreak.vastData.ads.push(this.adBreaks_[n].vmapBreak.vastData.ads[i]);else this.adBreaks_[r].vmapBreak.adSources.push(this.adBreaks_[n].vmapBreak.adSource);this.adBreaks_.splice(n,1),n--}this.adBreaks_&&this.adBreaks_.length&&this.adBreaks_.sort((function(e,t){return e.time-t.time}))}},{key:"checkBreakTypes_",value:function(){for(var e=0,t=0,r=0,n=0,i=0;i<this.adBreaks_.length;i++)if(void 0!==this.adBreaks_[i].vmapBreak.vastData){for(var a=0;a<this.adBreaks_[i].vmapBreak.vastData.ads.length;a++)for(var o=0;o<this.adBreaks_[i].vmapBreak.vastData.ads[a].creatives.length;o++)switch(n++,this.adBreaks_[i].vmapBreak.vastData.ads[a].creatives[o].type){case"linear":e++;break;case"nonlinear":t++;break;case"companion":r++}this.adBreaks_[i].vmapBreak.breakType=e===n?_.AdvertisementType.LINEAR:t===n?_.AdvertisementType.NONLINEAR:r===n?_.AdvertisementType.COMPANION:e+r===n?_.AdvertisementType.LINEAR:t+r===n?_.AdvertisementType.NONLINEAR:_.AdvertisementType.MIXED}}},{key:"generateAdInfos_",value:function(){for(var e,t,r,n=[],i=0;i<this.adBreaks_.length;i++){for(var a=this.adBreaks_[i],o=a.vmapBreak,s=(null===(e=null==o?void 0:o.vastData)||void 0===e?void 0:e.ads)||[],u=[],l=a.time,c=0,d=s.length;c<d;c++)for(var h=s[c].creatives,p=0,f=h.length;p<f;p++){var y=h[p],g=y.type,m=void 0,v=void 0,T=void 0;g===_.AdvertisementType.LINEAR?(m=this.findLinearMediaFIle_(y.mediaFiles),T={duration:y.duration,start:l,id:y.adId,mimeType:null===m?null:m.mimeType||null,type:g,media:null===m?null:m.fileURL||null,skipDelay:y.skipDelay,clickThroughUrl:(null===(t=y.videoClickThroughURLTemplate)||void 0===t?void 0:t.url)||null,url:null===m?null:m.fileURL||null}):g===_.AdvertisementType.NONLINEAR&&(T={duration:null===(v=this.findNonLinearStaticResource_(y.variations,!1))?null:this.nonLinearDuration_(v.minSuggestedDuration),start:l,id:s[c].id,mimeType:null===v?null:v.type||null,type:g,media:null===v?null:v.staticResource||null,skipDelay:null,clickThroughUrl:null===v?null:(null===(r=v.nonlinearClickThroughURLTemplate)||void 0===r?void 0:r.url)||null,url:null===v?null:v.staticResource||null}),T&&u.push(T)}n.push(u)}this.adBreakInfoList_=n}},{key:"updateAds_",value:function(){return p(this,void 0,void 0,o().mark((function e(){var t,r,n,a,s,u,l,c,d,h,p,f,y,g,m,T,A=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(voplayer.Log.debug("[AdManager] updateAds_"),this.player_.control.adsEnabled){e.next=4;break}return voplayer.Log.warning("[AdManager] updateAds_: ads not enabled in license"),e.abrupt("return");case 4:if(this.adBreaks_=[],!(null===this.vmap_||this.vmap_.adBreaks&&0===this.vmap_.adBreaks.length)){e.next=8;break}return this.handleAdRequestError_(b.ErrorCodes.ADS_VMAP_MAST_EMPTY_RESPONSE,"No ad breaks found in "+(void 0!==this.adRequestProtocol?this.adRequestProtocol:"ad Request")),e.abrupt("return");case 8:t=[],r=i(this.vmap_.adBreaks),e.prev=10,r.s();case 12:if((n=r.n()).done){e.next=28;break}if(a=n.value,!this.loadVastWithVmap_){e.next=24;break}return e.prev=15,e.next=18,this.getVASTDataFromAdSource_(a.adSource,a);case 18:a.vastData=e.sent,e.next=24;break;case 21:e.prev=21,e.t0=e.catch(15),"VMAP"===this.adRequestProtocol&&("Timeout"===e.t0?this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_DOCUMENT_RETRIEVAL_TIMEOUT,"VAST request timeout",a):void 0!==e.t0&&this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR,"VAST request failed",a),this.disableTunnelIfAdUnavailable_&&function(){var e=A.parseOffset_(a.timeOffset);-1===t.indexOf(e)&&t.push(e);var r=A.adBreaks_.filter((function(t){return t.time!==e}));A.adBreaks_=r}());case 24:if(s=this.parseOffset_(a.timeOffset),!t.includes(s)){for(u=this.parseExtensions_(a.extensions),l=-1,c=null,d=null,h=0;h<u.length;h++)"voskip"===(p=u[h]).type&&(l=p.status,c=p.offsetValue,d=p.offsetFormat);if(this.vmapSkipOffsetOverride_&&this.vmapSkipOffsetOverride_.vmapSkipStatus!==_.VmapSkipStatus.NO_OVERRIDE&&(this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat===_.VmapSkipOffsetFormat.PERCENT&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue>=0&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue<=100||this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat===_.VmapSkipOffsetFormat.SECONDS&&this.vmapSkipOffsetOverride_.vmapSkipOffsetValue>=0)?(l=this.vmapSkipOffsetOverride_.vmapSkipStatus,c=this.vmapSkipOffsetOverride_.vmapSkipOffsetValue,d=this.vmapSkipOffsetOverride_.vmapSkipOffsetFormat):-1!==this.voSkipGlobalStatus_&&(l=this.voSkipGlobalStatus_,c=this.voSkipGlobalOffsetValue_,d=this.voSkipGlobalOffsetFormat_),this.adBreaks_.push({time:s,vmapBreak:a,skipStatus:l,skipOffsetValue:c,skipOffsetFormat:d,url:this.vmap_.url,seekDisabled:!1}),null!==s&&null!==a.repeatAfter){f=this.parseRepeatDelay_(a.repeatAfter),y=s;do{y+=f,this.adBreaks_.push({time:y,vmapBreak:a,skipStatus:l,skipOffsetValue:c,skipOffsetFormat:d,url:this.vmap_.url,seekDisabled:!1})}while(y<this.player_.duration)}}case 26:e.next=12;break;case 28:e.next=33;break;case 30:e.prev=30,e.t1=e.catch(10),r.e(e.t1);case 33:return e.prev=33,r.f(),e.finish(33);case 36:this.rearrangeAds(),this.checkBreakTypes_(),this.applyStartTimePolicyForAds(),this.generateAdInfos_(),this.liveHack&&(voplayer.Log.debug("[AdManager] updateAds_: liveHack "+this.liveHack),this.rewriteVMAPForLiveHack_()),e.prev=41,this.currentAdBreak_=null,this.currentAdBreakIdx_=-1,this.adBreaksInfo_=[],this.adInfo_={id:null,mimeType:null,start:null,duration:null,type:null,media:null,clickThroughUrl:null,skipDelay:null,url:null},g=0;case 47:if(!(g<this.adBreaks_.length)){e.next=65;break}(m={activeAdIdx:null,duration:null,nbAds:null,id:null,position:null,start:null,type:null,adIds:null,adInfos:null,url:null}).id=this.adBreaks_[g].vmapBreak.breakId,e.t2=this.adBreaks_[g].time,e.next=0===e.t2?53:-1===e.t2?55:57;break;case 53:return m.position=_.AdvertisementPosition.PREROLL,e.abrupt("break",59);case 55:return m.position=_.AdvertisementPosition.POSTROLL,e.abrupt("break",59);case 57:return m.position=_.AdvertisementPosition.MIDROLL,e.abrupt("break",59);case 59:m.start=this.adBreaks_[g].time,m.url=this.adBreaks_[g].url,this.adBreaksInfo_.push(m);case 62:g++,e.next=47;break;case 65:if(T=Object.assign({},this.adBreaksInfo_),this.player_.emitEvent(v.Player.AD_BREAKS_LOADED,{adBreaksInfo:T}),!this.adCallbackForTunnelSkipping_){e.next=70;break}return e.next=70,this.adCallbackForTunnelSkipping_(T);case 70:e.next=75;break;case 72:e.prev=72,e.t3=e.catch(41),voplayer.Log.error("[AdManager] updateAds_: Failed to establish adBreakInfo ",e.t3);case 75:case"end":return e.stop()}}),e,this,[[10,30,33,36],[15,21],[41,72]])})))}},{key:"startAd_Break_",value:function(e){var t=this;voplayer.Log.debug("[AdManager] startAd_Break_: idx ",e);var r=this.adBreaks_[e];if(!this.player_.control.adsEnabled)return voplayer.Log.warning("[AdManager] startAd_Break_: ads not enabled in license"),!1;if(r.lastPlayed&&!(Date.now()-r.lastPlayed>3e5))return!1;if(this.currentAdBreak_=r,this.currentAdBreakIdx_=e,voplayer.Log.log("[AdManager] startAd_Break_: adBreak ",r),r.lastPlayed=Date.now(),"VAST"!==this.adRequestProtocol&&r.vmapBreak&&"linear"!==r.vmapBreak.breakType&&"nonlinear"!==r.vmapBreak.breakType)return voplayer.Log.warning("[AdManager] startAd_Break_: Ad break type not supported"),this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_ADBREAK_TYPE_NOT_SUPPORTED,'Ad break type "'+r.vmapBreak.breakType+'" not supported',r.vmapBreak),this.sendBreakStartTrackingEvent_(),this.sendDelayedBreakEndTrackingEvent_(),!1;var n=r.vmapBreak.vastData,i=!1,a=!1;if(!n)return this.sendBreakStartTrackingEvent_(),this.sendDelayedBreakEndTrackingEvent_(),!1;if(0===n.ads.length)return this.handleAdRequestError_(b.ErrorCodes.ADS_VAST_EMPTY_RESPONSE,"Empty VAST response"),"VMAP"===this.adRequestProtocol?this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR,"Empty VAST response",r.vmapBreak):"MAST"===this.adRequestProtocol&&this.handleVmapMastError_(b.ErrorCodes.ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR,"Empty VAST response",r.vmapBreak),this.sendBreakStartTrackingEvent_(),this.sendDelayedBreakEndTrackingEvent_(),!1;if(n.ads.forEach((function(e){e.creatives.forEach((function(r){if(r.type===_.AdvertisementType.LINEAR&&(i=!0),r.type===_.AdvertisementType.NONLINEAR&&(a=!0),r.type===_.AdvertisementType.COMPANION)for(var n in t.handleVastError(b.ErrorCodes.ADS_VAST_AD_TYPE_NOT_EXPECTED,"Companion ad not supported by player",!1),e.errorURLTemplates){var o=e.errorURLTemplates[n];t.callEventURI_(o,"200")}}))})),this.adTagStrategy_.setAdMute(this.player_.muted),this.adTagStrategy_.adVolume=this.player_.volume,i||a)this.currentAd_.type=i?_.AdvertisementType.LINEAR:_.AdvertisementType.NONLINEAR,voplayer.Log.log("[AdManager] startAd_Break_: Ad break contains linear ads"),this.remainingAdsInBreak_=this.arrayFromArgs_(n.ads),this.playingAdBreak_=!0,this.timeAdBreakStart=Date.now(),this.sendBreakStartTrackingEvent_(),this.startAd_(this.currentAd_.type)?this.liveHack||(this.adTagStrategy_.setAdMute(this.player_.muted),this.adTagStrategy_.adVolume=this.player_.volume):(voplayer.Log.warning("[AdManager] startAd_Break_: ad Failed"),this.adFailed_());else{for(var o in this.sendBreakStartTrackingEvent_(),this.handleVastError(b.ErrorCodes.ADS_VAST_LINEARITY_NOT_EXPECTED,"No linear or nonlinear ad in VAST",!1),n.ads){var s=n.ads[o];for(var u in s.errorURLTemplates){var l=s.errorURLTemplates[u];this.callEventURI_(l,"201")}}"VMAP"===this.adRequestProtocol?this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR,"No linear or nonlinear ad in VAST",r.vmapBreak):"MAST"===this.adRequestProtocol&&this.handleVmapMastError_(b.ErrorCodes.ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR,"No linear or nonlinear ad in VAST",r.vmapBreak)}return i}},{key:"arrayFromArgs_",value:function(e){return Array.prototype.slice.call(e)}},{key:"getVASTDataFromAdSource_",value:function(e,t){return p(this,void 0,void 0,o().mark((function r(){var n,i,a,s,u,l,c,d,h,p,f,g=this;return o().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(voplayer.Log.debug("[AdManager] getVASTDataFromAdSource_: adSource,adBreak ",e,t),this.player_.control.adsEnabled){r.next=4;break}return voplayer.Log.warning("[AdManager] getVASTDataFromAdSource_: ads not enabled in license"),r.abrupt("return",null);case 4:if(null===e.vastAdData){r.next=10;break}s=e.vastAdData.outerHTML,n=(new window.DOMParser).parseFromString(s,"text/xml"),voplayer.Log.debug("[AdManager] getVASTDataFromAdSource_: VAST data: xml"+n),r.next=28;break;case 10:if("vast3"===e.adTagURI.templateType||"vast2"===e.adTagURI.templateType){r.next=13;break}return this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_TEMPLATE_NOT_SUPPORTED,"VMAP Ad response type "+e.adTagURI.templateType+" not supported",t),r.abrupt("return",Promise.reject());case 13:return u={url:e.adTagURI.uri,responseType:"text"},l=null,r.prev=15,r.next=18,T.XhrLoader.asyncLoad(u,{retryDelay:500,maxRetry:0,timeout:1e3*this.vastTimeout_});case 18:l=r.sent,r.next=26;break;case 21:return r.prev=21,r.t0=r.catch(15),voplayer.Log.error("[AdManager] getVASTDataFromAdSource_: Failed downloading VAST "+r.t0),this.handleVastError(b.ErrorCodes.ADS_REQUEST_FAILED,"Failed to load VAST: "+r.t0,!1),r.abrupt("return",Promise.reject(r.t0));case 26:"string"==typeof l.data&&(c=l.data),c&&(n=(new window.DOMParser).parseFromString(c,"text/xml"));case 28:if(!n){r.next=45;break}return r.next=31,new y.VASTParser;case 31:return(a=r.sent).on("VAST-error",(function(e){var t=e.ERRORCODE,r=e.ERRORMESSAGE;303!==t&&(g.handleVastError(t+1e3*b.ErrorCategories.ADVERTISEMENT_CATEGORY,r,!1),301!==t&&302!==t||(g.wrapperErrorReceived_=!0))})),a.on("VAST-ad-parsed",(function(e){var t=e.type;"ERROR"===t&&g.handleVastError(b.ErrorCodes.ADS_VAST_XML_PARSING_ERROR,t,!1)})),r.prev=34,r.next=37,a.parseVAST(n,{allowMultipleAds:!0,timeout:1e3*this.vastTimeout_});case 37:i=r.sent,r.next=45;break;case 40:return r.prev=40,r.t1=r.catch(34),voplayer.Log.error("[AdManager] getVASTDataFromAdSource_: Failed parsing VAST "+r.t1),this.handleVastError(b.ErrorCodes.ADS_VAST_XML_PARSING_ERROR,"Failed to parse VAST",!1),r.abrupt("return",Promise.reject(r.t1));case 45:if(!(i&&i.ads.length>=1)){r.next=54;break}if(!(parseFloat(i.version)>=4)){r.next=51;break}for(d in this.handleVastError(b.ErrorCodes.ADS_VAST_REPONSE_NOT_SUPPORTED,"VAST version "+i.version+" not supported.",!1),i.ads)for(p in(h=i.ads[d]).errorURLTemplates)f=h.errorURLTemplates[p],this.callEventURI_(f,"102");return"VMAP"!==this.adRequestProtocol&&"MAST"!==this.adRequestProtocol||this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_TEMPLATE_NOT_SUPPORTED,"Ad response type not supported",t),r.abrupt("return",Promise.reject());case 51:("3.0"===i.version||"4.0"===i.version)&&i.ads.length>1&&(i.ads=i.ads.filter((function(e){return null!==e.sequence&&void 0!==e.sequence&&""!==e.sequence})),i.ads=i.ads.sort((function(e,t){return parseInt(e.sequence,10)-parseInt(t.sequence,10)}))),r.next=56;break;case 54:return!1===this.wrapperErrorReceived_?(this.handleVastError(b.ErrorCodes.ADS_VAST_EMPTY_RESPONSE,"Empty VAST response",!1),"VMAP"===this.adRequestProtocol?this.handleVmapMastError_(b.ErrorCodes.ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR,"Empty VAST response",t):"MAST"===this.adRequestProtocol&&this.handleVmapMastError_(b.ErrorCodes.ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR,"Empty VAST response",t)):this.wrapperErrorReceived_=!1,r.abrupt("return",Promise.reject());case 56:return r.abrupt("return",i);case 57:case"end":return r.stop()}}),r,this,[[15,21],[34,40]])})))}},{key:"findLinearCreativeInAd_",value:function(e){for(var t=0;t<e.creatives.length;t++)if("linear"===e.creatives[t].type)return e.creatives[t];return null}},{key:"findNonLinearCreativeInAd_",value:function(e){for(var t=0;t<e.creatives.length;t++)if("nonlinear"===e.creatives[t].type)return e.creatives[t];return null}},{key:"findLinearMediaFIle_",value:function(e){var t,r=this;if(!e)return null;var n=this.player_.getConfiguration().ads.maxAdBitrate,i=this.player_.getConfiguration().ads.maxAdResolutionHeight,a=this.adTagStrategy_.supportedMimeTypes,o=e.filter((function(e){return a.indexOf(e.mimeType)>-1}));if(!o.length)return this.handleVastError(b.ErrorCodes.ADS_VAST_MEDIAFILE_NOT_SUPPORTED,"Couldn’t find MediaFile that is supported by this video player, based on the attributes of the MediaFile element",!0),null;var s=o.filter((function(e){return"streaming"===e.deliveryType}));if(s.length>0)if(v.Player.isBrowserSafariMacOS()||v.Player.isBrowserSafariiOS()||v.Player.isBrowserSafariiPadOS()){if(void 0!==(t=s.find((function(e){return!r.isDash_(e)&&r.isHls_(e)}))))return t}else{if(void 0!==(t=s.find((function(e){return r.isDash_(e)}))))return t;if(void 0!==(t=s.find((function(e){return r.isHls_(e)}))))return t}var u=o.filter((function(e){return"progressive"===e.deliveryType}));return u=u.sort((function(e,t){return t.bitrate-e.bitrate})),n>0&&(u=u.filter((function(e){return e.bitrate<=n}))),i>0&&0===(u=u.filter((function(e){return e.height<=i}))).length&&this.handleVastError(b.ErrorCodes.ADS_VAST_SIZE_NOT_EXPECTED,"No media file found with a resolution height lower than "+i+" pixels",!0),u.length>0?u[0]:(this.handleVastError(b.ErrorCodes.ADS_VAST_FILE_NOT_FOUND,"No media file found",!0),null)}},{key:"findNonLinearStaticResource_",value:function(e,t){if(!e)return null;for(var r=this.adTagStrategy_.supportedMimeTypes,n=0;n<e.length;n++)if(r.indexOf(e[n].type)>-1)return e[n];return t&&this.handleVastError(b.ErrorCodes.ADS_VAST_NONLINEAR_RESSOURCE_NOT_SUPPORTED,"Couldn’t find NonLinear resource with supported type",!0),null}},{key:"startAd_",value:function(e){var t,r,n=this;if(voplayer.Log.debug("[AdManager] startAd_: type ",e),!this.player_.control.adsEnabled)return voplayer.Log.warning("[AdManager] startAd_: ads not enabled in license"),!1;if(this.currentAd_={data:null,url:null,mimeType:null,duration:null,timeBasedTrackingEvents:null,creative:null,type:_.AdvertisementType.NONE,previousTIme:-1},this.currentAd_.type=e,this.currentAd_.data=this.remainingAdsInBreak_.shift(),_.AdvertisementType.LINEAR===this.currentAd_.type&&(this.currentAd_.creative=this.findLinearCreativeInAd_(this.currentAd_.data)),_.AdvertisementType.NONLINEAR===this.currentAd_.type&&(this.currentAd_.creative=this.findNonLinearCreativeInAd_(this.currentAd_.data)),null===this.currentAd_.creative)return this.adEnded(!1),!1;this.currentAd_.url=null;var i=null;if(_.AdvertisementType.LINEAR===this.currentAd_.type){var a=this.findLinearMediaFIle_(this.currentAd_.creative.mediaFiles);null!==a&&(this.currentAd_.url=a.fileURL?a.fileURL:null,this.currentAd_.mimeType=a.mimeType,this.currentAd_.duration=this.currentAd_.creative.duration,i=null===(t=this.currentAd_.creative.videoClickThroughURLTemplate)||void 0===t?void 0:t.url)}if(_.AdvertisementType.NONLINEAR===this.currentAd_.type){var o=this.findNonLinearStaticResource_(this.currentAd_.creative.variations,!0);null!==o&&(this.currentAd_.url=o.staticResource?o.staticResource:null,this.currentAd_.mimeType=o.type,this.currentAd_.duration=this.nonLinearDuration_(o.minSuggestedDuration),i=null===(r=o.nonlinearClickThroughURLTemplate)||void 0===r?void 0:r.url)}if(null===this.currentAd_.url)return this.adEnded(!1),!1;if(this.currentAd_.previousTIme=-1,null!==this.adTagStrategy_){this.adInfo_.id=this.currentAd_.data.id,this.adInfo_.mimeType=this.currentAd_.mimeType,this.adInfo_.duration=this.currentAd_.duration,this.adInfo_.media=this.currentAd_.url,this.adInfo_.type=this.currentAd_.type,this.adInfo_.clickThroughUrl=i,this.adInfo_.skipDelay=this.adSkipOffset,this.adInfo_.url=this.currentAd_.url,this.adBreaksInfo_[this.currentAdBreakIdx_].activeAdIdx++;var s=Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]),u=Object.assign({},this.adInfo_);this.player_.emitEvent(v.Player.AD_STARTED,{adBreakInfo:s,adInfo:u,id:this.currentAd_.data.id,clickThroughUrl:i,adType:this.currentAd_.type,media:this.currentAd_.url}),this.adStarted_=!0,this.adTagStrategy_.startAd(this.currentAd_).then((function(){voplayer.Log.debug("[AdManager] startAd_: resolve play promise"),n.pendingPlayPromise_&&n.pendingPlayPromise_.resolve()})).catch((function(e){voplayer.Log.debug("[AdManager] startAd_: reject play promise with",e),n.pendingPlayPromise_&&n.pendingPlayPromise_.reject(e)})).then((function(){n.pendingPlayPromise_&&(n.pendingPlayPromise_=null)}))}return!0}},{key:"adFailed_",value:function(){voplayer.Log.debug("[AdManager] adFailed_"),this.adStarted_=!1,0===this.remainingAdsInBreak_.length?this.adBreakEnded_(!0):this.startAd_(this.currentAd_.type)}},{key:"adBreakEnded_",value:function(e){return p(this,void 0,void 0,o().mark((function t(){var r=this;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(voplayer.Log.debug("[AdManager] adBreakEnded_ : adWasPlayed "+e),null!==this.currentAdBreak_&&(this.timeAdBreakEnd=Date.now(),this.sendBreakEndTrackingEvent_()),this.currentAdBreak_=null,this.currentAdBreakIdx_=-1,this.playingAdBreak_&&((void 0===e||e)&&(this.player_.muted=this.adMuted),this.playingAdBreak_=!1,null!==this.adTagStrategy_&&this.adTagStrategy_.adBreakEnded()),!this.inPostRoll_){t.next=12;break}return t.next=8,this.checkPostroll_();case 8:t.sent||this.adTagStrategy_.postrollReloadFeatureContent().then((function(){r.player_.emitEvent(v.Player.ENDED)})),t.next=16;break;case 12:return t.next=14,this.checkMidroll_(null);case 14:if(!t.sent&&_.AdvertisementType.LINEAR===this.currentAd_.type)try{this.adTagStrategy_.resumeFeatureContent()}catch(e){voplayer.Log.error("[AdManager] adBreakEnded_: resumeFeatureContent failed with",e)}case 16:case"end":return t.stop()}}),t,this)})))}},{key:"hasPostRolls_",value:function(){return voplayer.Log.debug("[AdManager] hasPostRolls_"),null!==this.adBreaks_&&this.adBreaks_.some((function(e){return-1===e.time}))}},{key:"nonLinearDuration_",value:function(e){return this.nonLinearDurationOverride_&&this.nonLinearDurationOverride_>=0?this.nonLinearDurationOverride_:e}},{key:"findVMAPEvent_",value:function(e,t){voplayer.Log.debug("[AdManager] findVMAPEvent_: vmapBreak,eventName "+e,t);var r=[];if(null!==e&&void 0!==e.trackingEvents&&void 0!==t)for(var n=0;n<e.trackingEvents.length;n++){var i=e.trackingEvents[n];i.event===t&&r.push(i.uri)}return r}},{key:"findVASTEvent_",value:function(e){return voplayer.Log.debug("[AdManager] findVASTEvent_: eventName "+e),null===this.currentAd_.creative||null===this.currentAd_.creative.trackingEvents?[]:this.currentAd_.creative.trackingEvents[e]||[]}},{key:"normaliseVASTProgressEvents_",value:function(e){var t=this;voplayer.Log.debug("[AdManager] normaliseVASTProgressEvents_: trackingEvents "+e);var r=[],n=Object.keys(e);return null===n?[]:(n.forEach((function(n){var i=n.split("-");if("progress"===i[0]&&_.AdvertisementType.LINEAR===t.currentAd_.type)if("%"===i[1].substr(-1,1)){var a=t.currentAd_.duration/100*parseInt(i[1].substr(0,i[1].length-1),10);e[n].forEach((function(e){r.push({time:a,uri:e,eventName:"progress",position:i[1]})}))}else e[n].forEach((function(e){r.push({time:parseFloat(i[1]),uri:e,eventName:"progress",position:i[1]})}));else{var o,s=i[0].toLowerCase();switch(s){case"firstquartile":o=.25*t.currentAd_.duration,e[n].forEach((function(e){r.push({time:o,uri:e,eventName:s})}));break;case"midpoint":o=.5*t.currentAd_.duration,e[n].forEach((function(e){r.push({time:o,uri:e,eventName:s})}));break;case"thirdquartile":o=.75*t.currentAd_.duration,e[n].forEach((function(e){r.push({time:o,uri:e,eventName:s})}))}}})),r)}},{key:"sendBreakStartTrackingEvent_",value:function(){var e=this,t=this.currentAdBreakIdx_;if(t<0||t>=this.adBreaks_.length)voplayer.Log.warning("[AdManager] sendBreakStartTrackingEvent_:  invalid adBreak index "+t);else{var r=[];if(this.adBreaks_[t].vmapBreak.vastData&&this.adBreaks_[t].vmapBreak.vastData.ads){var n,a=i(this.adBreaks_[t].vmapBreak.vastData.ads);try{for(a.s();!(n=a.n()).done;){var o=n.value;r.push(o.id)}}catch(e){a.e(e)}finally{a.f()}for(var s=0;s<this.adBreaks_[t].vmapBreak.vastData.ads.length;s++)for(var u=0;u<this.adBreaks_[t].vmapBreak.vastData.ads[s].creatives.length;u++){if(this.adBreaks_[t].vmapBreak.vastData.ads[s].creatives[u].type===_.AdvertisementType.LINEAR)this.adBreaksInfo_[t].duration+=this.adBreaks_[t].vmapBreak.vastData.ads[s].creatives[u].duration,null===this.adBreaksInfo_[t].type?this.adBreaksInfo_[t].type=_.AdvertisementType.LINEAR:this.adBreaksInfo_[t].type===_.AdvertisementType.NONLINEAR&&(this.adBreaksInfo_[t].type=_.AdvertisementType.MIXED);else if(this.adBreaks_[t].vmapBreak.vastData.ads[s].creatives[u].type===_.AdvertisementType.NONLINEAR){var l=this.findNonLinearStaticResource_(this.adBreaks_[t].vmapBreak.vastData.ads[s].creatives[u].variations,!1);null!==l&&(this.adBreaksInfo_[t].duration+=l.minSuggestedDuration),null===this.adBreaksInfo_[t].type?this.adBreaksInfo_[t].type=_.AdvertisementType.NONLINEAR:this.adBreaksInfo_[t].type===_.AdvertisementType.LINEAR&&(this.adBreaksInfo_[t].type=_.AdvertisementType.MIXED)}this.adBreaksInfo_[t].nbAds++}}this.adInfo_.start=this.adBreaksInfo_[t].start,this.adBreaksInfo_[t].adIds=r,this.adBreaksInfo_[t].adInfos=this.adBreakInfoList_[t];var c=Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]);this.player_.emitEvent(v.Player.AD_BREAK_STARTED,{adBreakInfo:c,breakId:this.adBreaks_[t].vmapBreak.breakId,breakAdIds:r,position:this.adBreaks_[t].time,breakType:this.adBreaks_[t].vmapBreak.breakType})}null!==this.currentAdBreak_&&this.findVMAPEvent_(this.currentAdBreak_.vmapBreak,"breakStart").forEach((function(t){e.callEventURI_(t,null)}))}},{key:"sendDelayedBreakEndTrackingEvent_",value:function(){var e=this;null!==this.timeoutSendBreakEndTrackingEvent_&&(clearTimeout(this.timeoutSendBreakEndTrackingEvent_),this.timeoutSendBreakEndTrackingEvent_=null),this.timeoutSendBreakEndTrackingEvent_=setTimeout((function(){e.sendBreakEndTrackingEvent_(),e.timeoutSendBreakEndTrackingEvent_=null}),2e3)}},{key:"sendBreakEndTrackingEvent_",value:function(){var e=this;if(null!==this.currentAdBreak_){var t=Object.assign({},this.adBreaksInfo_[this.currentAdBreakIdx_]);this.player_.emitEvent(v.Player.AD_BREAK_ENDED,{adBreakInfo:t,breakId:this.currentAdBreak_.vmapBreak.breakId,breakType:this.currentAdBreak_.vmapBreak.breakType}),this.findVMAPEvent_(this.currentAdBreak_.vmapBreak,"breakEnd").forEach((function(t){e.callEventURI_(t,null)})),this.adBreaksInfo_[this.currentAdBreakIdx_].activeAdIdx=-1,this.adInfo_={id:null,mimeType:null,start:null,duration:null,type:null,media:null,clickThroughUrl:null,skipDelay:null,url:null},this.currentAdBreak_=null,this.currentAdBreakIdx_=-1}}},{key:"sendVmapErrorTrackingEvent_",value:function(e,t){var r=this;null!==t&&this.findVMAPEvent_(t,"error").forEach((function(t){r.callEventURI_(t,e)}))}},{key:"callEventURI_",value:function(e,t){e=(e=(e=(e=e.replace("[CONTENTPLAYHEAD]",this.currentAd_.previousTIme.toString())).replace("[ADMMT]",this.currentAd_.previousTIme.toString())).replace("[CACHEBUSTING]",(1e8*Math.random()).toString().substring(0,8))).replace("[ASSETURI]",this.currentAd_.url),null!==t&&(e=(e=(e=e.replace("[ERRORCODE]",t)).replace("[ERROR_CODE]",t)).replace("[ERROR_MESSAGE]","")),voplayer.Log.debug("[AdManager] callEventURI_: uri "+e),T.XhrLoader.asyncLoad({url:e,responseType:"text"},{retryDelay:500,maxRetry:0,timeout:1e3*this.vastTimeout_}).catch((function(e){voplayer.Log.warning("[AdManager] callEventURI_:  Failed",e)}))}},{key:"parseRepeatDelay_",value:function(e){var t=e.split(":"),r=60*parseInt(t[0],10)*60;return r+=60*parseInt(t[1],10),r+=parseInt(t[2].split(".")[0],10)}},{key:"parseOffset_",value:function(e){if(null!==e&&"%"===e.substr(-1,1)){var t=parseFloat(e.substr(0,e.length));return 0===t?0:isNaN(this.player_.duration)?null:this.player_.duration*(t/100)}return null!==e&&e.indexOf(":")>-1?this.parseRepeatDelay_(e):"start"===e?0:"end"===e?-1:(voplayer.Log.warning("[AdManager] parseOffset_: unexpected offset string "+e),null)}},{key:"parseExtensions_",value:function(e){var t=[];try{for(var r={type:null,status:null,offsetValue:null,offsetFormat:null,vmapRefreshPeriod:null},n=0;n<e.length;n++)e[n].attributes&&"voskip"===e[n].attributes.type&&(r.type="voskip",r.status=parseInt(e[n].children["voextension:Skip"].attributes.status,10),r.offsetValue=parseInt(e[n].children["voextension:Skip"].attributes.offsetValue,10),r.offsetFormat=e[n].children["voextension:Skip"].attributes.offsetFormat.toLowerCase(),r.offsetValue>0&&("percent"===r.offsetFormat||"seconds"===r.offsetFormat)&&t.push(r)),e[n].attributes&&"vovmaprefresh"===e[n].attributes.type&&(r.type="vovmaprefresh",r.vmapRefreshPeriod=parseInt(e[n].children["voextension:Vmaprefresh"].attributes.vmapAutoRefreshPeriod,10),r.vmapRefreshPeriod>0&&t.push(r))}catch(e){voplayer.Log.warning("[AdManager] parseExtensions_: Failed to parse VMAP extensions ",e)}return t}},{key:"trimHeaders",value:function(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=r.trim(),i=e[r].trim();t[n]=i}return t}}])&&s(t.prototype,r),n&&s(t,n),Object.defineProperty(t,"prototype",{writable:!1}),h}(f.EventEmitter);t.AdManager=w},9087:function(e,t,r){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(){i=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function c(e,t,r,n){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),o=new w(n||[]);return a._invoke=function(e,t,r){var n="suspendedStart";return function(i,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw a;return R()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=E(o,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var h={};function p(){}function f(){}function y(){}var g={};l(g,o,(function(){return this}));var m=Object.getPrototypeOf,v=m&&m(m(I([])));v&&v!==t&&r.call(v,o)&&(g=v);var _=y.prototype=p.prototype=Object.create(g);function T(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function i(a,o,s,u){var l=d(e[a],e,o);if("throw"!==l.type){var c=l.arg,h=c.value;return h&&"object"==n(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,s,u)}),(function(e){i("throw",e,s,u)})):t.resolve(h).then((function(e){c.value=e,s(c)}),(function(e){return i("throw",e,s,u)}))}u(l.arg)}var a;this._invoke=function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return a=a?a.then(n,n):n()}}function E(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=d(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function b(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function w(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(b,this),this.reset(!0)}function I(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:R}}function R(){return{value:void 0,done:!0}}return f.prototype=y,l(_,"constructor",y),l(y,"constructor",f),f.displayName=l(y,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,l(e,u,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},T(A.prototype),l(A.prototype,s,(function(){return this})),e.AsyncIterator=A,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new A(c(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},T(_),l(_,u,"Generator"),l(_,o,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=I,w.prototype={constructor:w,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return o.type="throw",o.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),k(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;k(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:I(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},e}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var o=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(i,a){function o(e){try{u(n.next(e))}catch(e){a(e)}}function s(e){try{u(n.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,s)}u((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.AdTagStrategy=void 0;var s=r(5417),u=r(4695),l=r(2815),c=function(){function e(t){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.configure=function(e){r.useMainPlayerVideoElt_=e.useMainPlayerVideoElement,r.mainPlayerResumeCallback_=e.mainPlayerResumeCallback,r.mainPlayerReloadCallback_=e.mainPlayerReloadCallback,r.nonLinearClosable_=e.nonLinearClosable,r.seekToAllowed_=e.seekToAllowed},this.destroy=function(){r.unregisterAdVideoEvents_(),r.resetAdBanner_(),r.adBannerImgContainer_&&(r.adBannerImgContainer_.parentNode&&r.adBannerImgContainer_.parentNode.removeChild(r.adBannerImgContainer_),r.adBannerImgContainer_),r.adBannerContainer_&&(r.adBannerContainer_.parentNode&&r.adBannerContainer_.parentNode.removeChild(r.adBannerContainer_),r.adBannerContainer_),r.background_&&(r.background_.parentNode&&r.background_.parentNode.removeChild(r.background_),r.background_),r.adPlayer_.destroy(),r.adPlayer_=null,r.adVideoElement_&&(r.adVideoElement_.addEventListener("emptied",r.handleEmptiedEvent),r.adVideoElement_.pause(),r.adVideoElement_.srcObject=null,r.adVideoElement_.removeAttribute("src"),r.adVideoElement_.load())},this.handleEmptiedEvent=function(){r.adVideoElement_.removeEventListener("emptied",r.handleEmptiedEvent),r.adVideoElement_=null},this.stop=function(){r.isActive_=!1,r.unregisterAdVideoEvents_(),r.adPlayer_.pause(),r.adPlayer_.reset(),r.liveAutoRefreshPauseTIme_=null,r.resetAdBanner_(),null!==r.background_&&(r.background_.style.display="none"),r.player_.adManager.liveHack&&(r.player_.muted=r.initiallyMuted_),voplayer.Log.debug("[AdTagStrategy] stop end")},this.skipAd=function(){voplayer.Log.debug("[AdTagStrategy] skipAd"),r.adSkipped_=!0,r.ended_()},this.adBreakEnded=function(){voplayer.Log.debug("[AdTagStrategy] adBreakEnded"),r.isActive_=!1,r.player_.adManager.liveHack&&(r.player_.muted=r.initiallyMuted_)},this.setAdMute=function(e){voplayer.Log.debug("[AdTagStrategy] setAdMute: mute ",e),r.adPlayer_.muted=e},this.pause=function(){r.isActive_&&(u.AdvertisementType.LINEAR===r.adType_?(r.adPlayer_.pause(),r.adPlayer_.currentTime<r.adDuration_&&r.player_.adManager.adPause()):(r.player_.corePlayer.pause(),r.pauseAdBanner_(),r.player_.adManager.adPause()),r.adPaused_=!0)},this.play=function(){if(r.player_.control.adsEnabled){if(!r.isActive_)return null;var e=null;return u.AdvertisementType.LINEAR===r.adType_?e=r.adPlayer_.play():(r.resumeAdBanner_(),e=r.player_.corePlayer.play()),r.adPaused_&&!0===r.player_.adManager.adPrerollAdBreakAutostart?r.player_.adManager.adResume():(r.player_.adManager.adPlay(),r.player_.adManager.adPrerollAdBreakAutostart=!0),r.adPaused_=!1,e}voplayer.Log.warning("[AdTagStrategy] play: ads not enabled in license")},this.createAdVideoElement_=function(){if(voplayer.Log.debug("[AdTagStrategy] createAdVideoElement_: start"),r.player_.control.adsEnabled){if(null===r.adVideoElement_){var e=r.player_.videoElement;r.background_=document.createElement("div"),r.background_.className="sqp-ad-container",r.background_.style.backgroundColor="black",r.background_.style.width="100%",r.background_.style.height="100%",r.background_.style.position="absolute",r.background_.style.top="0",r.background_.style.left="0",r.background_.style.display="none",r.useMainPlayerVideoElt_?r.adVideoElement_=r.player_.videoElement:(r.adVideoElement_=document.createElement("video"),r.adVideoElement_.style.width="100%",r.adVideoElement_.style.height="100%",r.adVideoElement_.style.position="absolute",r.adVideoElement_.style.top="0",r.adVideoElement_.style.left="0",r.adVideoElement_.style.zIndex="0",e.parentNode.insertBefore(r.background_,e.nextSibling),r.background_.appendChild(r.adVideoElement_),voplayer.Log.log("[AdTagStrategy] createAdVideoElement_: adVideoElement_ created")),null!==r.adBannerContainer_&&r.adBannerContainer_.parentNode.removeChild(r.adBannerContainer_),r.adBannerContainer_=document.createElement("div"),r.adBannerContainer_.className="sqp-ad-banner-container",r.adBannerContainer_.style.width="100%",r.adBannerContainer_.style.height="100%",r.adBannerContainer_.style.position="absolute",r.adBannerContainer_.style.top="0",r.adBannerContainer_.style.left="0",r.adBannerContainer_.style.display="flex",r.adBannerContainer_.style.justifyContent="center",e.parentNode.insertBefore(r.adBannerContainer_,e.nextSibling),voplayer.Log.log("[AdTagStrategy] createAdVideoElement_: adBannerContainer_ created"),r.adPlayer_=new s.Player(r.adVideoElement_,!0),r.adPlayer_.license=r.player_.control.license}}else voplayer.Log.warning("[AdTagStrategy] createAdVideoElement_: ads not enabled in license")},this.adSeeking_=function(){r.player_.emitEvent(s.Player.AD_SEEKING),r.adPlayer_.currentTime},this.adSeeked_=function(){r.player_.emitEvent(s.Player.AD_SEEKED),r.adPlayer_.currentTime},this.timeUpdated_=function(){r.player_.adManager.adTimeUpdated(r.adPlayer_.currentTime)},this.durationChange_=function(){r.adDuration_=r.adPlayer_.duration},this.pause_=function(){r.isActive_&&!1===r.adPaused_&&(u.AdvertisementType.LINEAR===r.adType_?r.adPlayer_.currentTime<r.adDuration_&&r.player_.adManager.adPause():(r.pauseAdBanner_(),r.player_.adManager.adPause()),r.adPaused_=!0)},this.ended_=function(){if(voplayer.Log.debug("[AdTagStrategy] ended_: isActive_ "+r.isActive_),u.AdvertisementType.LINEAR===r.adType_){if(r.adPlayer_.reset(),!r.isActive_)return;r.unregisterAdVideoEvents_()}u.AdvertisementType.NONLINEAR===r.adType_&&r.resetAdBanner_(),r.player_.adManager.adEnded(!0)},this.playerWarning_=function(e){voplayer.Log.warning("[AdTagStrategy] playerWarning_: Ad Warning ",e),r.isActive_&&e.code&&e.code===l.ErrorCodes.PLAY_PROMISE_INTERRUPTED&&r.player_.emitEvent(s.Player.WARNING,e)},this.playerError_=function(e){if(voplayer.Log.warning("[AdTagStrategy] playerError_: error ",e),r.isActive_){if(e.code&&1001===e.code)r.player_.adManager.handleVastError(l.ErrorCodes.ADS_VAST_FILE_NOT_FOUND,"Unable to access media file, http error : "+e.code,!0);else if(e.data&&e.data.status)switch(e.data.status){case 404:case 401:case 403:case 500:r.player_.adManager.handleVastError(l.ErrorCodes.ADS_VAST_FILE_NOT_FOUND,"Unable to access media file, http error : "+e.data.status,!0);break;default:r.player_.adManager.handleVastError(l.ErrorCodes.ADS_VAST_PROBLEM_DISPLAYING_MEDIAFILE,"Player Error : "+JSON.stringify(e),!0)}else r.player_.adManager.handleVastError(l.ErrorCodes.ADS_VAST_PROBLEM_DISPLAYING_MEDIAFILE,"Player Error : "+JSON.stringify(e),!0);r.adError_=!0,r.ended_()}},this.adLoaded_=function(){voplayer.Log.debug("[AdTagStrategy] Ad adLoaded "+r.isActive_),r.isActive_&&(u.AdvertisementType.LINEAR===r.adType_&&(r.adDuration_=r.adPlayer_.duration),r.player_.adManager.adLoaded(r.adDuration_))},this.createAdBanner_=function(e,t){return voplayer.Log.debug("[AdTagStrategy] createAdBanner_:fileURL,duration ",e,t),new Promise((function(n,i){r.adBannerImgContainer_=document.createElement("div"),r.adBannerImgContainer_.className="sqp-ad-banner-img-container",r.adBannerImgContainer_.style.position="absolute",r.adBannerImgContainer_.style.maxWidth="94%",r.adBannerImgContainer_.style.bottom="60px",r.adBannerImgContainer_.style.height="25%",r.adBannerImgContainer_.style.zIndex="8",r.adBannerContainer_.appendChild(r.adBannerImgContainer_),r.adBannerImg_=document.createElement("img"),r.adBannerImg_.className="sqp-ad-banner-img",r.adBannerImg_.style.width="100%",r.adBannerImg_.style.height="100%",r.adBannerImgContainer_.appendChild(r.adBannerImg_),r.nonLinearClosable_&&(r.closeButton_=document.createElement("button"),r.closeButton_.className="sqp-nonlinear-close-button",r.closeButton_.innerText="X",r.closeButton_.style.position="absolute",r.closeButton_.style.top="0%",r.closeButton_.style.right="0%",r.closeButton_.style.backgroundColor="rgba(0, 0, 0, 0.5)",r.closeButton_.style.color="white",r.closeButton_.style.border="none",r.closeButton_.style.padding="10px",r.closeButton_.style.fontSize="12px",r.closeButton_.style.cursor="pointer",r.adBannerImgContainer_.appendChild(r.closeButton_)),r.adDuration_=t,r.adBannerRemainingDuration_=r.adDuration_,r.adBannerImg_.onerror=function(){r.player_.adManager.handleVastError(l.ErrorCodes.ADS_VAST_UNABLE_TO_FETCH_RESSOURCE,"Unable to fetch resourse for adBanner with url "+e,!0),r.ended_(),i()},r.adBannerImg_.onclick=function(e){e.stopPropagation(),r.player_.adManager.adActivated()},r.nonLinearClosable_&&(r.closeButton_.onclick=function(e){e.stopPropagation(),r.player_.adManager.skipAd()}),r.adBannerInitialTime_=Date.now()/1e3,r.adBannerPlaying_=!0,r.adBannerProgressTimer_=setInterval((function(){r.player_.adManager.adTimeUpdated(Date.now()/1e3-r.adBannerInitialTime_)}),500),r.adBannerEndTimer_=setTimeout((function(){r.ended_()}),1e3*r.adBannerRemainingDuration_),r.adBannerImg_.addEventListener("load",(function(){r.adLoaded_(),n()})),r.adBannerImg_.src=e}))},this.pauseAdBanner_=function(){r.adBannerEndTimer_&&(clearTimeout(r.adBannerEndTimer_),r.adBannerEndTimer_=null),r.adBannerProgressTimer_&&(clearInterval(r.adBannerProgressTimer_),r.adBannerProgressTimer_=null),r.adBannerPlaying_=!1,r.adBannerRemainingDuration_-=Date.now()/1e3-r.adBannerInitialTime_},this.resumeAdBanner_=function(){null===r.adBannerProgressTimer_&&null===r.adBannerEndTimer_&&(r.adBannerInitialTime_=Date.now()/1e3,r.adBannerPlaying_=!0,r.adBannerProgressTimer_=setInterval((function(){r.player_.adManager.adTimeUpdated(Date.now()/1e3-r.adBannerInitialTime_+r.adDuration_-r.adBannerRemainingDuration_)}),500),r.adBannerEndTimer_=setTimeout((function(){r.ended_()}),1e3*r.adBannerRemainingDuration_))},this.resetAdBanner_=function(){if(r.adBannerImg_&&r.adBannerImg_.removeEventListener("load",r.adLoaded_),r.adBannerContainer_)for(;r.adBannerContainer_.firstChild;)r.adBannerContainer_.removeChild(r.adBannerContainer_.lastChild);null!==r.adBannerProgressTimer_&&(clearInterval(r.adBannerProgressTimer_),r.adBannerProgressTimer_=null),r.adBannerEndTimer_&&(clearTimeout(r.adBannerEndTimer_),r.adBannerEndTimer_=null),r.adDuration_=-1,r.adBannerRemainingDuration_=-1,r.adBannerPlaying_=!1},this.player_=t,this.player_.control.adsEnabled?(this.adVideoElement_=null,this.adBannerContainer_=null,this.adPlayer_=null,this.isActive_=!1,this.useMainPlayerVideoElt_=this.player_.getConfiguration().ads.useMainPlayerVideoElement,this.nonLinearClosable_=this.player_.getConfiguration().ads.nonLinearClosable,this.mainPlayerResumeCallback_=this.player_.getConfiguration().ads.mainPlayerResumeCallback,this.mainPlayerReloadCallback_=this.player_.getConfiguration().ads.mainPlayerReloadCallback,this.seekToAllowed_=this.player_.getConfiguration().ads.seekToAllowed,this.mainPlayerManifestUri_=null,this.mainPlayerConfiguration_=null,this.mainPlayerResumeTime_=null,this.liveAutoRefreshPauseTIme_=null,this.adType_=u.AdvertisementType.NONE,this.adBannerProgressTimer_=null,this.adBannerInitialTime_=-1,this.adDuration_=-1,this.adBannerRemainingDuration_=-1,this.adBannerPlaying_=!1,this.createAdVideoElement_()):voplayer.Log.warning("[AdTagStrategy] constructor: ads not enabled in license")}var t,r,n;return t=e,(r=[{key:"startAd",value:function(e){return o(this,void 0,void 0,i().mark((function t(){var r,n=this;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(voplayer.Log.debug("[AdTagStrategy] startAd: url ",e.url),this.adSkipped_=!1,this.adError_=!1,this.adType_=e.type,this.player_.control.adsEnabled){t.next=7;break}return voplayer.Log.warning("[AdTagStrategy] startAd: ads not enabled in license"),t.abrupt("return",void 0);case 7:if(u.AdvertisementType.LINEAR!==this.adType_){t.next=38;break}if(this.background_&&this.background_.style&&this.background_.style.display&&"none"===this.background_.style.display&&(this.background_.style.display="block"),this.adPaused_=!1,this.regisertAdVideoEvents_(),this.player_.adManager.liveHack&&(this.initiallyMuted_=this.player_.muted,this.player_.muted=!0,this.liveAutoRefreshPauseTIme_=this.player_.currentTime),this.player_._pauseMainContentOnly(),this.useMainPlayerVideoElt_&&(this.mainPlayerConfiguration_=this.player_.getConfiguration(),this.mainPlayerResumeTime_=this.player_.adManager.lastValidPlayerTime,this.mainPlayerManifestUri_=this.player_.getManifestUri()),this.isActive_=!0,r={url:e.url},e.mimeType&&(r.mimeType=e.mimeType),e.duration&&(r.imageDuration=e.duration,r.preserveImageAspectRatio=!0),!this.useMainPlayerVideoElt_){t.next=21;break}return t.next=21,this.player_.reset(!0);case 21:return this.adPlayer_.configure({autostart:this.player_.getConfiguration().autostart,iosPlaysInline:this.player_.getConfiguration().iosPlaysInline}),t.prev=22,t.next=25,this.adPlayer_.load(r);case 25:t.next=30;break;case 27:return t.prev=27,t.t0=t.catch(22),t.abrupt("return",Promise.reject(t.t0));case 30:if(!0!==this.player_.adManager.adPrerollAdBreakAutostart){t.next=34;break}return t.abrupt("return",this.play());case 34:return this.adPaused_=!0,t.abrupt("return",Promise.resolve());case 36:t.next=39;break;case 38:u.AdvertisementType.NONLINEAR===this.adType_&&(this.background_.style.display="none",this.isActive_=!0,this.createAdBanner_(e.url,e.duration).then((function(){return n.play()})).catch((function(){return Promise.resolve()})));case 39:case"end":return t.stop()}}),t,this,[[22,27]])})))}},{key:"postrollReloadFeatureContent",value:function(){return o(this,void 0,void 0,i().mark((function e(){var t=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==this.background_&&(this.background_.style.display="none"),!this.useMainPlayerVideoElt_||null===this.mainPlayerManifestUri_){e.next=10;break}if(null!==this.mainPlayerReloadCallback_&&void 0!==this.mainPlayerReloadCallback_){e.next=7;break}return this.player_.configure(this.mainPlayerConfiguration_),e.abrupt("return",this.player_.load(this.mainPlayerManifestUri_).then((function(){t.player_.pause()})));case 7:return e.abrupt("return",this.mainPlayerReloadCallback_(this.mainPlayerManifestUri_,this.mainPlayerConfiguration_));case 8:e.next=11;break;case 10:return e.abrupt("return",Promise.resolve());case 11:case"end":return e.stop()}}),e,this)})))}},{key:"resumeFeatureContent",value:function(){return o(this,void 0,void 0,i().mark((function e(){var t=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!==this.background_&&(this.background_.style.display="none"),!this.useMainPlayerVideoElt_||null===this.mainPlayerManifestUri_){e.next=23;break}if(null!==this.mainPlayerResumeCallback_&&void 0!==this.mainPlayerResumeCallback_){e.next=20;break}return this.player_.configure(this.mainPlayerConfiguration_),e.prev=4,e.next=7,this.player_.load(this.mainPlayerManifestUri_,this.mainPlayerResumeTime_);case 7:e.next=13;break;case 9:return e.prev=9,e.t0=e.catch(4),voplayer.Log.error("[AdTagStrategy] Error during resumeFeatureContent : "+e.t0),e.abrupt("return",Promise.reject(e.t0));case 13:if(!0!==this.mainPlayerConfiguration_.autostart){e.next=17;break}e.next=18;break;case 17:return e.abrupt("return",this.player_.play().then((function(){t.player_.adManager.liveHack&&t.liveAutoRefreshPauseTIme_&&t.player_.adManager.timeAdBreakEnd&&t.player_.adManager.timeAdBreakStart&&t.player_.adManager.timeAdBreakEnd>t.player_.adManager.timeAdBreakStart&&(t.player_.currentTime=t.liveAutoRefreshPauseTIme_+(t.player_.adManager.timeAdBreakEnd-t.player_.adManager.timeAdBreakStart)/1e3)})).catch((function(e){voplayer.Log.error("[AdTagStrategy] Error during resumeFeatureContent : "+e)})));case 18:e.next=21;break;case 20:return e.abrupt("return",this.mainPlayerResumeCallback_(this.mainPlayerManifestUri_,this.mainPlayerConfiguration_,this.mainPlayerResumeTime_));case 21:e.next=24;break;case 23:return e.abrupt("return",this.player_.play().then((function(){t.player_.isLive()&&(null===t.player_.getStartTime()?t.player_.currentTime=t.player_.seekRange().end:t.player_.currentTime=t.player_.getStartTime()),t.player_.adManager.liveHack&&t.liveAutoRefreshPauseTIme_&&t.player_.adManager.timeAdBreakEnd&&t.player_.adManager.timeAdBreakStart&&t.player_.adManager.timeAdBreakEnd>t.player_.adManager.timeAdBreakStart&&(t.player_.currentTime=t.liveAutoRefreshPauseTIme_+(t.player_.adManager.timeAdBreakEnd-t.player_.adManager.timeAdBreakStart)/1e3)})).catch((function(e){voplayer.Log.error("[AdTagStrategy] Error during resumeFeatureContent : "+e)})));case 24:case"end":return e.stop()}}),e,this,[[4,9]])})))}},{key:"adVolume",get:function(){return this.adPlayer_.volume},set:function(e){this.adPlayer_.volume=e}},{key:"adMuted",get:function(){return this.adPlayer_.muted}},{key:"playing",get:function(){return u.AdvertisementType.LINEAR===this.adType_?this.adPlayer_.playing:this.adBannerPlaying_}},{key:"adSkipped",get:function(){return this.adSkipped_}},{key:"adError",get:function(){return this.adError_}},{key:"adPosition",get:function(){return u.AdvertisementType.LINEAR===this.adType_?this.adPlayer_.currentTime:this.player_.currentTime},set:function(e){voplayer.Log.debug("[AdTagStrategy] set adPosition:position ",e),this.seekToAllowed_?this.adPlayer_.currentTime=e:(this.player_.emitEvent(s.Player.WARNING,{code:l.ErrorCodes.ADS_LINEAR_SEEK_FORBIDDEN}),voplayer.Log.info("[AdTagStrategy] set adPosition: Seeking during ads is forbidden."))}},{key:"adDuration",get:function(){return this.adDuration_}},{key:"supportedMimeTypes",get:function(){var e=["video/mp4","video/x-msvideo","video/mpeg","application/dash+xml","application/x-mpegURL","application/x-mpegurl","image/png","image/jpg","image/jpeg","image/gif"];return(navigator.userAgent.indexOf("Firefox")>-1||navigator.userAgent.indexOf("Chrome")>-1||navigator.userAgent.indexOf("Edge")>-1)&&e.push("video/webm"),e}},{key:"regisertAdVideoEvents_",value:function(){this.adPlayer_.on(s.Player.ERROR,this.playerError_),this.adPlayer_.on(s.Player.WARNING,this.playerWarning_),this.adPlayer_.on(s.Player.LOADED,this.adLoaded_),this.adPlayer_.on(s.Player.ENDED,this.ended_),this.adPlayer_.on(s.Player.TIMEUPDATE,this.timeUpdated_),this.adPlayer_.on(s.Player.DURATION_CHANGE,this.durationChange_),this.adPlayer_.on(s.Player.PAUSE,this.pause_),this.adPlayer_.on(s.Player.SEEKING,this.adSeeking_),this.adPlayer_.on(s.Player.SEEKED,this.adSeeked_)}},{key:"unregisterAdVideoEvents_",value:function(){this.adPlayer_.off(s.Player.ERROR,this.playerError_),this.adPlayer_.off(s.Player.WARNING,this.playerWarning_),this.adPlayer_.off(s.Player.LOADED,this.adLoaded_),this.adPlayer_.off(s.Player.ENDED,this.ended_),this.adPlayer_.off(s.Player.TIMEUPDATE,this.timeUpdated_),this.adPlayer_.off(s.Player.DURATION_CHANGE,this.durationChange_),this.adPlayer_.off(s.Player.PAUSE,this.pause_),this.adPlayer_.off(s.Player.SEEKING,this.adSeeking_),this.adPlayer_.off(s.Player.SEEKED,this.adSeeked_)}}])&&a(t.prototype,r),n&&a(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();t.AdTagStrategy=c},5993:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AdManager=void 0;var n=r(489);Object.defineProperty(t,"AdManager",{enumerable:!0,get:function(){return n.AdManager}});var i="undefined"!=typeof window&&window||r.g,a=i.voplayer;a||(a=i.voplayer={}),a.ads={},a.ads.AdManager=n.AdManager,t.default=a},2553:function(e,t,r){"use strict";var n=r(9509).Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function a(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=u,this.end=l,t=4;break;case"utf8":this.fillLast=s,t=4;break;case"base64":this.text=c,this.end=d,t=3;break;default:return this.write=h,void(this.end=p)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function o(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function s(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function u(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function l(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function c(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function d(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function p(e){return e&&e.length?this.write(e):""}t.s=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},a.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=o(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if(i=o(t[n]),i>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if(i=o(t[n]),i>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},5663:function(e,t){var r={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5};for(var n in r)"millisecond"===n?r.ms=r[n]:r[n.charAt(0)]=r[n],r[n+"s"]=r[n];function i(e){this.count=0;var t=function(e){var t=e.match(a);if(t&&r[t[2]])return t.slice(1);return null}(e);t&&(this.time=Number(t[0])*r[t[1]],this.type=t[1])}i.prototype.do=function(e){this.time&&(this.interval=setInterval((function(){t.count++,e.call(t)}),this.time));var t=this;return this},i.prototype.stop=function(){return this.interval&&(clearInterval(this.interval),delete this.interval),this};var a=/^\s*(\d+(?:\.\d+)?)\s*([a-z]+)\s*$/},306:function(e,t){(function(){"use strict";t.stripBOM=function(e){return"\ufeff"===e[0]?e.substring(1):e}}).call(this)},4096:function(e,t,r){(function(){"use strict";var e,n,i,a,o,s={}.hasOwnProperty;e=r(5532),n=r(8381).defaults,a=function(e){return"string"==typeof e&&(e.indexOf("&")>=0||e.indexOf(">")>=0||e.indexOf("<")>=0)},o=function(e){return"<![CDATA["+i(e)+"]]>"},i=function(e){return e.replace("]]>","]]]]><![CDATA[>")},t.Builder=function(){function t(e){var t,r,i;for(t in this.options={},r=n[.2])s.call(r,t)&&(i=r[t],this.options[t]=i);for(t in e)s.call(e,t)&&(i=e[t],this.options[t]=i)}return t.prototype.buildObject=function(t){var r,i,u,l,c,d;return r=this.options.attrkey,i=this.options.charkey,1===Object.keys(t).length&&this.options.rootName===n[.2].rootName?t=t[c=Object.keys(t)[0]]:c=this.options.rootName,d=this,u=function(e,t){var n,l,c,h,p,f;if("object"!=typeof t)d.options.cdata&&a(t)?e.raw(o(t)):e.txt(t);else if(Array.isArray(t)){for(h in t)if(s.call(t,h))for(p in l=t[h])c=l[p],e=u(e.ele(p),c).up()}else for(p in t)if(s.call(t,p))if(l=t[p],p===r){if("object"==typeof l)for(n in l)f=l[n],e=e.att(n,f)}else if(p===i)e=d.options.cdata&&a(l)?e.raw(o(l)):e.txt(l);else if(Array.isArray(l))for(h in l)s.call(l,h)&&(e="string"==typeof(c=l[h])?d.options.cdata&&a(c)?e.ele(p).raw(o(c)).up():e.ele(p,c).up():u(e.ele(p),c).up());else"object"==typeof l?e=u(e.ele(p),l).up():"string"==typeof l&&d.options.cdata&&a(l)?e=e.ele(p).raw(o(l)).up():(null==l&&(l=""),e=e.ele(p,l.toString()).up());return e},l=e.create(c,this.options.xmldec,this.options.doctype,{headless:this.options.headless,allowSurrogateChars:this.options.allowSurrogateChars}),u(l,t).end(this.options.renderOpts)},t}()}).call(this)},8381:function(e,t){(function(){t.defaults={.1:{explicitCharkey:!1,trim:!0,normalize:!0,normalizeTags:!1,attrkey:"@",charkey:"#",explicitArray:!1,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!1,validator:null,xmlns:!1,explicitChildren:!1,childkey:"@@",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,emptyTag:""},.2:{explicitCharkey:!1,trim:!1,normalize:!1,normalizeTags:!1,attrkey:"$",charkey:"_",explicitArray:!0,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!0,validator:null,xmlns:!1,explicitChildren:!1,preserveChildrenOrder:!1,childkey:"$$",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,rootName:"root",xmldec:{version:"1.0",encoding:"UTF-8",standalone:!0},doctype:null,renderOpts:{pretty:!0,indent:"  ",newline:"\n"},headless:!1,chunkSize:1e4,emptyTag:"",cdata:!1}}}).call(this)},9082:function(e,t,r){(function(){"use strict";var e,n,i,a,o,s,u,l,c,d=function(e,t){return function(){return e.apply(t,arguments)}},h={}.hasOwnProperty;l=r(6099),a=r(7187),e=r(306),u=r(7526),c=r(5663).setImmediate,n=r(8381).defaults,o=function(e){return"object"==typeof e&&null!=e&&0===Object.keys(e).length},s=function(e,t,r){var n,i;for(n=0,i=e.length;n<i;n++)t=(0,e[n])(t,r);return t},i=function(e,t,r){var n;return(n=Object.create(null)).value=r,n.writable=!0,n.enumerable=!0,n.configurable=!0,Object.defineProperty(e,t,n)},t.Parser=function(r){function a(e){var r,i,a;if(this.parseStringPromise=d(this.parseStringPromise,this),this.parseString=d(this.parseString,this),this.reset=d(this.reset,this),this.assignOrPush=d(this.assignOrPush,this),this.processAsync=d(this.processAsync,this),!(this instanceof t.Parser))return new t.Parser(e);for(r in this.options={},i=n[.2])h.call(i,r)&&(a=i[r],this.options[r]=a);for(r in e)h.call(e,r)&&(a=e[r],this.options[r]=a);this.options.xmlns&&(this.options.xmlnskey=this.options.attrkey+"ns"),this.options.normalizeTags&&(this.options.tagNameProcessors||(this.options.tagNameProcessors=[]),this.options.tagNameProcessors.unshift(u.normalize)),this.reset()}return function(e,t){for(var r in t)h.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(a,r),a.prototype.processAsync=function(){var e,t;try{return this.remaining.length<=this.options.chunkSize?(e=this.remaining,this.remaining="",this.saxParser=this.saxParser.write(e),this.saxParser.close()):(e=this.remaining.substr(0,this.options.chunkSize),this.remaining=this.remaining.substr(this.options.chunkSize,this.remaining.length),this.saxParser=this.saxParser.write(e),c(this.processAsync))}catch(e){if(t=e,!this.saxParser.errThrown)return this.saxParser.errThrown=!0,this.emit(t)}},a.prototype.assignOrPush=function(e,t,r){return t in e?(e[t]instanceof Array||i(e,t,[e[t]]),e[t].push(r)):this.options.explicitArray?i(e,t,[r]):i(e,t,r)},a.prototype.reset=function(){var e,t,r,n,a;return this.removeAllListeners(),this.saxParser=l.parser(this.options.strict,{trim:!1,normalize:!1,xmlns:this.options.xmlns}),this.saxParser.errThrown=!1,this.saxParser.onerror=(a=this,function(e){if(a.saxParser.resume(),!a.saxParser.errThrown)return a.saxParser.errThrown=!0,a.emit("error",e)}),this.saxParser.onend=function(e){return function(){if(!e.saxParser.ended)return e.saxParser.ended=!0,e.emit("end",e.resultObject)}}(this),this.saxParser.ended=!1,this.EXPLICIT_CHARKEY=this.options.explicitCharkey,this.resultObject=null,n=[],e=this.options.attrkey,t=this.options.charkey,this.saxParser.onopentag=function(r){return function(a){var o,u,l,c,d;if((l={})[t]="",!r.options.ignoreAttrs)for(o in d=a.attributes)h.call(d,o)&&(e in l||r.options.mergeAttrs||(l[e]={}),u=r.options.attrValueProcessors?s(r.options.attrValueProcessors,a.attributes[o],o):a.attributes[o],c=r.options.attrNameProcessors?s(r.options.attrNameProcessors,o):o,r.options.mergeAttrs?r.assignOrPush(l,c,u):i(l[e],c,u));return l["#name"]=r.options.tagNameProcessors?s(r.options.tagNameProcessors,a.name):a.name,r.options.xmlns&&(l[r.options.xmlnskey]={uri:a.uri,local:a.local}),n.push(l)}}(this),this.saxParser.onclosetag=function(e){return function(){var r,a,u,l,c,d,p,f,y,g;if(d=n.pop(),c=d["#name"],e.options.explicitChildren&&e.options.preserveChildrenOrder||delete d["#name"],!0===d.cdata&&(r=d.cdata,delete d.cdata),y=n[n.length-1],d[t].match(/^\s*$/)&&!r?(a=d[t],delete d[t]):(e.options.trim&&(d[t]=d[t].trim()),e.options.normalize&&(d[t]=d[t].replace(/\s{2,}/g," ").trim()),d[t]=e.options.valueProcessors?s(e.options.valueProcessors,d[t],c):d[t],1===Object.keys(d).length&&t in d&&!e.EXPLICIT_CHARKEY&&(d=d[t])),o(d)&&(d="function"==typeof e.options.emptyTag?e.options.emptyTag():""!==e.options.emptyTag?e.options.emptyTag:a),null!=e.options.validator&&(g="/"+function(){var e,t,r;for(r=[],e=0,t=n.length;e<t;e++)l=n[e],r.push(l["#name"]);return r}().concat(c).join("/"),function(){var t;try{return d=e.options.validator(g,y&&y[c],d)}catch(r){return t=r,e.emit("error",t)}}()),e.options.explicitChildren&&!e.options.mergeAttrs&&"object"==typeof d)if(e.options.preserveChildrenOrder){if(y){for(u in y[e.options.childkey]=y[e.options.childkey]||[],p={},d)h.call(d,u)&&i(p,u,d[u]);y[e.options.childkey].push(p),delete d["#name"],1===Object.keys(d).length&&t in d&&!e.EXPLICIT_CHARKEY&&(d=d[t])}}else l={},e.options.attrkey in d&&(l[e.options.attrkey]=d[e.options.attrkey],delete d[e.options.attrkey]),!e.options.charsAsChildren&&e.options.charkey in d&&(l[e.options.charkey]=d[e.options.charkey],delete d[e.options.charkey]),Object.getOwnPropertyNames(d).length>0&&(l[e.options.childkey]=d),d=l;return n.length>0?e.assignOrPush(y,c,d):(e.options.explicitRoot&&(f=d,i(d={},c,f)),e.resultObject=d,e.saxParser.ended=!0,e.emit("end",e.resultObject))}}(this),r=function(e){return function(r){var i,a;if(a=n[n.length-1])return a[t]+=r,e.options.explicitChildren&&e.options.preserveChildrenOrder&&e.options.charsAsChildren&&(e.options.includeWhiteChars||""!==r.replace(/\\n/g,"").trim())&&(a[e.options.childkey]=a[e.options.childkey]||[],(i={"#name":"__text__"})[t]=r,e.options.normalize&&(i[t]=i[t].replace(/\s{2,}/g," ").trim()),a[e.options.childkey].push(i)),a}}(this),this.saxParser.ontext=r,this.saxParser.oncdata=function(e){var t;if(t=r(e))return t.cdata=!0}},a.prototype.parseString=function(t,r){var n;null!=r&&"function"==typeof r&&(this.on("end",(function(e){return this.reset(),r(null,e)})),this.on("error",(function(e){return this.reset(),r(e)})));try{return""===(t=t.toString()).trim()?(this.emit("end",null),!0):(t=e.stripBOM(t),this.options.async?(this.remaining=t,c(this.processAsync),this.saxParser):this.saxParser.write(t).close())}catch(e){if(n=e,!this.saxParser.errThrown&&!this.saxParser.ended)return this.emit("error",n),this.saxParser.errThrown=!0;if(this.saxParser.ended)throw n}},a.prototype.parseStringPromise=function(e){return new Promise((t=this,function(r,n){return t.parseString(e,(function(e,t){return e?n(e):r(t)}))}));var t},a}(a),t.parseString=function(e,r,n){var i,a;return null!=n?("function"==typeof n&&(i=n),"object"==typeof r&&(a=r)):("function"==typeof r&&(i=r),a={}),new t.Parser(a).parseString(e,i)},t.parseStringPromise=function(e,r){var n;return"object"==typeof r&&(n=r),new t.Parser(n).parseStringPromise(e)}}).call(this)},7526:function(e,t){(function(){"use strict";var e;e=new RegExp(/(?!xmlns)^.*:/),t.normalize=function(e){return e.toLowerCase()},t.firstCharLowerCase=function(e){return e.charAt(0).toLowerCase()+e.slice(1)},t.stripPrefix=function(t){return t.replace(e,"")},t.parseNumbers=function(e){return isNaN(e)||(e=e%1==0?parseInt(e,10):parseFloat(e)),e},t.parseBooleans=function(e){return/^(?:true|false)$/i.test(e)&&(e="true"===e.toLowerCase()),e}}).call(this)},5055:function(e,t,r){(function(){"use strict";var e,n,i,a,o={}.hasOwnProperty;n=r(8381),e=r(4096),i=r(9082),a=r(7526),t.defaults=n.defaults,t.processors=a,t.ValidationError=function(e){function t(e){this.message=e}return function(e,t){for(var r in t)o.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(t,Error),t}(),t.Builder=e.Builder,t.Parser=i.Parser,t.parseString=i.parseString,t.parseStringPromise=i.parseStringPromise}).call(this)},7557:function(e){(function(){e.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(this)},9335:function(e){(function(){e.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(this)},8369:function(e){(function(){var t,r,n,i,a,o,s,u=[].slice,l={}.hasOwnProperty;t=function(){var e,t,r,n,i,o;if(o=arguments[0],i=2<=arguments.length?u.call(arguments,1):[],a(Object.assign))Object.assign.apply(null,arguments);else for(e=0,r=i.length;e<r;e++)if(null!=(n=i[e]))for(t in n)l.call(n,t)&&(o[t]=n[t]);return o},a=function(e){return!!e&&"[object Function]"===Object.prototype.toString.call(e)},o=function(e){var t;return!!e&&("function"==(t=typeof e)||"object"===t)},n=function(e){return a(Array.isArray)?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},i=function(e){var t;if(n(e))return!e.length;for(t in e)if(l.call(e,t))return!1;return!0},s=function(e){var t,r;return o(e)&&(r=Object.getPrototypeOf(e))&&(t=r.constructor)&&"function"==typeof t&&t instanceof t&&Function.prototype.toString.call(t)===Function.prototype.toString.call(Object)},r=function(e){return a(e.valueOf)?e.valueOf():e},e.exports.assign=t,e.exports.isFunction=a,e.exports.isObject=o,e.exports.isArray=n,e.exports.isEmpty=i,e.exports.isPlainObject=s,e.exports.getValue=r}).call(this)},594:function(e){(function(){e.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(this)},2750:function(e,t,r){(function(){var t;t=r(9335),r(2026),e.exports=function(){function e(e,r,n){if(this.parent=e,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),null==r)throw new Error("Missing attribute name. "+this.debugInfo(r));this.name=this.stringify.name(r),this.value=this.stringify.attValue(n),this.type=t.Attribute,this.isId=!1,this.schemaTypeInfo=null}return Object.defineProperty(e.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(e.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(e.prototype,"textContent",{get:function(){return this.value},set:function(e){return this.value=e||""}}),Object.defineProperty(e.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(e.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(e.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"specified",{get:function(){return!0}}),e.prototype.clone=function(){return Object.create(this)},e.prototype.toString=function(e){return this.options.writer.attribute(this,this.options.writer.filterOptions(e))},e.prototype.debugInfo=function(e){return null==(e=e||this.name)?"parent: <"+this.parent.name+">":"attribute: {"+e+"}, parent: <"+this.parent.name+">"},e.prototype.isEqualNode=function(e){return e.namespaceURI===this.namespaceURI&&(e.prefix===this.prefix&&(e.localName===this.localName&&e.value===this.value))},e}()}).call(this)},6170:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;t=r(9335),n=r(6488),e.exports=function(e){function r(e,n){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=t.CData,this.value=this.stringify.cdata(n)}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return this.options.writer.cdata(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},6488:function(e,t,r){(function(){var t,n={}.hasOwnProperty;t=r(2026),e.exports=function(e){function t(e){t.__super__.constructor.call(this,e),this.value=""}return function(e,t){for(var r in t)n.call(t,r)&&(e[r]=t[r]);function i(){this.constructor=e}i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype}(t,e),Object.defineProperty(t.prototype,"data",{get:function(){return this.value},set:function(e){return this.value=e||""}}),Object.defineProperty(t.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(t.prototype,"textContent",{get:function(){return this.value},set:function(e){return this.value=e||""}}),t.prototype.clone=function(){return Object.create(this)},t.prototype.substringData=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},t.prototype.appendData=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},t.prototype.insertData=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},t.prototype.deleteData=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},t.prototype.replaceData=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},t.prototype.isEqualNode=function(e){return!!t.__super__.isEqualNode.apply(this,arguments).isEqualNode(e)&&e.data===this.data},t}(t)}).call(this)},2096:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;t=r(9335),n=r(6488),e.exports=function(e){function r(e,n){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=t.Comment,this.value=this.stringify.comment(n)}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return this.options.writer.comment(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},383:function(e,t,r){(function(){var t,n;t=r(3933),n=r(6210),e.exports=function(){function e(){this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new t,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Object.create(this.defaultParams)}return Object.defineProperty(e.prototype,"parameterNames",{get:function(){return new n(Object.keys(this.defaultParams))}}),e.prototype.getParameter=function(e){return this.params.hasOwnProperty(e)?this.params[e]:null},e.prototype.canSetParameter=function(e,t){return!0},e.prototype.setParameter=function(e,t){return null!=t?this.params[e]=t:delete this.params[e]},e}()}).call(this)},3933:function(e){(function(){e.exports=function(){function e(){}return e.prototype.handleError=function(e){throw new Error(e)},e}()}).call(this)},1770:function(e){(function(){e.exports=function(){function e(){}return e.prototype.hasFeature=function(e,t){return!0},e.prototype.createDocumentType=function(e,t,r){throw new Error("This DOM method is not implemented.")},e.prototype.createDocument=function(e,t,r){throw new Error("This DOM method is not implemented.")},e.prototype.createHTMLDocument=function(e){throw new Error("This DOM method is not implemented.")},e.prototype.getFeature=function(e,t){throw new Error("This DOM method is not implemented.")},e}()}).call(this)},6210:function(e){(function(){e.exports=function(){function e(e){this.arr=e||[]}return Object.defineProperty(e.prototype,"length",{get:function(){return this.arr.length}}),e.prototype.item=function(e){return this.arr[e]||null},e.prototype.contains=function(e){return-1!==this.arr.indexOf(e)},e}()}).call(this)},1179:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;n=r(2026),t=r(9335),e.exports=function(e){function r(e,n,i,a,o,s){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD element name. "+this.debugInfo());if(null==i)throw new Error("Missing DTD attribute name. "+this.debugInfo(n));if(!a)throw new Error("Missing DTD attribute type. "+this.debugInfo(n));if(!o)throw new Error("Missing DTD attribute default. "+this.debugInfo(n));if(0!==o.indexOf("#")&&(o="#"+o),!o.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(n));if(s&&!o.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(n));this.elementName=this.stringify.name(n),this.type=t.AttributeDeclaration,this.attributeName=this.stringify.name(i),this.attributeType=this.stringify.dtdAttType(a),s&&(this.defaultValue=this.stringify.dtdAttDefault(s)),this.defaultValueType=o}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.toString=function(e){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},6347:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;n=r(2026),t=r(9335),e.exports=function(e){function r(e,n,i){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD element name. "+this.debugInfo());i||(i="(#PCDATA)"),Array.isArray(i)&&(i="("+i.join(",")+")"),this.name=this.stringify.name(n),this.type=t.ElementDeclaration,this.value=this.stringify.dtdElementValue(i)}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.toString=function(e){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},9078:function(e,t,r){(function(){var t,n,i,a={}.hasOwnProperty;i=r(8369).isObject,n=r(2026),t=r(9335),e.exports=function(e){function r(e,n,a,o){if(r.__super__.constructor.call(this,e),null==a)throw new Error("Missing DTD entity name. "+this.debugInfo(a));if(null==o)throw new Error("Missing DTD entity value. "+this.debugInfo(a));if(this.pe=!!n,this.name=this.stringify.name(a),this.type=t.EntityDeclaration,i(o)){if(!o.pubID&&!o.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(a));if(o.pubID&&!o.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(a));if(this.internal=!1,null!=o.pubID&&(this.pubID=this.stringify.dtdPubID(o.pubID)),null!=o.sysID&&(this.sysID=this.stringify.dtdSysID(o.sysID)),null!=o.nData&&(this.nData=this.stringify.dtdNData(o.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(a))}else this.value=this.stringify.dtdEntityValue(o),this.internal=!0}return function(e,t){for(var r in t)a.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(r.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(r.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(r.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(r.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(r.prototype,"xmlVersion",{get:function(){return null}}),r.prototype.toString=function(e){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},4777:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;n=r(2026),t=r(9335),e.exports=function(e){function r(e,n,i){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing DTD notation name. "+this.debugInfo(n));if(!i.pubID&&!i.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(n));this.name=this.stringify.name(n),this.type=t.NotationDeclaration,null!=i.pubID&&(this.pubID=this.stringify.dtdPubID(i.pubID)),null!=i.sysID&&(this.sysID=this.stringify.dtdSysID(i.sysID))}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(r.prototype,"systemId",{get:function(){return this.sysID}}),r.prototype.toString=function(e){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},9077:function(e,t,r){(function(){var t,n,i,a={}.hasOwnProperty;i=r(8369).isObject,n=r(2026),t=r(9335),e.exports=function(e){function r(e,n,a,o){var s;r.__super__.constructor.call(this,e),i(n)&&(n=(s=n).version,a=s.encoding,o=s.standalone),n||(n="1.0"),this.type=t.Declaration,this.version=this.stringify.xmlVersion(n),null!=a&&(this.encoding=this.stringify.xmlEncoding(a)),null!=o&&(this.standalone=this.stringify.xmlStandalone(o))}return function(e,t){for(var r in t)a.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.toString=function(e){return this.options.writer.declaration(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},6544:function(e,t,r){(function(){var t,n,i,a,o,s,u,l,c={}.hasOwnProperty;l=r(8369).isObject,u=r(2026),t=r(9335),n=r(1179),a=r(9078),i=r(6347),o=r(4777),s=r(663),e.exports=function(e){function r(e,n,i){var a,o,s,u,c,d;if(r.__super__.constructor.call(this,e),this.type=t.DocType,e.children)for(o=0,s=(u=e.children).length;o<s;o++)if((a=u[o]).type===t.Element){this.name=a.name;break}this.documentObject=e,l(n)&&(n=(c=n).pubID,i=c.sysID),null==i&&(i=(d=[n,i])[0],n=d[1]),null!=n&&(this.pubID=this.stringify.dtdPubID(n)),null!=i&&(this.sysID=this.stringify.dtdSysID(i))}return function(e,t){for(var r in t)c.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"entities",{get:function(){var e,r,n,i,a;for(i={},r=0,n=(a=this.children).length;r<n;r++)(e=a[r]).type!==t.EntityDeclaration||e.pe||(i[e.name]=e);return new s(i)}}),Object.defineProperty(r.prototype,"notations",{get:function(){var e,r,n,i,a;for(i={},r=0,n=(a=this.children).length;r<n;r++)(e=a[r]).type===t.NotationDeclaration&&(i[e.name]=e);return new s(i)}}),Object.defineProperty(r.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(r.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(r.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),r.prototype.element=function(e,t){var r;return r=new i(this,e,t),this.children.push(r),this},r.prototype.attList=function(e,t,r,i,a){var o;return o=new n(this,e,t,r,i,a),this.children.push(o),this},r.prototype.entity=function(e,t){var r;return r=new a(this,!1,e,t),this.children.push(r),this},r.prototype.pEntity=function(e,t){var r;return r=new a(this,!0,e,t),this.children.push(r),this},r.prototype.notation=function(e,t){var r;return r=new o(this,e,t),this.children.push(r),this},r.prototype.toString=function(e){return this.options.writer.docType(this,this.options.writer.filterOptions(e))},r.prototype.ele=function(e,t){return this.element(e,t)},r.prototype.att=function(e,t,r,n,i){return this.attList(e,t,r,n,i)},r.prototype.ent=function(e,t){return this.entity(e,t)},r.prototype.pent=function(e,t){return this.pEntity(e,t)},r.prototype.not=function(e,t){return this.notation(e,t)},r.prototype.up=function(){return this.root()||this.documentObject},r.prototype.isEqualNode=function(e){return!!r.__super__.isEqualNode.apply(this,arguments).isEqualNode(e)&&(e.name===this.name&&(e.publicId===this.publicId&&e.systemId===this.systemId))},r}(u)}).call(this)},6934:function(e,t,r){(function(){var t,n,i,a,o,s,u,l={}.hasOwnProperty;u=r(8369).isPlainObject,i=r(1770),n=r(383),a=r(2026),t=r(9335),s=r(5549),o=r(6434),e.exports=function(e){function r(e){r.__super__.constructor.call(this,null),this.name="#document",this.type=t.Document,this.documentURI=null,this.domConfig=new n,e||(e={}),e.writer||(e.writer=new o),this.options=e,this.stringify=new s(e)}return function(e,t){for(var r in t)l.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"implementation",{value:new i}),Object.defineProperty(r.prototype,"doctype",{get:function(){var e,r,n,i;for(r=0,n=(i=this.children).length;r<n;r++)if((e=i[r]).type===t.DocType)return e;return null}}),Object.defineProperty(r.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(r.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(r.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(r.prototype,"xmlEncoding",{get:function(){return 0!==this.children.length&&this.children[0].type===t.Declaration?this.children[0].encoding:null}}),Object.defineProperty(r.prototype,"xmlStandalone",{get:function(){return 0!==this.children.length&&this.children[0].type===t.Declaration&&"yes"===this.children[0].standalone}}),Object.defineProperty(r.prototype,"xmlVersion",{get:function(){return 0!==this.children.length&&this.children[0].type===t.Declaration?this.children[0].version:"1.0"}}),Object.defineProperty(r.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(r.prototype,"origin",{get:function(){return null}}),Object.defineProperty(r.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(r.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(r.prototype,"contentType",{get:function(){return null}}),r.prototype.end=function(e){var t;return t={},e?u(e)&&(t=e,e=this.options.writer):e=this.options.writer,e.document(this,e.filterOptions(t))},r.prototype.toString=function(e){return this.options.writer.document(this,this.options.writer.filterOptions(e))},r.prototype.createElement=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createDocumentFragment=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createTextNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createComment=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createCDATASection=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createProcessingInstruction=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createAttribute=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createEntityReference=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagName=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.importNode=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createElementNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createAttributeNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagNameNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementById=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.adoptNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.normalizeDocument=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.renameNode=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByClassName=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createEvent=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createRange=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createNodeIterator=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.createTreeWalker=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},r}(a)}).call(this)},9227:function(e,t,r){(function(){var t,n,i,a,o,s,u,l,c,d,h,p,f,y,g,m,v,_,T,A,E,b,k,w={}.hasOwnProperty;k=r(8369),E=k.isObject,A=k.isFunction,b=k.isPlainObject,T=k.getValue,t=r(9335),p=r(6934),f=r(2161),a=r(6170),o=r(2096),g=r(9406),_=r(3595),y=r(4981),d=r(9077),h=r(6544),s=r(1179),l=r(9078),u=r(6347),c=r(4777),i=r(2750),v=r(5549),m=r(6434),n=r(594),e.exports=function(){function e(e,r,n){var i;this.name="?xml",this.type=t.Document,e||(e={}),i={},e.writer?b(e.writer)&&(i=e.writer,e.writer=new m):e.writer=new m,this.options=e,this.writer=e.writer,this.writerOptions=this.writer.filterOptions(i),this.stringify=new v(e),this.onDataCallback=r||function(){},this.onEndCallback=n||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}return e.prototype.createChildNode=function(e){var r,n,i,a,o,s,u,l;switch(e.type){case t.CData:this.cdata(e.value);break;case t.Comment:this.comment(e.value);break;case t.Element:for(n in i={},u=e.attribs)w.call(u,n)&&(r=u[n],i[n]=r.value);this.node(e.name,i);break;case t.Dummy:this.dummy();break;case t.Raw:this.raw(e.value);break;case t.Text:this.text(e.value);break;case t.ProcessingInstruction:this.instruction(e.target,e.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+e.constructor.name)}for(o=0,s=(l=e.children).length;o<s;o++)a=l[o],this.createChildNode(a),a.type===t.Element&&this.up();return this},e.prototype.dummy=function(){return this},e.prototype.node=function(e,t,r){var n;if(null==e)throw new Error("Missing node name.");if(this.root&&-1===this.currentLevel)throw new Error("Document can only have one root node. "+this.debugInfo(e));return this.openCurrent(),e=T(e),null==t&&(t={}),t=T(t),E(t)||(r=(n=[t,r])[0],t=n[1]),this.currentNode=new f(this,e,t),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,null!=r&&this.text(r),this},e.prototype.element=function(e,r,n){var i,a,o,s,u,l;if(this.currentNode&&this.currentNode.type===t.DocType)this.dtdElement.apply(this,arguments);else if(Array.isArray(e)||E(e)||A(e))for(s=this.options.noValidation,this.options.noValidation=!0,(l=new p(this.options).element("TEMP_ROOT")).element(e),this.options.noValidation=s,a=0,o=(u=l.children).length;a<o;a++)i=u[a],this.createChildNode(i),i.type===t.Element&&this.up();else this.node(e,r,n);return this},e.prototype.attribute=function(e,t){var r,n;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(e));if(null!=e&&(e=T(e)),E(e))for(r in e)w.call(e,r)&&(n=e[r],this.attribute(r,n));else A(t)&&(t=t.apply()),this.options.keepNullAttributes&&null==t?this.currentNode.attribs[e]=new i(this,e,""):null!=t&&(this.currentNode.attribs[e]=new i(this,e,t));return this},e.prototype.text=function(e){var t;return this.openCurrent(),t=new _(this,e),this.onData(this.writer.text(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.cdata=function(e){var t;return this.openCurrent(),t=new a(this,e),this.onData(this.writer.cdata(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.comment=function(e){var t;return this.openCurrent(),t=new o(this,e),this.onData(this.writer.comment(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.raw=function(e){var t;return this.openCurrent(),t=new g(this,e),this.onData(this.writer.raw(t,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.instruction=function(e,t){var r,n,i,a,o;if(this.openCurrent(),null!=e&&(e=T(e)),null!=t&&(t=T(t)),Array.isArray(e))for(r=0,a=e.length;r<a;r++)n=e[r],this.instruction(n);else if(E(e))for(n in e)w.call(e,n)&&(i=e[n],this.instruction(n,i));else A(t)&&(t=t.apply()),o=new y(this,e,t),this.onData(this.writer.processingInstruction(o,this.writerOptions,this.currentLevel+1),this.currentLevel+1);return this},e.prototype.declaration=function(e,t,r){var n;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return n=new d(this,e,t,r),this.onData(this.writer.declaration(n,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.doctype=function(e,t,r){if(this.openCurrent(),null==e)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new h(this,t,r),this.currentNode.rootNodeName=e,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this},e.prototype.dtdElement=function(e,t){var r;return this.openCurrent(),r=new u(this,e,t),this.onData(this.writer.dtdElement(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.attList=function(e,t,r,n,i){var a;return this.openCurrent(),a=new s(this,e,t,r,n,i),this.onData(this.writer.dtdAttList(a,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.entity=function(e,t){var r;return this.openCurrent(),r=new l(this,!1,e,t),this.onData(this.writer.dtdEntity(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.pEntity=function(e,t){var r;return this.openCurrent(),r=new l(this,!0,e,t),this.onData(this.writer.dtdEntity(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.notation=function(e,t){var r;return this.openCurrent(),r=new c(this,e,t),this.onData(this.writer.dtdNotation(r,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},e.prototype.up=function(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this},e.prototype.end=function(){for(;this.currentLevel>=0;)this.up();return this.onEnd()},e.prototype.openCurrent=function(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)},e.prototype.openNode=function(e){var r,i,a,o;if(!e.isOpen){if(this.root||0!==this.currentLevel||e.type!==t.Element||(this.root=e),i="",e.type===t.Element){for(a in this.writerOptions.state=n.OpenTag,i=this.writer.indent(e,this.writerOptions,this.currentLevel)+"<"+e.name,o=e.attribs)w.call(o,a)&&(r=o[a],i+=this.writer.attribute(r,this.writerOptions,this.currentLevel));i+=(e.children?">":"/>")+this.writer.endline(e,this.writerOptions,this.currentLevel),this.writerOptions.state=n.InsideTag}else this.writerOptions.state=n.OpenTag,i=this.writer.indent(e,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+e.rootNodeName,e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),e.children?(i+=" [",this.writerOptions.state=n.InsideTag):(this.writerOptions.state=n.CloseTag,i+=">"),i+=this.writer.endline(e,this.writerOptions,this.currentLevel);return this.onData(i,this.currentLevel),e.isOpen=!0}},e.prototype.closeNode=function(e){var r;if(!e.isClosed)return r="",this.writerOptions.state=n.CloseTag,r=e.type===t.Element?this.writer.indent(e,this.writerOptions,this.currentLevel)+"</"+e.name+">"+this.writer.endline(e,this.writerOptions,this.currentLevel):this.writer.indent(e,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(e,this.writerOptions,this.currentLevel),this.writerOptions.state=n.None,this.onData(r,this.currentLevel),e.isClosed=!0},e.prototype.onData=function(e,t){return this.documentStarted=!0,this.onDataCallback(e,t+1)},e.prototype.onEnd=function(){return this.documentCompleted=!0,this.onEndCallback()},e.prototype.debugInfo=function(e){return null==e?"":"node: <"+e+">"},e.prototype.ele=function(){return this.element.apply(this,arguments)},e.prototype.nod=function(e,t,r){return this.node(e,t,r)},e.prototype.txt=function(e){return this.text(e)},e.prototype.dat=function(e){return this.cdata(e)},e.prototype.com=function(e){return this.comment(e)},e.prototype.ins=function(e,t){return this.instruction(e,t)},e.prototype.dec=function(e,t,r){return this.declaration(e,t,r)},e.prototype.dtd=function(e,t,r){return this.doctype(e,t,r)},e.prototype.e=function(e,t,r){return this.element(e,t,r)},e.prototype.n=function(e,t,r){return this.node(e,t,r)},e.prototype.t=function(e){return this.text(e)},e.prototype.d=function(e){return this.cdata(e)},e.prototype.c=function(e){return this.comment(e)},e.prototype.r=function(e){return this.raw(e)},e.prototype.i=function(e,t){return this.instruction(e,t)},e.prototype.att=function(){return this.currentNode&&this.currentNode.type===t.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},e.prototype.a=function(){return this.currentNode&&this.currentNode.type===t.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},e.prototype.ent=function(e,t){return this.entity(e,t)},e.prototype.pent=function(e,t){return this.pEntity(e,t)},e.prototype.not=function(e,t){return this.notation(e,t)},e}()}).call(this)},8833:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;n=r(2026),t=r(9335),e.exports=function(e){function r(e){r.__super__.constructor.call(this,e),this.type=t.Dummy}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return""},r}(n)}).call(this)},2161:function(e,t,r){(function(){var t,n,i,a,o,s,u,l,c={}.hasOwnProperty;l=r(8369),u=l.isObject,s=l.isFunction,o=l.getValue,a=r(2026),t=r(9335),n=r(2750),i=r(663),e.exports=function(e){function r(e,n,i){var a,o,s,u;if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(n),this.type=t.Element,this.attribs={},this.schemaTypeInfo=null,null!=i&&this.attribute(i),e.type===t.Document&&(this.isRoot=!0,this.documentObject=e,e.rootObject=this,e.children))for(o=0,s=(u=e.children).length;o<s;o++)if((a=u[o]).type===t.DocType){a.name=this.name;break}}return function(e,t){for(var r in t)c.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(r.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(r.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(r.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(r.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(r.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(r.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(r.prototype,"attributes",{get:function(){return this.attributeMap&&this.attributeMap.nodes||(this.attributeMap=new i(this.attribs)),this.attributeMap}}),r.prototype.clone=function(){var e,t,r,n;for(t in(r=Object.create(this)).isRoot&&(r.documentObject=null),r.attribs={},n=this.attribs)c.call(n,t)&&(e=n[t],r.attribs[t]=e.clone());return r.children=[],this.children.forEach((function(e){var t;return(t=e.clone()).parent=r,r.children.push(t)})),r},r.prototype.attribute=function(e,t){var r,i;if(null!=e&&(e=o(e)),u(e))for(r in e)c.call(e,r)&&(i=e[r],this.attribute(r,i));else s(t)&&(t=t.apply()),this.options.keepNullAttributes&&null==t?this.attribs[e]=new n(this,e,""):null!=t&&(this.attribs[e]=new n(this,e,t));return this},r.prototype.removeAttribute=function(e){var t,r,n;if(null==e)throw new Error("Missing attribute name. "+this.debugInfo());if(e=o(e),Array.isArray(e))for(r=0,n=e.length;r<n;r++)t=e[r],delete this.attribs[t];else delete this.attribs[e];return this},r.prototype.toString=function(e){return this.options.writer.element(this,this.options.writer.filterOptions(e))},r.prototype.att=function(e,t){return this.attribute(e,t)},r.prototype.a=function(e,t){return this.attribute(e,t)},r.prototype.getAttribute=function(e){return this.attribs.hasOwnProperty(e)?this.attribs[e].value:null},r.prototype.setAttribute=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getAttributeNode=function(e){return this.attribs.hasOwnProperty(e)?this.attribs[e]:null},r.prototype.setAttributeNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.removeAttributeNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagName=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getAttributeNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.setAttributeNS=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.removeAttributeNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getAttributeNodeNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.setAttributeNodeNS=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagNameNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.hasAttribute=function(e){return this.attribs.hasOwnProperty(e)},r.prototype.hasAttributeNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.setIdAttribute=function(e,t){return this.attribs.hasOwnProperty(e)?this.attribs[e].isId:t},r.prototype.setIdAttributeNS=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.setIdAttributeNode=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagName=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByTagNameNS=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.getElementsByClassName=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.isEqualNode=function(e){var t,n,i;if(!r.__super__.isEqualNode.apply(this,arguments).isEqualNode(e))return!1;if(e.namespaceURI!==this.namespaceURI)return!1;if(e.prefix!==this.prefix)return!1;if(e.localName!==this.localName)return!1;if(e.attribs.length!==this.attribs.length)return!1;for(t=n=0,i=this.attribs.length-1;0<=i?n<=i:n>=i;t=0<=i?++n:--n)if(!this.attribs[t].isEqualNode(e.attribs[t]))return!1;return!0},r}(a)}).call(this)},663:function(e){(function(){e.exports=function(){function e(e){this.nodes=e}return Object.defineProperty(e.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),e.prototype.clone=function(){return this.nodes=null},e.prototype.getNamedItem=function(e){return this.nodes[e]},e.prototype.setNamedItem=function(e){var t;return t=this.nodes[e.nodeName],this.nodes[e.nodeName]=e,t||null},e.prototype.removeNamedItem=function(e){var t;return t=this.nodes[e],delete this.nodes[e],t||null},e.prototype.item=function(e){return this.nodes[Object.keys(this.nodes)[e]]||null},e.prototype.getNamedItemNS=function(e,t){throw new Error("This DOM method is not implemented.")},e.prototype.setNamedItemNS=function(e){throw new Error("This DOM method is not implemented.")},e.prototype.removeNamedItemNS=function(e,t){throw new Error("This DOM method is not implemented.")},e}()}).call(this)},2026:function(e,t,r){(function(){var t,n,i,a,o,s,u,l,c,d,h,p,f,y,g,m,v,_={}.hasOwnProperty;v=r(8369),m=v.isObject,g=v.isFunction,y=v.isEmpty,f=v.getValue,l=null,i=null,a=null,o=null,s=null,h=null,p=null,d=null,u=null,n=null,c=null,t=null,e.exports=function(){function e(e){this.parent=e,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.value=null,this.children=[],this.baseURI=null,l||(l=r(2161),i=r(6170),a=r(2096),o=r(9077),s=r(6544),h=r(9406),p=r(3595),d=r(4981),u=r(8833),n=r(9335),c=r(2390),r(663),t=r(7557))}return Object.defineProperty(e.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(e.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(e.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(e.prototype,"childNodes",{get:function(){return this.childNodeList&&this.childNodeList.nodes||(this.childNodeList=new c(this.children)),this.childNodeList}}),Object.defineProperty(e.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(e.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){var e;return e=this.parent.children.indexOf(this),this.parent.children[e-1]||null}}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){var e;return e=this.parent.children.indexOf(this),this.parent.children[e+1]||null}}),Object.defineProperty(e.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(e.prototype,"textContent",{get:function(){var e,t,r,i,a;if(this.nodeType===n.Element||this.nodeType===n.DocumentFragment){for(a="",t=0,r=(i=this.children).length;t<r;t++)(e=i[t]).textContent&&(a+=e.textContent);return a}return null},set:function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),e.prototype.setParent=function(e){var t,r,n,i,a;for(this.parent=e,e&&(this.options=e.options,this.stringify=e.stringify),a=[],r=0,n=(i=this.children).length;r<n;r++)t=i[r],a.push(t.setParent(this));return a},e.prototype.element=function(e,t,r){var n,i,a,o,s,u,l,c,d,h,p;if(u=null,null===t&&null==r&&(t=(d=[{},null])[0],r=d[1]),null==t&&(t={}),t=f(t),m(t)||(r=(h=[t,r])[0],t=h[1]),null!=e&&(e=f(e)),Array.isArray(e))for(a=0,l=e.length;a<l;a++)i=e[a],u=this.element(i);else if(g(e))u=this.element(e.apply());else if(m(e)){for(s in e)if(_.call(e,s))if(p=e[s],g(p)&&(p=p.apply()),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&0===s.indexOf(this.stringify.convertAttKey))u=this.attribute(s.substr(this.stringify.convertAttKey.length),p);else if(!this.options.separateArrayItems&&Array.isArray(p)&&y(p))u=this.dummy();else if(m(p)&&y(p))u=this.element(s);else if(this.options.keepNullNodes||null!=p)if(!this.options.separateArrayItems&&Array.isArray(p))for(o=0,c=p.length;o<c;o++)i=p[o],(n={})[s]=i,u=this.element(n);else m(p)?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&0===s.indexOf(this.stringify.convertTextKey)?u=this.element(p):(u=this.element(s)).element(p):u=this.element(s,p);else u=this.dummy()}else u=this.options.keepNullNodes||null!==r?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&0===e.indexOf(this.stringify.convertTextKey)?this.text(r):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&0===e.indexOf(this.stringify.convertCDataKey)?this.cdata(r):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&0===e.indexOf(this.stringify.convertCommentKey)?this.comment(r):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&0===e.indexOf(this.stringify.convertRawKey)?this.raw(r):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&0===e.indexOf(this.stringify.convertPIKey)?this.instruction(e.substr(this.stringify.convertPIKey.length),r):this.node(e,t,r):this.dummy();if(null==u)throw new Error("Could not create any elements with: "+e+". "+this.debugInfo());return u},e.prototype.insertBefore=function(e,t,r){var n,i,a,o,s;if(null!=e?e.type:void 0)return o=t,(a=e).setParent(this),o?(i=children.indexOf(o),s=children.splice(i),children.push(a),Array.prototype.push.apply(children,s)):children.push(a),a;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return i=this.parent.children.indexOf(this),s=this.parent.children.splice(i),n=this.parent.element(e,t,r),Array.prototype.push.apply(this.parent.children,s),n},e.prototype.insertAfter=function(e,t,r){var n,i,a;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(e));return i=this.parent.children.indexOf(this),a=this.parent.children.splice(i+1),n=this.parent.element(e,t,r),Array.prototype.push.apply(this.parent.children,a),n},e.prototype.remove=function(){var e;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return e=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[e,e-e+1].concat([])),this.parent},e.prototype.node=function(e,t,r){var n,i;return null!=e&&(e=f(e)),t||(t={}),t=f(t),m(t)||(r=(i=[t,r])[0],t=i[1]),n=new l(this,e,t),null!=r&&n.text(r),this.children.push(n),n},e.prototype.text=function(e){var t;return m(e)&&this.element(e),t=new p(this,e),this.children.push(t),this},e.prototype.cdata=function(e){var t;return t=new i(this,e),this.children.push(t),this},e.prototype.comment=function(e){var t;return t=new a(this,e),this.children.push(t),this},e.prototype.commentBefore=function(e){var t,r;return t=this.parent.children.indexOf(this),r=this.parent.children.splice(t),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,r),this},e.prototype.commentAfter=function(e){var t,r;return t=this.parent.children.indexOf(this),r=this.parent.children.splice(t+1),this.parent.comment(e),Array.prototype.push.apply(this.parent.children,r),this},e.prototype.raw=function(e){var t;return t=new h(this,e),this.children.push(t),this},e.prototype.dummy=function(){return new u(this)},e.prototype.instruction=function(e,t){var r,n,i,a,o;if(null!=e&&(e=f(e)),null!=t&&(t=f(t)),Array.isArray(e))for(a=0,o=e.length;a<o;a++)r=e[a],this.instruction(r);else if(m(e))for(r in e)_.call(e,r)&&(n=e[r],this.instruction(r,n));else g(t)&&(t=t.apply()),i=new d(this,e,t),this.children.push(i);return this},e.prototype.instructionBefore=function(e,t){var r,n;return r=this.parent.children.indexOf(this),n=this.parent.children.splice(r),this.parent.instruction(e,t),Array.prototype.push.apply(this.parent.children,n),this},e.prototype.instructionAfter=function(e,t){var r,n;return r=this.parent.children.indexOf(this),n=this.parent.children.splice(r+1),this.parent.instruction(e,t),Array.prototype.push.apply(this.parent.children,n),this},e.prototype.declaration=function(e,t,r){var i,a;return i=this.document(),a=new o(i,e,t,r),0===i.children.length?i.children.unshift(a):i.children[0].type===n.Declaration?i.children[0]=a:i.children.unshift(a),i.root()||i},e.prototype.dtd=function(e,t){var r,i,a,o,u,l,c,d,h;for(r=this.document(),i=new s(r,e,t),a=o=0,l=(d=r.children).length;o<l;a=++o)if(d[a].type===n.DocType)return r.children[a]=i,i;for(a=u=0,c=(h=r.children).length;u<c;a=++u)if(h[a].isRoot)return r.children.splice(a,0,i),i;return r.children.push(i),i},e.prototype.up=function(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent},e.prototype.root=function(){var e;for(e=this;e;){if(e.type===n.Document)return e.rootObject;if(e.isRoot)return e;e=e.parent}},e.prototype.document=function(){var e;for(e=this;e;){if(e.type===n.Document)return e;e=e.parent}},e.prototype.end=function(e){return this.document().end(e)},e.prototype.prev=function(){var e;if((e=this.parent.children.indexOf(this))<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[e-1]},e.prototype.next=function(){var e;if(-1===(e=this.parent.children.indexOf(this))||e===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[e+1]},e.prototype.importDocument=function(e){var t;return(t=e.root().clone()).parent=this,t.isRoot=!1,this.children.push(t),this},e.prototype.debugInfo=function(e){var t,r;return null!=(e=e||this.name)||(null!=(t=this.parent)?t.name:void 0)?null==e?"parent: <"+this.parent.name+">":(null!=(r=this.parent)?r.name:void 0)?"node: <"+e+">, parent: <"+this.parent.name+">":"node: <"+e+">":""},e.prototype.ele=function(e,t,r){return this.element(e,t,r)},e.prototype.nod=function(e,t,r){return this.node(e,t,r)},e.prototype.txt=function(e){return this.text(e)},e.prototype.dat=function(e){return this.cdata(e)},e.prototype.com=function(e){return this.comment(e)},e.prototype.ins=function(e,t){return this.instruction(e,t)},e.prototype.doc=function(){return this.document()},e.prototype.dec=function(e,t,r){return this.declaration(e,t,r)},e.prototype.e=function(e,t,r){return this.element(e,t,r)},e.prototype.n=function(e,t,r){return this.node(e,t,r)},e.prototype.t=function(e){return this.text(e)},e.prototype.d=function(e){return this.cdata(e)},e.prototype.c=function(e){return this.comment(e)},e.prototype.r=function(e){return this.raw(e)},e.prototype.i=function(e,t){return this.instruction(e,t)},e.prototype.u=function(){return this.up()},e.prototype.importXMLBuilder=function(e){return this.importDocument(e)},e.prototype.replaceChild=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.removeChild=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.appendChild=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.hasChildNodes=function(){return 0!==this.children.length},e.prototype.cloneNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.normalize=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.isSupported=function(e,t){return!0},e.prototype.hasAttributes=function(){return 0!==this.attribs.length},e.prototype.compareDocumentPosition=function(e){var r,n;return(r=this)===e?0:this.document()!==e.document()?(n=t.Disconnected|t.ImplementationSpecific,Math.random()<.5?n|=t.Preceding:n|=t.Following,n):r.isAncestor(e)?t.Contains|t.Preceding:r.isDescendant(e)?t.Contains|t.Following:r.isPreceding(e)?t.Preceding:t.Following},e.prototype.isSameNode=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.lookupPrefix=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.isDefaultNamespace=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.lookupNamespaceURI=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.isEqualNode=function(e){var t,r,n;if(e.nodeType!==this.nodeType)return!1;if(e.children.length!==this.children.length)return!1;for(t=r=0,n=this.children.length-1;0<=n?r<=n:r>=n;t=0<=n?++r:--r)if(!this.children[t].isEqualNode(e.children[t]))return!1;return!0},e.prototype.getFeature=function(e,t){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.setUserData=function(e,t,r){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.getUserData=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},e.prototype.contains=function(e){return!!e&&(e===this||this.isDescendant(e))},e.prototype.isDescendant=function(e){var t,r,n,i;for(r=0,n=(i=this.children).length;r<n;r++){if(e===(t=i[r]))return!0;if(t.isDescendant(e))return!0}return!1},e.prototype.isAncestor=function(e){return e.isDescendant(this)},e.prototype.isPreceding=function(e){var t,r;return t=this.treePosition(e),r=this.treePosition(this),-1!==t&&-1!==r&&t<r},e.prototype.isFollowing=function(e){var t,r;return t=this.treePosition(e),r=this.treePosition(this),-1!==t&&-1!==r&&t>r},e.prototype.treePosition=function(e){var t,r;return r=0,t=!1,this.foreachTreeNode(this.document(),(function(n){if(r++,!t&&n===e)return t=!0})),t?r:-1},e.prototype.foreachTreeNode=function(e,t){var r,n,i,a,o;for(e||(e=this.document()),n=0,i=(a=e.children).length;n<i;n++){if(o=t(r=a[n]))return o;if(o=this.foreachTreeNode(r,t))return o}},e}()}).call(this)},2390:function(e){(function(){e.exports=function(){function e(e){this.nodes=e}return Object.defineProperty(e.prototype,"length",{get:function(){return this.nodes.length||0}}),e.prototype.clone=function(){return this.nodes=null},e.prototype.item=function(e){return this.nodes[e]||null},e}()}).call(this)},4981:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;t=r(9335),n=r(6488),e.exports=function(e){function r(e,n,i){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing instruction target. "+this.debugInfo());this.type=t.ProcessingInstruction,this.target=this.stringify.insTarget(n),this.name=this.target,i&&(this.value=this.stringify.insValue(i))}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(e))},r.prototype.isEqualNode=function(e){return!!r.__super__.isEqualNode.apply(this,arguments).isEqualNode(e)&&e.target===this.target},r}(n)}).call(this)},9406:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;t=r(9335),n=r(2026),e.exports=function(e){function r(e,n){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing raw text. "+this.debugInfo());this.type=t.Raw,this.value=this.stringify.raw(n)}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return this.options.writer.raw(this,this.options.writer.filterOptions(e))},r}(n)}).call(this)},1996:function(e,t,r){(function(){var t,n,i,a={}.hasOwnProperty;t=r(9335),i=r(751),n=r(594),e.exports=function(e){function r(e,t){this.stream=e,r.__super__.constructor.call(this,t)}return function(e,t){for(var r in t)a.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),r.prototype.endline=function(e,t,i){return e.isLastRootNode&&t.state===n.CloseTag?"":r.__super__.endline.call(this,e,t,i)},r.prototype.document=function(e,t){var r,n,i,a,o,s,u,l,c;for(n=i=0,o=(u=e.children).length;i<o;n=++i)(r=u[n]).isLastRootNode=n===e.children.length-1;for(t=this.filterOptions(t),c=[],a=0,s=(l=e.children).length;a<s;a++)r=l[a],c.push(this.writeChildNode(r,t,0));return c},r.prototype.attribute=function(e,t,n){return this.stream.write(r.__super__.attribute.call(this,e,t,n))},r.prototype.cdata=function(e,t,n){return this.stream.write(r.__super__.cdata.call(this,e,t,n))},r.prototype.comment=function(e,t,n){return this.stream.write(r.__super__.comment.call(this,e,t,n))},r.prototype.declaration=function(e,t,n){return this.stream.write(r.__super__.declaration.call(this,e,t,n))},r.prototype.docType=function(e,t,r){var i,a,o,s;if(r||(r=0),this.openNode(e,t,r),t.state=n.OpenTag,this.stream.write(this.indent(e,t,r)),this.stream.write("<!DOCTYPE "+e.root().name),e.pubID&&e.sysID?this.stream.write(' PUBLIC "'+e.pubID+'" "'+e.sysID+'"'):e.sysID&&this.stream.write(' SYSTEM "'+e.sysID+'"'),e.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(e,t,r)),t.state=n.InsideTag,a=0,o=(s=e.children).length;a<o;a++)i=s[a],this.writeChildNode(i,t,r+1);t.state=n.CloseTag,this.stream.write("]")}return t.state=n.CloseTag,this.stream.write(t.spaceBeforeSlash+">"),this.stream.write(this.endline(e,t,r)),t.state=n.None,this.closeNode(e,t,r)},r.prototype.element=function(e,r,i){var o,s,u,l,c,d,h,p,f;for(h in i||(i=0),this.openNode(e,r,i),r.state=n.OpenTag,this.stream.write(this.indent(e,r,i)+"<"+e.name),p=e.attribs)a.call(p,h)&&(o=p[h],this.attribute(o,r,i));if(l=0===(u=e.children.length)?null:e.children[0],0===u||e.children.every((function(e){return(e.type===t.Text||e.type===t.Raw)&&""===e.value})))r.allowEmpty?(this.stream.write(">"),r.state=n.CloseTag,this.stream.write("</"+e.name+">")):(r.state=n.CloseTag,this.stream.write(r.spaceBeforeSlash+"/>"));else if(!r.pretty||1!==u||l.type!==t.Text&&l.type!==t.Raw||null==l.value){for(this.stream.write(">"+this.endline(e,r,i)),r.state=n.InsideTag,c=0,d=(f=e.children).length;c<d;c++)s=f[c],this.writeChildNode(s,r,i+1);r.state=n.CloseTag,this.stream.write(this.indent(e,r,i)+"</"+e.name+">")}else this.stream.write(">"),r.state=n.InsideTag,r.suppressPrettyCount++,!0,this.writeChildNode(l,r,i+1),r.suppressPrettyCount--,!1,r.state=n.CloseTag,this.stream.write("</"+e.name+">");return this.stream.write(this.endline(e,r,i)),r.state=n.None,this.closeNode(e,r,i)},r.prototype.processingInstruction=function(e,t,n){return this.stream.write(r.__super__.processingInstruction.call(this,e,t,n))},r.prototype.raw=function(e,t,n){return this.stream.write(r.__super__.raw.call(this,e,t,n))},r.prototype.text=function(e,t,n){return this.stream.write(r.__super__.text.call(this,e,t,n))},r.prototype.dtdAttList=function(e,t,n){return this.stream.write(r.__super__.dtdAttList.call(this,e,t,n))},r.prototype.dtdElement=function(e,t,n){return this.stream.write(r.__super__.dtdElement.call(this,e,t,n))},r.prototype.dtdEntity=function(e,t,n){return this.stream.write(r.__super__.dtdEntity.call(this,e,t,n))},r.prototype.dtdNotation=function(e,t,n){return this.stream.write(r.__super__.dtdNotation.call(this,e,t,n))},r}(i)}).call(this)},6434:function(e,t,r){(function(){var t,n={}.hasOwnProperty;t=r(751),e.exports=function(e){function t(e){t.__super__.constructor.call(this,e)}return function(e,t){for(var r in t)n.call(t,r)&&(e[r]=t[r]);function i(){this.constructor=e}i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype}(t,e),t.prototype.document=function(e,t){var r,n,i,a,o;for(t=this.filterOptions(t),a="",n=0,i=(o=e.children).length;n<i;n++)r=o[n],a+=this.writeChildNode(r,t,0);return t.pretty&&a.slice(-t.newline.length)===t.newline&&(a=a.slice(0,-t.newline.length)),a},t}(t)}).call(this)},5549:function(e){(function(){var t=function(e,t){return function(){return e.apply(t,arguments)}},r={}.hasOwnProperty;e.exports=function(){function e(e){var n,i,a;for(n in this.assertLegalName=t(this.assertLegalName,this),this.assertLegalChar=t(this.assertLegalChar,this),e||(e={}),this.options=e,this.options.version||(this.options.version="1.0"),i=e.stringify||{})r.call(i,n)&&(a=i[n],this[n]=a)}return e.prototype.name=function(e){return this.options.noValidation?e:this.assertLegalName(""+e||"")},e.prototype.text=function(e){return this.options.noValidation?e:this.assertLegalChar(this.textEscape(""+e||""))},e.prototype.cdata=function(e){return this.options.noValidation?e:(e=(e=""+e||"").replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(e))},e.prototype.comment=function(e){if(this.options.noValidation)return e;if((e=""+e||"").match(/--/))throw new Error("Comment text cannot contain double-hypen: "+e);return this.assertLegalChar(e)},e.prototype.raw=function(e){return this.options.noValidation?e:""+e||""},e.prototype.attValue=function(e){return this.options.noValidation?e:this.assertLegalChar(this.attEscape(e=""+e||""))},e.prototype.insTarget=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.insValue=function(e){if(this.options.noValidation)return e;if((e=""+e||"").match(/\?>/))throw new Error("Invalid processing instruction value: "+e);return this.assertLegalChar(e)},e.prototype.xmlVersion=function(e){if(this.options.noValidation)return e;if(!(e=""+e||"").match(/1\.[0-9]+/))throw new Error("Invalid version number: "+e);return e},e.prototype.xmlEncoding=function(e){if(this.options.noValidation)return e;if(!(e=""+e||"").match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+e);return this.assertLegalChar(e)},e.prototype.xmlStandalone=function(e){return this.options.noValidation?e:e?"yes":"no"},e.prototype.dtdPubID=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdSysID=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdElementValue=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdAttType=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdAttDefault=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdEntityValue=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.dtdNData=function(e){return this.options.noValidation?e:this.assertLegalChar(""+e||"")},e.prototype.convertAttKey="@",e.prototype.convertPIKey="?",e.prototype.convertTextKey="#text",e.prototype.convertCDataKey="#cdata",e.prototype.convertCommentKey="#comment",e.prototype.convertRawKey="#raw",e.prototype.assertLegalChar=function(e){var t,r;if(this.options.noValidation)return e;if(t="","1.0"===this.options.version){if(t=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,r=e.match(t))throw new Error("Invalid character in string: "+e+" at index "+r.index)}else if("1.1"===this.options.version&&(t=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,r=e.match(t)))throw new Error("Invalid character in string: "+e+" at index "+r.index);return e},e.prototype.assertLegalName=function(e){var t;if(this.options.noValidation)return e;if(this.assertLegalChar(e),t=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!e.match(t))throw new Error("Invalid character in name");return e},e.prototype.textEscape=function(e){var t;return this.options.noValidation?e:(t=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,e.replace(t,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;"))},e.prototype.attEscape=function(e){var t;return this.options.noValidation?e:(t=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,e.replace(t,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;"))},e}()}).call(this)},3595:function(e,t,r){(function(){var t,n,i={}.hasOwnProperty;t=r(9335),n=r(6488),e.exports=function(e){function r(e,n){if(r.__super__.constructor.call(this,e),null==n)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=t.Text,this.value=this.stringify.text(n)}return function(e,t){for(var r in t)i.call(t,r)&&(e[r]=t[r]);function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype}(r,e),Object.defineProperty(r.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(r.prototype,"wholeText",{get:function(){var e,t,r;for(r="",t=this.previousSibling;t;)r=t.data+r,t=t.previousSibling;for(r+=this.data,e=this.nextSibling;e;)r+=e.data,e=e.nextSibling;return r}}),r.prototype.clone=function(){return Object.create(this)},r.prototype.toString=function(e){return this.options.writer.text(this,this.options.writer.filterOptions(e))},r.prototype.splitText=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r.prototype.replaceWholeText=function(e){throw new Error("This DOM method is not implemented."+this.debugInfo())},r}(n)}).call(this)},751:function(e,t,r){(function(){var t,n,i,a={}.hasOwnProperty;i=r(8369).assign,t=r(9335),r(9077),r(6544),r(6170),r(2096),r(2161),r(9406),r(3595),r(4981),r(8833),r(1179),r(6347),r(9078),r(4777),n=r(594),e.exports=function(){function e(e){var t,r,n;for(t in e||(e={}),this.options=e,r=e.writer||{})a.call(r,t)&&(n=r[t],this["_"+t]=this[t],this[t]=n)}return e.prototype.filterOptions=function(e){var t,r,a,o,s,u,l,c;return e||(e={}),e=i({},this.options,e),(t={writer:this}).pretty=e.pretty||!1,t.allowEmpty=e.allowEmpty||!1,t.indent=null!=(r=e.indent)?r:"  ",t.newline=null!=(a=e.newline)?a:"\n",t.offset=null!=(o=e.offset)?o:0,t.dontPrettyTextNodes=null!=(s=null!=(u=e.dontPrettyTextNodes)?u:e.dontprettytextnodes)?s:0,t.spaceBeforeSlash=null!=(l=null!=(c=e.spaceBeforeSlash)?c:e.spacebeforeslash)?l:"",!0===t.spaceBeforeSlash&&(t.spaceBeforeSlash=" "),t.suppressPrettyCount=0,t.user={},t.state=n.None,t},e.prototype.indent=function(e,t,r){var n;return!t.pretty||t.suppressPrettyCount?"":t.pretty&&(n=(r||0)+t.offset+1)>0?new Array(n).join(t.indent):""},e.prototype.endline=function(e,t,r){return!t.pretty||t.suppressPrettyCount?"":t.newline},e.prototype.attribute=function(e,t,r){var n;return this.openAttribute(e,t,r),n=" "+e.name+'="'+e.value+'"',this.closeAttribute(e,t,r),n},e.prototype.cdata=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<![CDATA[",t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+="]]>"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.comment=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"\x3c!-- ",t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=" --\x3e"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.declaration=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<?xml",t.state=n.InsideTag,i+=' version="'+e.version+'"',null!=e.encoding&&(i+=' encoding="'+e.encoding+'"'),null!=e.standalone&&(i+=' standalone="'+e.standalone+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+"?>",i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.docType=function(e,t,r){var i,a,o,s,u;if(r||(r=0),this.openNode(e,t,r),t.state=n.OpenTag,s=this.indent(e,t,r),s+="<!DOCTYPE "+e.root().name,e.pubID&&e.sysID?s+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(s+=' SYSTEM "'+e.sysID+'"'),e.children.length>0){for(s+=" [",s+=this.endline(e,t,r),t.state=n.InsideTag,a=0,o=(u=e.children).length;a<o;a++)i=u[a],s+=this.writeChildNode(i,t,r+1);t.state=n.CloseTag,s+="]"}return t.state=n.CloseTag,s+=t.spaceBeforeSlash+">",s+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),s},e.prototype.element=function(e,r,i){var o,s,u,l,c,d,h,p,f,y,g,m,v,_;for(f in i||(i=0),y=!1,g="",this.openNode(e,r,i),r.state=n.OpenTag,g+=this.indent(e,r,i)+"<"+e.name,m=e.attribs)a.call(m,f)&&(o=m[f],g+=this.attribute(o,r,i));if(l=0===(u=e.children.length)?null:e.children[0],0===u||e.children.every((function(e){return(e.type===t.Text||e.type===t.Raw)&&""===e.value})))r.allowEmpty?(g+=">",r.state=n.CloseTag,g+="</"+e.name+">"+this.endline(e,r,i)):(r.state=n.CloseTag,g+=r.spaceBeforeSlash+"/>"+this.endline(e,r,i));else if(!r.pretty||1!==u||l.type!==t.Text&&l.type!==t.Raw||null==l.value){if(r.dontPrettyTextNodes)for(c=0,h=(v=e.children).length;c<h;c++)if(((s=v[c]).type===t.Text||s.type===t.Raw)&&null!=s.value){r.suppressPrettyCount++,y=!0;break}for(g+=">"+this.endline(e,r,i),r.state=n.InsideTag,d=0,p=(_=e.children).length;d<p;d++)s=_[d],g+=this.writeChildNode(s,r,i+1);r.state=n.CloseTag,g+=this.indent(e,r,i)+"</"+e.name+">",y&&r.suppressPrettyCount--,g+=this.endline(e,r,i),r.state=n.None}else g+=">",r.state=n.InsideTag,r.suppressPrettyCount++,y=!0,g+=this.writeChildNode(l,r,i+1),r.suppressPrettyCount--,y=!1,r.state=n.CloseTag,g+="</"+e.name+">"+this.endline(e,r,i);return this.closeNode(e,r,i),g},e.prototype.writeChildNode=function(e,r,n){switch(e.type){case t.CData:return this.cdata(e,r,n);case t.Comment:return this.comment(e,r,n);case t.Element:return this.element(e,r,n);case t.Raw:return this.raw(e,r,n);case t.Text:return this.text(e,r,n);case t.ProcessingInstruction:return this.processingInstruction(e,r,n);case t.Dummy:return"";case t.Declaration:return this.declaration(e,r,n);case t.DocType:return this.docType(e,r,n);case t.AttributeDeclaration:return this.dtdAttList(e,r,n);case t.ElementDeclaration:return this.dtdElement(e,r,n);case t.EntityDeclaration:return this.dtdEntity(e,r,n);case t.NotationDeclaration:return this.dtdNotation(e,r,n);default:throw new Error("Unknown XML node type: "+e.constructor.name)}},e.prototype.processingInstruction=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<?",t.state=n.InsideTag,i+=e.target,e.value&&(i+=" "+e.value),t.state=n.CloseTag,i+=t.spaceBeforeSlash+"?>",i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.raw=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r),t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.text=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r),t.state=n.InsideTag,i+=e.value,t.state=n.CloseTag,i+=this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.dtdAttList=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ATTLIST",t.state=n.InsideTag,i+=" "+e.elementName+" "+e.attributeName+" "+e.attributeType,"#DEFAULT"!==e.defaultValueType&&(i+=" "+e.defaultValueType),e.defaultValue&&(i+=' "'+e.defaultValue+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.dtdElement=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ELEMENT",t.state=n.InsideTag,i+=" "+e.name+" "+e.value,t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.dtdEntity=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!ENTITY",t.state=n.InsideTag,e.pe&&(i+=" %"),i+=" "+e.name,e.value?i+=' "'+e.value+'"':(e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),e.nData&&(i+=" NDATA "+e.nData)),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.dtdNotation=function(e,t,r){var i;return this.openNode(e,t,r),t.state=n.OpenTag,i=this.indent(e,t,r)+"<!NOTATION",t.state=n.InsideTag,i+=" "+e.name,e.pubID&&e.sysID?i+=' PUBLIC "'+e.pubID+'" "'+e.sysID+'"':e.pubID?i+=' PUBLIC "'+e.pubID+'"':e.sysID&&(i+=' SYSTEM "'+e.sysID+'"'),t.state=n.CloseTag,i+=t.spaceBeforeSlash+">"+this.endline(e,t,r),t.state=n.None,this.closeNode(e,t,r),i},e.prototype.openNode=function(e,t,r){},e.prototype.closeNode=function(e,t,r){},e.prototype.openAttribute=function(e,t,r){},e.prototype.closeAttribute=function(e,t,r){},e}()}).call(this)},5532:function(e,t,r){(function(){var t,n,i,a,o,s,u,l,c,d;d=r(8369),l=d.assign,c=d.isFunction,i=r(1770),a=r(6934),o=r(9227),u=r(6434),s=r(1996),t=r(9335),n=r(594),e.exports.create=function(e,t,r,n){var i,o;if(null==e)throw new Error("Root element needs a name.");return n=l({},t,r,n),o=(i=new a(n)).element(e),n.headless||(i.declaration(n),null==n.pubID&&null==n.sysID||i.dtd(n)),o},e.exports.begin=function(e,t,r){var n;return c(e)&&(t=(n=[e,t])[0],r=n[1],e={}),t?new o(e,t,r):new a(e)},e.exports.stringWriter=function(e){return new u(e)},e.exports.streamWriter=function(e,t){return new s(e,t)},e.exports.implementation=new i,e.exports.nodeType=t,e.exports.writerState=n}).call(this)}},function(e){var t;return t=5993,e(e.s=t)}])}));