/*! For license information please see voplayer-smartlib.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var i=t();for(var n in i)("object"==typeof exports?exports:e)[n]=i[n]}}(self,(function(){return(self.webpackChunkvoplayer_html5=self.webpackChunkvoplayer_html5||[]).push([[154],{4239:function(e,t,i){"use strict";var n,r,a,s;function o(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return d(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return d(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function l(e,t,i,n){return h(e,t),c(i,"set"),function(e,t,i){if(t.set)t.set.call(e,i);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=i}}(e,i,n),n}function u(e,t,i){return h(e,t),c(i,"get"),function(e,t){if(t.get)return t.get.call(e);return t.value}(e,i)}function c(e,t){if(void 0===e)throw new TypeError("attempted to "+t+" private static field before its declaration")}function h(e,t){if(e!==t)throw new TypeError("Private static access of wrong provenance")}function v(){return v="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=S(e)););return e}(e,t);if(n){var r=Object.getOwnPropertyDescriptor(n,t);return r.get?r.get.call(arguments.length<3?e:i):r.value}},v.apply(this,arguments)}function p(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}function f(e,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},f(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=S(e);if(t){var r=S(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return function(e,t){if(t&&("object"===k(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return g(e)}(this,i)}}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function S(e){return S=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},S(e)}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function E(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function m(e,t,i){return t&&E(e.prototype,t),i&&E(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function A(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}e=i.nmd(e),"undefined"!=typeof self?self:i.g,s=function(){return(("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule=("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule||[]).push([[256],{7575:function(e,t,i){i.r(t),i.d(t,{AdBreakTracker:function(){return N},AdDataTracker:function(){return k},AdEventTracker:function(){return P},AdFriendlyObstructionPurpose:function(){return C.D6},AdManager:function(){return C.X2},AdMetrics:function(){return r},AdMetricsBuilder:function(){return a},AdMetricsManager:function(){return T},AdSession:function(){return O.A},AdTracker:function(){return D},AdTrackingManager:function(){return b},AdViewState:function(){return C.up},InternalAdManager:function(){return L.A},OMSDKManager:function(){return w},OMSessionHandler:function(){return F}});var n=i(3445),r=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;_(this,e),A(this,"adSkippable",void 0),A(this,"adSkipped",void 0),A(this,"adProgress",void 0),A(this,"adDuration",void 0),A(this,"stallsNumber",void 0),A(this,"stallsDuration",void 0),A(this,"layerSwitchesNumber",void 0),A(this,"averageBitrate",void 0),A(this,"creativeId",void 0),A(this,"adId",void 0),A(this,"adIndex",void 0),A(this,"adCount",void 0),A(this,"adFormat",void 0),A(this,"impressionDate",void 0),void 0===t?(this.adSkippable=!1,this.adSkipped=!1,this.adProgress=-1,this.adDuration=0,this.stallsNumber=0,this.stallsDuration=0,this.layerSwitchesNumber=0,this.averageBitrate=0,this.creativeId="",this.adId="",this.adIndex=-1,this.adCount=-1,this.adFormat="",this.impressionDate=-1):(this.adSkippable=t.adSkippable,this.adSkipped=t.adSkipped,this.adProgress=t.adProgress,this.adDuration=t.adDuration,this.stallsNumber=t.stallsNumber,this.stallsDuration=t.stallsDuration,this.layerSwitchesNumber=t.layerSwitchesNumber,this.averageBitrate=t.averageBitrate,this.creativeId=t.creativeId,this.adId=t.adId,this.adIndex=t.adIndex,this.adCount=t.adCount,this.adFormat=t.adFormat,this.impressionDate=t.impressionDate)}return m(e,[{key:"toString",value:function(){return"\n{ adSkippable="+this.adSkippable+"\n  adSkipped="+this.adSkipped+"\n  adProgress="+this.adProgress+"\n  adDuration="+this.adDuration+"\n  stallsNumber="+this.stallsNumber+"\n  stallsDuration="+this.stallsDuration+"\n  layerSwitchesNumber="+this.layerSwitchesNumber+"\n  averageBitrate="+this.averageBitrate+"\n  creativeId='"+this.creativeId+"'\n  adId='"+this.adId+"'\n  adIndex="+this.adIndex+"\n  adCount="+this.adCount+"\n  adFormat='"+this.adFormat+"'\n  impressionDate="+this.impressionDate+" ("+n.A.formatTime(this.impressionDate)+")\n}"}}],[{key:"merge",value:function(t){if(void 0!==t&&t.length>0){var i=new e,n=t[t.length-1];i.adSkippable=n.adSkippable,i.adSkipped=n.adSkipped,i.adProgress=n.adProgress,i.creativeId=n.creativeId,i.adId=n.adId;for(var r=0,a=0,s=0;s<t.length;s++){var o=t[s];i.adDuration+=o.adDuration,i.stallsNumber+=o.stallsNumber,i.stallsDuration+=o.stallsDuration,i.layerSwitchesNumber+=o.layerSwitchesNumber,r+=o.averageBitrate*o.adDuration,a+=o.adDuration}return 0!==a&&(i.averageBitrate=Math.round(r/a)),i}}}]),e}(),a=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;_(this,e),A(this,"adMetrics",void 0),A(this,"timeSpentPerLayer",void 0),A(this,"quartiles",void 0),void 0===t&&void 0===i&&void 0===n?(this.timeSpentPerLayer={},this.quartiles={},this.reset()):(this.adMetrics=t,this.timeSpentPerLayer=i,this.quartiles=n)}return m(e,[{key:"isInitialized",value:function(){return""!==this.adMetrics.adId}},{key:"import",value:function(e){return void 0!==e&&1===e.length&&e[0].impressionDate<=0&&(this.adMetrics=e[0]),this.adMetrics.impressionDate=Date.now(),this}},{key:"setAdSkippable",value:function(e){return this.adMetrics.adSkippable=e,this}},{key:"setAdSkipped",value:function(e){return this.adMetrics.adSkipped=e,this}},{key:"addProgress",value:function(e){return this.quartiles[e]=!0,this.adMetrics.adProgress=Math.max(this.adMetrics.adProgress,e),this}},{key:"init",value:function(e,t,i){return this.adMetrics.adFormat=e,this.adMetrics.adIndex=t,this.adMetrics.adCount=i,this}},{key:"setCreativeId",value:function(e){return this.adMetrics.creativeId=e,this}},{key:"setAdId",value:function(e){return this.adMetrics.adId=e,this}},{key:"addTimeSpentPerLayer",value:function(e,t){return(e=Math.round(e))>0&&(void 0!==this.timeSpentPerLayer[e]?this.timeSpentPerLayer[e]+=t:this.timeSpentPerLayer[e]=t),this}},{key:"addLayerSwitch",value:function(){return this.adMetrics.layerSwitchesNumber++,this}},{key:"addStall",value:function(e){return this.adMetrics.stallsNumber++,this.adMetrics.stallsDuration+=e,this}},{key:"reset",value:function(){return this.adMetrics=new r,this.timeSpentPerLayer={},this.quartiles={},this}},{key:"clone",value:function(){return new e(new r(this.adMetrics),Object.assign({},this.timeSpentPerLayer),Object.assign({},this.quartiles))}},{key:"build",value:function(){var e=0,t=0;for(var i in this.timeSpentPerLayer){var n=this.timeSpentPerLayer[i];e+=i*n,t+=n}return 0!==t&&(this.adMetrics.averageBitrate=Math.round(e/t)),this.adMetrics.adDuration=t,this.adMetrics}}]),e}(),s=i(8379),d=i(1142),c=i(1134),h=i(6506),f="BpkAdTracker",E=function(){function e(){_(this,e),A(this,"proceeded",void 0),this.proceeded={}}return m(e,[{key:"canProcess",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=void 0===this.proceeded[e];return t&&(this.proceeded[e]=Date.now()),t}},{key:"resetProcess",value:function(){this.proceeded={}}}]),e}(),k=function(){function e(t,i,n){_(this,e),A(this,"adTrackingManager",void 0),A(this,"sessionToken",void 0),A(this,"timeReference",void 0),A(this,"adBreaks",void 0),this.adTrackingManager=t,this.sessionToken=i,this.timeReference=n,this.adBreaks=[]}return m(e,[{key:"hasRemainingAdBreaks",value:function(e){return void 0!==this.adBreaks.find((function(t){return e<t.position+t.duration}))}},{key:"resetProgression",value:function(e){this.adBreaks.forEach((function(t){return t.resetProgression(e)}))}}]),e}(),N=function(e){p(i,e);var t=y(i);function i(e,n,r,a,s){var o;return _(this,i),A(g(o=t.call(this)),"adData",void 0),A(g(o),"id",void 0),A(g(o),"position",void 0),A(g(o),"duration",void 0),A(g(o),"live",void 0),A(g(o),"ads",void 0),o.adData=e,o.id=n,o.position=r,o.duration=a,o.live=s,o.ads=[],o}return m(i,[{key:"resetProgression",value:function(e){e<=this.position&&this.resetProcess(),this.ads.forEach((function(t){return t.resetProgression(e)}))}},{key:"processBegin",value:function(){var e;if(this.canProcess(0)){var t=this.adData.adTrackingManager;s.g.d(f,"Processing ad break begin...",t.handler.id),s.g.d(f,"Duration: "+this.duration+"ms",t.handler.id),t.notifyAdBreakData(this),t.notifyAdBreakBegin(this.adData.sessionToken);var i=null===(e=t.handler.adSession)||void 0===e?void 0:e.adEventsListener;null==i||i.onAdBreakBegin(this.toData())}}},{key:"processEnd",value:function(){var e;if(this.canProcess(1)){var t=this.adData.adTrackingManager;s.g.d(f,"Processing ad break end...",t.handler.id),t.notifyAdBreakEnd(this.adData.sessionToken);var i=null===(e=t.handler.adSession)||void 0===e?void 0:e.adEventsListener;null!=i&&i.onAdBreakEnd(this.toData()),this.resetProcess()}}},{key:"resetProcess",value:function(){v(S(i.prototype),"resetProcess",this).call(this),this.ads.forEach((function(e){return e.resetProcess()}))}},{key:"toData",value:function(){return{id:this.id,startPosition:this.position||0,duration:!0===this.live?-1:this.duration,ads:this.ads.map((function(e){return e.toData()})),adCount:!0===this.live?-1:this.ads.length}}}]),i}(E),D=function(e){p(i,e);var t=y(i);function i(e,n,r,a,s,o,d,l,u,c){var h;return _(this,i),A(g(h=t.call(this)),"adBreak",void 0),A(g(h),"index",void 0),A(g(h),"position",void 0),A(g(h),"duration",void 0),A(g(h),"skippablePosition",void 0),A(g(h),"skippable",void 0),A(g(h),"creativeId",void 0),A(g(h),"adId",void 0),A(g(h),"events",void 0),A(g(h),"clickable",void 0),A(g(h),"verifications",void 0),A(g(h),"watched",void 0),A(g(h),"progression",void 0),h.adBreak=e,h.index=n,h.position=r,h.duration=a,h.skippable=s,h.skippablePosition=o,h.creativeId=d,h.adId=l,h.events=[],h.clickable=u,h.verifications=c,h.watched=[],h.progression=0,h}return m(i,[{key:"flatWatched",value:function(){var e=JSON.parse(JSON.stringify(this.watched)).slice(0),t=[],i=null;e=e.sort((function(e,t){return e[0]>t[0]?1:e[0]<t[0]?-1:0})),t.push(e[0]);for(var n=1;n<e.length;n++)(i=t[t.length-1])[1]<e[n][0]?t.push(e[n]):i[1]<e[n][1]&&(i[1]=e[n][1],t.pop(),t.push(i));this.watched=t}},{key:"resetProgression",value:function(e){e<=this.position&&(this.watched=[],this.progression=0,this.resetProcess()),this.events.forEach((function(t){return t.resetProgression(e)}))}},{key:"updateProgression",value:function(e,t){if(!(e>t||e<this.position||t<this.position||e>this.position+this.duration||t>this.position+this.duration)){var i=this.watched.reduce((function(e,t){return e+(t[1]-t[0])}),0)/this.duration;this.watched.push([e-this.position,t-this.position]),this.flatWatched();var n=this.watched.reduce((function(e,t){return e+(t[1]-t[0])}),0)/this.duration;this.progression=n;var r=this.adBreak.adData,a=r.adTrackingManager;i<=.25&&n>=.25&&a.notifyAdProgress(r.sessionToken,this,25),i<=.5&&n>=.5&&a.notifyAdProgress(r.sessionToken,this,50),i<=.75&&n>=.75&&a.notifyAdProgress(r.sessionToken,this,75),this.events.forEach((function(e){return e.processEvents(i,n)})),1===i&&1===n?s.g.d(f,"Ad already seen (100%)",a.handler.id):s.g.d(f,"Ad progressed from "+Math.floor(1e5*i)/1e3+"% to "+Math.floor(1e5*n)/1e3+"%",a.handler.id)}}},{key:"processBegin",value:function(){var e;if(this.canProcess(0)){var t=this.adBreak.adData,i=t.adTrackingManager;s.g.d(f,"Processing ad begin "+this.adId+"...",i.handler.id),s.g.d(f,"Start: "+this.position+"ms",i.handler.id),s.g.d(f,"End  : "+(this.position+this.duration)+"ms",i.handler.id),s.g.d(f,"Duration: "+this.duration+"ms",i.handler.id),i.notifyAdData(this),i.notifyAdBegin(t.sessionToken,this),!0===this.skippable&&i.notifyAdSkippable(t.sessionToken,this.skippablePosition,this.position+this.duration,this.adBreak.position+this.adBreak.duration),i.notifyAdProgress(t.sessionToken,this,0);var n=null===(e=i.handler.adSession)||void 0===e?void 0:e.adEventsListener,r=this.toData(),a=this.adBreak.toData();null!=n&&n.onAdBegin(r,a),!0===this.skippable&&(null==n||n.onAdSkippable(r,a,this.skippablePosition,this.position+this.duration,this.adBreak.position+this.adBreak.duration))}}},{key:"processEnd",value:function(){var e;if(this.canProcess(1)){var t=this.adBreak.adData,i=t.adTrackingManager;s.g.d(f,"Processing ad end "+this.adId+"...",i.handler.id),this.progression>=.95&&(this.updateProgression(this.position,this.position+this.duration),i.notifyAdProgress(t.sessionToken,this,100)),i.notifyAdEnd(t.sessionToken,this);var n=null===(e=i.handler.adSession)||void 0===e?void 0:e.adEventsListener;null!=n&&n.onAdEnd(this.toData(),this.adBreak.toData()),this.resetProcess()}}},{key:"toData",value:function(){return{index:this.index,creativeId:this.creativeId,adId:this.adId,startPosition:this.position,skipPosition:this.skippablePosition,duration:this.duration,clickURL:this.clickable.uri}}}]),i}(E),P=function(e){p(i,e);var t=y(i);function i(e,n,r,a,s){var o;return _(this,i),A(g(o=t.call(this)),"ad",void 0),A(g(o),"type",void 0),A(g(o),"url",void 0),A(g(o),"offset",void 0),A(g(o),"position",void 0),A(g(o),"progression",void 0),o.ad=e,o.type=n,o.url=r,o.offset=a,o.position=s,o.progression=0,o.processProgression(),o}return m(i,[{key:"resetProgression",value:function(e){e<=this.ad.position&&this.resetProcess()}},{key:"processProgression",value:function(){switch(void 0!==this.type?this.type.toLowerCase():void 0){case void 0:this.progression=(this.position-this.ad.position)/this.ad.duration;break;case"start":this.progression=0;break;case"firstquartile":this.progression=.25;break;case"midpoint":this.progression=.5;break;case"thirdquartile":this.progression=.75;break;case"complete":this.progression=1;break;case"progress":this.progression=this.offset/this.ad.duration;break;case"impression":this.progression=0}}},{key:"processEvents",value:function(e,t){var i=this.ad.adBreak.adData.adTrackingManager;if(e<=this.progression&&this.progression<=t){if(!this.canProcess())return!1;s.g.d(f,"Processing "+(this.type||"timed event")+" ("+Math.floor(100*this.progression)+"%)...",i.handler.id),void 0!==this.url&&this.url.length>0&&(s.g.d(f,"Requesting "+this.url,i.handler.id),h.A.getInstance().adEvent(i.handler,this.url))}return!0}}]),i}(E),R="BpkAdTrackingMgr",b=function(){function e(t,i){_(this,e),A(this,"handler",void 0),A(this,"playerAdapter",void 0),A(this,"listeners",void 0),A(this,"adData",void 0),A(this,"adList",void 0),A(this,"updatePositionJob",void 0),A(this,"updateSessionJob",void 0),A(this,"started",void 0),A(this,"paused",void 0),A(this,"buffering",void 0),A(this,"lastPosition",void 0),A(this,"lastPositionBeforePause",void 0),A(this,"lastPositionAfterSeek",void 0),A(this,"firstImageDate",void 0),A(this,"currentAdTracker",void 0),A(this,"currentAdBreakTracker",void 0),A(this,"currentAdData",void 0),A(this,"currentAdBreakData",void 0),A(this,"adPalSession",void 0),A(this,"bkYouSession",void 0),A(this,"sessionToken",void 0),A(this,"baseURL",void 0),A(this,"nonce",void 0),A(this,"adPalSessionRequest",void 0),A(this,"firstFileReceived",void 0),A(this,"firstFileProceeded",void 0),A(this,"podsSentNumber",void 0),A(this,"sessionUpdateInterval",void 0),A(this,"positionHistory",void 0),this.handler=t,this.playerAdapter=i,this.listeners=[],this.adData=void 0,this.adList=[],this.updatePositionJob=void 0,this.updateSessionJob=void 0,this.started=!1,this.paused=!1,this.buffering=!1,this.lastPosition=0,this.lastPositionBeforePause=0,this.lastPositionAfterSeek=0,this.bkYouSession=!1,this.baseURL=void 0,this.nonce=void 0,this.adPalSessionRequest=void 0,this.firstFileReceived=!1,this.firstFileProceeded=!1,this.podsSentNumber=0,this.sessionUpdateInterval=e.SESSION_UPDATE_INTERVAL,this.positionHistory=[]}return m(e,[{key:"initBkYouSession",value:function(e,t,i,n,r){this.baseURL=e,this.sessionToken=t,this.bkYouSession=!0,void 0!==n&&(this.adPalSession=n,this.nonce=r),this.parseAdPods(i),s.g.d(R,"BkYou session initialized",this.handler.id)}},{key:"updateBkYouSession",value:function(){var e=this;if(!0!==this.handler.stopped&&!0===this.bkYouSession){s.g.d(R,"Updating ad tracking file...",this.handler.id);var t=this.baseURL,i={userAgent:this.handler.smartLib.getParameters().userAgent};h.A.getInstance().adTracking(this.handler,i,t,!0).then((function(t){if(!0!==e.handler.stopped)if(void 0!==e.updateSessionJob&&c.A.getInstance().cancel(e.updateSessionJob),t.httpStatus>=200&&t.httpStatus<300){var i;try{i=JSON.parse(t.content)}catch(t){return s.g.d(R,"Ad tracking updated file unreadable",e.handler.id),void(!0===e.firstFileReceived&&(e.updateSessionJob=c.A.getInstance().asyncDelay(e.sessionUpdateInterval,(function(){e.updateSessionJob=void 0,e.updateBkYouSession()}))))}e.firstFileReceived=!0,e.parseAdPods(i),e.isLive()?e.updateSessionJob=c.A.getInstance().asyncDelay(e.sessionUpdateInterval,(function(){e.updateSessionJob=void 0,e.updateBkYouSession()})):s.g.d(R,"Stopping ad tracking file update (VOD stream)...",e.handler.id)}else s.g.d(R,"Stopping ad tracking file update (status code "+t.httpStatus+")",e.handler.id)}))}}},{key:"fixAdBreak",value:function(e){var t;e.ads.forEach((function(i,n){var r=e.ads[n+1];void 0!==r&&r.position<i.position+i.duration&&(r.position=i.position+i.duration,r.events.filter((function(e){return e.position<r.position})).forEach((function(e){e.position=r.position}))),t=i})),void 0!==t&&(e.duration=t.position+t.duration-e.position)}},{key:"parseAdPods",value:function(t){var i=this,n=t.sessiontoken||"",r=t.timereference_ms||0,a=t.refresh_delay_ms||e.SESSION_UPDATE_INTERVAL;a>=2e3&&a<=e.SESSION_UPDATE_INTERVAL?(this.sessionUpdateInterval=a,s.g.d(R,"Setting refresh delay to "+this.sessionUpdateInterval+"ms",this.handler.id)):s.g.d(R,"Setting refresh delay to "+e.SESSION_UPDATE_INTERVAL+"ms (default value)",this.handler.id);var o=new k(this,n,r),d=t.adpods;Array.isArray(d)&&(d.forEach((function(e){var t=e.id||"",n=e.starttime_ms+r,a=e.duration_ms||0,s=e.ads,d=new N(o,t,n,a,i.isLive());Array.isArray(s)&&s.forEach((function(e,t){var i=e.starttime_ms+r,n=e.duration_ms,a=e.trackingevents,s=!0;if(Array.isArray(a)&&a.length>0&&(s=i>0&&a[0].time_ms>0||0===i),void 0!==i&&void 0!==n&&!0===s){var o,l,u,c=e.skippable_ms+r||0,h=0!==c&&null!=c,v=e.creativeid||"",p=e.adid+"-"+i||"",f={uri:(null===(o=e.videoclicks)||void 0===o?void 0:o.clickthroughurl)||"",trackers:(null===(l=e.videoclicks)||void 0===l?void 0:l.clicktracking)||[],customClick:(null===(u=e.videoclicks)||void 0===u?void 0:u.customclick)||[]},y=e.adverifications||[],g=[];y.forEach((function(e){g.push({vendor:e.vendor||"",javascriptResources:e.javascriptresources||[],executableResources:e.executableresources||[],trackingEvents:e.trackingevents||[],verificationParameters:e.verificationparameters||""})}));var S=new D(d,t,i,n,h,c,v,p,f,g);d.ads.push(S),Array.isArray(a)&&a.forEach((function(e){var t=e.callbackurl;if(void 0!==t){var n=e.type,a=e.offset_ms,s=e.time_ms+r||i,o=new P(S,n,t,a,s);S.events.push(o)}}))}})),d.ads.length>0&&o.adBreaks.push(d)})),o.adBreaks.forEach((function(e){i.fixAdBreak(e)})));var l=this.mergeEvents(o);this.adList=this.adData.adBreaks.map((function(e){return e.toData()})),this.notifyAdDataListener(l)}},{key:"start",value:function(){var t=this;void 0===this.updatePositionJob&&(this.updatePositionJob=c.A.getInstance().asyncDelay(e.POSITION_UPDATE_INTERVAL,(function(){t.updatePositionJob=void 0,t.onPositionUpdated(t.playerAdapter.getPosition())})))}},{key:"stop",value:function(){void 0!==this.updatePositionJob&&(s.g.d(R,"Ad tracking paused (player event)",this.handler.id),c.A.getInstance().cancel(this.updatePositionJob),this.updatePositionJob=void 0)}},{key:"isLive",value:function(){return void 0!==this.firstImageDate&&this.playerAdapter.getDuration()<=0}},{key:"mergeEvents",value:function(e){var t=this,i=!1;if(void 0===this.adData)this.adData=e,s.g.d(R,e.adBreaks.length+" ad break(s) parsed",this.handler.id),i=!0;else{var n=0,r=0;this.adData.sessionToken=e.sessionToken,this.adData.timeReference=e.timeReference;var a=e.adBreaks.map((function(e){return e.id}));this.adData.adBreaks.forEach((function(e,n,s){var o;a.includes(e.id)||(null===(o=t.currentAdTracker)||void 0===o?void 0:o.adBreak.id)===e.id||(s.splice(n,1),r++,i=!0)})),e.adBreaks.forEach((function(e,r){e.adData=t.adData;var a=t.adData.adBreaks.find((function(t){return t.id===e.id}));if(void 0!==a){e.ads.filter((function(e){return void 0===a.ads.find((function(t){return t.adId===e.adId}))})).forEach((function(e){var t=a.ads.findIndex((function(t){return t.position>e.position}));-1===t?a.ads.push(e):a.ads.splice(t,0,e),n++,i=!0}));var o=a.duration;t.fixAdBreak(a),o!==a.duration&&s.g.d(R,"Ad break "+(r+1)+"/"+t.adData.adBreaks.length+": updated duration from "+o+" to "+a.duration,t.handler.id)}else t.adData.adBreaks.push(e),n+=e.ads.length,i=!0})),s.g.d(R,this.adData.adBreaks.length+" ad break(s) in total, "+e.adBreaks.length+" ad break(s) parsed, "+n+" new ad(s), "+r+" deleted ad(s)",this.handler.id)}if(this.notifyAdsUpdated(this.adData),this.started&&!this.paused&&!this.buffering){var o=this.playerAdapter.getPosition();void 0===this.updatePositionJob&&this.adData.hasRemainingAdBreaks(o)>0&&(s.g.d(R,"Ad tracking resumed",this.handler.id),this.lastPosition=o),this.checkStart(),this.isLive()&&this.checkAdBreakEnded(o)}return i}},{key:"onPositionUpdated",value:function(t){var i,n=this,r=this.lastPosition!==t?this.lastPosition:t-1,a=t;if(!0===this.firstFileReceived&&!1===this.firstFileProceeded&&(this.firstFileProceeded=!0,s.g.d(R,"Processing all events since first image...",this.handler.id),void 0===this.positionHistory[this.positionHistory.length-1].end&&(this.positionHistory[this.positionHistory.length-1].end=t),this.positionHistory.forEach((function(t){n.lastPosition=t.start;for(var i=t.start;i<=t.end+e.POSITION_UPDATE_INTERVAL;i+=e.POSITION_UPDATE_INTERVAL){var r=Math.min(i,t.end);s.g.d(R,"Between "+n.lastPosition+" and "+r,n.handler.id),n.onPositionUpdated(r),n.lastPosition=r}})),s.g.d(R,"Tracking catch-up finished",this.handler.id)),r<a&&a-r<e.POSITION_SEEK_ERROR_DELTA){var o,d,l=null===(o=this.adData)||void 0===o?void 0:o.adBreaks.find((function(e){return e.position<=a&&a<e.position+e.duration})),u=null==l?void 0:l.ads.find((function(e){return e.position<=a&&a<e.position+e.duration}));if(void 0!==u){if(void 0===this.currentAdTracker){s.g.d(R,"Entering ad "+u.adId+"...",this.handler.id);var c=r-u.position>=e.POSITION_SEEK_ERROR_DELTA;c||(s.g.d(R,"Update position start from "+r+" to "+u.position,this.handler.id),r=u.position),this.currentAdData=u.toData(),this.currentAdBreakData=l.toData(),l.processBegin(),u.processBegin(),u.updateProgression(r,a),c&&(s.g.d(R,"Ad skipped (previous position was "+e.POSITION_SEEK_ERROR_DELTA+"ms after ad start)",this.handler.id),this.notifyAdSkipped(this.adData.sessionToken,u))}else this.currentAdTracker===u?u.updateProgression(r,a):this.currentAdTracker!==u&&(s.g.d(R,"Changing from ad "+this.currentAdTracker.adId+" to "+u.adId+"...",this.handler.id),u.adBreak.id===this.currentAdTracker.adBreak.id&&this.currentAdTracker.updateProgression(r,this.currentAdTracker.position+this.currentAdTracker.duration),this.currentAdTracker.progression<1&&(s.g.d(R,"Ad skipped (progression not complete)",this.handler.id),this.notifyAdSkipped(this.adData.sessionToken,this.currentAdTracker)),this.currentAdTracker.processEnd(),u.adBreak.id!==this.currentAdTracker.adBreak.id?(this.currentAdTracker.adBreak.processEnd(),this.currentAdData=u.toData(),this.currentAdBreakData=l.toData(),l.processBegin()):this.currentAdData=u.toData(),u.processBegin(),a-u.position>=e.POSITION_SEEK_ERROR_DELTA?(s.g.d(R,"Ad skipped (new position is "+e.POSITION_SEEK_ERROR_DELTA+"ms after ad start)",this.handler.id),this.notifyAdSkipped(this.adData.sessionToken,u)):u.updateProgression(u.position,a));this.currentAdTracker=u,this.currentAdBreakTracker=l}else void 0!==this.currentAdTracker&&(s.g.d(R,"Exiting ad "+this.currentAdTracker.adId+"...",this.handler.id),a-(this.currentAdTracker.position+this.currentAdTracker.duration)<e.POSITION_UPDATE_INTERVAL&&(a=this.currentAdTracker.position+this.currentAdTracker.duration),r>=this.currentAdTracker.position&&this.currentAdTracker.updateProgression(r,a),this.currentAdTracker.progression<1&&(s.g.d(R,"Ad skipped (progression not complete)",this.handler.id),this.notifyAdSkipped(this.adData.sessionToken,this.currentAdTracker)),this.currentAdTracker.processEnd(),void 0!==l||this.isLive()||(this.currentAdBreakTracker.processEnd(),this.currentAdBreakTracker=void 0,this.currentAdBreakData=void 0),this.currentAdTracker=void 0,this.currentAdData=void 0);this.lastPosition=t,null!==(d=this.adData)&&void 0!==d&&d.hasRemainingAdBreaks(a)?this.paused||this.buffering?s.g.d(R,"Ad tracking paused (playback paused, onPositionUpdated)",this.handler.id):this.start():s.g.d(R,"Ad tracking paused (no more event, onPositionUpdated)",this.handler.id)}else s.g.d(R,"Processing trackers from "+r+"ms to "+a+"ms, resuming tracking...",this.handler.id),(null===(i=this.adData)||void 0===i?void 0:i.hasRemainingAdBreaks(a))&&(this.paused||this.buffering||this.start())}},{key:"checkStart",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.playerAdapter.getPosition();(null===(e=this.adData)||void 0===e?void 0:e.hasRemainingAdBreaks(t))>0?this.onPositionUpdated(t):s.g.d(R,"Ad tracking paused (no more event, checkStart)",this.handler.id)}},{key:"checkAdBreakEnded",value:function(t){var i=this.lastPosition!==t?this.lastPosition:t-1,n=t;if(i<n&&n-i<e.POSITION_SEEK_ERROR_DELTA){var r,a=null===(r=this.adData)||void 0===r?void 0:r.adBreaks.find((function(e){return e.position<=n&&n<e.position+e.duration}));void 0!==this.currentAdBreakTracker&&(void 0===a?(void 0!==this.currentAdTracker&&this.currentAdTracker.position+this.currentAdTracker.duration-n<e.POSITION_SEEK_ERROR_DELTA&&(this.currentAdTracker.updateProgression(this.currentAdTracker.position,this.currentAdTracker.position+this.currentAdTracker.duration),this.currentAdTracker.processEnd(),this.currentAdTracker=void 0,this.currentAdData=void 0),this.currentAdBreakTracker.processEnd(),this.currentAdBreakTracker=void 0,this.currentAdBreakData=void 0,s.g.d(R,"Ad break end detected",this.handler.id)):s.g.d(R,"Ad break not yet ended",this.handler.id))}}},{key:"adUserInteraction",value:function(e){var t,i,n=this;null===(t=this.currentAdTracker)||void 0===t||null===(i=t.clickable)||void 0===i||i.trackers.forEach((function(e){s.g.d(R,"Requesting click tracker "+e.clickurl,n.handler.id),h.A.getInstance().adEvent(n.handler,e.clickurl)}))}},{key:"getCurrentAd",value:function(){return this.currentAdData}},{key:"getCurrentAdBreak",value:function(){return this.currentAdBreakData}},{key:"getPositionForBookmark",value:function(){if(this.playerAdapter.getDuration()>0){var e=this.playerAdapter.getPosition(),t=this.adList.find((function(t){return t.startPosition<e&&e<=t.startPosition+t.duration}));return void 0!==t&&(e=t.startPosition),this.adList.filter((function(t){return t.startPosition+t.duration<e})).forEach((function(t){e-=t.duration})),e}return-1}},{key:"getPositionForPlayback",value:function(e,t){for(var i,n=e,r=this.adList.sort((function(e,t){return e.startPosition-t.startPosition})),a=0;a<r.length;a++){var s=r[a];if(s.startPosition>n)break;n+=s.duration,i=s}return!0===t&&void 0!==i&&n===i.startPosition+i.duration?i.startPosition:n}},{key:"onFirstImage",value:function(e,t){var i,n=this;this.started=!0,this.paused=!1,this.buffering=!1,this.lastPosition=t,this.firstImageDate=Date.now(),this.positionHistory.push({start:t}),s.g.d(R,"Ad tracking enabled (live:"+this.isLive()+")",this.handler.id),void 0!==this.adData&&(this.adList=this.adData.adBreaks.map((function(e){return e.live=n.isLive(),e.toData()}))),this.checkStart(t),this.updateBkYouSession(),this.isLive()&&this.playerAdapter.getPosition()<12623004e5&&s.g.e(R,"The player position does not return a position as a timestamp in millis. The ad tracking might not work.",this.handler.id),null===(i=this.adPalSession)||void 0===i||i.sendPlaybackStart()}},{key:"onPause",value:function(){var e=this.playerAdapter.getPosition();this.buffering||this.firstFileProceeded||(this.positionHistory[this.positionHistory.length-1].end=e),this.paused=!0,this.stop(),this.lastPosition!==e?this.onPositionUpdated(e):s.g.d(R,"Ignoring player position "+e+", already proceeded...",this.handler.id),this.lastPosition=e,this.lastPositionBeforePause=this.lastPosition}},{key:"onResume",value:function(){this.paused=!1,this.buffering||(this.lastPosition=this.playerAdapter.getPosition(),!1===this.firstFileProceeded&&this.positionHistory.push({start:this.lastPosition}),Math.abs(this.lastPosition-this.lastPositionBeforePause)<1e3&&(s.g.d(R,"Reverting position because of bad position when resuming...",this.handler.id),this.lastPosition=this.lastPositionBeforePause,this.lastPositionBeforePause=0),this.checkStart())}},{key:"onBufferingStart",value:function(){var e=this.playerAdapter.getPosition();!1===this.buffering&&!1===this.firstFileProceeded&&(this.positionHistory[this.positionHistory.length-1].end=e),this.buffering=!0,this.stop(),this.lastPosition!==e?this.onPositionUpdated(e):s.g.d(R,"Ignoring player position "+e+", already proceeded...",this.handler.id),this.lastPosition=e}},{key:"onBufferingEnd",value:function(e){if(this.buffering=!1,!this.paused){var t=this.playerAdapter.getPosition();!1===this.firstFileProceeded&&this.positionHistory.push({start:t}),this.lastPosition!==t&&(s.g.d(R,"Position updated during buffering, period switch ?",this.handler.id),this.onPositionUpdated(t)),Math.abs(this.lastPosition-this.lastPositionAfterSeek)<1e3&&(s.g.d(R,"Reverting position because of seek...",this.handler.id),this.lastPosition=this.lastPositionAfterSeek,this.lastPositionAfterSeek=0),this.checkStart()}}},{key:"onSeek",value:function(t,i){var n,r;if(!1===this.firstFileProceeded&&!1===this.buffering&&(this.positionHistory[this.positionHistory.length-1].end=t,this.positionHistory.push({start:i})),t<this.lastPosition&&this.lastPosition-t<e.POSITION_SEEK_ERROR_DELTA&&(s.g.d(R,"Updating seek start position from "+t+" to "+this.lastPosition,this.handler.id),t=this.lastPosition),this.buffering?(this.lastPositionAfterSeek=i,r=this.lastPosition):(Math.abs(this.lastPosition-t)<e.POSITION_SEEK_ERROR_DELTA&&(this.onPositionUpdated(t),this.lastPosition=t),r=t,this.lastPositionAfterSeek=0),this.lastPosition=i,Math.abs(i-t)<e.POSITION_SEEK_ERROR_DELTA)if(i<t){var a;if(t-i<2e3)return s.g.d(R,"Ignoring seek...",this.handler.id),this.lastPosition=t,void this.onPositionUpdated(t);s.g.d(R,"Reset ad trackers with position "+i,this.handler.id),null===(a=this.adData)||void 0===a||a.resetProgression(i)}else{s.g.d(R,"Small seek detected, proceeding events from "+r+" to "+i,this.handler.id);for(var o=r;o<=i;o+=e.POSITION_UPDATE_INTERVAL){var d=Math.min(o+e.POSITION_UPDATE_INTERVAL,i);s.g.d(R,"Between "+o+" and "+d,this.handler.id),this.lastPosition=o,this.onPositionUpdated(d)}}else void 0!==this.currentAdTracker&&this.notifyAdSkipped(this.adData.sessionToken,this.currentAdTracker),this.onPositionUpdated(i),s.g.d(R,"Reset ad trackers with position "+i,this.handler.id),null===(n=this.adData)||void 0===n||n.resetProgression(i)}},{key:"onStop",value:function(e){var t;this.stop(),this.lastPosition=this.playerAdapter.getPosition(),this.onPositionUpdated(this.lastPosition),this.stop(),void 0!==this.updateSessionJob&&c.A.getInstance().cancel(this.updateSessionJob),null===(t=this.adPalSession)||void 0===t||t.sendPlaybackEnd()}},{key:"addListener",value:function(e){void 0===e||this.listeners.includes(e)||this.listeners.push(e)}},{key:"removeListener",value:function(e){var t=this.listeners.indexOf(e);-1!==t&&this.listeners.splice(t,1)}},{key:"notifyEvent",value:function(e,t,i,n,r,a){"function"==typeof e[t]&&e[t](i,n,r,a)}},{key:"notifyAdBreakData",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdBreakData",e)}))}},{key:"notifyAdBreakBegin",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdBreakBegin",e)}))}},{key:"notifyAdData",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdData",e)}))}},{key:"notifyAdBegin",value:function(e,t){var i,n=this;this.listeners.forEach((function(i){n.notifyEvent(i,"onAdBegin",e,t.creativeId,t.adId)})),null===(i=this.adPalSession)||void 0===i||i.sendAdImpression()}},{key:"notifyAdSkippable",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdSkippable",e)}))}},{key:"notifyAdProgress",value:function(e,t,i){var n=this;this.listeners.forEach((function(r){n.notifyEvent(r,"onAdProgress",e,t.creativeId,t.adId,i)}))}},{key:"notifyAdSkipped",value:function(e,t){var i=this,n=[];t.adBreak.ads.forEach((function(e){e.position>t.position&&i.lastPosition>=e.position+e.duration&&n.push(e.adId)})),this.listeners.forEach((function(r){i.notifyEvent(r,"onAdSkipped",e,t.creativeId,t.adId,n)}))}},{key:"notifyAdEnd",value:function(e,t){var i=this;this.isLive()&&(void 0!==this.updateSessionJob&&c.A.getInstance().cancel(this.updateSessionJob),this.updateBkYouSession()),this.listeners.forEach((function(n){i.notifyEvent(n,"onAdEnd",e,t.creativeId,t.adId)}))}},{key:"notifyAdBreakEnd",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdBreakEnd",e)}))}},{key:"notifyAdsUpdated",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onAdsUpdated",e)}))}},{key:"notifyAdDataListener",value:function(t){var i,n,r=this.firstImageDate||Date.now(),a=0===this.podsSentNumber&&!1===this.firstFileProceeded&&Date.now()-r<=e.POSITION_START_DELTA;s.g.d(R,"On ad data (firstData: "+a+", dataUpdated: "+t+")",this.handler.id),!0!==a&&!0!==t||(this.podsSentNumber=this.adList.length,s.g.d(R,"On ad data (length: "+this.podsSentNumber+")",this.handler.id),null===(i=this.handler.adSession)||void 0===i||null===(n=i.adDataListener)||void 0===n||n.onAdData(this.adList))}}]),e}();A(b,"POSITION_UPDATE_INTERVAL",1e3),A(b,"POSITION_START_DELTA",4e3),A(b,"POSITION_SEEK_ERROR_DELTA",6e3),A(b,"SESSION_UPDATE_INTERVAL",5e3);var I="BpkAdMetricsMgr",T=function(){function e(t){_(this,e),A(this,"handler",void 0),A(this,"timeline",void 0),A(this,"builder",void 0),A(this,"adMetrics",void 0),A(this,"firstImageDate",void 0),A(this,"lastLayerBitrate",void 0),A(this,"adBreakPlaying",void 0),A(this,"adPlaying",void 0),A(this,"adSkipped",void 0),A(this,"adLastLayerSwitchDate",void 0),A(this,"adLastBufferingStartDate",void 0),A(this,"adBreakPosition",void 0),this.handler=t,this.timeline=this.handler.sessionReport.timeline,this.builder=new a,this.adMetrics={}}return m(e,[{key:"onStart",value:function(){this.adMetrics={},this.adLastLayerSwitchDate=0,this.firstImageDate=0,this.lastLayerBitrate=0,this.adLastBufferingStartDate=-1,this.adBreakPlaying=!1,this.adPlaying=!1,this.adSkipped=!1,this.adBreakPosition="midroll"}},{key:"onFirstImage",value:function(e,t){this.lastLayerBitrate=e,this.adLastLayerSwitchDate=Date.now(),this.firstImageDate=Date.now()}},{key:"onLayerSwitch",value:function(e){this.adBreakPlaying&&this.firstImageDate>0&&(this.builder.addTimeSpentPerLayer(this.lastLayerBitrate,Date.now()-this.adLastLayerSwitchDate),this.adLastLayerSwitchDate=Date.now(),this.lastLayerBitrate!==e&&this.builder.addLayerSwitch()),this.lastLayerBitrate=e}},{key:"onBufferingStart",value:function(){this.adBreakPlaying&&(this.adLastBufferingStartDate=Date.now())}},{key:"onStallEnd",value:function(){this.adBreakPlaying&&this.adLastBufferingStartDate>=0&&this.builder.addStall(Date.now()-this.adLastBufferingStartDate),this.adLastBufferingStartDate=-1}},{key:"onRebufferingEnd",value:function(){this.adLastBufferingStartDate=-1}},{key:"onSeek",value:function(e,t){var i,r,a;this.adBreakPlaying&&(s.g.d(I,"Ad seeked from "+n.A.formatTime(e)+" to "+n.A.formatTime(t),null===(i=this.handler)||void 0===i?void 0:i.id),Math.abs(t-e)<b.POSITION_SEEK_ERROR_DELTA?s.g.d(I,"Ignoring seek < "+b.POSITION_SEEK_ERROR_DELTA+"ms",null===(r=this.handler)||void 0===r?void 0:r.id):(s.g.d(I,"Ad skipped (above seek threshold)",null===(a=this.handler)||void 0===a?void 0:a.id),this.adSkipped=!0))}},{key:"onStop",value:function(e){this.adBreakPlaying&&(this.handleAdEnd(),this.adBreakPlaying=!1)}},{key:"onAdBreakData",value:function(e){var t,i,n;Math.abs(e.position+e.duration-(null===(t=this.handler.playerAdapter)||void 0===t?void 0:t.getDuration()))<1e4?this.adBreakPosition="postroll":Date.now()-this.firstImageDate<b.POSITION_START_DELTA?this.adBreakPosition="preroll":this.adBreakPosition="midroll",s.g.d(I,"Ad break position is "+this.adBreakPosition,null===(i=this.handler)||void 0===i?void 0:i.id),this.adBreakPlaying=!0,void 0!==this.timeline&&this.timeline.pushEvent(null===(n=d.A.analyticsModule)||void 0===n?void 0:n.SessionTrackerEvents.AdBreakStart)}},{key:"onAdData",value:function(e){this.builder.isInitialized()&&this.adSkipped&&this.handleAdEnd();var t=!0===e.adBreak.live?-1:e.adBreak.ads.length;this.builder.reset().import(this.adMetrics[e.adId]).setCreativeId(e.creativeId).setAdId(e.adId).init(this.adBreakPosition,e.index,t),this.adSkipped=!1,this.adLastLayerSwitchDate=Date.now(),this.adPlaying=!0}},{key:"onAdSkippable",value:function(e){this.builder.setAdSkippable(!0)}},{key:"onAdSkipped",value:function(e,t,i,n){var r=this;if(this.adSkipped=!0,n.length>0){var a;s.g.d(I,"Ad break skipped",null===(a=this.handler)||void 0===a?void 0:a.id);var o=1;n.forEach((function(e){r.adMetrics[e].forEach((function(e){if(e.impressionDate<=0){e.adSkipped=!0,e.adProgress=0,e.impressionDate=Date.now();var t=r.builder.adMetrics;e.adIndex=t.adIndex+o,e.adCount=t.adCount,e.adFormat=t.adFormat,o++}}))}))}}},{key:"onAdProgress",value:function(e,t,i,n){var r;this.builder.addProgress(n),n>0&&void 0===this.builder.quartiles[n-25]&&(s.g.d(I,"Ad skipped (no previous progress)",null===(r=this.handler)||void 0===r?void 0:r.id),this.adSkipped=!0)}},{key:"onAdEnd",value:function(e,t,i){this.handleAdEnd(),this.adPlaying=!1}},{key:"onAdBreakEnd",value:function(e){var t;!0===this.adPlaying&&(this.builder.setAdSkipped(!0),this.handleAdEnd()),this.adBreakPlaying=!1,void 0!==this.timeline&&this.timeline.pushEventProgress(null===(t=d.A.analyticsModule)||void 0===t?void 0:t.SessionTrackerEvents.AdBreakStop,0)}},{key:"onKeepaliveSessionReportUpdateRequested",value:function(e){if(this.adBreakPlaying){var t=this.builder.clone().addTimeSpentPerLayer(this.lastLayerBitrate,Date.now()-this.adLastLayerSwitchDate);this.adLastBufferingStartDate>=0&&t.addStall(Date.now()-this.adLastBufferingStartDate);var i=t.build();i.adId.length>0&&this.storeMetrics(i)}e.adMetrics=this.generateMetrics()}},{key:"onEndSessionReportUpdateRequested",value:function(e){e.adMetrics=this.generateMetrics()}},{key:"storeMetrics",value:function(e){var t=e.adId;void 0===this.adMetrics[t]&&(this.adMetrics[t]=[]);var i=this.adMetrics[t].findIndex((function(t){return t.impressionDate===e.impressionDate}));-1===i?this.adMetrics[t].push(e):this.adMetrics[t][i]=e}},{key:"generateMetrics",value:function(){var e=[];return Object.values(this.adMetrics).forEach((function(t){t.forEach((function(t){return e.push(t)}))})),e}},{key:"handleAdEnd",value:function(){var e;this.builder.setAdSkipped(this.adSkipped).addTimeSpentPerLayer(this.lastLayerBitrate,Date.now()-this.adLastLayerSwitchDate);var t=this.builder.build();t.adId.length>0&&this.storeMetrics(t),s.g.d(I,"Ad metrics : "+t.toString(),null===(e=this.handler)||void 0===e?void 0:e.id),this.builder.reset(),this.adSkipped=!1}},{key:"onAdsUpdated",value:function(e){var t=this;e.adBreaks.forEach((function(e){e.ads.forEach((function(e){if(void 0===t.adMetrics[e.adId]){var i,n=(new a).setCreativeId(e.creativeId).setAdId(e.adId).build();t.adMetrics[e.adId]=[n],s.g.d(I,"Adding ad metrics report for ad id "+e.adId,null===(i=t.handler)||void 0===i?void 0:i.id)}}))}))}}]),e}(),C=i(5690),O=i(1105),L=i(5305),M="BpkOMSDKMgr",w=function(){function e(){_(this,e),A(this,"smartLib",void 0)}return m(e,[{key:"init",value:function(){s.g.d(M,"Initializing OM SDK manager...")}},{key:"release",value:function(){}},{key:"attachInstance",value:function(e){this.smartLib=e}},{key:"attachHandler",value:function(e){s.g.d(M,"Attaching OM SDK handler..."),this.omsdkHandler=e}},{key:"isEnabled",value:function(){return void 0!==this.omsdkHandler&&null!==this.omsdkHandler}}],[{key:"getInstance",value:function(){return u(e,e,U)||l(e,e,U,new e),u(e,e,U)}}]),e}(),U={writable:!0,value:void 0},B="BpkOMSessionHandler",F=function(){function e(t,i){_(this,e),A(this,"handler",void 0),A(this,"adSession",void 0),A(this,"playerAdapter",void 0),A(this,"internalAdManager",void 0),A(this,"omsdkHandler",void 0),A(this,"omAdSession",void 0),A(this,"firstImageDate",void 0),A(this,"adBreakPosition",void 0),A(this,"pause",void 0),A(this,"buffering",void 0),this.handler=t,this.adSession=t.adSession,this.playerAdapter=i,this.internalAdManager=this.handler.smartLib.internalAdManager,this.omsdkHandler=w.getInstance().omsdkHandler,this.firstImageDate=0,this.adBreakPosition="midroll",this.pause=!1,this.buffering=!1}return m(e,[{key:"onStart",value:function(){}},{key:"onRedirectionEnd",value:function(){}},{key:"onFirstImage",value:function(e,t){this.firstImageDate=Date.now()}},{key:"onLayerSwitch",value:function(e){}},{key:"onPause",value:function(){var e;!1===this.pause&&null!==(e=this.omAdSession)&&void 0!==e&&e.pause(),this.pause=!0}},{key:"onResume",value:function(){var e;!0===this.pause&&null!==(e=this.omAdSession)&&void 0!==e&&e.resume(),this.pause=!1}},{key:"onBufferingStart",value:function(){var e;!1===this.buffering&&null!==(e=this.omAdSession)&&void 0!==e&&e.bufferStart(),this.buffering=!0}},{key:"onBufferingEnd",value:function(e){var t;!0===this.buffering&&null!==(t=this.omAdSession)&&void 0!==t&&t.bufferFinish(),this.buffering=!1}},{key:"onStallEnd",value:function(){}},{key:"onRebufferingEnd",value:function(){}},{key:"onSeek",value:function(e,t){var i;void 0!==this.adData&&Math.abs(t-e)>=1e3&&(t>=this.adData.position+this.adData.duration||t<this.adData.position)&&(null===(i=this.omAdSession)||void 0===i||i.skipped())}},{key:"onStop",value:function(e){this.adData=void 0,void 0!==this.omAdSession&&(this.omAdSession.finish(),this.omAdSession=void 0),this.adBreakPosition="midroll"}},{key:"onStartSessionReportUpdateRequested",value:function(e){}},{key:"onKeepaliveSessionReportUpdateRequested",value:function(e){}},{key:"onEndSessionReportUpdateRequested",value:function(e){}},{key:"onAdBreakData",value:function(e){Math.abs(e.position+e.duration-this.playerAdapter.getDuration())<1e4?this.adBreakPosition="postroll":Date.now()-this.firstImageDate<b.POSITION_START_DELTA?this.adBreakPosition="preroll":this.adBreakPosition="midroll",s.g.d(B,"Ad break position is "+this.adBreakPosition,this.handler.id)}},{key:"onAdBreakBegin",value:function(e){}},{key:"startAdSession",value:function(e,t){var i,n,r,a=this;this.omAdSession=e,void 0!==(null===(i=this.adSession)||void 0===i?void 0:i.adView)&&this.omAdSession.setAdView(this.adSession.adView),(null===(n=this.adSession)||void 0===n?void 0:n.adFriendlyObstructionViews.length)>0&&this.adSession.adFriendlyObstructionViews.forEach((function(e){a.omAdSession.registerAdFriendlyObstructionView(e.view,e.purpose,e.reason)})),this.omAdSession.start(),void 0!==(null===(r=this.adSession)||void 0===r?void 0:r.adViewState)&&this.omAdSession.setAdViewState(this.adSession.adViewState),!0===t.skippable?this.omAdSession.loaded(t.skippablePosition-t.position,t.duration,this.adBreakPosition,this.playerAdapter.getVolume()):this.omAdSession.loaded(-1,t.duration,this.adBreakPosition,this.playerAdapter.getVolume()),s.g.d(B,"OM ad session loaded",this.handler.id)}},{key:"onAdData",value:function(e){var t,i,n,r,a=this;void 0!==this.adData&&(null!==(t=this.omAdSession)&&void 0!==t&&t.finish(),this.omAdSession=void 0),this.adData=e,r=void 0!==(null===(i=this.adSession)||void 0===i?void 0:i.adVerificationData)?o(this.adSession.adVerificationData):[],this.adData.verifications.forEach((function(e){var t=e.javascriptResources.find((function(e){return"omid"===e.apiframework}));r.push({verificationVendor:e.vendor,verificationURL:t.url,verificationParameters:e.verificationParameters})}));var s=this.omsdkHandler.createOMAdSession(this.internalAdManager.omPartnerName,this.internalAdManager.omPartnerVersion,null===(n=this.adSession)||void 0===n?void 0:n.adCustomReference,r,(function(t){a.startAdSession(t,e)}));void 0!==s&&this.startAdSession(s,e)}},{key:"onAdBegin",value:function(e,t,i){}},{key:"onAdSkippable",value:function(e){}},{key:"onAdProgress",value:function(e,t,i,n){var r;null===(r=this.omAdSession)||void 0===r||r.progress(n)}},{key:"onAdEnd",value:function(e,t,i){var n;this.adData=void 0,null!==(n=this.omAdSession)&&void 0!==n&&n.finish(),this.omAdSession=void 0}},{key:"onAdBreakEnd",value:function(e){this.adData=void 0,void 0!==this.omAdSession&&(this.omAdSession.finish(),this.omAdSession=void 0),this.adBreakPosition="midroll"}},{key:"onVolumeChanged",value:function(e){var t;s.g.d(B,"Volume is now "+e,this.handler.id),null===(t=this.omAdSession)||void 0===t||t.volumeChange(e)}},{key:"onPlayerError",value:function(e,t){var i;s.g.e(B,"Broadpeak status code "+e,this.handler.id),s.g.e(B,"Player error code "+t,this.handler.id),null===(i=this.omAdSession)||void 0===i||i.error(e,t)}}]),e}();d.A.adModule={AdMetrics:r,AdMetricsBuilder:a,AdMetricsManager:T,AdTrackingManager:b,AdDataTracker:k,AdBreakTracker:N,AdTracker:D,AdEventTracker:P,AdManager:C.X2,AdViewState:C.up,AdFriendlyObstructionPurpose:C.D6,AdSession:O.A,InternalAdManager:L.A,OMSDKManager:w,OMSessionHandler:F}}},function(e){return e.O(0,[153],(function(){return e(e.s=7575)})),e.O()}])},"object"==k(t)&&"object"==k(e)?e.exports=s():(r=[],void 0===(a="function"==typeof(n=s)?n.apply(t,r):n)||(e.exports=a))},4154:function(e,t,i){"use strict";var n,r,a,s;function o(e,t,i,n){return u(e,t),l(i,"set"),A(e,i,n),n}function d(e,t,i){return u(e,t),l(i,"get"),_(e,i)}function l(e,t){if(void 0===e)throw new TypeError("attempted to "+t+" private static field before its declaration")}function u(e,t){if(e!==t)throw new TypeError("Private static access of wrong provenance")}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=y(e)););return e}(e,t);if(n){var r=Object.getOwnPropertyDescriptor(n,t);return r.get?r.get.call(arguments.length<3?e:i):r.value}},c.apply(this,arguments)}function h(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&v(e,t)}function v(e,t){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},v(e,t)}function p(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=y(e);if(t){var r=y(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return function(e,t){if(t&&("object"===R(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return f(e)}(this,i)}}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function g(e,t,i){!function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,i)}function S(e,t){return _(e,m(e,t,"get"))}function _(e,t){return t.get?t.get.call(e):t.value}function E(e,t,i){return A(e,m(e,t,"set"),i),i}function m(e,t,i){if(!t.has(e))throw new TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function A(e,t,i){if(t.set)t.set.call(e,i);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=i}}function k(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function N(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function D(e,t,i){return t&&N(e.prototype,t),i&&N(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function P(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function R(e){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},R(e)}e=i.nmd(e),"undefined"!=typeof self?self:i.g,s=function(){return(("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule=("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule||[]).push([[153],{7745:function(e,t,i){var n;i.r(t),i.d(t,{AnalyticsRequestManager:function(){return a.bW},AnalyticsSession:function(){return a.Bw},BroadpeakCDNCacheKeepaliveManager:function(){return a._7},CacheHandler:function(){return d},CacheKeepaliveManager:function(){return a.sX},CacheManager:function(){return a.Go},GenericPlayerAdapter:function(){return a.qT},GenericPlayerApi:function(){return a.NQ},Metrics:function(){return a.K1},MetricsManager:function(){return a.pi},PlayerAdapter:function(){return a.nm},PlayerEventListener:function(){return a.Wy},PlayerManager:function(){return a.dx},SessionTrackerEvent:function(){return a.lX},SessionTrackerEvents:function(){return a.c5},SessionTrackerTimeline:function(){return a.EC}});var r=i(8100),a=i(904),s=i(1262),o="sl-",d=function(){function e(){var t=this;if(k(this,e),P(this,"storage",void 0),s.gD.d("BpkCacheHandler","Init cache handler, localStorage is "+("undefined"!=typeof localStorage?"available":"unavailable")+"..."),this.storage={},"undefined"!=typeof localStorage){for(var i=[],n=0;n<localStorage.length;n++)i.push(localStorage.key(n));i.filter((function(e){return e.startsWith(o)})).map((function(e){return{key:e,value:localStorage.getItem(e)}})).forEach((function(e){void 0!==e.value&&(t.storage[e.key]=e.value)}))}}return D(e,[{key:"set",value:function(e,t){e=o+e,this.storage[e]=t,setTimeout((function(){var i;null===(i=localStorage)||void 0===i||i.setItem(e,t)}),1)}},{key:"get",value:function(e){var t;return!1===(arguments.length>1&&void 0!==arguments[1]&&arguments[1])&&(e=o+e),e in this.storage?this.storage[e]:("undefined"!=typeof localStorage&&(t=localStorage.getItem(e)),null!=t?(this.storage[e]=t,t):void 0)}},{key:"delete",value:function(e){e=o+e,delete this.storage[e],setTimeout((function(){var t;null===(t=localStorage)||void 0===t||t.removeItem(e)}),1)}},{key:"keys",value:function(){return void 0!==this.storage?Object.keys(this.storage).map((function(e){return e.replace(o,"")})):[]}}]),e}();r.A.analyticsModule={PlayerManagerHandler:(n=new WeakMap,function(){function e(){k(this,e),g(this,n,{writable:!0,value:void 0})}return D(e,[{key:"loadPlayerAdapters",value:function(){var e,t,i,s,o,d,l,u,c,h,v,p,f,y,g,_;return E(this,n,{}),S(this,n).generic=a.qT,this.addAdapter("voplayer",null===(e=r.A.voplayerModule)||void 0===e?void 0:e.VOPlayerAdapter),this.addAdapter("theoplayer",null===(t=r.A.theoplayerModule)||void 0===t?void 0:t.THEOPlayerAdapter),this.addAdapter("shaka",null===(i=r.A.shakaModule)||void 0===i?void 0:i.ShakaPlayerAdapter),this.addAdapter("dashjs",null===(s=r.A.dashjsModule)||void 0===s?void 0:s.DashJsPlayerAdapter),this.addAdapter("html5",null===(o=r.A.html5Module)||void 0===o?void 0:o.HTML5PlayerAdapter),this.addAdapter("avplay",null===(d=r.A.avplayModule)||void 0===d?void 0:d.AVPlayAdapter),this.addAdapter("diw387",null===(l=r.A.diw387Module)||void 0===l?void 0:l.SagemcomDIW387Adapter),this.addAdapter("hbbtv1",null===(u=r.A.hbbtv1Module)||void 0===u?void 0:u.HbbTV1PlayerAdapter),this.addAdapter("kaltura",null===(c=r.A.kalturaModule)||void 0===c?void 0:c.KalturaPlayerAdapter),this.addAdapter("connectplayer",null===(h=r.A.connectplayerModule)||void 0===h?void 0:h.ConnectPlayerAdapter),this.addAdapter("rxplayer",null===(v=r.A.rxplayerModule)||void 0===v?void 0:v.RxPlayerAdapter),this.addAdapter("hlsjs",null===(p=r.A.hlsjsModule)||void 0===p?void 0:p.HlsJsPlayerAdapter),this.addAdapter("bitmovin",null===(f=r.A.bitmovinModule)||void 0===f?void 0:f.BitmovinPlayerAdapter),this.addAdapter("reactnativeconnectplayer",null===(y=r.A.reactnativeconnectplayerModule)||void 0===y?void 0:y.ReactNativeConnectPlayerAdapter),this.addAdapter("reactnativetheoplayer",null===(g=r.A.reactnativetheoplayerModule)||void 0===g?void 0:g.ReactNativeTHEOplayerAdapter),this.addAdapter("chromecast",null===(_=r.A.chromecastModule)||void 0===_?void 0:_.ChromecastPlayerAdapter),S(this,n)}},{key:"addAdapter",value:function(e,t){void 0!==t&&(S(this,n)[e]=t)}},{key:"attachPlayer",value:function(e,t){for(var i in S(this,n))if(void 0!==S(this,n)[i]&&S(this,n)[i].checkPlayer(e,t)){var r=new(S(this,n)[i]);return r.attachPlayer(e,t),r}}}]),e}()),PlayerEventListener:a.Wy,GenericPlayerApi:a.NQ,CacheHandler:d},r.A.getInstance().registerPlayerAdapters()},904:function(e,t,i){i.d(t,{bW:function(){return U},Bw:function(){return le},_7:function(){return ye},sX:function(){return fe},Go:function(){return ve},qT:function(){return u},NQ:function(){return O},K1:function(){return V},pi:function(){return K},nm:function(){return s},Wy:function(){return _},dx:function(){return I},lX:function(){return te},c5:function(){return j},EC:function(){return se}});var n=i(8379),r=i(3473),a="BpkPlayerAdapter",s=function(){function e(){var t=this;k(this,e),P(this,"handler",void 0),P(this,"webOSVersion",void 0),"undefined"!=typeof webOS&&webOS.deviceInfo((function(e){t.webOSVersion=e.sdkVersion}))}return D(e,[{key:"getName",value:function(){return""}},{key:"getVersion",value:function(){return""}},{key:"getOSName",value:function(){return r.A.getInstance().osName}},{key:"getOSVersion",value:function(){return r.A.getInstance().osVersion}},{key:"getDeviceType",value:function(){return r.A.getInstance().deviceType}},{key:"getBitrate",value:function(){return-1}},{key:"getPosition",value:function(){return 0}},{key:"getDuration",value:function(){return 0}},{key:"getVolume",value:function(){return 1}},{key:"getCapabilities",value:function(){return{adTracking:!1}}},{key:"initSessionPlayerObjects",value:function(){}},{key:"releaseSessionPlayerObjects",value:function(){}},{key:"initDiversitySession",value:function(e){}},{key:"releaseDiversitySession",value:function(){}},{key:"fillSessionReport",value:function(e){e.playerName=this.getName(),e.playerVersion=this.getVersion(),e.osName=this.getOSName(),e.osVersion=this.getOSVersion(),e.deviceType=this.getDeviceType()}},{key:"onStart",value:function(){var e=this.handler.sessionReport;this.fillSessionReport(e)}},{key:"onKeepaliveSessionReportUpdateRequested",value:function(e){this.fillSessionReport(e)}},{key:"onEndSessionReportUpdateRequested",value:function(e){this.fillSessionReport(e)}},{key:"notifyLoading",value:function(){void 0!==this.handler?this.handler.notifyLoading():n.g.e(a,"Implementation error: session.attachPlayer should be called prior to onLoading event. This event is called when the player starts buffering the first time.")}},{key:"notifyPrecacheEnded",value:function(){void 0!==this.handler?this.handler.notifyPrecacheEnded():n.g.e(a,"Implementation error: session.attachPlayer should be called prior to onPrecachedEnded event. This event is called when the player starts buffering chunks.")}},{key:"notifyFirstImage",value:function(){void 0!==this.handler?this.handler.notifyFirstImage(this.getBitrate(),this.getPosition()):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onSessionStart event. This event is called when the first image is displayed.")}},{key:"notifyPause",value:function(){void 0!==this.handler?this.handler.notifyPause():n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onSessionPause event.")}},{key:"notifyResume",value:function(){void 0!==this.handler?this.handler.notifyResume():n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onSessionResume event.")}},{key:"notifyLayerSwitch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getBitrate();void 0!==this.handler?this.handler.notifyLayerSwitch(e):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onLayerSwitch event.")}},{key:"notifySeek",value:function(e,t){void 0!==this.handler?this.handler.notifySeek(e,t):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onSeek event.")}},{key:"notifyStallStart",value:function(){void 0!==this.handler?this.handler.notifyBufferingStart():n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onStallStart event.")}},{key:"notifyStallEnd",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];void 0!==this.handler?this.handler.notifyBufferingEnd(e):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onStallEnd event.")}},{key:"notifyClose",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;void 0!==this.handler?this.handler.notifyClose(e):n.g.e(a,"Implementation error: session.attachPlayer should be called prior to onSessionStart event. This event is called when the player is closing.")}},{key:"notifyVolumeChanged",value:function(e){void 0!==this.handler?this.handler.notifyVolumeChanged(e):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onVolumeChanged event.")}},{key:"notifyPlayerError",value:function(e,t){void 0!==this.handler?this.handler.notifyPlayerError(e,t):n.g.e(a,"Implementation error: session.getURL(...) or session.getQuery()/session.startStreamingSession(...) should be called prior to onPlayerError event.")}},{key:"attachPlayer",value:function(e,t){return n.g.e(a,"Implementation error: attachPlayer not implemented for this adapter."),!1}},{key:"detachPlayer",value:function(){n.g.e(a,"Implementation error: detachPlayer not implemented for this adapter.")}},{key:"attachSession",value:function(e){this.handler=e}},{key:"detachSession",value:function(){this.handler=void 0}},{key:"setStatusCode",value:function(e){void 0!==this.handler&&(this.handler.sessionReport.statusCode=e)}},{key:"setPlayerErrorCode",value:function(e){void 0!==this.handler&&(this.handler.sessionReport.playerErrorCode=String(e))}},{key:"setCustomParameter",value:function(e,t){void 0!==this.handler&&(void 0===this.handler.streamingSession?n.g.e(a,"Set custom parameter on player adapter is only available when using StreamingSession API.",this.handler.id):this.handler.streamingSession.setCustomParameter(e,t))}}],[{key:"checkPlayer",value:function(e,t){return n.g.e(a,"Implementation error: static checkPlayer not implemented for this adapter."),!1}}]),e}(),l=i(4404),u=function(e){h(i,e);var t=p(i);function i(){var e;return k(this,i),P(f(e=t.call(this)),"player",void 0),P(f(e),"listener",void 0),e}return D(i,[{key:"getName",value:function(){return this.player.getPlayerName()}},{key:"getVersion",value:function(){return this.player.getVersion()}},{key:"getOSName",value:function(){return this.player.getOSName()}},{key:"getOSVersion",value:function(){return this.player.getDeviceVersion()}},{key:"getDeviceType",value:function(){return this.player.getDeviceType()}},{key:"getBitrate",value:function(){return this.player.getCurrentBitrate()}},{key:"getPosition",value:function(){return this.player.getCurrentPosition()}},{key:"getDuration",value:function(){return this.player.getTotalDuration()}},{key:"getCapabilities",value:function(){return"function"==typeof this.player.getCapabilities?this.player.getCapabilities():c(y(i.prototype),"getCapabilities",this).call(this)}},{key:"attachPlayer",value:function(e,t){return!!i.checkPlayer(e,t)&&(this.player=e,this.listener=t,this.player.playerAdapter=this,!0)}},{key:"detachPlayer",value:function(){void 0!==this.player&&(this.player.playerAdapter=void 0),this.player=void 0,this.listener=void 0}}],[{key:"checkPlayer",value:function(e,t){return l.A.hasMethods(e,["getPlayerName","getVersion","getOSName","getDeviceVersion","getDeviceType","getCurrentPosition","getTotalDuration","getCurrentBitrate"])}}]),i}(s),v="BpkPlayerEventListener",_=function(){function e(){k(this,e)}return D(e,null,[{key:"addPlayerAdapter",value:function(t){-1===e.playerAdapters.indexOf(t)&&e.playerAdapters.push(t)}},{key:"removePlayerAdapter",value:function(t){var i=e.playerAdapters.indexOf(t);-1!==i&&e.playerAdapters.splice(i,1)}},{key:"isStarted",value:function(){var t=e.playerAdapters;return t.length>0&&void 0!==t[t.length-1].handler&&t[t.length-1].handler.metricsManager.started}},{key:"isPlaying",value:function(){var t=e.playerAdapters;return t.length>0&&void 0!==t[t.length-1].handler&&t[t.length-1].handler.metricsManager.playing}},{key:"isBuffering",value:function(){var t=e.playerAdapters;return t.length>0&&void 0!==t[t.length-1].handler&&t[t.length-1].handler.metricsManager.buffering}},{key:"onSessionStart",value:function(){var t=e.playerAdapters;t.length>0?t.forEach((function(e){return e.notifyFirstImage()})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onSessionStart event. If you don't attach any player, please remove this call, SmartLib is now handling it automatically.")}},{key:"onSessionPause",value:function(){var t=e.playerAdapters;t.length>0?t.forEach((function(e){return e.notifyPause()})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onSessionPause event.")}},{key:"onSessionResume",value:function(){var t=e.playerAdapters;t.length>0?t.forEach((function(e){return e.notifyResume()})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onSessionResume event.")}},{key:"onLayerSwitch",value:function(t){var i=e.playerAdapters;i.length>0?i.forEach((function(e){return e.notifyLayerSwitch(t)})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onLayerSwitch event.")}},{key:"onSeek",value:function(t,i){var r=e.playerAdapters;r.length>0?r.forEach((function(e){return e.notifySeek(t,i)})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onSeek event.")}},{key:"onStallStart",value:function(){var t=e.playerAdapters;t.length>0?t.forEach((function(e){return e.notifyStallStart()})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onStallStart event.")}},{key:"onStallEnd",value:function(t){var i=e.playerAdapters;i.length>0?i.forEach((function(e){return e.notifyStallEnd(t)})):n.g.e(v,"Implementation error: SmartLib.attachPlayer(...) should be called prior to onStallEnd event.")}}]),e}();P(_,"playerAdapters",[]);var m="BpkPlayerMgr",A=function(){function e(){k(this,e)}return D(e,null,[{key:"loadPlayerAdapters",value:function(){return{}}}]),e}(),N=new WeakMap,R=new WeakMap,b=new WeakMap,I=function(){function e(){k(this,e),P(this,"smartLib",void 0),g(this,N,{writable:!0,value:A}),g(this,R,{writable:!0,value:{}}),g(this,b,{writable:!0,value:void 0})}return D(e,[{key:"init",value:function(e){S(this,N)===A&&(E(this,N,e),E(this,R,S(this,N).loadPlayerAdapters()),n.g.v(m,"Compatible players: "+Object.keys(S(this,R))))}},{key:"release",value:function(){this.setPlayerAdapter(void 0)}},{key:"attachInstance",value:function(e){this.smartLib=e}},{key:"getAdapters",value:function(){return S(this,R)}},{key:"setPlayerAdapter",value:function(e){void 0!==S(this,b)&&S(this,b)!==e&&(n.g.i(m,"Player "+S(this,b).getName()+" detached"),S(this,b).detachPlayer()),S(this,b)!==e&&void 0!==e?e instanceof u&&(n.g.d(m,"Attaching generic player to SmartLib singleton"),_.addPlayerAdapter(e)):S(this,b)!==e&&void 0===e&&S(this,b)instanceof u&&(n.g.d(m,"Detaching generic player from SmartLib singleton"),_.removePlayerAdapter(S(this,b))),S(this,b)!==e?E(this,b,e):void 0!==e&&n.g.i(m,"Player "+S(this,b).getName()+" already attached"),void 0!==e&&n.g.i(m,"Player "+e.getName()+" attached")}},{key:"getPlayerAdapter",value:function(){return S(this,b)}}],[{key:"getInstance",value:function(){return d(e,e,T)||o(e,e,T,new e),d(e,e,T)}}]),e}(),T={writable:!0,value:void 0},C="BpkGenericPlayerApi",O=function(){function e(){k(this,e),P(this,"playerAdapter",void 0)}return D(e,[{key:"getPlayerName",value:function(){return""}},{key:"getVersion",value:function(){return""}},{key:"getOSName",value:function(){return""}},{key:"getDeviceVersion",value:function(){return""}},{key:"getDeviceType",value:function(){return""}},{key:"getCurrentPosition",value:function(){return 0}},{key:"getTotalDuration",value:function(){return 0}},{key:"getCurrentBitrate",value:function(){return 0}},{key:"getCapabilities",value:function(){return{}}},{key:"notifyPrecacheEnded",value:function(){void 0!==this.playerAdapter?this.playerAdapter.notifyPrecacheEnded():n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyPrecacheEnded. This event is called when the player starts buffering chunks.")}},{key:"notifyFirstImage",value:function(){void 0!==this.playerAdapter?this.playerAdapter.notifyFirstImage():n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyFirstImage. This event is called when the first image is displayed.")}},{key:"notifyPause",value:function(){void 0!==this.playerAdapter?this.playerAdapter.notifyPause():n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyPause.")}},{key:"notifyResume",value:function(){void 0!==this.playerAdapter?this.playerAdapter.notifyResume():n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyResume.")}},{key:"notifyLayerSwitch",value:function(e){void 0!==this.playerAdapter?this.playerAdapter.notifyLayerSwitch(e):n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyLayerSwitch.")}},{key:"notifyStallStart",value:function(){void 0!==this.playerAdapter?this.playerAdapter.notifyStallStart():n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyStallStart.")}},{key:"notifyStallEnd",value:function(e){void 0!==this.playerAdapter?this.playerAdapter.notifyStallEnd(e):n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifyStallEnd.")}},{key:"notifySeek",value:function(e,t){void 0!==this.playerAdapter?this.playerAdapter.notifySeek(e,t):n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to notifySeek.")}},{key:"setPlayerErrorCode",value:function(e){void 0!==this.playerAdapter?this.playerAdapter.setPlayerErrorCode(e):n.g.e(C,"Implementation error: session.attachPlayer(...) should be called prior to setPlayerErrorCode. This has to be called before stopStreamingSession when the player error code as a string.")}}]),e}(),L=i(1134),M=i(1142),w="BpkAnalyticsRequestMgr",U=function(){function e(){k(this,e)}return D(e,[{key:"buildAnalyticsAddress",value:function(t){return(t=t.trim()).endsWith("/")||(t+="/"),t+e.METRICS_RECEIVER_PATH}},{key:"endSession",value:function(t,i){var r=this,a=t.sessionReport;if(0!==i.analyticsAddress.length){var s=i.analyticsAddress.split(","),o=[];return s.forEach((function(s){var d;0===s.indexOf(e.NOCACHE_PREFIX)?(n.g.d(w,e.NOCACHE_PREFIX+" option used, no need to store the report in cache"),s=r.buildAnalyticsAddress(s.substring(e.NOCACHE_PREFIX.length))):(s=r.buildAnalyticsAddress(s),d=M.A.analyticsModule.CacheManager.getInstance().storeSessionReport(s,a.toEndSessionJSON(),!0,Date.now(),!0)),n.g.i(w,"Posting metrics to "+s,t.id);var l=r.postSession(s,a.toEndSessionJSON(),i).then((function(e){return n.g.i(w,"Send session metrics ended with status code "+e.httpStatus+" ("+s+")",t.id),e.httpStatus>=200&&e.httpStatus<300?(void 0!==d&&M.A.analyticsModule.CacheManager.getInstance().deleteSessionReport(d),M.A.analyticsModule.CacheManager.getInstance().push(),!0):(void 0!==d&&M.A.analyticsModule.CacheManager.getInstance().update(d,"sending",!1),!1)}));o.push(l)})),Promise.all(o).then((function(){n.g.d(w,"Send session metrics done",t.id)}))}return n.g.w(w,"Metrics platform URL is null, metrics won't be posted anywhere.",t.id),Promise.resolve(!1)}},{key:"endSessionCache",value:function(e,t,i){return 0!==e.length?(n.g.i(w,"Posting cache to "+e),this.postSession(e,t,i).then((function(t){return n.g.i(w,"Send cache ended with status code "+t.httpStatus+" ("+e+")"),t.httpStatus>=200&&t.httpStatus<300}))):(n.g.w(w,"Metrics platform URL is null, cache won't be posted anywhere."),Promise.resolve(!1))}},{key:"postSession",value:function(t,i,r){return new Promise((function(a,s){var o={Connection:"close"};void 0!==r.userAgent&&(o["User-Agent"]=r.userAgent);var d=JSON.stringify(i);n.g.v(w,"Executing POST request with body: "+d),L.A.getInstance().asyncPost(t,o,d,e.POST_SESSION_REQUEST_TIMEOUT,(function(e){var t=0;void 0!==e.statusCode&&(t=parseInt(e.statusCode,10)),a({httpStatus:t})}))}))}}],[{key:"getInstance",value:function(){return d(e,e,B)||o(e,e,B,new e),d(e,e,B)}}]),e}();P(U,"METRICS_RECEIVER_PATH","fservices/metricsReceiver"),P(U,"POST_SESSION_REQUEST_TIMEOUT",5e3),P(U,"NOCACHE_PREFIX","nocache=");var B={writable:!0,value:void 0},F=i(4943),x=function(){function e(t){k(this,e),P(this,"index",void 0),P(this,"buffer",void 0),this.index=0,this.buffer=new Uint8Array(t)}return D(e,[{key:"put",value:function(e){return this.buffer[this.index++]=e,this}},{key:"putChar",value:function(e){return this.buffer[this.index++]=(65280&e)>>8,this.buffer[this.index++]=255&e,this}},{key:"putByteBuffer",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.buffer.length,i=e.buffer;return this.buffer.length>=this.index+t&&(this.buffer.set(i,this.index),this.index+=t),this}},{key:"set",value:function(e,t){return this.buffer[t]=e,this}},{key:"data",value:function(){return this.buffer}},{key:"base64",value:function(){return F.A.bufferToBase64(this)}},{key:"length",value:function(){return this.index}},{key:"capacity",value:function(){return this.buffer.length}},{key:"remaining",value:function(){return this.capacity()-this.length()}},{key:"toString",value:function(){return F.A.bufferToString(this.buffer,this.index)+"(length:"+this.length()+")"}}]),e}();P(x,"EMPTY",new x(0));var H=i(3445),V=D((function e(t){k(this,e),P(this,"redirectionTime",void 0),P(this,"startupTime",void 0),P(this,"completion",void 0),P(this,"playbackType",void 0),P(this,"playbackDuration",void 0),P(this,"sessionDuration",void 0),P(this,"contentDuration",void 0),P(this,"stallsNumber",void 0),P(this,"maxStallDuration",void 0),P(this,"totalStallsDuration",void 0),P(this,"rebufferingsNumber",void 0),P(this,"maxRebufferingDuration",void 0),P(this,"totalRebufferingDuration",void 0),P(this,"minBitrate",void 0),P(this,"maxBitrate",void 0),P(this,"averageBitrate",void 0),P(this,"layerSwitchesNumber",void 0),P(this,"timeSpentPerLayer",void 0),P(this,"preStartupTime",void 0),void 0!==t?(this.redirectionTime=t.redirectionTime,this.startupTime=t.startupTime,this.completion=t.completion,this.playbackType=t.playbackType,this.playbackDuration=t.playbackDuration,this.sessionDuration=t.sessionDuration,this.contentDuration=t.contentDuration,this.stallsNumber=t.stallsNumber,this.maxStallDuration=t.maxStallDuration,this.totalStallsDuration=t.totalStallsDuration,this.rebufferingsNumber=t.rebufferingsNumber,this.maxRebufferingDuration=t.maxRebufferingDuration,this.totalRebufferingDuration=t.totalRebufferingDuration,this.minBitrate=t.minBitrate,this.maxBitrate=t.maxBitrate,this.averageBitrate=t.averageBitrate,this.layerSwitchesNumber=t.layerSwitchesNumber,this.timeSpentPerLayer=JSON.parse(JSON.stringify(t.timeSpentPerLayer)),this.preStartupTime=t.preStartupTime):(this.redirectionTime=0,this.startupTime=0,this.completion=0,this.playbackType="",this.playbackDuration=0,this.sessionDuration=0,this.contentDuration=0,this.stallsNumber=0,this.maxStallDuration=0,this.totalStallsDuration=0,this.rebufferingsNumber=0,this.maxRebufferingDuration=0,this.totalRebufferingDuration=0,this.minBitrate=0,this.maxBitrate=0,this.averageBitrate=0,this.layerSwitchesNumber=0,this.timeSpentPerLayer={},this.preStartupTime=0)}));P(V,"PLAYBACK_TYPE_LIVE","LIVE"),P(V,"PLAYBACK_TYPE_VOD","VOD");var G=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new V;k(this,e),P(this,"metrics",void 0),P(this,"watchingRanges",void 0),this.metrics=t,this.reset()}return D(e,[{key:"setRedirectionTime",value:function(e){return this.metrics.redirectionTime=e,this}},{key:"setStartupTime",value:function(e){return this.metrics.startupTime=e,this}},{key:"setSessionDuration",value:function(e){return this.metrics.sessionDuration=e,this}},{key:"setContentDuration",value:function(e){return this.metrics.contentDuration=e,this}},{key:"setPlaybackType",value:function(e){return this.metrics.playbackType=e,this}},{key:"setFirstLayer",value:function(e){return e>0&&(this.metrics.maxBitrate=e,this.metrics.minBitrate=e),this}},{key:"setPreStartupTime",value:function(e){return this.metrics.preStartupTime=e,this}},{key:"addTimeSpentPerLayer",value:function(e,t){if((e=Math.round(e))>0){var i=this.metrics.timeSpentPerLayer[e];void 0===i&&(i=0),i+=t,this.metrics.timeSpentPerLayer[e]=i,this.metrics.maxBitrate<e&&(this.metrics.maxBitrate=e),(this.metrics.minBitrate>e||0===this.metrics.minBitrate)&&(this.metrics.minBitrate=e)}return this}},{key:"addLayerSwitch",value:function(){return this.metrics.layerSwitchesNumber++,this}},{key:"addPlaybackDuration",value:function(e){return this.metrics.playbackDuration+=e,this}},{key:"addWatchingRange",value:function(e,t){return e<t&&(n.g.v("BpkMetrics","Add watching range, duration "+(t-e)+"ms"),this.watchingRanges.push({start:e,end:t,duration:t-e})),this}},{key:"addStall",value:function(e){return this.metrics.stallsNumber++,this.metrics.totalStallsDuration+=e,this.metrics.maxStallDuration<e&&(this.metrics.maxStallDuration=e),this}},{key:"addRebuffering",value:function(e){return this.metrics.rebufferingsNumber++,this.metrics.totalRebufferingDuration+=e,this.metrics.maxRebufferingDuration<e&&(this.metrics.maxRebufferingDuration=e),this}},{key:"clone",value:function(){var t=new e(new V(this.metrics));return t.watchingRanges=JSON.parse(JSON.stringify(this.watchingRanges)),t}},{key:"computeCompletion",value:function(){if(this.metrics.playbackType===V.PLAYBACK_TYPE_LIVE||0===this.metrics.contentDuration)return 1e3;var e=JSON.parse(JSON.stringify(this.watchingRanges)),t=e.slice(0);if(1===e.length)return Math.floor(1e3*t[0].duration/this.metrics.contentDuration);if(0===e.length)return 0;var i=[],n=null;t=t.sort((function(e,t){return parseInt(e.start,10)>parseInt(t.start,10)?1:parseInt(e.start,10)<parseInt(t.start,10)?-1:0})),i.push(t[0]);for(var r=1;r<t.length;r++)n=i[i.length-1],parseInt(n.end,10)<parseInt(t[r].start,10)?i.push(t[r]):parseInt(n.end,10)<parseInt(t[r].end,10)&&(n.end=parseInt(t[r].end,10),n.duration=n.end-n.start,i.pop(),i.push(n));for(var a=0,s=0;s<i.length;s++)i[s].duration=parseInt(i[s].end,10)-parseInt(i[s].start,10),a+=parseInt(i[s].duration,10);this.watchingRanges=i;var o=Math.floor(1e3*a/this.metrics.contentDuration);return o>1e3?1e3:o}},{key:"build",value:function(){var e=0,t=0;for(var i in this.metrics.timeSpentPerLayer){var n=this.metrics.timeSpentPerLayer[i];e+=i*n,t+=n}return 0!==t&&(this.metrics.averageBitrate=Math.round(e/t)),this.metrics.completion=this.computeCompletion(),this.metrics.completion<0?this.metrics.completion=0:this.metrics.completion>1e3&&(this.metrics.completion=1e3),this.metrics.startupTime+=this.metrics.preStartupTime,this.metrics}},{key:"reset",value:function(){return this.watchingRanges=[],this}}]),e}(),q="BpkMetricsMgr",K=function(){function e(t,i){k(this,e),P(this,"handler",void 0),P(this,"builder",void 0),P(this,"playerAdapter",void 0),P(this,"timeline",void 0),P(this,"started",void 0),P(this,"playing",void 0),P(this,"buffering",void 0),P(this,"seeking",void 0),P(this,"bitrate",void 0),P(this,"redirectionStartDate",void 0),P(this,"playingStartDate",void 0),P(this,"bufferingStartDate",void 0),P(this,"lastLayerSwitchDate",void 0),P(this,"lastSeekDate",void 0),P(this,"playOnNextBufferingEnd",void 0),P(this,"startPosition",void 0),this.handler=t,this.builder=new G,this.playerAdapter=i,this.timeline=this.handler.sessionReport.timeline,this.started=!1,this.playing=!1,this.buffering=!1,this.seeking=!1,this.bitrate=-1,this.redirectionStartDate=Date.now(),this.playingStartDate=Date.now(),this.bufferingStartDate=0,this.lastLayerSwitchDate=0,this.lastSeekDate=0,this.playOnNextBufferingEnd=!1,this.startPosition=0}return D(e,[{key:"onStart",value:function(){this.redirectionStartDate=Date.now()}},{key:"onRedirectionEnd",value:function(){this.builder.setRedirectionTime(Date.now()-this.redirectionStartDate),this.playingStartDate=Date.now()}},{key:"onPrecacheEnded",value:function(){this.playingStartDate=Date.now()}},{key:"onFirstImage",value:function(e,t){n.g.i(q,"Streaming session started ("+e+"kbps,"+H.A.formatTime(t)+")",this.handler.id),this.started=!0,this.playing=!0,this.startPosition=t,this.builder.setContentDuration(this.playerAdapter.getDuration()).setPlaybackType(this.playerAdapter.getDuration()<=0?V.PLAYBACK_TYPE_LIVE:V.PLAYBACK_TYPE_VOD),this.builder.setStartupTime(Date.now()-this.redirectionStartDate),this.playingStartDate=Date.now(),this.builder.setFirstLayer(e),this.bitrate=e,this.lastLayerSwitchDate=Date.now()}},{key:"onLayerSwitch",value:function(e){var t;n.g.d(q,"Player changed layer to "+e+"kbps",this.handler.id),this.started&&(this.builder.addTimeSpentPerLayer(this.bitrate,Date.now()-this.lastLayerSwitchDate),this.lastLayerSwitchDate=Date.now(),this.bitrate!==e&&this.bitrate>0&&(n.g.d(q,"Player changed layer, before: "+this.bitrate+"kbps, now: "+e+"kbps",this.handler.id),null!==(t=this.timeline)&&void 0!==t&&t.pushEventBitrate(j.LayerSwitch,e),this.builder.addLayerSwitch())),this.bitrate=e}},{key:"onPause",value:function(){var e;this.playing&&(n.g.d(q,"Player is paused",this.handler.id),null!==(e=this.timeline)&&void 0!==e&&e.pushEvent(j.Pause),this.playing=!1,this.buffering||this.builder.addPlaybackDuration(Date.now()-this.playingStartDate),this.builder.addWatchingRange(this.startPosition,this.playerAdapter.getPosition()))}},{key:"onResume",value:function(){var e;this.started&&!this.playing&&(n.g.d(q,"Player is resumed",this.handler.id),null!==(e=this.timeline)&&void 0!==e&&e.pushEvent(j.Resume),this.playing=!0,this.buffering=!1,this.playingStartDate=Date.now())}},{key:"onBufferingStart",value:function(){if(!this.buffering&&this.started){var t;n.g.d(q,"Player is buffering",this.handler.id),null===(t=this.timeline)||void 0===t||t.pushEvent(j.BufferingStart);var i=Date.now();this.buffering=!0,this.seeking&&i-this.lastSeekDate>e.MAX_TIME_BETWEEN_SEEK_AND_REBUFFERING&&(this.seeking=!1),this.bufferingStartDate=i,this.playOnNextBufferingEnd=!1,this.playing&&this.builder.addPlaybackDuration(i-this.playingStartDate)}}},{key:"onBufferingEnd",value:function(e){var t=Date.now();e&&this.playOnNextBufferingEnd&&!this.buffering&&(this.playingStartDate=t,this.playOnNextBufferingEnd=!1),this.started&&this.bufferingStartDate>0&&(this.buffering=!1,e?this.playingStartDate=t:this.playOnNextBufferingEnd=!0,this.seeking?(this.seeking=!1,this.handler.notifyRebufferingEnd()):this.handler.notifyStallEnd(),this.bufferingStartDate=0)}},{key:"onStallEnd",value:function(){var e,t=Date.now()-this.bufferingStartDate;this.builder.addStall(t),null!==(e=this.timeline)&&void 0!==e&&e.pushEvent(j.StallStop),n.g.d(q,"Player stalled for "+t+"ms",this.handler.id)}},{key:"onRebufferingEnd",value:function(){var e,t=Date.now()-this.bufferingStartDate;this.builder.addRebuffering(t),null!==(e=this.timeline)&&void 0!==e&&e.pushEvent(j.RebufferingStop),n.g.d(q,"Player buffered for "+t+"ms",this.handler.id)}},{key:"onSeek",value:function(e,t){var i;n.g.d(q,"Player seeked from "+H.A.formatTime(e)+" to "+H.A.formatTime(t),this.handler.id),null!==(i=this.timeline)&&void 0!==i&&i.pushEventPositionStartEnd(j.Seek,e,t),this.builder.addWatchingRange(this.startPosition,e),this.startPosition=t,this.seeking=!0,this.lastSeekDate=Date.now()}},{key:"onStop",value:function(e){if(this.started){var t=Date.now();this.playing&&!this.buffering&&this.builder.addPlaybackDuration(t-this.playingStartDate),this.buffering&&this.onBufferingEnd(!1),this.playing&&(this.builder.addWatchingRange(this.startPosition,this.playerAdapter.getPosition()),this.playing=!1),this.builder.setSessionDuration(t-this.redirectionStartDate).addTimeSpentPerLayer(this.bitrate,t-this.lastLayerSwitchDate),this.started=!1}}},{key:"onStartSessionReportUpdateRequested",value:function(e){e.metrics=this.builder.build()}},{key:"onKeepaliveSessionReportUpdateRequested",value:function(e){var t=Date.now(),i=this.builder.clone();if(this.playing&&!this.buffering&&i.addPlaybackDuration(t-this.playingStartDate),this.started&&this.bufferingStartDate>0){var n=Date.now()-this.bufferingStartDate;this.seeking?i.addRebuffering(n):i.addStall(n)}this.playing&&i.addWatchingRange(this.startPosition,this.playerAdapter.getPosition()),i.setSessionDuration(t-this.redirectionStartDate).addTimeSpentPerLayer(this.bitrate,t-this.lastLayerSwitchDate);var r=this.handler.getCustomParameters().pre_startup_time,a=0;void 0===r||isNaN(r)||(a=parseInt(r,10)),i.setPreStartupTime(a),e.metrics=i.build()}},{key:"onEndSessionReportUpdateRequested",value:function(e){var t=Date.now();this.builder.setSessionDuration(t-this.redirectionStartDate);var i=this.handler.getCustomParameters().pre_startup_time,n=0;void 0===i||isNaN(i)||(n=parseInt(i,10)),this.builder.setPreStartupTime(n),e.metrics=this.builder.build()}}]),e}();P(K,"MAX_TIME_BETWEEN_SEEK_AND_REBUFFERING",1e3);var j={None:0,Start:1,Stop:2,RedirectionEnd:3,FirstImage:4,Pause:5,Resume:6,BufferingStart:7,StallStart:8,StallStop:9,RebufferingStart:10,RebufferingStop:11,Seek:12,LayerSwitch:13,AdBreakStart:14,AdBreakStop:15,NetworkAvailable:16,NetworkLost:17,Mute:18,Unmute:19,Multicast:20,Unicast:21,PrecacheEnded:22,DataSummary:144,EmptySummary:145},W=[j.RedirectionEnd,j.Pause,j.Resume,j.BufferingStart,j.StallStart,j.StallStop,j.RebufferingStart,j.RebufferingStop,j.AdBreakStart,j.NetworkLost,j.Mute,j.Unmute,j.Multicast,j.Unicast,j.PrecacheEnded],Y=[j.Start],Q=[j.LayerSwitch],z=[j.FirstImage],J=[j.Seek],X=[j.Stop],$=[j.AdBreakStop],Z=[j.NetworkAvailable],ee="BpkSessionTrackerEvent",te=function(){function e(t){k(this,e),P(this,"eventId",void 0),P(this,"eventDate",void 0),P(this,"eventData",void 0),P(this,"startStopEvent",void 0),P(this,"startEventId",void 0),P(this,"stopEventId",void 0),P(this,"triggerStartEventId",void 0),P(this,"keepLastOnly",void 0),P(this,"attachEventId",void 0),P(this,"attachMaxDurationBeforeStart",void 0),P(this,"startEvent",void 0),P(this,"stopEvent",void 0),P(this,"attachedEvent",void 0),P(this,"compressed",void 0),P(this,"compressedData",void 0),this.eventId=t,this.eventDate=Date.now(),this.eventData={},this.startStopEvent=!1,this.startEventId=0,this.stopEventId=0,this.triggerStartEventId=0,this.keepLastOnly=!1,this.attachEventId=0,this.attachMaxDurationBeforeStart=-1,this.startEvent=null,this.stopEvent=null,this.attachedEvent=null,this.addDataSizeInTimeline=!1,this.compressed=!1,this.compressedData=void 0,this.updateMetadata()}return D(e,[{key:"updateMetadata",value:function(){switch(this.startStopEvent=!1,this.startEventId=j.None,this.stopEventId=j.None,this.triggerStartEventId=j.None,this.keepLastOnly=!1,this.attachEventId=j.None,this.attachMaxDurationBeforeStart=-1,this.eventId){case j.None:break;case j.Start:this.startStopEvent=!0,this.startEventId=j.Start,this.stopEventId=j.Stop,this.addDataSizeInTimeline=!0;break;case j.Stop:this.startStopEvent=!0,this.startEventId=j.Start,this.stopEventId=j.Stop;break;case j.RedirectionEnd:this.startStopEvent=!1,this.attachEventId=j.Start;break;case j.FirstImage:this.startStopEvent=!1,this.keepLastOnly=!0,this.attachEventId=j.RedirectionEnd;break;case j.Pause:case j.Resume:this.startStopEvent=!0,this.startEventId=j.Pause,this.stopEventId=j.Resume;break;case j.StallStart:this.startStopEvent=!0,this.startEventId=j.StallStart,this.stopEventId=j.StallStop;break;case j.StallStop:this.startStopEvent=!0,this.startEventId=j.StallStart,this.stopEventId=j.StallStop,this.triggerStartEventId=j.BufferingStart;break;case j.RebufferingStart:this.startStopEvent=!0,this.startEventId=j.RebufferingStart,this.stopEventId=j.RebufferingStop;break;case j.RebufferingStop:this.startStopEvent=!0,this.startEventId=j.RebufferingStart,this.stopEventId=j.RebufferingStop,this.triggerStartEventId=j.BufferingStart,this.attachEventId=j.Seek,this.attachMaxDurationBeforeStart=K.MAX_TIME_BETWEEN_SEEK_AND_REBUFFERING;break;case j.AdBreakStart:this.startStopEvent=!0,this.startEventId=j.AdBreakStart,this.stopEventId=j.AdBreakStop;break;case j.AdBreakStop:this.startStopEvent=!0,this.startEventId=j.AdBreakStart,this.stopEventId=j.AdBreakStop,this.attachEventId=j.Seek,this.attachMaxDurationBeforeStart=0;break;case j.BufferingStart:case j.Seek:case j.LayerSwitch:case j.NetworkAvailable:case j.NetworkLost:this.startStopEvent=!1;break;case j.Mute:case j.Unmute:case j.Multicast:case j.Unicast:case j.PrecacheEnded:this.startStopEvent=!1,this.addDataSizeInTimeline=!0}}},{key:"getEventName",value:function(){switch(this.eventId){case j.None:return"None";case j.Start:return"Start";case j.Stop:return"Stop";case j.RedirectionEnd:return"RedirectionEnd";case j.FirstImage:return"FirstImage";case j.Pause:return"Pause";case j.Resume:return"Resume";case j.BufferingStart:return"BufferingStart";case j.StallStart:return"StallStart";case j.StallStop:return"StallStop";case j.RebufferingStart:return"RebufferingStart";case j.RebufferingStop:return"RebufferingStop";case j.Seek:return"Seek";case j.LayerSwitch:return"LayerSwitch";case j.AdBreakStart:return"AdBreakStart";case j.AdBreakStop:return"AdBreakStop";case j.NetworkAvailable:return"NetworkAvailable";case j.NetworkLost:return"NetworkLost";case j.Mute:return"Mute";case j.Unmute:return"Unmute";case j.Multicast:return"Multicast";case j.Unicast:return"Unicast";case j.PrecacheEnded:return"PrecacheEnded"}return""}},{key:"isStartEvent",value:function(){return this.startStopEvent&&this.eventId===this.startEventId}},{key:"isStopEvent",value:function(){return this.startStopEvent&&this.eventId===this.stopEventId}},{key:"addEventData",value:function(e,t){"string"==typeof e&&(this.eventData[e]=t)}},{key:"toData",value:function(e){for(var t=Math.abs(this.eventDate-e)/100,i=F.A.floor(t/65535),n=t%65535,r=Object.keys(this.eventData).length,a=3*i+1+2+2*r+(this.addDataSizeInTimeline?1:0),s=new x(a),o=0;o<i;o++)s.put(j.None),s.put(255),s.put(255);switch(s.put(this.eventId),s.putChar(n),this.addDataSizeInTimeline&&s.put(2*r),this.eventId){case j.Start:var d=parseInt(this.eventData.networkType,10),l=parseInt(this.eventData.muteState,10);s.putChar(d),s.putChar(l);break;case j.Stop:var u=parseInt(this.eventData.statusCode,10);s.putChar(u);break;case j.FirstImage:var c=parseInt(this.eventData.bitrate,10),h=parseInt(this.eventData.position,10);s.putChar(c),s.putChar(h);break;case j.Seek:var v=parseInt(this.eventData.positionStart,10),p=parseInt(this.eventData.positionEnd,10);s.putChar(v),s.putChar(p);break;case j.LayerSwitch:var f=parseInt(this.eventData.bitrate,10);s.putChar(f);break;case j.AdBreakStop:var y=parseInt(this.eventData.progress,10);s.putChar(y);break;case j.NetworkAvailable:var g=parseInt(this.eventData.state,10);s.putChar(g)}return s}},{key:"formatDate",value:function(e){return H.A.formatDate(new Date(e))}},{key:"print",value:function(){for(var e in n.g.v(ee,"   |"),n.g.v(ee,"   ├--\x3e "+this.getEventName()+" -> "+(!0===this.compressed?"compressed":"not compressed")+" -> "+this.eventDate),n.g.v(ee,"   |      date: "+this.formatDate(this.eventDate)),this.eventData)n.g.v(ee,"   |      "+e+": "+this.eventData[e]);this.isStartEvent()&&null!==this.stopEvent&&n.g.v(ee,"   |      stop event: "+this.stopEvent.getEventName()+" "+this.stopEvent.eventDate),this.isStopEvent()&&null!==this.startEvent&&n.g.v(ee,"   |      start event: "+this.startEvent.getEventName()+" "+this.startEvent.eventDate),null!==this.attachedEvent&&n.g.v(ee,"   |      attached event: "+this.attachedEvent.getEventName()+" "+this.attachedEvent.eventDate)}},{key:"toString",value:function(){return this.getEventName()+" ("+this.formatDate(this.eventDate)+")"}}]),e}(),ie=function(){function e(t,i){k(this,e),P(this,"timeline",void 0),P(this,"minIndex",void 0),P(this,"initialBitrate",void 0),P(this,"builder",void 0),P(this,"summaryDuration",void 0),P(this,"pauseDuration",void 0),P(this,"nbNetworkDisconnected",void 0),P(this,"nbNetworkWifi",void 0),P(this,"nbNetworkMobile",void 0),P(this,"nbNetworkEthernet",void 0),P(this,"lastNetworkState",void 0),P(this,"muteDuration",void 0),P(this,"lastMuteState",void 0),this.timeline=t,this.minIndex=i,this.initialBitrate=void 0,this.builder=void 0,this.summaryDuration=0,this.pauseDuration=0,this.nbNetworkDisconnected=0,this.nbNetworkWifi=0,this.nbNetworkMobile=0,this.nbNetworkEthernet=0,this.lastNetworkState=void 0,this.muteDuration=0,this.lastMuteState=void 0,this.init()}return D(e,[{key:"init",value:function(){for(var e=this.minIndex;e>=0;e--){var t=this.timeline.events[e];switch(t.eventId){case j.LayerSwitch:case j.FirstImage:void 0===this.initialBitrate&&(this.initialBitrate=parseInt(t.eventData.bitrate,10));break;case j.Start:void 0===this.lastNetworkState&&(this.lastNetworkState=parseInt(t.eventData.networkType,10)),void 0===this.lastMuteState&&(this.lastMuteState=parseInt(t.eventData.muteState,10));break;case j.NetworkAvailable:void 0===this.lastNetworkState&&(this.lastNetworkState=parseInt(t.eventData.state,10));break;case j.NetworkLost:void 0===this.lastNetworkState&&(this.lastNetworkState=0);break;case j.Mute:void 0===this.lastMuteState&&(this.lastMuteState=1);break;case j.Unmute:void 0===this.lastMuteState&&(this.lastMuteState=0)}}void 0!==this.initialBitrate&&0!==this.initialBitrate||(this.initialBitrate=-1),void 0===this.lastNetworkState&&(this.lastNetworkState=1),void 0===this.lastMuteState&&(this.lastMuteState=0)}},{key:"update",value:function(e){if(!(this.minIndex>=this.timeline.events.length||e>=this.timeline.events.length)){var t,i,n,r=this.timeline.events[this.minIndex],a=this.timeline.events[e],s=this.initialBitrate,o=r.eventDate,d=1===this.lastMuteState?r.eventDate:-1;this.builder=new G,this.summaryDuration=a.eventDate-r.eventDate,this.pauseDuration=0,this.nbNetworkDisconnected=0,this.nbNetworkWifi=0,this.nbNetworkMobile=0,this.nbNetworkEthernet=0,this.muteDuration=0;for(var l=this.minIndex;l<=e;l++){var u=this.timeline.events[l];switch(u.eventId){case j.Pause:!1!==t&&void 0!==t||(null!==u.stopEvent&&u.stopEvent.eventDate<=a.eventDate&&(this.pauseDuration+=u.stopEvent.eventDate-u.eventDate),t=!0);break;case j.Resume:void 0===t&&(this.pauseDuration+=u.eventDate-r.eventDate),t=!1;break;case j.StallStart:!1!==i&&void 0!==i||(null!==u.stopEvent&&u.stopEvent.eventDate<=a.eventDate&&this.builder.addStall(u.stopEvent.eventDate-u.eventDate),i=!0);break;case j.StallStop:void 0===i&&this.builder.addStall(u.eventDate-r.eventDate),i=!1;break;case j.RebufferingStart:!1!==n&&void 0!==n||(null!==u.stopEvent&&u.stopEvent.eventDate<=a.eventDate&&this.builder.addRebuffering(u.stopEvent.eventDate-u.eventDate),n=!0);break;case j.RebufferingStop:void 0===n&&this.builder.addRebuffering(u.eventDate-r.eventDate),n=!1;break;case j.LayerSwitch:this.builder.addLayerSwitch(),-1!==s&&this.builder.addTimeSpentPerLayer(s,u.eventDate-o),s=parseInt(u.eventData.bitrate,10),o=u.eventDate;break;case j.NetworkAvailable:this.lastNetworkState=parseInt(u.eventData.state,10),this.lastNetworkState>=10&&this.lastNetworkState<20?this.nbNetworkWifi++:this.lastNetworkState>=20&&this.lastNetworkState<30?this.nbNetworkMobile++:this.lastNetworkState>=30&&this.lastNetworkState<40&&this.nbNetworkEthernet++;break;case j.NetworkLost:this.lastNetworkState=0,this.nbNetworkDisconnected++;break;case j.Mute:this.lastMuteState=1,d=u.eventDate;break;case j.Unmute:this.lastMuteState=0,-1!==d&&(this.muteDuration+=u.eventDate-d,d=-1)}}-1!==s&&this.builder.addTimeSpentPerLayer(s,a.eventDate-o),-1!==d&&(this.muteDuration+=a.eventDate-d),this.builder.build()}}},{key:"data",value:function(){if(void 0===this.builder)return x.EMPTY;var t=new x(e.BUFFER_SIZE),i=this.builder.metrics;return t.put(j.DataSummary).putChar(Math.round(this.summaryDuration/1e3)).put(e.BUFFER_SIZE-2-1-1).putChar(this.pauseDuration/100).put(i.stallsNumber).putChar(i.totalStallsDuration/100).put(i.rebufferingsNumber).putChar(i.totalRebufferingDuration/100).put(i.layerSwitchesNumber).putChar(i.minBitrate).putChar(i.maxBitrate).putChar(i.averageBitrate).put(this.nbNetworkDisconnected).put(this.nbNetworkWifi).put(this.nbNetworkMobile).put(this.nbNetworkEthernet).putChar(this.lastNetworkState).putChar(Math.round(this.muteDuration/1e3)).put(this.lastMuteState),t}},{key:"toString",value:function(){if(void 0===this.builder)return"no data";var e=this.builder.metrics;return this.summaryDuration+", "+this.pauseDuration+", "+e.minBitrate+", "+e.maxBitrate+", "+e.layerSwitchesNumber+", "+e.averageBitrate+", "+e.stallsNumber+", "+e.totalStallsDuration+", "+e.rebufferingsNumber+", "+e.totalRebufferingDuration+", "+this.nbNetworkDisconnected+", "+this.nbNetworkWifi+", "+this.nbNetworkMobile+", "+this.nbNetworkEthernet+", "+this.lastNetworkState+", "+this.muteDuration+", "+this.lastMuteState}}]),e}();P(ie,"BUFFER_SIZE",28);var ne="BpkSessionTrackerEncoder",re=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.DEFAULT_BUFFER_SIZE,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.DEFAULT_END_EVENTS_DURATION,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.DEFAULT_END_EVENTS_NUMBER;k(this,e),P(this,"timeline",void 0),P(this,"maxBufferSize",void 0),P(this,"maxEndEventsDuration",void 0),P(this,"maxEndEventsNumber",void 0),P(this,"events",void 0),P(this,"uncompressedData",void 0),P(this,"uncompressedDataFull",void 0),P(this,"compressedStartData",void 0),P(this,"minSummaryIndex",void 0),P(this,"maxEndBufferSize",void 0),P(this,"summary",void 0),this.maxBufferSize=i,this.maxEndEventsDuration=n,this.maxEndEventsNumber=r,this.timeline=t,this.events=this.timeline.events,this.uncompressedData=new x(this.maxBufferSize),this.uncompressedDataFull=!1,this.compressedStartData=void 0,this.minSummaryIndex=0,this.maxEndBufferSize=this.maxBufferSize,this.summary=void 0}return D(e,[{key:"onEventAdded",value:function(e){if(!this.uncompressedDataFull){var t=e;this.events.length>=2&&(t=this.events[this.events.length-2]),e.compressedData=e.toData(t.eventDate),e.compressed=!0,e.compressedData.capacity()<=this.uncompressedData.remaining()?this.uncompressedData.putByteBuffer(e.compressedData):(this.uncompressedDataFull=!0,this.compressedStartData=new x(this.maxBufferSize))}}},{key:"onEventUpdated",value:function(){this.uncompressedData=new x(this.maxBufferSize);for(var e=0;e<this.events.length;e++)void 0!==this.events[e].compressedData&&this.uncompressedData.putByteBuffer(this.events[e].compressedData)}},{key:"process",value:function(){var e=Date.now();if(!this.uncompressedDataFull)return n.g.d(ne,"Timeline encoder generated uncompressed data ("+this.events.length+" events, "+this.uncompressedData.length()+" bytes)"),this.uncompressedData;for(var t,i="",r=[],a=0,s=this.events.length-1;s>=0;s--){var o=this.events[s];if(!(e-o.eventDate<this.maxEndEventsDuration&&r.length<this.maxEndEventsNumber)){t=s;break}var d=void 0;if(s<this.events.length-1){var l=this.events[s+1];d=o.toData(l.eventDate)}else d=o.toData(o.eventDate);if(!(a+d.length()<=this.maxEndBufferSize)){t=s;break}r.push(d),a+=d.length()}if(0===this.compressedStartData.length()){this.minSummaryIndex=t;for(var u=this.maxBufferSize-ie.BUFFER_SIZE-a,c=0;c<t;c++){var h=this.events[c];if(this.compressedStartData.length()+h.compressedData.length()>u){this.minSummaryIndex=c;break}this.compressedStartData.putByteBuffer(h.compressedData)}this.maxEndBufferSize=this.compressedStartData.remaining()-ie.BUFFER_SIZE,this.uncompressedData=void 0,i+="first iteration, "}var v=new x(this.maxBufferSize);if(v.putByteBuffer(this.compressedStartData,this.compressedStartData.length()),this.minSummaryIndex===t)v.put(j.EmptySummary),i+="no summary";else{void 0===this.summary&&(this.summary=new ie(this.timeline,this.minSummaryIndex)),this.summary.update(t);var p=this.summary.data();v.putByteBuffer(p),i+="summary {"+this.summary.toString()+"}"}for(var f=0;f<r.length;f++)v.putByteBuffer(r[f]);return n.g.d(ne,"Timeline encoder generated compressed data ("+this.events.length+" events before encoding, "+(this.minSummaryIndex+r.length)+" events after encoding, "+v.length()+" bytes, ~"+(Date.now()-e)+"ms, "+i+")"),v}},{key:"extend",value:function(){if(this.uncompressedDataFull)n.g.w(ne,"Failed to extend size from "+e.DEFAULT_BUFFER_SIZE+" to 768");else{this.maxBufferSize=768,this.maxEndEventsDuration=4e4,this.maxEndEventsNumber=40;var t=this.uncompressedData;this.uncompressedData=new x(this.maxBufferSize),this.uncompressedData.putByteBuffer(t,t.length()),n.g.v(ne,"Extended size from "+e.DEFAULT_BUFFER_SIZE+" to 768")}}}]),e}();P(re,"DEFAULT_BUFFER_SIZE",384),P(re,"DEFAULT_END_EVENTS_DURATION",15e3),P(re,"DEFAULT_END_EVENTS_NUMBER",20);var ae="BpkSessionTrackerTimeline",se=function(){function e(){k(this,e),P(this,"session",void 0),P(this,"startDate",void 0),P(this,"stopDate",void 0),P(this,"events",void 0),P(this,"firstImageWithoutBitrateEvent",void 0),P(this,"encoder",void 0),this.startDate=null,this.stopDate=null,this.events=[],this.firstImageWithoutBitrateEvent=null,this.encoder=new re(this)}return D(e,[{key:"pushEvent",value:function(e){if(this.checkType(e,W)){var t=this.createEvent(e);this.encoder.onEventAdded(t)}}},{key:"pushEventStart",value:function(e,t,i){if(this.checkType(e,Y)){var n=this.createEvent(e);n.addEventData("networkType",t),n.addEventData("muteState",i),this.encoder.onEventAdded(n)}}},{key:"pushEventBitrate",value:function(e,t){if(this.checkType(e,Q)){var i=this.createEvent(e);i.addEventData("bitrate",t),null!==this.firstImageWithoutBitrateEvent&&(this.firstImageWithoutBitrateEvent.addEventData("bitrate",t),this.firstImageWithoutBitrateEvent=null),this.encoder.onEventAdded(i)}}},{key:"pushEventBitratePosition",value:function(e,t,i){if(this.checkType(e,z)){var n=this.createEvent(e);n.addEventData("bitrate",t),n.addEventData("position",i),e===j.FirstImage&&t<=0&&(this.firstImageWithoutBitrateEvent=n),this.encoder.onEventAdded(n)}}},{key:"pushEventPositionStartEnd",value:function(e,t,i){if(this.checkType(e,J)){var n=this.createEvent(e);n.addEventData("positionStart",t),n.addEventData("positionEnd",i),this.encoder.onEventAdded(n)}}},{key:"pushEventStatusCode",value:function(e,t){if(this.checkType(e,X)){var i=this.createEvent(e);i.addEventData("statusCode",t),this.encoder.onEventAdded(i)}}},{key:"pushEventProgress",value:function(e,t){if(this.checkType(e,$)){var i=this.createEvent(e);i.addEventData("progress",t),this.encoder.onEventAdded(i)}}},{key:"pushEventState",value:function(e,t){if(this.checkType(e,Z)){var i=this.createEvent(e);i.addEventData("state",t),this.encoder.onEventAdded(i)}}},{key:"createEvent",value:function(e){var t=new te(e);return n.g.v(ae,"Creating event "+t.getEventName()+"..."),this.applyKeepLastOnly(t),this.events.push(t),this.updateTimelineProperties(t),this.reconciliateStopWithStartEvent(t),t}},{key:"checkType",value:function(e,t){return t.indexOf(e)>=0||(n.g.v(ae,"Can't push event '"+e+"' to timeline, invalid parameters"),!1)}},{key:"applyKeepLastOnly",value:function(e){if(e.keepLastOnly)for(var t=this.events.length-1;t>=0;t--)if(this.events[t].eventId===e.eventId)return this.events.splice(t,1),void this.encoder.onEventUpdated()}},{key:"attachEvent",value:function(e){if(e.attachEventId>j.None){var t=-1;null!==e.startEvent&&-1!==e.attachMaxDurationBeforeStart&&(t=e.startEvent.eventDate-e.attachMaxDurationBeforeStart);for(var i=this.events.length-1;i>=0;i--){var n=this.events[i];if(n.eventId===e.attachEventId&&(-1===t||n.eventDate>=t))return void(e.attachedEvent=n)}}}},{key:"updateTimelineProperties",value:function(e){switch(e.eventId){case j.Start:this.startDate=Date.now();break;case j.Stop:this.stopDate=Date.now()}}},{key:"reconciliateStopWithStartEvent",value:function(e){if(e.isStopEvent())for(var t=this.events.length-1;t>=0;t--){var i=this.events[t];if(i.eventId===e.startEventId)return i.stopEvent=e,void(e.startEvent=i);if(i.eventId===e.triggerStartEventId)return i.eventId=e.startEventId,i.startStopEvent=!0,i.startEventId=e.startEventId,i.stopEventId=e.stopEventId,void 0!==i.compressedData&&(i.compressedData.set(i.eventId,0),this.encoder.onEventUpdated()),i.stopEvent=e,void(e.startEvent=i)}}},{key:"onStart",value:function(e,t){this.pushEventStart(j.Start,e,t)}},{key:"onRedirectionEnd",value:function(){this.pushEvent(j.RedirectionEnd)}},{key:"onPrecacheEnded",value:function(){this.pushEvent(j.PrecacheEnded)}},{key:"onFirstImage",value:function(e,t){this.pushEventBitratePosition(j.FirstImage,e,t)}},{key:"onStop",value:function(e){this.pushEventStatusCode(j.Stop,e)}},{key:"onForeground",value:function(){n.g.d(ae,"Received event onForeground but ignored")}},{key:"onBackground",value:function(){n.g.d(ae,"Received event onBackground but ignored")}},{key:"onNetworkAvailable",value:function(e){n.g.v(ae,"Received event onNetworkAvailable type:"+e),this.pushEventState(j.NetworkAvailable,e)}},{key:"onNetworkLost",value:function(){n.g.d(ae,"Received event onNetworkLost"),this.pushEvent(j.NetworkLost)}},{key:"onMute",value:function(){n.g.d(ae,"Received event onMute"),this.pushEvent(j.Mute)}},{key:"onUnmute",value:function(){n.g.d(ae,"Received event onUnmute"),this.pushEvent(j.Unmute)}},{key:"onMulticastUsed",value:function(){this.pushEvent(j.Multicast)}},{key:"onUnicastUsed",value:function(){this.pushEvent(j.Unicast)}},{key:"data",value:function(){return this.encoder.process()}},{key:"formatDate",value:function(e){return H.A.formatDate(new Date(e))}},{key:"print",value:function(){n.g.v(ae,"Timeline (startDate:"+this.formatDate(this.startDate)+", stopDate:"+this.formatDate(this.stopDate)+")");for(var e=0;e<this.events.length;e++)this.events[e].print()}},{key:"toString",value:function(){for(var e=[],t=0;t<this.events.length;t++)e.push(this.events[t].toString());return e.join(", ")}}]),e}(),oe=i(828),de="BpkAnalyticsSession",le=function(e){h(i,e);var t=p(i);function i(e,n){var r;return k(this,i),P(f(r=t.call(this,e,n)),"started",void 0),r.getURL=void 0,r.getQuery=void 0,r.startStreamingSession=void 0,r.stopAnalyticsSession=r.stopStreamingSession,r.stopStreamingSession=void 0,r.started=!1,r}return D(i,[{key:"attachPlayer",value:function(e,t){c(y(i.prototype),"attachPlayer",this).call(this,e,t),void 0===this.handler&&(this.handler=this.smartLib.sessionManager.createSessionHandler(this),this.handler.initPlayerAdapter(),this.handler.addListener(this))}},{key:"onLoading",value:function(){n.g.i(de,"Session is loading...",this.id),!1===this.started?(this.started=!0,this.handler.adSession=this.adSession,void 0!==this.adSession&&(this.adSession.handler=this.handler),this.handler.start("").catch((function(e){}))):n.g.d(de,"Exception: onLoading, the session is already running.",this.id)}},{key:"updateSessionReportValue",value:function(e){var t=this.handler.sessionReport;void 0!==this.customParameters["report."+e]&&(t[e]=this.customParameters["report."+e])}},{key:"onClose",value:function(e){n.g.i(de,"Session is closing (status code: "+e+")...",this.id),void 0!==this.handler.sessionReport.metrics&&(this.handler.sessionReport.metrics.redirectionTime=-1),this.updateSessionReportValue("requestedURL"),this.updateSessionReportValue("redirectedURL"),this.stopAnalyticsSession(e)}}]),i}(oe.f),ue=i(7418),ce="BpkCacheMgr",he=function(){function e(){k(this,e)}return D(e,[{key:"set",value:function(e,t){}},{key:"get",value:function(e){}},{key:"delete",value:function(e){}},{key:"keys",value:function(){return[]}}]),e}(),ve=function(){function e(){k(this,e),P(this,"smartLib",void 0),P(this,"cacheHandler",void 0),this.cacheHandler=new he}return D(e,[{key:"init",value:function(e){this.cacheHandler=e}},{key:"attachInstance",value:function(e){var t=this;n.g.d(ce,"Init cache manager..."),this.smartLib=e,this.getCacheData("report-").forEach((function(e){void 0!==e&&(e.value.sending=!1,t.store(e.key,e.value))}))}},{key:"get",value:function(e){var t=this.cacheHandler.get(e);if(void 0!==t)try{return t.startsWith("{")||(t=F.A.base64ToString(t)),t=JSON.parse(t)}catch(t){return n.g.e(ce,"Error while parsing "+e+" ("+t.message+")"),void this.cacheHandler.delete(e)}}},{key:"store",value:function(e,t){this.cacheHandler.set(e,F.A.stringToBase64(JSON.stringify(t)))}},{key:"update",value:function(e,t,i){var r=this.get(e);void 0!==r&&(n.g.d(ce,"Updating "+e+", set "+t+" to "+i),r[t]=i,this.store(e,r))}},{key:"storeSessionReport",value:function(e,t){var i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Date.now(),a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s="report-"+F.A.randomIntFromInterval(1e6,9999999)+r;n.g.i(ce,"Storing "+s+" in cache...");var o={version:this.smartLib.getVersion(),date:r,sending:a,address:e,report:t};return this.store(s,o),!0===i&&this.clean(),s}},{key:"deleteSessionReport",value:function(e){n.g.i(ce,"Deleting "+e+" from cache..."),this.cacheHandler.delete(e)}},{key:"storeKeepaliveReport",value:function(e,t){n.g.i(ce,"Storing keepalive-"+t.session_id+" in cache...");var i={version:this.smartLib.getVersion(),date:Date.now(),address:e,report:t};this.store("keepalive-"+t.session_id,i)}},{key:"deleteKeepaliveReport",value:function(e){n.g.i(ce,"Deleting keepalive-"+e+" from cache..."),this.cacheHandler.delete("keepalive-"+e)}},{key:"getCacheData",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return this.cacheHandler.keys().filter((function(e){return e.startsWith(t)})).map((function(t){var i=e.get(t);return void 0===i?void 0:{key:t,value:i}}))}},{key:"push",value:function(){var e=this;this.clean(),n.g.i(ce,"Sending cache content if any..."),this.getCacheData("report-").forEach((function(t){var i;void 0!==t&&(!0===t.value.sending?n.g.d(ce,"Sending cache "+t.key+" already in progress..."):(n.g.d(ce,"Sending cache "+t.key+"..."),t.value.sending=!0,t.value.report.delay=Math.round((Date.now()-t.value.date)/1e3),e.store(t.key,t.value),null===(i=M.A.analyticsModule)||void 0===i||i.AnalyticsRequestManager.getInstance().endSessionCache(t.value.address,t.value.report,e.smartLib.getParameters()).then((function(i){!0===i?e.deleteSessionReport(t.key):(t.value.sending=!1,e.store(t.key,t.value))}))))}))}},{key:"cleanExpiredData",value:function(t){var i=this;return this.getCacheData(t).map((function(t){return void 0!==t&&(void 0===t.value.date||Date.now()-t.value.date>e.CACHE_DURATION)?(n.g.d(ce,"Cleaning "+t.key+" (cache duration reached)..."),void i.cacheHandler.delete(t.key)):t})).filter((function(e){return void 0!==e})).sort((function(e,t){return t.value.date-e.value.date}))}},{key:"clean",value:function(){var t=this;n.g.i(ce,"Clean expired data if any...");var i=this.cleanExpiredData("keepalive-"),r=this.smartLib.sessionManager.sessions.map((function(e){var t,i;return null===(t=e.handler)||void 0===t||null===(i=t.sessionReport)||void 0===i?void 0:i.sessionId}));i.forEach((function(e){-1===r.indexOf(e.value.report.session_id)&&(n.g.d(ce,"Migrating keepalive "+e.value.report.session_id+" to session..."),e.value.report.timeout=!0,e.value.address.split(",").forEach((function(i){0===i.indexOf(U.NOCACHE_PREFIX)?n.g.d(ce,U.NOCACHE_PREFIX+" option used, no need to store the report in cache"):t.storeSessionReport(U.getInstance().buildAnalyticsAddress(i),e.value.report,!1,e.value.date)})),t.cacheHandler.delete(e.key))}));var a=this.cleanExpiredData("report-");if(a.length>=e.CACHE_LIMIT)for(var s=e.CACHE_LIMIT;s<a.length;s++)this.deleteSessionReport(a[s].key)}},{key:"release",value:function(){this.clean()}}],[{key:"getInstance",value:function(){return d(e,e,pe)||o(e,e,pe,new e),d(e,e,pe)}}]),e}();P(ve,"CACHE_DURATION",1728e5),P(ve,"CACHE_LIMIT",20);var pe={writable:!0,value:void 0},fe=function(e){h(i,e);var t=p(i);function i(e){var r;return k(this,i),P(f(r=t.call(this,e)),"analyticsAddress",void 0),r.analyticsAddress=r.handler.smartLib.getParameters().analyticsAddress,n.g.d(ce,"Using cache keepalive manager...",r.handler.id),r}return D(i,[{key:"start",value:function(){c(y(i.prototype),"start",this).call(this),this.store()}},{key:"callback",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.store(),!0===t&&this.next()}},{key:"stop",value:function(){c(y(i.prototype),"stop",this).call(this),this.delete()}},{key:"store",value:function(){var e;null===(e=M.A.analyticsModule)||void 0===e||e.CacheManager.getInstance().storeKeepaliveReport(this.analyticsAddress,this.handler.sessionReport.toEndSessionJSON())}},{key:"delete",value:function(){var e;null===(e=M.A.analyticsModule)||void 0===e||e.CacheManager.getInstance().deleteKeepaliveReport(this.handler.sessionReport.sessionId)}}]),i}(ue.E),ye=function(e){h(i,e);var t=p(i);function i(e){var n;return k(this,i),P(f(n=t.call(this,e)),"cacheKeepaliveManager",void 0),n.cacheKeepaliveManager=new fe(e),n.cacheKeepaliveManager.next=function(){},n}return D(i,[{key:"start",value:function(){c(y(i.prototype),"start",this).call(this),this.cacheKeepaliveManager.store()}},{key:"callback",value:function(e){this.cacheKeepaliveManager.callback(e),c(y(i.prototype),"callback",this).call(this,e)}},{key:"stop",value:function(){c(y(i.prototype),"stop",this).call(this),this.cacheKeepaliveManager.delete()}}]),i}(ue.q);M.A.analyticsModule={PlayerManager:I,PlayerAdapter:s,GenericPlayerAdapter:u,PlayerEventListener:_,GenericPlayerApi:O,AnalyticsRequestManager:U,SessionTrackerTimeline:se,SessionTrackerEvent:te,SessionTrackerEvents:j,Metrics:V,MetricsManager:K,AnalyticsSession:le,CacheManager:ve,CacheKeepaliveManager:fe,BroadpeakCDNCacheKeepaliveManager:ye}},4404:function(e,t,i){i.d(t,{A:function(){return n}});var n=function(){function e(){k(this,e)}return D(e,null,[{key:"hasMethods",value:function(e,t){var i=!0;return void 0!==e&&(t.forEach((function(t){"function"!=typeof e[t]&&(i=!1)})),i)}},{key:"count",value:function(e,t){var i=0;for(var n in e)!0===t(n,e[n])&&i++;return i}}]),e}()}},function(e){return e(e.s=7745)}])},"object"==R(t)&&"object"==R(e)?e.exports=s():(r=[],void 0===(a="function"==typeof(n=s)?n.apply(t,r):n)||(e.exports=a))},3846:function(e,t,i){"use strict";var n,r,a,s;function o(e,t){h(e,t),t.add(e)}function d(e,t,i){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return i}function l(e){return function(e){if(Array.isArray(e))return u(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return u(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return u(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function c(e,t,i){h(e,t),t.set(e,i)}function h(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function v(e,t){return m(e,f(e,t,"get"))}function p(e,t,i){return g(e,f(e,t,"set"),i),i}function f(e,t,i){if(!t.has(e))throw new TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function y(e,t,i,n){return E(e,t),_(i,"set"),g(e,i,n),n}function g(e,t,i){if(t.set)t.set.call(e,i);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=i}}function S(e,t,i){return E(e,t),_(i,"get"),m(e,i)}function _(e,t){if(void 0===e)throw new TypeError("attempted to "+t+" private static field before its declaration")}function E(e,t){if(e!==t)throw new TypeError("Private static access of wrong provenance")}function m(e,t){return t.get?t.get.call(e):t.value}function A(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function k(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function N(e,t,i){return t&&k(e.prototype,t),i&&k(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function D(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}e=i.nmd(e),"undefined"!=typeof self?self:i.g,s=function(){return(("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule=("undefined"!=typeof self?self:i.g).webpackChunkSmartLibModule||[]).push([[806],{8184:function(e,t,i){i.r(t),i.d(t,{HostResolvingTask:function(){return T},MdnsManager:function(){return x},NanoCDNDescr:function(){return m},NanoCDNHostManager:function(){return K},NanoCDNRequestManager:function(){return R},NanoCDNService:function(){return C}});var n=i(8379),r=i(1134),a=i(6506),s=i(7964),u=i(7832),h=i(3473),f=i(3121),g=i(4404),_=function(){var e={"&amp;":"&","&lt;":"<","&gt;":">","&apos;":"'","&quot;":'"'};return{}.fromXML=function(e,r){return n(function(e){for(var n=String.prototype.split.call(e,/<([^!<>?](?:'[\S\s]*?'|"[\S\s]*?"|[^'"<>])*|!(?:--[\S\s]*?--|\[[^\[\]'"<>]+\[[\S\s]*?]]|DOCTYPE[^\[<>]*?\[[\S\s]*?]|(?:ENTITY[^"<>]*?"[\S\s]*?")?[\S\s]*?)|\?[\S\s]*?\?)>/),r=n.length,a={f:[]},s=a,o=[],d=0;d<r;){var l=n[d++];l&&v(l);var u=n[d++];u&&c(u)}return a;function c(e){var t=e.length,i=e[0];if("/"===i)for(var n=e.replace(/^\/|[\s\/].*$/g,"").toLowerCase();o.length;){var r=s.n&&s.n.toLowerCase();if(s=o.pop(),r===n)break}else if("?"===i)h({n:"?",r:e.substr(1,t-2)});else if("!"===i)"[CDATA["===e.substr(1,7)&&"]]"===e.substr(-2)?v(e.substr(8,t-10)):h({n:"!",r:e.substr(1)});else{var a=function(e){var t={f:[]},i=(e=e.replace(/\s*\/?$/,"")).search(/[\s='"\/]/);return i<0?t.n=e:(t.n=e.substr(0,i),t.t=e.substr(i)),t}(e);h(a),"/"===e[t-1]?a.c=1:(o.push(s),s=a)}}function h(e){s.f.push(e)}function v(e){(e=t(e))&&h(i(e))}}(e),r)};function t(e){return e&&e.replace(/^\s+|\s+$/g,"")}function i(t){return t.replace(/(&(?:lt|gt|amp|apos|quot|#(?:\d{1,6}|x[0-9a-fA-F]{1,5}));)/g,(function(t){if("#"===t[1]){var i="x"===t[2]?parseInt(t.substr(3),16):parseInt(t.substr(2),10);if(i>-1)return String.fromCharCode(i)}return e[t]||t}))}function n(e,a){if("string"==typeof e)return e;var s=e.r;if(s)return s;var o,d=function(e,n){if(e.t){for(var a,s,o=e.t.split(/([^\s='"]+(?:\s*=\s*(?:'[\S\s]*?'|"[\S\s]*?"|[^\s'"]*))?)/),d=o.length,l=0;l<d;l++){var u=t(o[l]);if(u){a||(a={});var c=u.indexOf("=");if(c<0)u="@"+u,s=null;else{s=u.substr(c+1).replace(/^\s+/,""),u="@"+u.substr(0,c).replace(/\s+$/,"");var h=s[0];h!==s[s.length-1]||"'"!==h&&'"'!==h||(s=s.substr(1,s.length-2)),s=i(s)}n&&(s=n(u,s)),r(a,u,s)}}return a}}(e,a),l=e.f,u=l.length;if(d||u>1)o=d||{},l.forEach((function(e){"string"==typeof e?r(o,"#",e):r(o,e.n,n(e,a))}));else if(u){var c=l[0];if(o=n(c,a),c.n){var h={};h[c.n]=o,o=h}}else o=e.c?null:"";return a&&(o=a(e.n||"",o)),o}function r(e,t,i){if(void 0!==i){var n=e[t];n instanceof Array?n.push(i):e[t]=t in e?[n,i]:i}}}(),E="BpkNanoCDNDescr",m=function(){function e(){A(this,e),D(this,"hostIP",void 0),D(this,"nbSessions",void 0),D(this,"maxNbSessions",void 0),D(this,"ratio",void 0),D(this,"jsonAccountId",void 0),D(this,"profilePrefetched",void 0),D(this,"mcastFrame",void 0),D(this,"versionDigit",void 0),D(this,"versionFull",void 0),D(this,"deviceType",void 0),D(this,"protocol",void 0),D(this,"bka100",void 0),D(this,"bka200",void 0),D(this,"priority",void 0),this.hostIP="",this.nbSessions=0,this.maxNbSessions=50,this.ratio=0,this.jsonAccountId="",this.profilePrefetched=-1,this.mcastFrame=-1,this.versionDigit="",this.versionFull="",this.deviceType="",this.protocol="",this.bka100=void 0,this.bka200=void 0,this.priority=0}return N(e,[{key:"processRatio",value:function(){this.ratio=this.maxNbSessions/(this.nbSessions+1)}},{key:"print",value:function(){n.g.d(E,"nanoCDN:"),n.g.d(E,"   host: "+this.hostIP+" protocol: "+this.protocol+" priority: "+this.priority+" session: "+this.nbSessions+" limit: "+this.maxNbSessions+" jsonid:"+this.jsonAccountId+" profile_prefetched:"+this.profilePrefetched+" mcast_frame:"+this.mcastFrame),n.g.d(E,"   version_digit: "+this.versionDigit+" version_full: "+this.versionFull),n.g.d(E,"   device_type: "+this.deviceType),n.g.d(E,'   bka100: "'+this.bka100+'" bka200: "'+this.bka200+'"')}}],[{key:"parseNanoCDNStatusResponse",value:function(t,i,n){var r,a,s=_(t);if(void 0!==s[e.NANOCDN_TAG]){(r=new e).hostIP=i,r.protocol=n;var o=s[e.NANOCDN_TAG][e.SESSIONS_TAG];void 0!==o&&(void 0!==o[e.SESSIONS_NUMBER_PARAM]&&(a=parseInt(o[e.SESSIONS_NUMBER_PARAM],10),isNaN(a)||(r.nbSessions=a)),void 0!==o[e.SESSIONS_LIMIT_PARAM]&&(a=parseInt(o[e.SESSIONS_LIMIT_PARAM],10),isNaN(a)||(r.maxNbSessions=a)));var d=s[e.NANOCDN_TAG][e.ACCESSTOKEN_TAG];if(void 0!==d){var l={};for(var u in d)l[u.substring(1)]=d[u];r.jsonAccountId=JSON.stringify(l)}var c=s[e.NANOCDN_TAG][e.CTRL_CHANNEL_TAG];void 0!==c&&(void 0!==c[e.CTRL_CHANNEL_PROFILE_PREFETCHED_PARAM]&&(a=parseInt(c[e.CTRL_CHANNEL_PROFILE_PREFETCHED_PARAM],10),isNaN(a)||(r.profilePrefetched=a)),void 0!==c[e.CTRL_CHANNEL_MCAST_FRAME_PARAM]&&(a=parseInt(c[e.CTRL_CHANNEL_MCAST_FRAME_PARAM],10),isNaN(a)||(r.mcastFrame=a)));var h=s[e.NANOCDN_TAG][e.VERSION_TAG];void 0!==h&&(void 0!==h[e.VERSION_DIGIT_PARAM]&&(r.versionDigit=h[e.VERSION_DIGIT_PARAM]),void 0!==h[e.VERSION_FULL_PARAM]&&(r.versionFull=h[e.VERSION_FULL_PARAM]));var v=s[e.NANOCDN_TAG][e.DEVICE_TAG];void 0!==v&&void 0!==v[e.DEVICE_TYPE_PARAM]&&(r.deviceType=v[e.DEVICE_TYPE_PARAM]);var p=s[e.NANOCDN_TAG][e.SMARTLIB_TAG];void 0!==p&&(r.bka100=p[e.SMARTLIB_BKA100_PARAM],r.bka200=p[e.SMARTLIB_BKA200_PARAM],void 0!==p[e.SMARTLIB_PRIORITY_PARAM]&&(a=parseInt(p[e.SMARTLIB_PRIORITY_PARAM],10),!isNaN(a)&&a>=0&&(r.priority=a))),r.processRatio()}return r}}]),e}();D(m,"NANOCDN_TAG","nanocdn"),D(m,"SESSIONS_TAG","sessions"),D(m,"SESSIONS_NUMBER_PARAM","@number"),D(m,"SESSIONS_LIMIT_PARAM","@limit"),D(m,"ACCESSTOKEN_TAG","access-token"),D(m,"CTRL_CHANNEL_TAG","ctrl-channel"),D(m,"CTRL_CHANNEL_PROFILE_PREFETCHED_PARAM","@profile_prefetched"),D(m,"CTRL_CHANNEL_MCAST_FRAME_PARAM","@mcast_frame"),D(m,"VERSION_TAG","version"),D(m,"VERSION_FULL_PARAM","@full"),D(m,"VERSION_DIGIT_PARAM","@digit"),D(m,"DEVICE_TAG","device"),D(m,"DEVICE_TYPE_PARAM","@type"),D(m,"SMARTLIB_TAG","smartlib"),D(m,"SMARTLIB_BKA100_PARAM","@bka100"),D(m,"SMARTLIB_BKA200_PARAM","@bka200"),D(m,"SMARTLIB_PRIORITY_PARAM","@priority");var k=i(1142),P="BpkNanoCDNRequestMgr",R=function(){function e(){A(this,e)}return N(e,[{key:"requestNanoCDNStatus",value:function(t,i){var a=this,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:k.A.getInstance().options.get(u.H.TIMEOUT_NANO_CDN_STATIC_RESOLVING),o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,d=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"https",l=arguments.length>5&&void 0!==arguments[5]&&arguments[5];n.g.d(P,"Requesting nanoCDN status with host '"+i+"' (httpsEnabled:"+t.nanoCDNHttpsEnabled+", protocol:"+d+", timeout:"+s+"ms)",null==o?void 0:o.id),!1===t.nanoCDNHttpsEnabled&&"https"===d&&(n.g.d(P,"Updating protocol to http, nanoCDNHttpsEnabled is set to false"),d="http");var c=e.NANOCDN_CORE_PORT;"https"===d&&(c=e.NANOCDN_CORE_PORT_HTTPS);var h=d+"://"+i+":"+c+"/nanocdnstatus.xml";return new Promise((function(e,u){var c=r.A.getInstance().asyncGet(h,{Connection:"close"},s,(function(r){var u=0,c="";void 0!==r.statusCode&&(u=parseInt(r.statusCode,10)),void 0!==r.body&&(c=r.body),n.g.d(P,"nanoCDN status with host '"+i+"' responded with status code "+u,null==o?void 0:o.id),u>=200&&u<300?(n.g.d(P,"Setting nanoCDNDescr protocol with : "+d),e({httpStatus:u,body:c,protocol:d})):!1===l&&"https"===d?a.requestNanoCDNStatus(t,i,s,o,"http").then((function(t){e(t)})):e({httpStatus:u,body:c,protocol:d})}));void 0!==o&&o.jobs.push(c)}))}},{key:"retrieveNanoCDNDescr",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return this.requestNanoCDNStatus(e,t).then((function(a){if(200===a.httpStatus){var s=m.parseNanoCDNStatusResponse(a.body,t,a.protocol);if(void 0!==s){if(s.print(),"string"==typeof s.bka200){n.g.i(P,"nanoCDN is overwriting the real time monitoring address to "+s.bka200);var o=e.smartLib.monitoringManager;void 0!==o&&(!1===o.initialized?o.init(s.bka200):(o.setAddress(s.bka200),o.initSettings()))}return s}}else!0===r&&0===i&&e.notifyNanoCDNNotFound(t);throw new Error("Can't resolve nanoCDN with host '"+t+"'")}))}},{key:"requestNanoCDN",value:function(t,i,r){var o=this,d=r.options,l=s.A.parseURL(t),c=a.A.isBroadpeakDomainName(t,i.broadpeakDomainNames)&&!0===d.get(u.H.REQUEST_BROADPEAK_CDN),v=!h.A.getInstance().isConnectedToWifi()||!0===d.get(u.H.REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI)&&h.A.getInstance().isConnectedToWifi();if(("127.0.0.1"===i.nanoCDNDescr.hostIP||"localhost"===i.nanoCDNDescr.hostIP)&&!1===v)return n.g.d(P,"Ignoring nanoCDN "+i.nanoCDNDescr.hostIP+" because of the Wi-Fi connection...",r.id),Promise.resolve(f.S.error(f.S.RESULT_NANOCDN_REQUEST_LOCAL_IF_CONNECTED_TO_WIFI_NOT_ALLOWED));var p=i.nanoCDNDescr.bka100;void 0!==p&&(n.g.i(P,'Using ssu="'+p+'" from nanoCDN configuration',r.id),i.analyticsAddress=p);var y="";""!==l.query&&(y="&"+l.query);var g="";i.analyticsAddress.length>1&&(g="&ssu="+s.A.extractHostnamePort(i.analyticsAddress));var S="";!0===r.options.get(u.H.MULTICAST_ONLY)&&(S="&mo=1");var _="";!0===r.options.get(u.H.ULTRA_LOW_LATENCY_SUPPORT)&&(_="&ulls=1");var E="";!0===r.options.get(u.H.SESSION_PRECACHE)&&c&&(E="&bk-precache=1");var m=e.NANOCDN_RR_PORT;"https"===i.nanoCDNDescr.protocol&&(m=e.NANOCDN_RR_PORT_HTTPS);var A=i.nanoCDNDescr.protocol+"://"+i.nanoCDNDescr.hostIP+":"+m+l.path+"?response=200&bk-ml=1"+g+"&us="+l.host+S+_+E+y;return n.g.d(P,"Sending request to the nanoCDN ("+i.nanoCDNDescr.hostIP+")...",r.id),n.g.d(P,"nanoCDNURL ("+A+")...",r.id),a.A.getInstance().getRedirectionLocation(A,r.options.get(u.H.TIMEOUT_NANO_CDN_REQUEST_ROUTER),i,r).then((function(e){n.g.d(P,"nanoCDN responded with status code "+e.httpStatus,r.id);var t=o.getNanoCDNDetailedErrorMessage(e,r),a=f.S.error(f.S.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN,t);if(200===e.httpStatus&&""!==e.redirectedURL)(a=f.S.success(e.redirectedURL)).nanoCDNUsed=!0,a.nanoCDNDescr=i.nanoCDNDescr;else if(509===e.httpStatus||503===e.httpStatus&&i.nanoCDNDescr.mcastFrame>0)a=f.S.error(f.S.RESULT_NANOCDN_SESSION_LIMIT_REACHED,t);else if(503===e.httpStatus&&0===i.nanoCDNDescr.mcastFrame)a=f.S.error(f.S.RESULT_NANOCDN_SERVICE_UNAVAILABLE,t);else if(404===e.httpStatus)a=f.S.error(f.S.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN,t);else if(e.httpStatus<=0)return f.S.error(f.S.RESULT_NANOCDN_RESPONSE_UNREADABLE);return r.sessionReport.nanoCDNStatusCode=e.httpStatus,r.sessionReport.nanoCDNErrorCode=a.getErrorCode(),a})).then((function(e){return e.isError()&&(r.sessionReport.nanoCDNErrorCode=e.getErrorCode(),r.sessionReport.nanoCDNStatusCode=e.getDetailedErrorCode()),e}))}},{key:"getNanoCDNDetailedErrorMessage",value:function(t,i){if(t.httpStatus>0){if(!0===t.headers.hasOwnProperty(e.HEADER_NANO_CDN_DETAILED_ERROR_CODE)){var r=t.headers[e.HEADER_NANO_CDN_DETAILED_ERROR_CODE];return n.g.d(P,"Reading detailed error code from header: "+r,i.id),r}return n.g.d(P,"Reading detailed error code from http status: "+t.httpStatus+" "+t.httpMessage,i.id),String(t.httpStatus+" "+t.httpMessage)}return String(f.S.RESULT_NO_DETAILED_ERROR)}},{key:"requestNanoCDNList",value:function(e,t,i,n,r){var a=this,s=n.smartLib.nanoCDNHostManager.getNanoCDNDescrList();if(i>=s.length)return{result:f.S.error(f.S.RESULT_NO_STREAMING_URL_FOUND),redirectedURL:"",nanoCDNDescr:void 0,errors:r};var o=s[i];return t.nanoCDNDescr=o,o===t.nanoCDNDescrConfigured?this.requestNanoCDNList(e,t,i+1,n,r):this.requestNanoCDN(e,t,n).then((function(d){return r[o.hostIP]=d.getErrorCode(),d.isError()&&s.length>=2?i+1>=s.length?{result:d,redirectedURL:d.getURL(),nanoCDNDescr:o,errors:r}:a.requestNanoCDNList(e,t,i+1,n,r):{result:d,redirectedURL:d.getURL(),nanoCDNDescr:o,errors:r}}))}},{key:"requestNanoCDNSession",value:function(e,t,i){var r=this,a=i.smartLib.nanoCDNHostManager.getNanoCDNDescrList(),s=a.length;return this.requestNanoCDN(e,t,i).then((function(n){var a={};return a[t.nanoCDNDescr.hostIP]=n.getErrorCode(),n.isError()&&s>=2?r.requestNanoCDNList(e,t,0,i,a):{result:n,nanoCDNDescr:t.nanoCDNDescr,errors:a}})).then((function(e){var t,r;if(n.g.d(P,"nanoCDN error codes : "+JSON.stringify(e.errors),i.id),e.result.isError()){var o,d=g.A.count(e.errors,(function(e,t){return t===f.S.RESULT_NANOCDN_SESSION_LIMIT_REACHED})),l=a.filter((function(t){return e.errors[t.hostIP]===f.S.RESULT_NANOCDN_SERVICE_UNAVAILABLE})),u=l.length;u>0&&(t=l[0].hostIP),o=d===s?f.S.RESULT_NANOCDN_SESSION_LIMIT_REACHED:u===s?f.S.RESULT_NANOCDN_SERVICE_UNAVAILABLE:d>=1?f.S.RESULT_NANOCDN_SESSION_LIMIT_REACHED_ON_REQUESTED_CHANNEL:0===d?f.S.RESULT_NANOCDN_CHANNEL_UNAVAILABLE:f.S.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN,r=f.S.error(o,e.result.getDetailedErrorMessage())}else i.smartLib.nanoCDNHostManager.setCurrentNanoCDNDescr(e.nanoCDNDescr),t=e.nanoCDNDescr.hostIP;return{result:e.result,nanoCDNHostIP:t,noFallbackResult:r}}))}},{key:"getQuery",value:function(e,t){var i="",r=t.options,a=!h.A.getInstance().isConnectedToWifi()||!0===r.get(u.H.REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI)&&h.A.getInstance().isConnectedToWifi(),s=!0===r.get(u.H.REQUEST_NANO_CDN),o=!0===r.get(u.H.REQUEST_NANO_CDN_DURING_GET_QUERY);if(!(a&&s&&o))return Promise.resolve(i);var d=t.smartLib.nanoCDNHostManager,l=e.hostIP,c=e.protocol;return this.requestNanoCDNStatus(d,l,r.get(u.H.TIMEOUT_NANO_CDN_STATIC_RESOLVING),t,c,!0).then((function(r){var a,s=null,o=d.getNanoCDNDescrList(),u=o.findIndex((function(e){return e.hostIP===l})),h=u;if(200===r.httpStatus){var v=m.parseNanoCDNStatusResponse(r.body,l,c);if(void 0!==v){if(h=d.updateNanoCDNList(v),d.setCurrentNanoCDNDescr(v),v.nbSessions<v.maxNbSessions)return i+="&nanocdnhost="+l+"&nanocdnprot="+c;s="nanoCDN '"+l+"' sessions limit reached (max: "+v.maxNbSessions+")"}else s="nanoCDN '"+l+"' body not readable"}else s="nanoCDN '"+l+"' is not responding";return n.g.d(P,s,t.id),o.length>=2&&(a=h>u?o[u]:d.getNextNanoCDNDescr(e),d.setCurrentNanoCDNDescr(a),n.g.d(P,"Try again with next nanoCDN: '"+a.hostIP+"'",t.id)),i}))}}],[{key:"getInstance",value:function(){return S(e,e,b)||y(e,e,b,new e),S(e,e,b)}}]),e}();D(R,"NANOCDN_RR_PORT","8000"),D(R,"NANOCDN_CORE_PORT","18081"),D(R,"HEADER_NANO_CDN_DETAILED_ERROR_CODE","x-bpk-error"),D(R,"NANOCDN_RR_PORT_HTTPS","8443"),D(R,"NANOCDN_CORE_PORT_HTTPS","18443");var b={writable:!0,value:void 0},I="BpkHostResolvingAsyncTask",T=function(){function e(t,i){A(this,e),D(this,"nanoCDNHostManager",void 0),D(this,"nanoCDNHost",void 0),D(this,"retry",void 0),D(this,"delay",void 0),D(this,"active",void 0),D(this,"waiting",void 0),D(this,"job",void 0),this.nanoCDNHostManager=t,this.nanoCDNHost=i,this.retry=0,this.delay=6e4,this.active=!0,this.waiting=!1,this.job=void 0}return N(e,[{key:"start",value:function(){var e,t=this,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n.g.d(I,"Starting resolving nanoCDN with host '"+this.nanoCDNHost+"'"),null===(e=k.A.nanoCDNModule)||void 0===e||e.NanoCDNRequestManager.getInstance().retrieveNanoCDNDescr(this.nanoCDNHostManager,this.nanoCDNHost,this.retry,i).then((function(e){t.active&&(n.g.i(I,"Found nanoCDN with host: '"+t.nanoCDNHost+"'"),e.jsonAccountId.length>0&&n.g.i(I,"Found json account id: '"+e.jsonAccountId+"'"),t.nanoCDNHostManager.updateNanoCDNList(e),t.active=!1,t.nanoCDNHostManager.removeHostResolvingAsyncTask(t.nanoCDNHost))})).catch((function(e){t.active&&(t.nanoCDNHostManager.removeNanoCDN(t.nanoCDNHost),t.retry++,n.g.d(I,e.message+" (retry:"+t.retry+")"),-1===t.delay?(n.g.i(I,"Can't found nanoCDN with host: '"+t.nanoCDNHost+"'"),t.active=!1,t.nanoCDNHostManager.removeHostResolvingAsyncTask(t.nanoCDNHost)):(t.waiting=!0,t.job=r.A.getInstance().asyncDelay(t.delay,(function(){t.waiting=!1,t.active&&t.start()}))))}))}},{key:"stop",value:function(){this.active&&n.g.d(I,"Host resolving stopped for '"+this.nanoCDNHost+"'"),this.active=!1,r.A.getInstance().cancel(this.job)}},{key:"setResolvingRetryDelay",value:function(e){this.delay=e}}]),e}(),C=N((function e(t,i,n){A(this,e),D(this,"name",void 0),D(this,"ipAddress",void 0),D(this,"nanoCDNDescr",void 0),this.name=t,this.ipAddress=i,this.nanoCDNDescr=n})),O="BpkMdnsMgr",L=function(){function e(){A(this,e)}return N(e,null,[{key:"start",value:function(e){}},{key:"stop",value:function(){}}]),e}(),M=new WeakMap,w=new WeakMap,U=new WeakMap,B=new WeakMap,F=new WeakMap,x=function(){function e(){A(this,e),c(this,M,{writable:!0,value:L}),D(this,"smartLib",void 0),c(this,w,{writable:!0,value:void 0}),c(this,U,{writable:!0,value:void 0}),c(this,B,{writable:!0,value:void 0}),c(this,F,{writable:!0,value:void 0}),p(this,w,!1),p(this,U,!1),p(this,B,[])}return N(e,[{key:"init",value:function(e){p(this,M,e)}},{key:"release",value:function(){var e=this;n.g.v(O,"Canceling timeout (release)..."),r.A.getInstance().cancel(v(this,F)),l(v(this,B)).forEach((function(t){return e.removeService(t)})),p(this,w,!1),p(this,U,!1)}},{key:"attachInstance",value:function(e){this.smartLib=e}},{key:"start",value:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0];v(this,M)===L?n.g.e(O,"Exception: nanoCDN discovery is not available for that platform"):v(this,w)?n.g.v(O,"mDNS discovery already started"):(n.g.i(O,"mDNS discovery starting (duringInit:"+i+")..."),v(this,M).start(e.MDNS_NANOCDN_SERVICE_TYPE),p(this,w,!0),!0===i&&(n.g.v(O,"Starting timeout..."),p(this,F,r.A.getInstance().asyncDelay(k.A.getInstance().options.get(u.H.TIMEOUT_NANO_CDN_DISCOVER_RESOLVING),(function(){var e;n.g.v(O,"Ending timeout ("+v(t,B).length+" service(s))..."),0===v(t,B).length&&(null===(e=k.A.getInstance().nanoCDNHostManager)||void 0===e||e.notifyNanoCDNNotFound("discover"))})))),p(this,U,i))}},{key:"stop",value:function(){!0===v(this,w)&&(p(this,w,!1),v(this,M).stop(),n.g.v(O,"Canceling timeout (stop)..."),r.A.getInstance().cancel(v(this,F)),n.g.i(O,"mDNS discovery stopped"))}},{key:"onServiceFound",value:function(e,t){var i,a=this;n.g.d(O,"Found service "+e+" ip:"+t),n.g.v(O,"Canceling timeout (onServiceFound)..."),r.A.getInstance().cancel(v(this,F)),null===(i=k.A.nanoCDNModule)||void 0===i||i.NanoCDNRequestManager.getInstance().retrieveNanoCDNDescr(this.smartLib.nanoCDNHostManager,t,0,v(this,U)).then((function(i){n.g.i(O,"Found nanoCDN with host: '"+t+"'"),i.jsonAccountId.length>0&&n.g.i(O,"Found json account id: '"+i.jsonAccountId+"'"),a.smartLib.nanoCDNHostManager.updateNanoCDNList(i),void 0===a.getService(e,t)&&v(a,B).push(new C(e,t,i)),n.g.i(O,"Registered nanoCDN with host: '"+t+"'")})).catch((function(e){n.g.i(O,e.message)}))}},{key:"onServiceLost",value:function(e,t){var i=this.getService(e,t);void 0===i&&this.removeService(i)}},{key:"getService",value:function(e,t){for(var i=0;i<v(this,B).length;i++){var n=v(this,B)[i];if(n.name===e&&n.ipAddress===t)return n}}},{key:"removeService",value:function(e){n.g.d(O,"Lost service "+e.name+" ip:"+e.ipAddress),this.smartLib.nanoCDNHostManager.removeNanoCDN(e.ipAddress);var t=v(this,B).indexOf(e);-1!==t&&v(this,B).splice(t,1)}}],[{key:"getInstance",value:function(){return S(e,e,H)||y(e,e,H,new e),S(e,e,H)}}]),e}(),H={writable:!0,value:void 0};D(x,"MDNS_NANOCDN_SERVICE_TYPE","_nanocdn._tcp.local.");var V="BpkNanoCDNHostMgr",G=new WeakSet,q=new WeakSet,K=function(){function e(){A(this,e),o(this,q),o(this,G),D(this,"smartLib",void 0),D(this,"state",void 0),D(this,"nanoCDNHostResolvingTasks",void 0),D(this,"nanoCDNHostConfigured",void 0),D(this,"currentNanoCDNDescr",void 0),D(this,"nanoCDNList",void 0),D(this,"nanoCDNReceiver",void 0),D(this,"nanoCDNResolvingRetryDelay",void 0),D(this,"lastHostResolvingDate",void 0),D(this,"nanoCDNHttpsEnabled",void 0),this.state=e.STATE_NONE,this.nanoCDNHostResolvingTasks={},this.nanoCDNHostConfigured="",this.currentNanoCDNDescr=void 0,this.nanoCDNList=[],this.nanoCDNReceiver=void 0,this.nanoCDNResolvingRetryDelay=e.DEFAULT_NANOCDN_RESOLVING_RETRY_DELAY,this.lastHostResolvingDate=0,this.nanoCDNHttpsEnabled=!0}return N(e,[{key:"init",value:function(e,t){this.smartLib=e,null==t&&(t=""),-1!==t.indexOf("*")&&(t=t.replace("*","127.0.0.1,discover")),this.nanoCDNHostConfigured!==t&&(this.nanoCDNHostConfigured=t,this.clearHostResolution(),this.launchHostResolution(!0))}},{key:"registerNanoCDNReceiver",value:function(e){this.nanoCDNReceiver=null==e?void 0:e}},{key:"setNanoCDNHttpsEnabled",value:function(e){this.nanoCDNHttpsEnabled=e}},{key:"setNanoCDNResolvingRetryDelay",value:function(e){var t=parseInt(e,10);for(var i in t>=0&&t<1e3?(this.nanoCDNResolvingRetryDelay=1e3,n.g.w(V,"nanoCDN resolving retry delay has been increased to 1000ms (minimum delay)")):t<=-1?(this.nanoCDNResolvingRetryDelay=-1,n.g.d(V,"nanoCDN resolving retry has been disabled")):this.nanoCDNResolvingRetryDelay=t,this.nanoCDNHostResolvingTasks)if(this.nanoCDNHostResolvingTasks[i].waiting){this.nanoCDNHostResolvingTasks[i].stop();var r=new T(this,i);r.setResolvingRetryDelay(this.nanoCDNResolvingRetryDelay),r.start(!0),this.nanoCDNHostResolvingTasks[i]=r}else this.nanoCDNHostResolvingTasks[i].setResolvingRetryDelay(this.nanoCDNResolvingRetryDelay)}},{key:"launchHostResolution",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(void 0!==this.nanoCDNHostConfigured&&0!==this.nanoCDNHostConfigured.length){this.state=e.STATE_IDLING;for(var i=this.nanoCDNHostConfigured.split(","),n=0;n<i.length;n++){var r=i[n];r===e.NANOCDN_HOST_DISCOVER?(this.state=e.STATE_DISCOVERING,this.lastHostResolvingDate=Date.now(),x.getInstance().start(t)):h.A.getInstance().appInBackground||(this.state=e.STATE_RESOLVING,this.lastHostResolvingDate=Date.now(),this.resolveNanoCDNHost(r,t))}}}},{key:"refreshHostResolution",value:function(){var t=this;if(void 0!==this.nanoCDNHostConfigured&&0!==this.nanoCDNHostConfigured.length&&this.nanoCDNHostConfigured===e.NANOCDN_HOST_DISCOVER){var i=l(this.nanoCDNList),n=[];this.state=e.STATE_DISCOVERING,this.lastHostResolvingDate=Date.now();for(var r=function(e){var r,a=i[e];n.push(null===(r=k.A.nanoCDNModule)||void 0===r?void 0:r.NanoCDNRequestManager.getInstance().requestNanoCDNStatus(t,a.hostIP).then((function(e){200!==e.httpStatus&&t.removeNanoCDN(a.hostIP)})))},a=0;a<i.length;a++)r(a);Promise.all(n).then((function(){x.getInstance().stop(),x.getInstance().start(!1)}))}}},{key:"clearHostResolution",value:function(){for(var t in this.state=e.STATE_IDLING,this.nanoCDNHostResolvingTasks)this.nanoCDNHostResolvingTasks[t].stop(),delete this.nanoCDNHostResolvingTasks[t];x.getInstance().stop(),this.clearNanoCDNList(),this.lastHostResolvingDate=0}},{key:"restartNanoCDNResolving",value:function(){this.clearHostResolution(),this.launchHostResolution(!0)}},{key:"resolveNanoCDNHost",value:function(e,t){if(n.g.i(V,"Initializing nanoCDN resolving with host '"+e+"'"),null!=e&&0!==e.length){void 0!==this.nanoCDNHostResolvingTasks[e]&&(this.nanoCDNHostResolvingTasks[e].stop(),delete this.nanoCDNHostResolvingTasks[e]);var i=new T(this,e);i.setResolvingRetryDelay(this.nanoCDNResolvingRetryDelay),i.start(t),this.nanoCDNHostResolvingTasks[e]=i}else n.g.i(V,"nanoCDN host resolving is not required for '"+e+"'")}},{key:"removeHostResolvingAsyncTask",value:function(e){void 0!==this.nanoCDNHostResolvingTasks[e]&&(this.nanoCDNHostResolvingTasks[e].stop(),delete this.nanoCDNHostResolvingTasks[e])}},{key:"getCurrentNanoCDNDescr",value:function(){return void 0===this.currentNanoCDNDescr&&this.nanoCDNList.length>0?this.nanoCDNList[0]:this.currentNanoCDNDescr}},{key:"setCurrentNanoCDNDescr",value:function(e){this.currentNanoCDNDescr=e}},{key:"getNextNanoCDNDescr",value:function(e){for(var t=0;t<this.nanoCDNList.length-1;t++)if(this.nanoCDNList[t].hostIP===e.hostIP)return this.nanoCDNList[t+1];return this.nanoCDNList[0]}},{key:"updateNanoCDNList",value:function(e){var t=this.nanoCDNList.findIndex((function(t){return t.hostIP===e.hostIP}));if(-1===t){var i;this.nanoCDNList.push(e),t=this.nanoCDNList.length-1;var r=e.jsonAccountId;n.g.i(V,"Calling onNanoCDNFound with host '"+e.hostIP+"' (jsonAccountId:'"+r+"')"+(void 0===this.nanoCDNReceiver?" (no receiver)":"")),"function"==typeof(null===(i=this.nanoCDNReceiver)||void 0===i?void 0:i.onNanoCDNFound)&&this.nanoCDNReceiver.onNanoCDNFound(r,e.hostIP)}else this.nanoCDNList[t]=e;var a=k.A.getInstance().options.get(u.H.REQUEST_NANO_CDN_PRIORITY),s=a===u.H.PRIORITY_APP_CONF?d(this,q,W).call(this,e):d(this,G,j).call(this,e,a===u.H.PRIORITY_LOAD_BALANCING);return s!==t&&(this.nanoCDNList.splice(t,1),this.nanoCDNList.splice(s,0,e),n.g.i(V,"nanoCDN '"+e.hostIP+" position changed ("+(s+1)+" / "+this.nanoCDNList.length+")")),s}},{key:"removeNanoCDN",value:function(e){for(var t,i=!1,r=0;r<this.nanoCDNList.length&&!i;r++)if(this.nanoCDNList[r].hostIP===e){i=!0,this.nanoCDNList.splice(r,1);break}0===this.nanoCDNList.length&&i&&(n.g.i(V,"Calling onNanoCDNLost with host '"+e+"'"+(void 0===this.nanoCDNReceiver?" (no receiver)":"")),"function"==typeof(null===(t=this.nanoCDNReceiver)||void 0===t?void 0:t.onNanoCDNLost)&&this.nanoCDNReceiver.onNanoCDNLost(e));var a=this.getCurrentNanoCDNDescr();void 0!==a&&a.hostIP===e&&(this.currentNanoCDNDescr=void 0)}},{key:"getNanoCDNDescrList",value:function(){return this.nanoCDNList}},{key:"getNanoCDNWithHost",value:function(e){for(var t=0;t<this.nanoCDNList.length;t++)if(this.nanoCDNList[t].hostIP===e)return this.nanoCDNList[t]}},{key:"clearNanoCDNList",value:function(){var e=this;0!==this.nanoCDNList.length&&(this.nanoCDNList.forEach((function(t){var i;n.g.i(V,"Calling onNanoCDNLost with host '"+t.hostIP+"'"+(void 0===e.nanoCDNReceiver?" (no receiver)":"")),"function"==typeof(null===(i=e.nanoCDNReceiver)||void 0===i?void 0:i.onNanoCDNLost)&&e.nanoCDNReceiver.onNanoCDNLost(t.hostIP)})),this.nanoCDNList=[],this.currentNanoCDNDescr=void 0)}},{key:"release",value:function(){this.clearHostResolution(),this.nanoCDNHostConfigured=""}},{key:"onForeground",value:function(){if(Date.now()-this.lastHostResolvingDate>2e3){var t=this.nanoCDNHostConfigured.split(",").length;0!==this.nanoCDNList.length&&this.nanoCDNList.length===t||this.nanoCDNHostConfigured===e.NANOCDN_HOST_DISCOVER||!0!==h.A.getInstance().networkAvailable?this.nanoCDNHostConfigured===e.NANOCDN_HOST_DISCOVER&&!0===h.A.getInstance().networkAvailable&&this.refreshHostResolution():(this.clearHostResolution(),this.launchHostResolution(!1))}else n.g.d(V,"Received event onForeground but ignored")}},{key:"onBackground",value:function(){Object.keys(this.nanoCDNHostResolvingTasks).length>0&&(n.g.d(V,"Host resolving will be resumed when the app will be restored"),this.clearHostResolution()),this.lastHostResolvingDate=0}},{key:"onNetworkAvailable",value:function(e){Date.now()-this.lastHostResolvingDate>2e3?(n.g.d(V,"Received event onNetworkAvailable type:"+e),this.clearHostResolution(),this.launchHostResolution(!1)):n.g.d(V,"Received event onNetworkAvailable but ignored")}},{key:"onNetworkLost",value:function(){n.g.d(V,"Received event onNetworkLost"),this.clearHostResolution()}},{key:"notifyNanoCDNNotFound",value:function(e){var t;n.g.i(V,"Calling onNanoCDNNotFound with host '"+e+"'"+(void 0===this.nanoCDNReceiver?" (no receiver)":"")),"function"==typeof(null===(t=this.nanoCDNReceiver)||void 0===t?void 0:t.onNanoCDNNotFound)&&this.nanoCDNReceiver.onNanoCDNNotFound(e)}}]),e}();function j(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=function(t){return e.ratio>t.ratio},n=this.nanoCDNList.findIndex((function(n){return t?i(n):e.priority===n.priority&&i(n)||e.priority>0&&(0===n.priority||e.priority<n.priority)}));return-1===n?this.nanoCDNList.length-1:n}function W(e){var t=this.nanoCDNHostConfigured.split(","),i=this.nanoCDNList.findIndex((function(i){return t.indexOf(e.hostIP)<t.indexOf(i.hostIP)}));return-1===i?this.nanoCDNList.length-1:i}D(K,"DEFAULT_NANOCDN_RESOLVING_RETRY_DELAY",6e4),D(K,"NANOCDN_HOST_DISCOVER","discover"),D(K,"STATE_NONE",0),D(K,"STATE_IDLING",1),D(K,"STATE_DISCOVERING",2),D(K,"STATE_RESOLVING",3),k.A.nanoCDNModule={NanoCDNRequestManager:R,HostResolvingTask:T,MdnsManager:x,NanoCDNDescr:m,NanoCDNHostManager:K,NanoCDNService:C}},4404:function(e,t,i){i.d(t,{A:function(){return n}});var n=function(){function e(){A(this,e)}return N(e,null,[{key:"hasMethods",value:function(e,t){var i=!0;return void 0!==e&&(t.forEach((function(t){"function"!=typeof e[t]&&(i=!1)})),i)}},{key:"count",value:function(e,t){var i=0;for(var n in e)!0===t(n,e[n])&&i++;return i}}]),e}()}},function(e){return e(e.s=8184)}])},"object"==P(t)&&"object"==P(e)?e.exports=s():(r=[],void 0===(a="function"==typeof(n=s)?n.apply(t,r):n)||(e.exports=a))},5102:function(module,exports,__webpack_require__){var __WEBPACK_AMD_DEFINE_FACTORY__,__WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__,t;function ownKeys(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach((function(t){_defineProperty(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,o=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return s=e.done,e},e:function(e){o=!0,a=e},f:function(){try{s||null==i.return||i.return()}finally{if(o)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function _regeneratorRuntime(){"use strict";_regeneratorRuntime=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function o(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(e){o=function(e,t,i){return e[t]=i}}function d(e,t,i,n){var r=t&&t.prototype instanceof c?t:c,a=Object.create(r.prototype),s=new k(n||[]);return a._invoke=function(e,t,i){var n="suspendedStart";return function(r,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===r)throw a;return D()}for(i.method=r,i.arg=a;;){var s=i.delegate;if(s){var o=E(s,i);if(o){if(o===u)continue;return o}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var d=l(e,t,i);if("normal"===d.type){if(n=i.done?"completed":"suspendedYield",d.arg===u)continue;return{value:d.arg,done:i.done}}"throw"===d.type&&(n="completed",i.method="throw",i.arg=d.arg)}}}(e,i,s),a}function l(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var u={};function c(){}function h(){}function v(){}var p={};o(p,r,(function(){return this}));var f=Object.getPrototypeOf,y=f&&f(f(N([])));y&&y!==t&&i.call(y,r)&&(p=y);var g=v.prototype=c.prototype=Object.create(p);function S(e){["next","throw","return"].forEach((function(t){o(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function n(r,a,s,o){var d=l(e[r],e,a);if("throw"!==d.type){var u=d.arg,c=u.value;return c&&"object"==_typeof(c)&&i.call(c,"__await")?t.resolve(c.__await).then((function(e){n("next",e,s,o)}),(function(e){n("throw",e,s,o)})):t.resolve(c).then((function(e){u.value=e,s(u)}),(function(e){return n("throw",e,s,o)}))}o(d.arg)}var r;this._invoke=function(e,i){function a(){return new t((function(t,r){n(e,i,t,r)}))}return r=r?r.then(a,a):a()}}function E(e,t){var i=e.iterator[t.method];if(void 0===i){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var n=l(i,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,u;var r=n.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function m(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(m,this),this.reset(!0)}function N(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:D}}function D(){return{value:void 0,done:!0}}return h.prototype=v,o(g,"constructor",v),o(v,"constructor",h),h.displayName=o(v,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,o(e,s,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},S(_.prototype),o(_.prototype,a,(function(){return this})),e.AsyncIterator=_,e.async=function(t,i,n,r,a){void 0===a&&(a=Promise);var s=new _(d(t,i,n,r),a);return e.isGeneratorFunction(i)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},S(g),o(g,s,"Generator"),o(g,r,(function(){return this})),o(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var i in e)t.push(i);return t.reverse(),function i(){for(;t.length;){var n=t.pop();if(n in e)return i.value=n,i.done=!1,i}return i.done=!0,i}},e.values=N,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(i,n){return s.type="throw",s.arg=e,t.next=i,n&&(t.method="next",t.arg=void 0),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var o=i.call(a,"catchLoc"),d=i.call(a,"finallyLoc");if(o&&d){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!d)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var s=a?a.completion:{};return s.type=e,s.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),A(i),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var r=n.arg;A(i)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:N(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),u}},e}function asyncGeneratorStep(e,t,i,n,r,a,s){try{var o=e[a](s),d=o.value}catch(e){return void i(e)}o.done?t(d):Promise.resolve(d).then(n,r)}function _asyncToGenerator(e){return function(){var t=this,i=arguments;return new Promise((function(n,r){var a=e.apply(t,i);function s(e){asyncGeneratorStep(a,n,r,s,o,"next",e)}function o(e){asyncGeneratorStep(a,n,r,s,o,"throw",e)}s(void 0)}))}}function _classPrivateFieldInitSpec(e,t,i){_checkPrivateRedeclaration(e,t),t.set(e,i)}function _checkPrivateRedeclaration(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldGet(e,t){return _classApplyDescriptorGet(e,_classExtractFieldDescriptor(e,t,"get"))}function _classPrivateFieldSet(e,t,i){return _classApplyDescriptorSet(e,_classExtractFieldDescriptor(e,t,"set"),i),i}function _classExtractFieldDescriptor(e,t,i){if(!t.has(e))throw new TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function _classStaticPrivateFieldSpecSet(e,t,i,n){return _classCheckPrivateStaticAccess(e,t),_classCheckPrivateStaticFieldDescriptor(i,"set"),_classApplyDescriptorSet(e,i,n),n}function _classApplyDescriptorSet(e,t,i){if(t.set)t.set.call(e,i);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=i}}function _classStaticPrivateFieldSpecGet(e,t,i){return _classCheckPrivateStaticAccess(e,t),_classCheckPrivateStaticFieldDescriptor(i,"get"),_classApplyDescriptorGet(e,i)}function _classCheckPrivateStaticFieldDescriptor(e,t){if(void 0===e)throw new TypeError("attempted to "+t+" private static field before its declaration")}function _classCheckPrivateStaticAccess(e,t){if(e!==t)throw new TypeError("Private static access of wrong provenance")}function _classApplyDescriptorGet(e,t){return t.get?t.get.call(e):t.value}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var i,n=_getPrototypeOf(e);if(t){var r=_getPrototypeOf(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return _possibleConstructorReturn(this,i)}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _defineProperty(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}module=__webpack_require__.nmd(module),"undefined"!=typeof self?self:__webpack_require__.g,t=function(){return function(){"use strict";var __webpack_modules__={8100:function(e,t,i){i.d(t,{A:function(){return T}});var n=i(1262),r=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"log",value:function(e,t,i){var r=n.Em.formatDate();switch(e){case 0:console.log(r+" V/"+t+": "+i);break;case 1:console.log(r+" D/"+t+": "+i);break;case 2:console.log(r+" I/"+t+": "+i);break;case 3:console.log(r+" W/"+t+": "+i);break;case 4:console.log(r+" E/"+t+": "+i);break;default:console.log(r+" ?/"+t+": "+i)}}}]),e}(),a="BpkRequestHandler",s=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"get",value:function(e,t,i,r){var s=new XMLHttpRequest,o=!1,d={};for(var l in s.onreadystatechange=function(){if(s.readyState===s.DONE){var e,t=null===(e=s.getAllResponseHeaders())||void 0===e?void 0:e.trim().split(/[\r\n]+/);null!=t&&t.forEach((function(e){var t=e.split(": "),i=t.shift().toLowerCase();d[i]=t.join(": ")})),s.status>=200&&s.status<300?r({body:s.responseText,headers:d,statusCode:s.status,message:s.statusText,responseURL:s.responseURL}):0!==s.status&&r({body:"",headers:d,statusCode:s.status,message:s.statusText})}},s.onerror=function(){o||(s.status<=0&&n.gD.e(a,"Request ended with error (CORS error, connection error...)"),r({body:"",headers:d,statusCode:s.status,message:s.statusText}))},s.ontimeout=function(){n.gD.d(a,"Request ended with timeout (xhr)"),r({body:"",headers:d,statusCode:s.status,message:s.statusText})},s.open("GET",e,!0),s.timeout=i,this.deleteUnsafeHeaders(t),t)s.setRequestHeader(l,t[l]);s.send(),setTimeout((function(){s.readyState!==s.DONE&&(o=!0,s.abort(),n.gD.d(a,"Request ended with timeout (setTimeout)"),r({body:"",headers:d,statusCode:s.status,message:s.statusText}))}),i+100)}},{key:"post",value:function(e,t,i,r,s){var o=new XMLHttpRequest,d=!1,l={};for(var u in o.onreadystatechange=function(){if(o.readyState===o.DONE){var e,t=null===(e=o.getAllResponseHeaders())||void 0===e?void 0:e.trim().split(/[\r\n]+/);null!=t&&t.forEach((function(e){var t=e.split(": "),i=t.shift().toLowerCase();l[i]=t.join(": ")})),o.status>=200&&o.status<300?s({body:o.responseText,headers:l,statusCode:o.status,message:o.statusText,responseURL:o.responseURL}):0!==o.status&&s({body:"",headers:l,statusCode:o.status,message:o.statusText})}},o.onerror=function(){d||(o.status<=0&&n.gD.e(a,"Request ended with error (CORS error, connection error...)"),s({body:"",headers:l,statusCode:o.status,message:o.statusText}))},o.ontimeout=function(){n.gD.d(a,"Request ended with timeout (xhr)"),s({body:"",headers:l,statusCode:o.status,message:o.statusText})},o.open("POST",e,!0),o.timeout=r,this.deleteUnsafeHeaders(t),t)o.setRequestHeader(u,t[u]);o.send(i),setTimeout((function(){o.readyState!==o.DONE&&(d=!0,o.abort(),n.gD.d(a,"Request ended with timeout (setTimeout)"),s({body:"",headers:l,statusCode:o.status,message:o.statusText}))}),r+100)}},{key:"deleteUnsafeHeaders",value:function(e){"object"!=("undefined"==typeof window?"undefined":_typeof(window))&&"function"!=typeof XMLHttpRequest||(delete e["User-Agent"],delete e["Accept-Charset"],delete e["Accept-Encoding"],delete e["Access-Control-Request-Headers"],delete e["Access-Control-Request-Method"],delete e.Connection,delete e["Content-Length"],delete e.Cookie,delete e.Cookie2,delete e.Date,delete e.DNT,delete e.Expect,delete e.Host,delete e["Keep-Alive"],delete e.Origin,delete e.Referer,delete e.TE,delete e.Trailer,delete e["Transfer-Encoding"],delete e.Upgrade,delete e.Via)}}]),e}(),o=i(4623),d="BpkBrowserAppStateMgr",l=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"initialized",void 0),_defineProperty(this,"listeners",void 0)}return _createClass(e,[{key:"init",value:function(){n.gD.d(d,"Initializing browser app state manager..."),this.initialized||(n.CE.getInstance().setDeviceInfo(this.getOSName(),this.getOSVersion(),this.getDeviceType()),n.CE.getInstance().bind(),this.listeners={},"undefined"!=typeof window&&void 0===window.addEventListener&&n.gD.w(d,"Are you running a React Native app? If yes, please import module '@broadpeak/smartlib-react-native'"),this.registerNetworkObserver(),this.initialized=!0)}},{key:"getOSName",value:function(){return"undefined"!=typeof device&&void 0!==device.platform?device.platform:"undefined"!=typeof window&&void 0!==window.navigator&&void 0!==window.navigator.platform?window.navigator.platform:"NA"}},{key:"getOSVersion",value:function(){var e;"undefined"!=typeof device&&void 0!==device.version?e=device.version:"undefined"!=typeof window&&void 0!==window.navigator&&void 0!==window.navigator.appVersion&&(e=window.navigator.appVersion),void 0===e&&(e="");var t=e.replace(/ /g,"_").replace(/;/g,"_");return t.length>=30?t.substring(0,30):0===t.length?"NA":t}},{key:"getDeviceType",value:function(){return"undefined"!=typeof cast&&void 0!==cast.receiver?"chromecast":"browser"}},{key:"release",value:function(){this.initialized&&(this.unregisterNetworkObserver(),this.initialized=!1)}},{key:"registerNetworkObserver",value:function(){this.listeners[0]=this.onNetworkOnline.bind(this),this.listeners[1]=this.onNetworkOffline.bind(this),"undefined"!=typeof window&&void 0!==window.navigator&&void 0!==window.navigator.onLine&&(!0===window.navigator.onLine?n.CE.getInstance().notifyNetworkAvailable(1):n.CE.getInstance().notifyNetworkLost()),window.addEventListener("online",this.listeners[0]),window.addEventListener("offline",this.listeners[1])}},{key:"unregisterNetworkObserver",value:function(){window.removeEventListener("online",this.listeners[0]),window.removeEventListener("offline",this.listeners[1]),this.listeners[0]=void 0,this.listeners[1]=void 0}},{key:"onNetworkOnline",value:function(){n.CE.getInstance().notifyNetworkAvailable(1)}},{key:"onNetworkOffline",value:function(){n.CE.getInstance().notifyNetworkLost()}},{key:"registerLifecycleEvents",value:function(){this.listeners[2]=this.onMoveToForeground.bind(this),this.listeners[3]=this.onMoveToBackground.bind(this),window.addEventListener("focus",this.listeners[2]),window.addEventListener("blur",this.listeners[3])}},{key:"unregisterLifecycleEvents",value:function(){window.removeEventListener("focus",this.listeners[2]),window.removeEventListener("blur",this.listeners[3]),this.listeners[2]=void 0,this.listeners[3]=void 0}},{key:"onMoveToForeground",value:function(){n.CE.getInstance().notifyForeground()}},{key:"onMoveToBackground",value:function(){n.CE.getInstance().notifyBackground()}}]),e}(),u="BpkWebOSAppStateMgr",c=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"initialized",void 0),_defineProperty(this,"subscriptionHandle",void 0),_defineProperty(this,"webOSVersion",void 0)}return _createClass(e,[{key:"init",value:function(){var e=this;n.gD.d(u,"Initializing webOS app state manager..."),this.initialized||(this.webOSVersion="",webOS.deviceInfo((function(t){e.webOSVersion=t.sdkVersion,void 0!==e.webOSVersion?(n.gD.d(u,"Receiving sdk version through deviceInfo: "+e.webOSVersion),e.registerNetworkObserver(),n.CE.getInstance().setDeviceInfo("webOS",e.webOSVersion,"tv"),n.CE.getInstance().bind()):webOS.service.request("luna://com.webos.service.tv.systemproperty",{method:"getSystemInfo",parameters:{keys:["sdkVersion"]},onSuccess:function(t){void 0!==t.sdkVersion&&(e.webOSVersion=t.sdkVersion,n.gD.d(u,"Receiving sdk version through luna API: "+e.webOSVersion),e.registerNetworkObserver(),n.CE.getInstance().setDeviceInfo("webOS",e.webOSVersion,"tv"),n.CE.getInstance().bind())},onFailure:function(e){n.gD.d(u,"Error while receiving webOS version ("+JSON.stringify(e)+")")}})})),this.initialized=!0)}},{key:"release",value:function(){this.initialized&&(this.unregisterNetworkObserver(),this.initialized=!1)}},{key:"updateNetworkState",value:function(e){var t,i,r="yes"===(null===(t=e.wifi)||void 0===t?void 0:t.onInternet);1==("yes"===(null===(i=e.wired)||void 0===i?void 0:i.onInternet))?n.CE.getInstance().notifyNetworkAvailable(30):!0===r?n.CE.getInstance().notifyNetworkAvailable(10):n.CE.getInstance().notifyNetworkLost()}},{key:"registerNetworkObserver",value:function(){var e=this.webOSVersion.startsWith("3")?"luna://com.webos.service.connectionmanager":"luna://com.palm.connectionmanager",t=this;webOS.service.request(e,{method:"getStatus",onSuccess:function(e){t.updateNetworkState(e)},onFailure:function(e){n.gD.e(u,"Failed to get network state"),n.gD.e(u,"["+e.errorCode+"]: "+e.errorText)}}),this.subscriptionHandle=webOS.service.request(e,{method:"getStatus",parameters:{subscribe:!0},onSuccess:function(e){void 0===e.subscribed||e.subscribed?t.updateNetworkState(e):n.gD.e(u,"Failed to subscribe network state")},onFailure:function(e){n.gD.e(u,"Failed to get network state"),n.gD.e(u,"["+e.errorCode+"]: "+e.errorText)}})}},{key:"unregisterNetworkObserver",value:function(){void 0!==this.subscriptionHandle&&(this.subscriptionHandle.cancel(),this.subscriptionHandle=void 0)}}]),e}(),h="BpkTizenAppStateMgr",v=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"initialized",void 0),_defineProperty(this,"networkInitializer",void 0),_defineProperty(this,"networkInitializerNumber",void 0),_defineProperty(this,"listeners",void 0)}return _createClass(e,[{key:"init",value:function(){n.gD.d(h,"Initializing tizen app state manager..."),this.initialized||(n.CE.getInstance().setDeviceInfo("Tizen",this.getOSVersion(),"tv"),n.CE.getInstance().bind(),this.listeners={},this.startNetworkInitializer(),this.initialized=!0)}},{key:"getOSVersion",value:function(){return"undefined"!=typeof tizen&&void 0!==tizen.systeminfo?tizen.systeminfo.getCapability("http://tizen.org/feature/platform.version"):"NA"}},{key:"release",value:function(){this.initialized&&(this.unregisterNetworkObserver(),this.initialized=!1)}},{key:"onNetworkChange",value:function(e){var t=null;try{t=webapis.network.getActiveConnectionType()}catch(e){n.gD.e(h,"getActiveConnectionType exception ["+e.code+"] message: "+e.message)}if(null!=t){switch(t){case webapis.network.NetworkActiveConnectionType.DISCONNECTED:n.CE.getInstance().notifyNetworkLost();break;case webapis.network.NetworkActiveConnectionType.WIFI:n.CE.getInstance().notifyNetworkAvailable(10);break;case webapis.network.NetworkActiveConnectionType.ETHERNET:n.CE.getInstance().notifyNetworkAvailable(30)}n.gD.d(h,"Active connection type: "+t)}}},{key:"startNetworkInitializer",value:function(){var e=this;void 0!==this.networkInitializer&&clearInterval(this.networkInitializer),this.networkInitializerNumber=0,this.networkInitializer=setInterval((function(){void 0!==webapis.network?(n.gD.d(h,"Network API is now available"),clearInterval(e.networkInitializer),e.networkInitializer=void 0,e.registerNetworkObserver()):e.networkInitializerNumber>30?(n.gD.e(h,"The webapis.js script is required in order to get the network type."),clearInterval(e.networkInitializer),e.networkInitializer=void 0):e.networkInitializerNumber++}),100)}},{key:"registerNetworkObserver",value:function(){void 0===webapis.network&&n.gD.e(h,"The webapis.js script is required in order to get the network type."),this.onNetworkChange(void 0);try{this.listeners[0]=webapis.network.addNetworkStateChangeListener(this.onNetworkChange)}catch(e){n.gD.e(h,"addNetworkStateChangeListener exception ["+e.code+"] name: "+e.name+" message: "+e.message)}}},{key:"unregisterNetworkObserver",value:function(){try{webapis.network.removeNetworkStateChangeListener(this.listeners[0])}catch(e){n.gD.e(h,"removeNetworkStateChangeListener exception ["+e.code+"] name: "+e.name+" message: "+e.message)}this.listeners[0]=void 0}}]),e}(),p=function(e){_inherits(i,e);var t=_createSuper(i);function i(e){var n;return _classCallCheck(this,i),_defineProperty(_assertThisInitialized(n=t.call(this)),"nonceManager",void 0),n.nonceManager=e,n}return _createClass(i,[{key:"getNonce",value:function(){return this.nonceManager.getNonce()}},{key:"sendAdClick",value:function(){"function"==typeof this.nonceManager.sendAdClick&&this.nonceManager.sendAdClick()}},{key:"sendAdImpression",value:function(){"function"==typeof this.nonceManager.sendAdImpression&&this.nonceManager.sendAdImpression()}},{key:"sendPlaybackStart",value:function(){"function"==typeof this.nonceManager.sendPlaybackStart&&this.nonceManager.sendPlaybackStart()}},{key:"sendPlaybackEnd",value:function(){"function"==typeof this.nonceManager.sendPlaybackEnd&&this.nonceManager.sendPlaybackEnd()}},{key:"sendTouch",value:function(e){"function"==typeof this.nonceManager.sendAdTouch&&this.nonceManager.sendAdTouch(e)}}]),i}(n.r2),f="BpkBrowserAdPalManager",y=function(e){_inherits(i,e);var t=_createSuper(i);function i(){var e;return _classCallCheck(this,i),_defineProperty(_assertThisInitialized(e=t.call(this)),"palSDK",void 0),_defineProperty(_assertThisInitialized(e),"consentSettings",void 0),_defineProperty(_assertThisInitialized(e),"nonceLoader",void 0),_defineProperty(_assertThisInitialized(e),"descriptionURL",void 0),_defineProperty(_assertThisInitialized(e),"partnerName",void 0),_defineProperty(_assertThisInitialized(e),"partnerVersion",void 0),_defineProperty(_assertThisInitialized(e),"omidVersion",void 0),_defineProperty(_assertThisInitialized(e),"playerType",void 0),_defineProperty(_assertThisInitialized(e),"playerVersion",void 0),_defineProperty(_assertThisInitialized(e),"ppid",void 0),_defineProperty(_assertThisInitialized(e),"videoPlayerHeight",void 0),_defineProperty(_assertThisInitialized(e),"videoPlayerWidth",void 0),_defineProperty(_assertThisInitialized(e),"willAdAutoPlay",void 0),_defineProperty(_assertThisInitialized(e),"willAdPlayMuted",void 0),e}return _createClass(i,[{key:"loadPALSDK",value:function(){this.palSDK=n.X2.getInstance().palSDK}},{key:"setConsentSettings",value:function(e){this.loadPALSDK(),void 0!==this.palSDK?(this.consentSettings=e,this.nonceLoader=new this.palSDK.pal.NonceLoader(e)):n.gD.e(f,"PAL SDK not attached, please set it through AdManager.getInstance().attachPALSDK(...)")}},{key:"generateAdPalSession",value:function(e){if(void 0===this.nonceLoader||void 0===this.consentSettings)return n.gD.e(f,"Cannot generate a nonce, please set a ConsentSettings through AdManager.getInstance().setConsentSettings(...)"),void e(void 0);var t=new this.palSDK.pal.NonceRequest;void 0!==this.descriptionURL&&(t.descriptionUrl=this.descriptionURL),void 0!==this.partnerName&&(t.omidPartnerName=this.partnerName),void 0!==this.partnerVersion&&(t.omidPartnerVersion=this.partnerVersion),void 0!==this.omidVersion&&(t.omidVersion=this.omidVersion),void 0!==this.playerType&&(t.playerType=this.playerType),void 0!==this.playerVersion&&(t.playerVersion=this.playerVersion),void 0!==this.ppid&&(t.ppid=this.ppid),void 0!==this.videoPlayerHeight&&(t.videoHeight=this.videoPlayerHeight),void 0!==this.videoPlayerWidth&&(t.videoWidth=this.videoPlayerWidth),void 0!==this.willAdAutoPlay&&(t.adWillAutoPlay=this.willAdAutoPlay),void 0!==this.willAdPlayMuted&&(t.adWillPlayMuted=this.willAdPlayMuted),this.nonceLoader.loadNonceManager(t).then((function(t){var i=new p(t);e(i)})).catch((function(t){n.gD.e(f,"Error when generating the nonce"),console.log(t),e(void 0)}))}},{key:"reset",value:function(){}},{key:"release",value:function(){this.consentSettings=void 0,this.nonceLoader=void 0}},{key:"setDescriptionURL",value:function(e){this.descriptionURL=e}},{key:"setOmidPartner",value:function(e,t){this.partnerName=e,this.partnerVersion=t}},{key:"setOmidVersion",value:function(e){this.omidVersion=e}},{key:"setPlayerType",value:function(e){this.playerType=e}},{key:"setPlayerVersion",value:function(e){this.playerVersion=e}},{key:"setPpid",value:function(e){this.ppid=e}},{key:"setVideoPlayerHeight",value:function(e){this.videoPlayerHeight=e}},{key:"setVideoPlayerWidth",value:function(e){this.videoPlayerWidth=e}},{key:"setWillAdAutoPlay",value:function(e){this.willAdAutoPlay=e}},{key:"setWillAdPlayMuted",value:function(e){this.willAdPlayMuted=e}}]),i}(n.GM),g=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"initialized",void 0)}return _createClass(e,[{key:"init",value:function(){n.gD.d("BpkNodeAppStateMgr","Initializing Node.js app state manager..."),this.initialized||(n.CE.getInstance().setDeviceInfo(this.getOSName(),this.getOSVersion(),this.getDeviceType()),n.CE.getInstance().bind(),this.initialized=!0)}},{key:"getOSName",value:function(){return"undefined"!=typeof process?process.platform:"NA"}},{key:"getOSVersion",value:function(){var e=(0,o.t)("os");return void 0!==e?e.release():"NA"}},{key:"getDeviceType",value:function(){return"nodejs"}},{key:"release",value:function(){this.initialized&&(this.initialized=!1)}}]),e}(),S=_createClass((function e(){_classCallCheck(this,e)}));_defineProperty(S,"CLICK","click"),_defineProperty(S,"INVITATION_ACCEPTED","invitationAccept");var _="BpkCoreEngine",E=new WeakMap,m=new WeakMap,A=new WeakMap,k=new WeakMap,N=new WeakMap,D=new WeakMap,P=new WeakMap,R=new WeakMap,b=new WeakMap,I=new WeakMap,T=function(){function e(){_classCallCheck(this,e),_classPrivateFieldInitSpec(this,E,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,m,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,A,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,k,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,N,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,D,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,P,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,R,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,b,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,I,{writable:!0,value:void 0}),_classPrivateFieldSet(this,E,!1),_classPrivateFieldSet(this,m,void 0),_classPrivateFieldSet(this,A,new r)}return _createClass(e,[{key:"init",value:function(){if(!_classPrivateFieldGet(this,E)){_classPrivateFieldSet(this,E,!0);var e=Date.now();this.log(n.$b.VERBOSE,_,"Loading engine..."),this.log(n.$b.VERBOSE,_,"Version: 600c344"),this.registerSystemFunctions(),"object"==("undefined"==typeof window?"undefined":_typeof(window))?this.registerSmartLibFunctions(window):(this.registerSmartLibFunctions(),"undefined"==typeof XMLHttpRequest&&(i.g.XMLHttpRequest=(0,o.t)("xmlhttprequest").XMLHttpRequest)),this.log(n.$b.VERBOSE,_,"Took "+(Date.now()-e)+"ms to load")}}},{key:"registerSystemFunctions",value:function(){this.log(n.$b.VERBOSE,_,"Registering system functions..."),n.gD.getInstance().init(_classPrivateFieldGet(this,A)),n.Je.getInstance().init(new s),n.x6.getInstance().internalAdManager.setPalManager(new y)}},{key:"registerPlayerAdapters",value:function(){void 0!==e.analyticsModule&&(_classPrivateFieldSet(this,k,new e.analyticsModule.PlayerManagerHandler),"object"==("undefined"==typeof window?"undefined":_typeof(window))&&(window.PlayerEventListener=e.analyticsModule.PlayerEventListener,window.GenericPlayerApi=e.analyticsModule.GenericPlayerApi))}},{key:"loadModules",value:function(){"object"==("undefined"==typeof window?"undefined":_typeof(window))?(n.x6.analyticsModule=this.getBrowserModule("analyticsSmartLibModule","Analytics",n.x6.analyticsModule),n.x6.adModule=this.getBrowserModule("adSmartLibModule","Ad",n.x6.adModule),n.x6.nanoCDNModule=this.getBrowserModule("nanocdnSmartLibModule","nanoCDN",n.x6.nanoCDNModule),n.x6.diversityModule=this.getBrowserModule("diversitySmartLibModule","Diversity",n.x6.diversityModule),n.x6.monitoringModule=this.getBrowserModule("monitoringSmartLibModule","Monitoring",n.x6.monitoringModule),e.reactNativeModule=this.getBrowserModule("reactnativeSmartLibModule","React Native",e.reactNativeModule),e.omsdkModule=this.getBrowserModule("omsdkModuleSmartLibModule","OMSDK",e.omsdkModule),void 0!==e.omsdkModule&&(window.OMSDKHandler=e.omsdkModule.OMSDKHandler),e.shakaModule=this.getBrowserModule("shakaSmartLibModule","Shaka Player",e.shakaModule),void 0!==e.shakaModule&&(window.ShakaPlayerAdapter=e.shakaModule.ShakaPlayerAdapter),e.dashjsModule=this.getBrowserModule("dashjsSmartLibModule","Dash.js",e.dashjsModule),void 0!==e.dashjsModule&&(window.DashJsPlayerAdapter=e.dashjsModule.DashJsPlayerAdapter),e.html5Module=this.getBrowserModule("html5SmartLibModule","HTML5 player",e.html5Module),e.avplayModule=this.getBrowserModule("avplaySmartLibModule","AVPlay",e.avplayModule),e.diw387Module=this.getBrowserModule("diw387SmartLibModule","Sagemcom",e.diw387Module),e.hbbtv1Module=this.getBrowserModule("hbbtv1SmartLibModule","HbbTV1",e.hbbtv1Module),e.kalturaModule=this.getBrowserModule("kalturaSmartLibModule","Kaltura",e.kalturaModule),e.connectplayerModule=this.getBrowserModule("connectplayerSmartLibModule","CONNECT Player",e.connectplayerModule),void 0!==e.connectplayerModule&&(window.ConnectPlayerAdapter=e.connectplayerModule.ConnectPlayerAdapter),e.rxplayerModule=this.getBrowserModule("rxplayerSmartLibModule","RxPlayer",e.rxplayerModule),e.hlsjsModule=this.getBrowserModule("hlsjsModule","HLS.js",e.hlsjsModule),e.bitmovinModule=this.getBrowserModule("bitmovinModule","Bitmovin",e.bitmovinModule),e.theoplayerModule=this.getBrowserModule("theoplayerModule","THEOplayer",e.theoplayerModule),void 0!==e.theoplayerModule&&(window.THEOPlayerAdapter=e.theoplayerModule.THEOPlayerAdapter),e.voplayerModule=this.getBrowserModule("voplayerModule","VOPlayer",e.voplayerModule),e.reactnativeconnectplayerModule=this.getBrowserModule("reactnativeconnectplayerModule","React Native CONNECT Player",e.reactnativeconnectplayerModule),e.reactnativetheoplayerModule=this.getBrowserModule("reactnativetheoplayerModule","React Native THEOplayer",e.reactnativetheoplayerModule),e.chromecastModule=this.getBrowserModule("chromecastModule","Chromecast",e.chromecastModule)):!0===e.modular&&(n.x6.analyticsModule=this.getNodeModule("analytics","Analytics"),n.x6.adModule=this.getNodeModule("ad","Ad"),n.x6.nanoCDNModule=this.getNodeModule("nanocdn","nanoCDN"),n.x6.diversityModule=this.getNodeModule("diversity","Diversity"),n.x6.monitoringModule=this.getNodeModule("monitoring","Monitoring"),e.reactNativeModule=this.getNodeModule("reactnative","React Native"),e.omsdkModule=this.getNodeModule("omsdkModule","OMSDK"),e.shakaModule=this.getNodeModule("shaka","Shaka Player"),e.dashjsModule=this.getNodeModule("dashjs","Dash.js"),e.html5Module=this.getNodeModule("html5","HTML5 player"),e.avplayModule=this.getNodeModule("avplay","AVPlay"),e.diw387Module=this.getNodeModule("diw387","Sagemcom"),e.hbbtv1Module=this.getNodeModule("hbbtv1","HbbTV1"),e.kalturaModule=this.getNodeModule("kaltura","Kaltura"),e.connectplayerModule=this.getNodeModule("connectplayer","CONNECT Player"),e.rxplayerModule=this.getNodeModule("rxplayer","RxPlayer"),e.hlsjsModule=this.getNodeModule("hlsjs","HLS.js"),e.bitmovinModule=this.getNodeModule("bitmovin","Bitmovin"),e.theoplayerModule=this.getNodeModule("theoplayer","THEOplayer"),e.voplayerModule=this.getNodeModule("voplayer","VOPlayer"),e.reactnativeconnectplayerModule=this.getNodeModule("reactnativeconnectplayer","React Native CONNECT Player"),e.reactnativetheoplayerModule=this.getNodeModule("reactnativetheoplayer","React Native THEOplayer"),e.chromecastModule=this.getNodeModule("chromecast","Chromecast")),void 0!==_classPrivateFieldGet(this,m)&&void 0!==n.x6.monitoringModule&&(_classPrivateFieldGet(this,m).RealTimeMonitoringManager=n.x6.monitoringModule.RealTimeMonitoringManager)}},{key:"getBrowserModule",value:function(e,t,i){if(void 0!==i)return n.gD.i(_,t+" module loaded (from index)"),i;n.gD.i(_,t+" module not loaded")}},{key:"getNodeModule",value:function(e,t){try{var i=(0,o.C)("@broadpeak/smartlib-"+e);return n.gD.i(_,t+" module loaded"),i}catch(i){try{var r=(0,o.t)("./"+e+".smartlib.browser");return n.gD.i(_,t+" module loaded"),r}catch(e){n.gD.i(_,t+" module not loaded")}}}},{key:"registerSmartLibFunctions",value:function(t){var i,r,a=this;_classPrivateFieldSet(this,m,t);var s=function(){var t,i;"undefined"!=typeof webOS?(void 0===_classPrivateFieldGet(a,P)&&_classPrivateFieldSet(a,P,new c),_classPrivateFieldGet(a,P).init()):"undefined"!=typeof tizen&&"undefined"!=typeof webapis?(void 0===_classPrivateFieldGet(a,R)&&_classPrivateFieldSet(a,R,new v),_classPrivateFieldGet(a,R).init()):"undefined"!=typeof sagem||(void 0!==(null===(t=e.reactNativeModule)||void 0===t?void 0:t.ReactNativeAppStateManager.platform)?(void 0===_classPrivateFieldGet(a,I)&&_classPrivateFieldSet(a,I,new e.reactNativeModule.ReactNativeAppStateManager),_classPrivateFieldGet(a,I).init()):"undefined"!=typeof window?(void 0===_classPrivateFieldGet(a,D)&&_classPrivateFieldSet(a,D,new l),_classPrivateFieldGet(a,D).init()):"undefined"!=typeof process&&"node"===process.release.name&&(void 0===_classPrivateFieldGet(a,b)&&_classPrivateFieldSet(a,b,new g),_classPrivateFieldGet(a,b).init())),a.loadModules(),void 0!==_classPrivateFieldGet(a,I)?_classPrivateFieldSet(a,N,new e.reactNativeModule.ReactNativeCacheHandler(e.reactNativeModule.ReactNativeAppStateManager.asyncCache)):void 0!==e.analyticsModule&&_classPrivateFieldSet(a,N,new e.analyticsModule.CacheHandler),null!==(i=n.x6.analyticsModule)&&void 0!==i&&i.CacheManager.getInstance().init(_classPrivateFieldGet(a,N)),void 0!==e.omsdkModule&&n.x6.adModule.OMSDKManager.getInstance().attachHandler(new e.omsdkModule.OMSDKHandler)};n.x6.prototype.defaultInit=n.x6.prototype.init,n.x6.prototype.init=function(e,t,i,n){s(),this.defaultInit(e,t,i,n)},n.x6.prototype.defaultInitAnalytics=n.x6.prototype.initAnalytics,n.x6.prototype.initAnalytics=function(e){s(),this.defaultInitAnalytics(e)},n.x6.prototype.defaultAttachPlayer=n.x6.prototype.attachPlayer,n.x6.prototype.attachPlayer=function(e,t){var i,r;null===(i=n.x6.analyticsModule)||void 0===i||i.PlayerManager.getInstance().init(_classPrivateFieldGet(a,k));var s=null===(r=_classPrivateFieldGet(a,k))||void 0===r?void 0:r.attachPlayer(e,t);this.defaultAttachPlayer(s)},n.x6.prototype.defaultRelease=n.x6.prototype.release,n.x6.prototype.release=function(){this.defaultRelease(),void 0!==_classPrivateFieldGet(a,D)&&(_classPrivateFieldGet(a,D).release(),_classPrivateFieldSet(a,D,void 0)),void 0!==_classPrivateFieldGet(a,P)&&(_classPrivateFieldGet(a,P).release(),_classPrivateFieldSet(a,P,void 0)),void 0!==_classPrivateFieldGet(a,R)&&(_classPrivateFieldGet(a,R).release(),_classPrivateFieldSet(a,R,void 0)),void 0!==_classPrivateFieldGet(a,b)&&(_classPrivateFieldGet(a,b).release(),_classPrivateFieldSet(a,b,void 0)),void 0!==_classPrivateFieldGet(a,I)&&(_classPrivateFieldGet(a,I).release(),_classPrivateFieldSet(a,I,void 0))},n.fF.prototype.defaultAttachPlayer=n.fF.prototype.attachPlayer,n.fF.prototype.attachPlayer=function(e,t){var i,r;null===(i=n.x6.analyticsModule)||void 0===i||i.PlayerManager.getInstance().init(_classPrivateFieldGet(a,k));var s=null===(r=_classPrivateFieldGet(a,k))||void 0===r?void 0:r.attachPlayer(e,t);this.defaultAttachPlayer(s)},"object"==_typeof(t)&&(t.SmartLib=Object.assign(n.x6.getInstance(),n.x6),t.SmartLib.getInstance=function(){return n.x6.getInstance()},t.LoggerManager=n.gD,t.PlayerEventListener=null===(i=e.analyticsModule)||void 0===i?void 0:i.PlayerEventListener,t.GenericPlayerApi=null===(r=e.analyticsModule)||void 0===r?void 0:r.GenericPlayerApi,t.PlayerApiImp={getVersion:function(){var e,t=null===(e=n.x6.analyticsModule)||void 0===e?void 0:e.PlayerManager.getInstance().getPlayerAdapter();return void 0!==t&&"function"==typeof t.getVersion?t.getVersion():"undefined"},getErrorCode:function(e){var t,i=null===(t=n.x6.analyticsModule)||void 0===t?void 0:t.PlayerManager.getInstance().getPlayerAdapter();return void 0!==i&&"function"==typeof i.getErrorCode?i.getErrorCode(e):n.x6.BPUnspecifiedError}},t.StreamingSession=n.fF,t.StreamingSessionOptions=n.H1,t.StreamingSessionResult=n.Sy,t.AdManager=n.X2,t.AdInteractionType=S)}},{key:"log",value:function(e,t,i){_classPrivateFieldGet(this,A).log(e,t,i)}}],[{key:"getInstance",value:function(){return _classStaticPrivateFieldSpecGet(e,e,C)||_classStaticPrivateFieldSpecSet(e,e,C,new e),_classStaticPrivateFieldSpecGet(e,e,C)}}]),e}(),C={writable:!0,value:void 0};_defineProperty(T,"modular",!0),_defineProperty(T,"analyticsModule",void 0),_defineProperty(T,"reactNativeModule",void 0),_defineProperty(T,"omsdkModule",void 0),_defineProperty(T,"shakaModule",void 0),_defineProperty(T,"dashjsModule",void 0),_defineProperty(T,"html5Module",void 0),_defineProperty(T,"avplayModule",void 0),_defineProperty(T,"diw387Module",void 0),_defineProperty(T,"hbbtv1Module",void 0),_defineProperty(T,"kalturaModule",void 0),_defineProperty(T,"connectplayerModule",void 0),_defineProperty(T,"rxplayerModule",void 0),_defineProperty(T,"hlsjsModule",void 0),_defineProperty(T,"bitmovinModule",void 0),_defineProperty(T,"theoplayerModule",void 0),_defineProperty(T,"voplayerModule",void 0),_defineProperty(T,"reactnativeconnectplayerModule",void 0),_defineProperty(T,"reactnativetheoplayerModule",void 0),_defineProperty(T,"chromecastModule",void 0)},4623:function _(__unused_webpack_module,__nested_webpack_exports__,__nested_webpack_require_67528__){function nativeRequire(lib){return eval("require")(lib)}function nativeMainRequire(lib){return eval("require").main.require(lib)}__nested_webpack_require_67528__.d(__nested_webpack_exports__,{C:function(){return nativeMainRequire},t:function(){return nativeRequire}})},1142:function(e,t,i){i.d(t,{A:function(){return P}});var n=i(8379),r=i(6506),a=i(5866),s=i(5941),o=i(8724),d=i(2765),l=i(7832),u=i(828),c=i(1262),h=i(4943),v="BpkSessionMgr",p=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"sessions",void 0)}return _createClass(e,[{key:"init",value:function(e){this.smartLib=e,this.sessions=[]}},{key:"createStreamingSession",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;this.cleanFinishedSessions();var i=new u.f(this.smartLib,l.H.create(e,t));return this.sessions.push(i),this.sessions.length>10&&c.gD.w(v,"There are "+this.sessions.length+" active sessions. Use session.stopStreamingSession() to free up memory."),i}},{key:"createAnalyticsSession",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;this.cleanFinishedSessions();var i=new P.analyticsModule.AnalyticsSession(this.smartLib,l.H.create(e,t));return this.sessions.push(i),this.sessions.length>10&&c.gD.w(v,"There are "+this.sessions.length+" active sessions. Use session.stopStreamingSession() to free up memory."),i}},{key:"cleanFinishedSessions",value:function(){for(var t=this.sessions.length-1;t>=0;t--){var i,n,r=this.sessions[t];void 0===r.handler&&Date.now()-r.date>e.UNSTARTED_SESSION_MAX_DURATION?(null!==(i=r.playerAdapter)&&void 0!==i&&i.detachPlayer(),this.sessions.splice(t,1)):(!0===(null===(n=r.handler)||void 0===n?void 0:n.stopped)||!0===r.stopped)&&this.sessions.splice(t,1)}}},{key:"createSessionHandler",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=new o.A(this.smartLib,e);return void 0!==e?(t.id=e.id,t.options=e.options,t.playerAdapter=e.playerAdapter):(t.id="main-"+h.A.randomIntFromInterval(10,99),t.options=new l.H),t}},{key:"createProxySessionHandler",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=new d.A(this.smartLib,e);return void 0!==e?(t.id=e.id,t.options=e.options,t.playerAdapter=e.playerAdapter):(t.id="main-"+h.A.randomIntFromInterval(10,99),t.options=new l.H),t}},{key:"release",value:function(){this.cleanFinishedSessions(),this.sessions.forEach((function(e){void 0!==P.analyticsModule&&e instanceof P.analyticsModule.AnalyticsSession?e.stopAnalyticsSession():e.stopStreamingSession()})),this.sessions=[]}}]),e}();_defineProperty(p,"UNSTARTED_SESSION_MAX_DURATION",36e4);var f=i(3473),y=i(5690),g=i(5305),S=i(1105),_="BpkSmartLib",E=new WeakMap,m=new WeakMap,A=new WeakMap,k=new WeakMap,N=new WeakMap,D=new WeakMap,P=function(){function e(){var t=this;_classCallCheck(this,e),_classPrivateFieldInitSpec(this,E,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,m,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,A,{writable:!0,value:void 0}),_defineProperty(this,"options",void 0),_defineProperty(this,"sessionManager",void 0),_defineProperty(this,"nanoCDNHostManager",void 0),_defineProperty(this,"adManager",void 0),_defineProperty(this,"internalAdManager",void 0),_defineProperty(this,"monitoringManager",void 0),_defineProperty(this,"handler",void 0),_defineProperty(this,"nanoCDNReceiver",void 0),_defineProperty(this,"nanoCDNHttpsEnabled",void 0),_classPrivateFieldInitSpec(this,k,{writable:!0,value:function(e){null!=e&&0!==e.length?_classPrivateFieldGet(t,A).analyticsAddress=e:_classPrivateFieldGet(t,A).analyticsAddress=""}}),_classPrivateFieldInitSpec(this,N,{writable:!0,value:function(i){null!=i&&0!==i.length?(void 0===e.nanoCDNModule&&n.g.w(_,"Warning: nanoCDNHost is set while the module is not loaded."),_classPrivateFieldGet(t,A).nanoCDNHost=i.replace(/\s/g,"")):_classPrivateFieldGet(t,A).nanoCDNHost=""}}),_classPrivateFieldInitSpec(this,D,{writable:!0,value:function(e){null!=e&&0!==e.length?_classPrivateFieldGet(t,A).broadpeakDomainNames=e.replace(/\s/g,""):_classPrivateFieldGet(t,A).broadpeakDomainNames=""}}),_classPrivateFieldSet(this,E,!1),_classPrivateFieldSet(this,m,!1),_classPrivateFieldSet(this,A,{analyticsAddress:"",nanoCDNHost:"",broadpeakDomainNames:"",forceTeardown:e.FORCE_TEARDOWN_DEFAULT,uuid:"",userAgent:void 0,deviceType:"",customParameters:{}}),this.options=new l.H,this.sessionManager=new p,this.adManager=new y.X2,this.internalAdManager=new g.A}return _createClass(e,[{key:"init",value:function(t,i,o,d){var l,u,c,h,v,p,y,g;return n.g.i(_,"Init SmartLib..."),n.g.i(_,"Version: "+a.A),_classPrivateFieldGet(this,E)||_classPrivateFieldGet(this,m)?(n.g.e(_,"Error: SmartLib already initialized, please use SmartLib.release() before initializing."),!1):void 0!==d?(n.g.e(_,"Error: this init method has been removed, please use SmartLib.init(analyticsAddress, nanoCDNHost, broadpeakDomainNames) instead."),!1):s.A.typeOrNullParameter(t,"string","Error: analyticsAddress should be a string or null")&&s.A.typeOrNullParameter(i,"string","Error: nanoCDNHost should be a string or null")&&s.A.typeOrNullParameter(o,"string","Error: broadpeakDomainNames should be a string or null")?(_classPrivateFieldGet(this,k).call(this,t),_classPrivateFieldGet(this,N).call(this,i),_classPrivateFieldGet(this,D).call(this,o),this.setForceTeardown(e.FORCE_TEARDOWN_DEFAULT),n.g.i(_,"Parameters:analyticsAddress="+t+", nanoCDNHost="+i+", broadpeakDomainNames="+o),void 0!==e.nanoCDNModule&&void 0===this.nanoCDNHostManager&&(this.nanoCDNHostManager=new e.nanoCDNModule.NanoCDNHostManager,void 0!==this.nanoCDNReceiver&&(this.nanoCDNHostManager.registerNanoCDNReceiver(this.nanoCDNReceiver),this.nanoCDNReceiver=void 0),void 0!==this.nanoCDNHttpsEnabled&&(this.nanoCDNHostManager.setNanoCDNHttpsEnabled(this.nanoCDNHttpsEnabled),this.nanoCDNHttpsEnabled=void 0)),this.sessionManager.init(this),null!==(l=this.nanoCDNHostManager)&&void 0!==l&&l.init(this,i),this.adManager.init(this),this.internalAdManager.init(this),void 0!==e.monitoringModule&&(this.monitoringManager=new e.monitoringModule.RealTimeMonitoringManager(this)),r.A.getInstance().attachInstance(this),null!==(u=e.analyticsModule)&&void 0!==u&&u.CacheManager.getInstance().attachInstance(this),null!==(c=e.analyticsModule)&&void 0!==c&&c.PlayerManager.getInstance().attachInstance(this),null!==(h=e.nanoCDNModule)&&void 0!==h&&h.MdnsManager.getInstance().attachInstance(this),null!==(v=e.diversityModule)&&void 0!==v&&v.DiversityManager.getInstance().attachInstance(this),null!==(p=e.diversityModule)&&void 0!==p&&p.DiversityPluginManager.getInstance().attachInstance(this),null!==(y=e.adModule)&&void 0!==y&&y.OMSDKManager.getInstance().attachInstance(this),f.A.getInstance().init(this),f.A.getInstance().addListener(this.nanoCDNHostManager),null!==(g=e.analyticsModule)&&void 0!==g&&g.CacheManager.getInstance().clean(),_classPrivateFieldSet(this,E,!0),!0):(n.g.e(_,"Error: initialization aborted"),!1)}},{key:"initAnalytics",value:function(t){var i,o,d,u;return n.g.i(_,"Init SmartLib analytics..."),n.g.i(_,"Version: "+a.A),_classPrivateFieldGet(this,m)||_classPrivateFieldGet(this,E)?(n.g.e(_,"Error: SmartLib analytics already initialized, please use SmartLib.release() before initializing."),!1):void 0===e.analyticsModule?(n.g.e(_,"Error: Analytics module not loaded, please import it."),!1):s.A.typeOrNullParameter(t,"string","Error: analyticsAddress should be a string or null")?(_classPrivateFieldGet(this,k).call(this,t),this.options=new l.H,n.g.i(_,"Parameters:analyticsAddress="+t),this.sessionManager.init(this),this.adManager.init(this),this.internalAdManager.init(this),void 0!==e.monitoringModule&&(this.monitoringManager=new e.monitoringModule.RealTimeMonitoringManager(this)),r.A.getInstance().attachInstance(this),null!==(i=e.analyticsModule)&&void 0!==i&&i.CacheManager.getInstance().attachInstance(this),null!==(o=e.analyticsModule)&&void 0!==o&&o.PlayerManager.getInstance().attachInstance(this),null!==(d=e.adModule)&&void 0!==d&&d.OMSDKManager.getInstance().attachInstance(this),f.A.getInstance().init(this),null!==(u=e.analyticsModule)&&void 0!==u&&u.CacheManager.getInstance().clean(),_classPrivateFieldSet(this,m,!0),!0):(n.g.e(_,"Error: initialization aborted"),!1)}},{key:"getURL",value:function(t,i){var r,a,o;return void 0!==this.handler&&(n.g.e(_,"Exception: A session is already running, please use SmartLib.stopStreamingSession(...) before. Stopping the current one...",this.handler.id),this.stopStreamingSession()),n.g.i(_,"getURL with "+t,null===(r=this.handler)||void 0===r?void 0:r.id),void 0===i&&(i=function(){}),s.A.typeParameter(i,"function","Error: callback should be a function")?_classPrivateFieldGet(this,E)?s.A.typeParameter(t,"string","Error: requestedURL should be a string")&&s.A.stringNonEmpty(t,"Error: requestedURL is empty")?(this.handler=this.sessionManager.createSessionHandler(),this.handler.options=l.H.create(this.options),this.handler.playerAdapter=null===(a=e.analyticsModule)||void 0===a?void 0:a.PlayerManager.getInstance().getPlayerAdapter(),this.handler.adSession=this.internalAdManager.adSession,void 0!==this.internalAdManager.adSession&&(this.internalAdManager.adSession.handler=this.handler),this.internalAdManager.adSession=new S.A,this.internalAdManager.adSession.setEventCallbackEnabled(this.internalAdManager.eventCallbackEnabled),this.internalAdManager.adSession.setAdEventsListener(this.internalAdManager.adEventsListener),this.handler.start(t,i)):(i(""),Promise.resolve("")):(n.g.e(_,"Exception: Implementation error, SmartLib.init(...) should be called prior to SmartLib.getURL(...)",null===(o=this.handler)||void 0===o?void 0:o.id),i(""),Promise.resolve("")):Promise.resolve("")}},{key:"getQuery",value:function(){var t;return void 0!==this.handler&&(n.g.e(_,"Exception: A session is already running, please use SmartLib.stopStreamingSession(...) before. Stopping the current one..."),this.stopStreamingSession()),n.g.i(_,"getQuery"),_classPrivateFieldGet(this,E)?(this.handler=this.sessionManager.createProxySessionHandler(),this.handler.options=l.H.create(this.options),this.handler.playerAdapter=null===(t=e.analyticsModule)||void 0===t?void 0:t.PlayerManager.getInstance().getPlayerAdapter(),this.handler.getQuery()):(n.g.e(_,"Exception: Implementation error, SmartLib.init(...) should be called prior to SmartLib.getQuery(...)"),"")}},{key:"startStreamingSession",value:function(e,t){var i,r,a,o;return n.g.i(_,"startStreamingSession with "+e+" and "+t,null===(i=this.handler)||void 0===i?void 0:i.id),_classPrivateFieldGet(this,E)?!(!s.A.typeParameter(e,"string","Error: contentURL should be a string")||!s.A.typeParameter(t,"string","Error: redirectedURL should be a string")||(void 0===this.handler?(n.g.e(_,"Exception: Implementation error, SmartLib.getQuery() should be called prior to SmartLib.startStreamingSession(...)",null===(r=this.handler)||void 0===r?void 0:r.id),1):this.handler instanceof d.A?(this.handler.start(e,t),0):(n.g.e(_,"Exception: Implementation error, SmartLib.startStreamingSession(...) cannot be called with SmartLib.getURL(...)",null===(a=this.handler)||void 0===a?void 0:a.id),1))):(n.g.e(_,"Exception: Implementation error, SmartLib.init(...) should be called prior to SmartLib.startStreamingSession(...)",null===(o=this.handler)||void 0===o?void 0:o.id),!1)}},{key:"stopStreamingSession",value:function(e){var t,i;void 0!==e?n.g.i(_,"Stopping streaming session with statusCode "+e+"...",null===(t=this.handler)||void 0===t?void 0:t.id):n.g.i(_,"Stopping streaming session...",null===(i=this.handler)||void 0===i?void 0:i.id);var r=this.handler;return void 0!==this.handler?(this.handler.stop(e),this.handler=void 0):n.g.d(_,"Stopping streaming session aborted, no session started"),r}},{key:"createStreamingSession",value:function(e){if(_classPrivateFieldGet(this,E)){var t=void 0===e;t&&(e=this.options);var i=this.sessionManager.createStreamingSession(this.options,e);return t?n.g.i(_,"Creating streaming session...",i.id):(n.g.i(_,"Creating streaming session with options...",i.id),e.printUpdates(_,"",i.id)),i}n.g.e(_,"Exception: Implementation error, SmartLib.init(...) should be called prior to SmartLib.createStreamingSession(...)")}},{key:"createAnalyticsSession",value:function(e){if(_classPrivateFieldGet(this,m)){var t=void 0===e;t&&(e=this.options);var i=this.sessionManager.createAnalyticsSession(this.options,e);return t?n.g.i(_,"Creating analytics session...",i.id):(n.g.i(_,"Creating analytics session with options...",i.id),e.printUpdates(_,"",i.id)),i}n.g.e(_,"Exception: Implementation error, SmartLib.initAnalytics(...) should be called prior to SmartLib.createAnalyticsSession(...)")}},{key:"release",value:function(){var t,i,a,s,o,d,u,c,h,v,p,y,g;_classPrivateFieldGet(this,E)&&(n.g.i(_,"Releasing SmartLib..."),this.stopStreamingSession(),this.internalAdManager.release(),this.adManager.release(),null!==(t=this.nanoCDNHostManager)&&void 0!==t&&t.release(),this.sessionManager.release(),null!==(i=this.monitoringManager)&&void 0!==i&&i.release(),f.A.getInstance().release(),null!==(a=e.diversityModule)&&void 0!==a&&a.DiversityManager.getInstance().release(),null!==(s=e.nanoCDNModule)&&void 0!==s&&s.MdnsManager.getInstance().release(),null!==(o=e.analyticsModule)&&void 0!==o&&o.PlayerManager.getInstance().release(),null!==(d=e.analyticsModule)&&void 0!==d&&d.CacheManager.getInstance().release(),r.A.getInstance().release(),null!==(u=e.diversityModule)&&void 0!==u&&u.DiversityManager.getInstance().release(),null!==(c=e.diversityModule)&&void 0!==c&&c.DiversityPluginManager.getInstance().release(),null!==(h=e.adModule)&&void 0!==h&&h.OMSDKManager.getInstance().release(this),_classPrivateFieldSet(this,E,!1)),_classPrivateFieldGet(this,m)&&(n.g.i(_,"Releasing SmartLib analytics..."),this.stopStreamingSession(),this.internalAdManager.release(),this.adManager.release(),this.sessionManager.release(),null!==(v=this.monitoringManager)&&void 0!==v&&v.release(),f.A.getInstance().release(),null!==(p=e.analyticsModule)&&void 0!==p&&p.PlayerManager.getInstance().release(),null!==(y=e.analyticsModule)&&void 0!==y&&y.CacheManager.getInstance().release(),r.A.getInstance().release(),null!==(g=e.adModule)&&void 0!==g&&g.OMSDKManager.getInstance().release(this),_classPrivateFieldSet(this,m,!1)),this.options=new l.H,_classPrivateFieldGet(this,k).call(this,""),_classPrivateFieldGet(this,N).call(this,""),_classPrivateFieldGet(this,D).call(this,"")}},{key:"attachPlayer",value:function(t,i){var r,a,s;void 0!==t?(null!==(r=e.analyticsModule)&&void 0!==r&&r.PlayerManager.getInstance().setPlayerAdapter(t),void 0!==this.handler&&n.g.e(_,"Exception: You cannot attach a player when a session is running, this player will be used for the next session.",this.handler.id)):void 0===e.analyticsModule?n.g.w(_,"Try to attach a player, but analytics module not loaded",null===(a=this.handler)||void 0===a?void 0:a.id):n.g.w(_,"Try to attach a player, but it is not recognized",null===(s=this.handler)||void 0===s?void 0:s.id)}},{key:"detachPlayer",value:function(){var t;return void 0!==this.handler?(n.g.e(_,"Exception: You cannot detach the player when a session is running",this.handler.id),!1):(null!==(t=e.analyticsModule)&&void 0!==t&&t.PlayerManager.getInstance().setPlayerAdapter(void 0),!0)}},{key:"isInitialized",value:function(){return _classPrivateFieldGet(this,E)}},{key:"isAnalyticsInitialized",value:function(){return _classPrivateFieldGet(this,m)}},{key:"getMetricsManager",value:function(){if(void 0!==this.handler)return this.handler.metricsManager}},{key:"getListener",value:function(){var t;return null===(t=e.analyticsModule)||void 0===t?void 0:t.PlayerManager.getInstance().getPlayerAdapter()}},{key:"getVersion",value:function(){return a.A}},{key:"getParameters",value:function(){return _classPrivateFieldGet(this,A)}},{key:"setUUID",value:function(e){s.A.typeParameter(e,"string","Error: uuid should be a string")&&(e.length>36?_classPrivateFieldGet(this,A).uuid=e.substring(0,36):_classPrivateFieldGet(this,A).uuid=e)}},{key:"setUserAgent",value:function(e){s.A.typeOrUndefinedParameter(e,"string","Error: userAgent should be a string")&&(_classPrivateFieldGet(this,A).userAgent=e)}},{key:"setDeviceType",value:function(e){s.A.typeParameter(e,"string","Error: deviceType should be a string")&&(_classPrivateFieldGet(this,A).deviceType=e,f.A.getInstance().deviceType=e)}},{key:"setCustomParameter",value:function(e,t){"string"==typeof e?_classPrivateFieldGet(this,A).customParameters[e]=s.A.toBkAString(String(t)):n.g.e(_,"Error: Only string values are accepted for custom parameters")}},{key:"resetCustomParameters",value:function(){_classPrivateFieldGet(this,A).customParameters={}}},{key:"setForceTeardown",value:function(t){"number"==typeof t?_classPrivateFieldGet(this,A).forceTeardown=t>=1?e.FORCE_TEARDOWN_ENABLED:t<=-1?e.FORCE_TEARDOWN_DISABLED:e.FORCE_TEARDOWN_DEFAULT:n.g.e(_,"Exception: parameter forceTeardown must be SmartLib.FORCE_TEARDOWN_DISABLED, SmartLib.FORCE_TEARDOWN_DEFAULT or SmartLib.FORCE_TEARDOWN_ENABLED")}},{key:"setNanoCDNResolvingRetryDelay",value:function(e){var t;void 0!==this.nanoCDNHostManager?(n.g.v(_,"Updating nanoCDN resolving retry asyncDelay to "+e+"ms"),null===(t=this.nanoCDNHostManager)||void 0===t||t.setNanoCDNResolvingRetryDelay(e)):n.g.e(_,"Exception: Implementation error, SmartLib.init(...) should be called prior to SmartLib.setNanoCDNResolvingRetryDelay(...)")}},{key:"registerNanoCDNReceiver",value:function(e){void 0!==this.nanoCDNHostManager?(this.nanoCDNReceiver=void 0,this.nanoCDNHostManager.registerNanoCDNReceiver(e)):this.nanoCDNReceiver=e}},{key:"setNanoCDNHttpsEnabled",value:function(e){n.g.v(_,"Updating nanoCDN https enabled to "+e),void 0!==this.nanoCDNHostManager?(this.nanoCDNHttpsEnabled=void 0,this.nanoCDNHostManager.setNanoCDNHttpsEnabled(e)):this.nanoCDNHttpsEnabled=e}},{key:"restartNanoCDNResolving",value:function(){var e;null===(e=this.nanoCDNHostManager)||void 0===e||e.restartNanoCDNResolving()}},{key:"setOption",value:function(e,t){n.g.d(_,"Setting global option "+l.H.optionToString(e)+" to "+l.H.valueToString(e,t)),this.options.set(e,t)}}],[{key:"getInstance",value:function(){return _classStaticPrivateFieldSpecGet(e,e,R)||_classStaticPrivateFieldSpecSet(e,e,R,new e),_classStaticPrivateFieldSpecGet(e,e,R)}}]),e}(),R={writable:!0,value:void 0};_defineProperty(P,"FORCE_TEARDOWN_DISABLED",-1),_defineProperty(P,"FORCE_TEARDOWN_DEFAULT",0),_defineProperty(P,"FORCE_TEARDOWN_ENABLED",1),_defineProperty(P,"BPSessionEndsNormally",200),_defineProperty(P,"BPFormatNotSupportedError",3001),_defineProperty(P,"BPDecodingError",3002),_defineProperty(P,"BPNetworkingError",3003),_defineProperty(P,"BPAccessRightError",3004),_defineProperty(P,"BPUnspecifiedError",3005),_defineProperty(P,"analyticsModule",void 0),_defineProperty(P,"adModule",void 0),_defineProperty(P,"nanoCDNModule",void 0),_defineProperty(P,"diversityModule",void 0),_defineProperty(P,"monitoringModule",void 0)},5866:function(e,t){t.A="05.00.06.600c344"},5690:function(e,t,i){i.d(t,{D6:function(){return s},X2:function(){return d},up:function(){return a}});var n=i(1142),r=i(8379),a=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"isAdViewState",value:function(t){switch(t){case e.MINIMIZED:case e.COLLAPSED:case e.NORMAL:case e.EXPANDED:case e.FULLSCREEN:return!0}return!1}}]),e}();_defineProperty(a,"MINIMIZED","minimized"),_defineProperty(a,"COLLAPSED","collapsed"),_defineProperty(a,"NORMAL","normal"),_defineProperty(a,"EXPANDED","expanded"),_defineProperty(a,"FULLSCREEN","fullscreen");var s=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"isAdFriendlyObstructionPurpose",value:function(e){return e>=0&&e<=3}}]),e}();_defineProperty(s,"VIDEO_CONTROLS",0),_defineProperty(s,"CLOSE_AD",1),_defineProperty(s,"NOT_VISIBLE",2),_defineProperty(s,"OTHER",3);var o="BpkAdMgr",d=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"palSDK",void 0)}return _createClass(e,[{key:"init",value:function(e){this.smartLib=e}},{key:"release",value:function(){}},{key:"getVersion",value:function(){return"04.05.00"}},{key:"attachPALSDK",value:function(e){"object"==_typeof(e.pal)?(r.g.d(o,"PAL SDK attached"),this.palSDK=e):r.g.e(o,"Try to attach PAL SDK, but it is not recognized")}},{key:"setConsentSettings",value:function(e){this.smartLib.internalAdManager.setConsentSettings(e)}},{key:"setPalParameters",value:function(e,t,i,n,r,a,s,o,d,l,u){this.smartLib.internalAdManager.setPalParameters(e,t,i,n,r,a,s,o,d,l,u)}},{key:"activateAdvertising",value:function(){this.smartLib.internalAdManager.adSession.activateAdvertising()}},{key:"setAdParameter",value:function(e,t){this.smartLib.internalAdManager.adSession.setAdParameter(e,t)}},{key:"setEventCallbackEnabled",value:function(e){this.smartLib.internalAdManager.eventCallbackEnabled=e,this.smartLib.internalAdManager.adSession.setEventCallbackEnabled(e)}},{key:"setAdEventsListener",value:function(e){this.smartLib.internalAdManager.adEventsListener=e,this.smartLib.internalAdManager.adSession.setAdEventsListener(e)}}],[{key:"getInstance",value:function(){return n.A.getInstance().adManager}}]),e}()},1105:function(e,t,i){i.d(t,{A:function(){return s}});var n=i(8379),r=i(5690),a="BpkAdSession",s=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"handler",void 0),_defineProperty(this,"adEventsListener",void 0),_defineProperty(this,"adDataListener",void 0),_defineProperty(this,"adActivated",void 0),_defineProperty(this,"adParameters",void 0),_defineProperty(this,"eventCallbackEnabled",void 0),_defineProperty(this,"adView",void 0),_defineProperty(this,"adViewState",void 0),_defineProperty(this,"adFriendlyObstructionViews",void 0),_defineProperty(this,"adCustomReference",void 0),_defineProperty(this,"adVerificationData",void 0),_defineProperty(this,"adPalSession",void 0),this.adEventsListener=void 0,this.adActivated=!1,this.adParameters={},this.eventCallbackEnabled=!0,this.adView=void 0,this.adViewState=r.up.NORMAL,this.adFriendlyObstructionViews=[],this.adCustomReference="",this.adVerificationData=[]}return _createClass(e,[{key:"setAdEventsListener",value:function(e){var t;this.adEventsListener=e,n.g.d(a,"Ad events listener "+(void 0!==e?"set":"unset"),null===(t=this.handler)||void 0===t?void 0:t.id)}},{key:"setAdDataListener",value:function(e){var t;this.adDataListener=e,n.g.d(a,"Ad data listener "+(void 0!==e?"set":"unset"),null===(t=this.handler)||void 0===t?void 0:t.id)}},{key:"activateAdvertising",value:function(){var e;!0!==this.adActivated&&(this.adActivated=!0,n.g.d(a,"Ad session activated",null===(e=this.handler)||void 0===e?void 0:e.id))}},{key:"setAdParameter",value:function(e,t){var i;this.activateAdvertising(),this.adParameters[e]=String(t),n.g.d(a,"Adding parameter "+e+"="+t,null===(i=this.handler)||void 0===i?void 0:i.id)}},{key:"setEventCallbackEnabled",value:function(e){this.eventCallbackEnabled=e}},{key:"isAdActivated",value:function(){return!0===this.adActivated}},{key:"getAdAllowedQuery",value:function(){var e=this,t=["ad_allowed","adallowed"],i=["0","false"],n=!0;return Object.keys(this.adParameters).forEach((function(r){t.indexOf(r.toLowerCase())>=0&&i.indexOf(e.adParameters[r].toLowerCase())>=0&&(n=!1)})),n}},{key:"resetAdParameters",value:function(){this.adParameters={}}},{key:"hasAdParameters",value:function(){return Object.keys(this.adParameters).length>0}},{key:"onDashEvent",value:function(e){this.isAdActivated()?this.eventCallbackEnabled?n.g.d(a,"Event received but legacy mode has been deprecated",this.handler.id):n.g.d(a,"Event received but callbacks are disabled",this.handler.id):n.g.d(a,"Event received but ad not enabled (advertising not activated)",this.handler.id)}}]),e}()},5305:function(e,t,i){i.d(t,{A:function(){return l}});var n=i(8379),r=i(1134),a="BpkAdPalSessionRequest",s=function(){function e(t){_classCallCheck(this,e),_defineProperty(this,"active",void 0),_defineProperty(this,"timeout",void 0),_defineProperty(this,"adPalManager",void 0),_defineProperty(this,"adPalSession",void 0),_defineProperty(this,"generating",void 0),_defineProperty(this,"waitNonceResolveCallback",void 0),this.active=!0,this.timeout=!1,this.adPalManager=t,this.generating=!1}return _createClass(e,[{key:"setAdPalSession",value:function(e){this.adPalSession=e,this.active=!1}},{key:"generateAdPalSession",value:function(){var t=this,i=function(){n.g.d(a,"Nonce generation timeout"),t.timeout=!0,void 0!==t.waitNonceResolveCallback&&(t.setAdPalSession(void 0),t.waitNonceResolveCallback(void 0))};this.generating=!0,r.A.getInstance().asyncNonce(e.TIMEOUT_DELAY,(function(e){var i=e.adPalSession;n.g.d(a,"AdPalSession async nonce="+(null==i?void 0:i.getNonce())),t.setAdPalSession(i)}),i,(function(){var r=setTimeout(i,e.TIMEOUT_DELAY);t.adPalManager.generateAdPalSession((function(e){clearTimeout(r),n.g.d(a,"AdPalSession nonce="+(null==e?void 0:e.getNonce())),t.setAdPalSession(e),void 0!==t.waitNonceResolveCallback&&!1===t.timeout?t.waitNonceResolveCallback(e):void 0!==t.waitNonceResolveCallback&&!0===t.timeout&&t.waitNonceResolveCallback(void 0)}))}))}},{key:"getAdPalSession",value:function(){var t=this;return void 0===this.adPalSession&&!0===this.active?new Promise((function(i,s){r.A.getInstance().waitNonce(e.TIMEOUT_DELAY,(function(e){var r=e.adPalSession;n.g.d(a,"AdPalSession nonce="+(null==r?void 0:r.getNonce())),t.setAdPalSession(r),i(t.adPalSession)}),(function(){t.active=!1,t.timeout=!0,t.waitNonceResolveCallback=void 0,i(t.adPalSession)}),(function(){!0===t.generating?t.waitNonceResolveCallback=i:(t.waitNonceResolveCallback=i,t.generateAdPalSession())}))})):new Promise((function(e,i){e(t.adPalSession)}))}}]),e}();_defineProperty(s,"TIMEOUT_DELAY",5e3);var o=i(1105),d="BpkInternalAdMgr",l=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"initialized",void 0),_defineProperty(this,"adPalManager",void 0),_defineProperty(this,"omPartnerName",void 0),_defineProperty(this,"omPartnerVersion",void 0),_defineProperty(this,"firstAdPalSessionRequest",void 0),_defineProperty(this,"palSessionEnabled",!1),_defineProperty(this,"adSession",void 0),_defineProperty(this,"eventCallbackEnabled",void 0),_defineProperty(this,"adEventsListener",void 0),this.initialized=!1,this.palSessionEnabled=!1,this.adSession=new o.A,this.eventCallbackEnabled=!0,this.adEventsListener=void 0}return _createClass(e,[{key:"init",value:function(e){this.smartLib=e,this.initialized||(this.initialized=!0,this.adSession=new o.A,this.eventCallbackEnabled=!0,this.adEventsListener=void 0,this.omPartnerName="Broadpeak_tv",this.omPartnerVersion=e.adManager.getVersion(),n.g.d(d,"Ad manager version: "+this.omPartnerVersion),n.g.d(d,"Ad manager loaded"))}},{key:"setPalManager",value:function(e){n.g.d(d,"Native PAL manager registered"),this.adPalManager=e}},{key:"requestAdPalSession",value:function(){var e=this.firstAdPalSessionRequest;return void 0!==e&&void 0===e.adPalSession&&!0===e.active&&!1===e.timeout?(n.g.d(d,"Using first nonce request..."),this.firstAdPalSessionRequest=void 0,e):(this.firstAdPalSessionRequest=void 0,e=new s(this.adPalManager),this.initialized&&this.palSessionEnabled?(n.g.d(d,"Generating nonce..."),e.generateAdPalSession(),void 0===this.adPalManager&&e.setAdPalSession(void 0)):e.setAdPalSession(void 0),e)}},{key:"release",value:function(){this.initialized=!1,this.palSessionEnabled=!1,this.adSession=new o.A,this.eventCallbackEnabled=!0,this.adEventsListener=void 0,void 0!==this.firstAdPalSessionRequest&&(this.firstAdPalSessionRequest.setAdPalSession(void 0),this.firstAdPalSessionRequest=void 0),void 0!==this.adPalManager&&this.adPalManager.reset()}},{key:"setConsentSettings",value:function(e){var t;null===(t=this.adPalManager)||void 0===t||t.setConsentSettings(e)}},{key:"setPalParameters",value:function(e,t,i,r,a,o,l,u,c,h,v){void 0!==this.adPalManager&&(void 0!==e&&this.adPalManager.setDescriptionURL(e),void 0!==t&&void 0!==i&&this.adPalManager.setOmidPartner(t,i),void 0!==r&&this.adPalManager.setOmidVersion(r),void 0!==a&&this.adPalManager.setPlayerType(a),void 0!==o&&this.adPalManager.setPlayerVersion(o),void 0!==l&&this.adPalManager.setPpid(l),0!==u&&this.adPalManager.setVideoPlayerHeight(u),0!==c&&this.adPalManager.setVideoPlayerWidth(c),this.adPalManager.setWillAdAutoPlay(h),this.adPalManager.setWillAdPlayMuted(v),this.palSessionEnabled||(this.palSessionEnabled=!0,n.g.d(d,"Generating first nonce..."),this.firstAdPalSessionRequest=new s(this.adPalManager),this.firstAdPalSessionRequest.generateAdPalSession()))}}]),e}();_defineProperty(l,"AD_NONCE_QUERY_PARAMETER","paln")},1262:function(e,t,i){i.d(t,{X2:function(){return o.X2},GM:function(){return l},r2:function(){return c},CE:function(){return s.A},Em:function(){return f.A},$b:function(){return a.$},gD:function(){return a.g},Je:function(){return r.A},x6:function(){return n.A},fF:function(){return h.f},H1:function(){return v.H},Sy:function(){return p.S}});var n=i(1142),r=i(6506),a=i(8379),s=(i(1134),i(3473)),o=i(5690),d="BpkAdPalMgr",l=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"setConsentSettings",value:function(e){a.g.e(d,"setConsentSettings not implemented")}},{key:"registerPalManagerCallback",value:function(e){a.g.e(d,"registerPalManagerCallback not implemented")}},{key:"unregisterPalManagerCallback",value:function(){a.g.e(d,"unregisterPalManagerCallback not implemented")}},{key:"generateAdPalSession",value:function(e){a.g.e(d,"generateAdPalSession not implemented")}},{key:"reset",value:function(){a.g.e(d,"reset not implemented")}},{key:"release",value:function(){}},{key:"setValue",value:function(e,t,i){a.g.e(d,"setValue not implemented")}},{key:"setDescriptionURL",value:function(e){this.setValue("setDescriptionURL",e)}},{key:"setOmidPartner",value:function(e,t){this.setValue("setOmidPartner",e,t)}},{key:"setOmidVersion",value:function(e){this.setValue("setOmidVersion",e)}},{key:"setPlayerType",value:function(e){this.setValue("setPlayerType",e)}},{key:"setPlayerVersion",value:function(e){this.setValue("setPlayerVersion",e)}},{key:"setPpid",value:function(e){this.setValue("setPpid",e)}},{key:"setVideoPlayerHeight",value:function(e){this.setValue("setVideoPlayerHeight",e)}},{key:"setVideoPlayerWidth",value:function(e){this.setValue("setVideoPlayerWidth",e)}},{key:"setWillAdAutoPlay",value:function(e){this.setValue("setWillAdAutoPlay",e)}},{key:"setWillAdPlayMuted",value:function(e){this.setValue("setWillAdPlayMuted",e)}}]),e}(),u="BpkAdPalSession",c=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"getNonce",value:function(){return a.g.e(u,"getNonce not implemented"),""}},{key:"sendAdClick",value:function(){a.g.e(u,"sendAdClick not implemented")}},{key:"sendAdImpression",value:function(){a.g.e(u,"sendAdImpression not implemented")}},{key:"sendPlaybackStart",value:function(){a.g.e(u,"sendPlaybackStart not implemented")}},{key:"sendPlaybackEnd",value:function(){a.g.e(u,"sendPlaybackEnd not implemented")}},{key:"sendTouch",value:function(e){a.g.e(u,"sendTouch not implemented")}},{key:"setAdView",value:function(e){}}]),e}(),h=i(828),v=i(7832),p=i(3121),f=i(3445)},7418:function(e,t,i){i.d(t,{E:function(){return d},q:function(){return l}});var n=i(6506),r=i(8379),a=i(1134),s=i(7832),o="BpkKeepAliveMgr",d=function(){function e(t){_classCallCheck(this,e),_defineProperty(this,"handler",void 0),_defineProperty(this,"active",void 0),_defineProperty(this,"delay",void 0),_defineProperty(this,"job",void 0),this.handler=t,this.active=!0,this.delay=t.options.get(s.H.SESSION_KEEPALIVE_FREQUENCY)}return _createClass(e,[{key:"start",value:function(){var e=this;this.job=a.A.getInstance().asyncDelay(this.delay,(function(){e.process()}))}},{key:"callback",value:function(e){r.g.e(o,"callback method should be overridden")}},{key:"process",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.active){this.handler.notifyKeepaliveSessionReportRequested(this.handler.sessionReport),this.handler.sessionReport.keepaliveRequestDate=Date.now();var t={userAgent:this.handler.smartLib.getParameters().userAgent};this.callback(t,e)}}},{key:"next",value:function(){var e=this;this.job=a.A.getInstance().asyncDelay(this.delay,(function(){e.process()}))}},{key:"stop",value:function(){this.active=!1,a.A.getInstance().cancel(this.job)}},{key:"isActive",value:function(){return this.active}}]),e}(),l=function(e){_inherits(i,e);var t=_createSuper(i);function i(e){var n;return _classCallCheck(this,i),_defineProperty(_assertThisInitialized(n=t.call(this,e)),"method",void 0),n.method="POST",r.g.d(o,"Using Broadpeak CDN keepalive manager...",n.handler.id),n}return _createClass(i,[{key:"callback",value:function(e){var t=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];n.A.getInstance().keepalive(this.handler,e,this.method).then((function(e){if(t.method=e.method,e.httpStatus>0&&(e.httpStatus<200||e.httpStatus>=300))r.g.d(o,"Stopping keepalive...",t.handler.id),t.stop();else{var n="";try{n=JSON.parse(e.body.trim())}catch(t){n=e.body.trim()}t.handler.notifyKeepaliveRequestEnded(e.httpStatus,n),!0===i&&t.next()}}))}}]),i}(d)},6506:function(e,t,i){i.d(t,{A:function(){return h}});var n=i(8379),r=i(7964),a=i(5866),s=i(1134),o=i(1262),d=i(5305),l=i(1142),u="BpkRequestMgr",c=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"get",value:function(e,t,i,n){n({body:void 0,headers:void 0,statusCode:-1,message:"",responseURL:""})}},{key:"post",value:function(e,t,i,n,r){r({body:void 0,headers:void 0,statusCode:-1,message:"",responseURL:""})}}]),e}(),h=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"requestHandler",c),_defineProperty(this,"smartLib",void 0)}var t;return _createClass(e,[{key:"init",value:function(e){this.requestHandler=e}},{key:"attachInstance",value:function(e){this.smartLib=e}},{key:"release",value:function(){}},{key:"getHeaders",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.smartLib.getParameters(),t={};return void 0!==e.userAgent&&(t["User-Agent"]=e.userAgent),t}},{key:"getURL",value:(t=_asyncToGenerator(_regeneratorRuntime().mark((function t(i,c,h){var v,p,f,y,g,S,_,E,m,A,k,N,D,P;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(g=h.options,S=void 0!==c.nanoCDNDescr&&!0===g.get(o.H1.REQUEST_NANO_CDN),_=e.isBroadpeakDomainName(i,c.broadpeakDomainNames)&&!0===g.get(o.H1.REQUEST_BROADPEAK_CDN),E=!0===g.get(o.H1.MULTICAST_ONLY)||!0===g.get(o.H1.LEGACY_MULTICAST_ONLY),S?"*"===c.broadpeakDomainNames?n.g.i(u,"Workflow 5.2 (v"+a.A+")",h.id):""===c.broadpeakDomainNames?n.g.i(u,"Workflow 5.4 (v"+a.A+")",h.id):_?n.g.i(u,"Workflow 5.6 => 5.2 (v"+a.A+")",h.id):n.g.i(u,"Workflow 5.6 => 5.4 (v"+a.A+")",h.id):"*"===c.broadpeakDomainNames?n.g.i(u,"Workflow 5.1 (v"+a.A+")",h.id):""===c.broadpeakDomainNames?n.g.i(u,"Workflow 5.3 (v"+a.A+")",h.id):_?n.g.i(u,"Workflow 5.5 => 5.1 (v"+a.A+")",h.id):n.g.i(u,"Workflow 5.5 => 5.3 (v"+a.A+")",h.id),n.g.i(u,"   Parameters",h.id),n.g.i(u,"      analyticsAddress="+c.analyticsAddress,h.id),n.g.i(u,"      nanoCDNHost="+(void 0===c.nanoCDNDescr?"":c.nanoCDNDescr.hostIP),h.id),n.g.i(u,"      broadpeakDomainNames="+c.broadpeakDomainNames,h.id),g.print(u,"   ",h.id),!S){t.next=17;break}if(void 0!==l.A.nanoCDNModule){t.next=4;break}return t.abrupt("return",(n.g.e(u,"Error: detected nanoCDN session but nanoCDNModule not loaded."),o.Sy.error(o.Sy.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN)));case 4:return t.next=6,l.A.nanoCDNModule.NanoCDNRequestManager.getInstance().requestNanoCDNSession(i,c,h);case 6:if(!(m=t.sent).result.isError()){t.next=15;break}if(!_){t.next=12;break}n.g.d(u,"nanoCDN responded an error, requesting the BkM (using nanoCDN: "+m.nanoCDNHostIP+")",h.id),t.next=15;break;case 12:if(!0!==E){t.next=14;break}return t.abrupt("return",m.noFallbackResult);case 14:m.result=o.Sy.success(i);case 15:t.next=19;break;case 17:if(!0!==E){t.next=19;break}return t.abrupt("return",(n.g.e(u,"Error: multicast only option enabled but session is not using nanoCDN."),o.Sy.error(o.Sy.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN)));case 19:if(void 0===(null===(v=m)||void 0===v?void 0:v.nanoCDNHostIP)&&!0===(null===(p=h.adSession)||void 0===p?void 0:p.getAdAllowedQuery())&&(A=this.smartLib.internalAdManager.requestAdPalSession()),t.t0=!E||!S||null!==(f=m)&&void 0!==f&&f.result.isError(),!t.t0){t.next=26;break}return t.next=24,this.requestBkM(i,c,null===(y=m)||void 0===y?void 0:y.nanoCDNHostIP,h);case 24:k=t.sent,t.t0=k.isError();case 26:if(!t.t0){t.next=28;break}return t.abrupt("return",k);case 28:if(void 0===m||m.result.isError()||(void 0!==k&&!0===_&&!1===E?-1!==k.url.indexOf("://"+m.nanoCDNHostIP+":")&&(k.nanoCDNUsed=!0,k.nanoCDNDescr=m.nanoCDNDescr,!0===h.options.get(o.H1.ULTRA_LOW_LATENCY_SUPPORT)&&(k.url=r.A.addOrReplaceParam(k.url,"ulls","1"))):k=m.result),void 0===k||!1!==k.nanoCDNUsed||void 0===h.adSession||!h.adSession.isAdActivated()){t.next=43;break}for(D in N=h.adSession.adParameters)k.url=r.A.addOrReplaceParam(k.url,D,encodeURIComponent(N[D]));if(t.t1=void 0!==A,!t.t1){t.next=41;break}if(t.t2=!0!==A.generating||_,t.t2){t.next=37;break}return t.next=37,s.A.getInstance().asyncDelayPromise(80);case 37:return t.next=39,A.getAdPalSession();case 39:void 0!==(P=t.sent)&&(k.url=r.A.addOrReplaceParam(k.url,d.A.AD_NONCE_QUERY_PARAMETER,P.getNonce()),h.adSession.adPalSession=P,P.setAdView(h.adSession.adView));case 41:return t.next=43,this.requestBkYou(k,c,h,_,P);case 43:return t.abrupt("return",void 0===k?o.Sy.error(o.Sy.RESULT_NO_STREAMING_URL_FOUND):k);case 44:case"end":return t.stop()}}),t,this)}))),function(e,i,n){return t.apply(this,arguments)})},{key:"requestBkM",value:function(t,i,a,s){var d,l=this,c=t,h=s.options,v=e.isBroadpeakDomainName(t,i.broadpeakDomainNames)&&!0===h.get(o.H1.REQUEST_BROADPEAK_CDN)||!0===h.get(o.H1.REQUEST_REDIRECT_THIRD_PARTY_CDN);return!0===(null===(d=s.diversitySession)||void 0===d?void 0:d.activated)&&(c=r.A.addOrReplaceParam(c,"diversity","1")),v?(c=r.A.addOrReplaceParam(c,"response","200"),c=r.A.addOrReplaceParam(c,"bk-ml","1"),"string"==typeof a&&0!==a.length?c=r.A.addOrReplaceParam(c,"nanocdnhost",a):!0===h.get(o.H1.REQUEST_DIVERSITY_DYNAMIC_FAILOVER)?c=r.A.addOrReplaceParam(c,"diversityfailover","1"):!0===h.get(o.H1.REQUEST_DIVERSITY)&&(c=r.A.addOrReplaceParam(c,"diversityhost","127.0.0.1:18080")),!0===h.get(o.H1.SESSION_PRECACHE)&&(c=r.A.addOrReplaceParam(c,"bk-precache","1")),e.isBroadpeakDomainName(t,i.broadpeakDomainNames)?n.g.d(u,"Sending request to the BkM/umbrella: "+c,s.id):n.g.d(u,"Sending request to third party CDN: "+c,s.id),this.getRedirectionLocation(c,e.CDN_REQUEST_TIMEOUT,i,s).then((function(r){return e.isBroadpeakDomainName(t,i.broadpeakDomainNames)?n.g.d(u,"BkM/umbrella responded with status code "+r.httpStatus,s.id):n.g.d(u,"Third party CDN responded with status code "+r.httpStatus,s.id),l.parseCDNTiming(r,s),s.sessionReport.cdnStatusCode=r.httpStatus,r.httpStatus>=200&&r.httpStatus<400?""!==r.redirectedURL?o.Sy.success(r.redirectedURL):o.Sy.error(o.Sy.RESULT_CDN_RESPONSE_UNREADABLE,l.getBkMDetailedErrorMessage(r,s)):r.httpStatus<=0?o.Sy.error(o.Sy.RESULT_CDN_RESPONSE_UNREADABLE):r.httpStatus>=400&&r.httpStatus<500?o.Sy.error(o.Sy.RESULT_REQUESTED_URL_NOT_FOUND_ON_CDN,l.getBkMDetailedErrorMessage(r,s)):r.httpStatus>=500&&r.httpStatus<600?o.Sy.error(o.Sy.RESULT_REQUESTED_URL_CDN_ERROR,l.getBkMDetailedErrorMessage(r,s)):o.Sy.error(o.Sy.RESULT_NO_STREAMING_URL_FOUND,l.getBkMDetailedErrorMessage(r,s))})).then((function(e){return e.isError()&&(s.sessionReport.cdnStatusCode=e.getDetailedErrorCode()),e}))):Promise.resolve(o.Sy.success(c))}},{key:"parseCDNTiming",value:function(t,i){var r=t.headers[e.HEADER_CDN_TIMING];if(void 0!==r&&"string"==typeof r){var a,s=_createForOfIteratorHelper(r.split(";"));try{for(s.s();!(a=s.n()).done;){var o=a.value;if(o.startsWith("bpkhttpserver,")){var d=o;try{var l=parseInt(o.split(",")[1],10);if(!isNaN(l))return n.g.d(u,"bpkhttpserver response timing: "+l+"ms",i.id),void(i.sessionReport.bpkhttpserverTiming=l)}catch(t){}return void n.g.e(u,"BkM/umbrella is providing a bpkhttpserver response timing but it cannot be parsed ("+d+")",i.id)}}}catch(e){s.e(e)}finally{s.f()}}n.g.d(u,"BkM/umbrella is not providing a response timing",i.id)}},{key:"getBkMDetailedErrorMessage",value:function(t,i){if(!0===t.headers.hasOwnProperty(e.HEADER_CDN_DETAILED_ERROR_CODE)){var r=t.headers[e.HEADER_CDN_DETAILED_ERROR_CODE];return n.g.d(u,"Reading detailed error code from header: "+r,i.id),r}return n.g.d(u,"Reading detailed error code from http status: "+t.httpStatus+" "+t.httpMessage,i.id),String(t.httpStatus+" "+t.httpMessage)}},{key:"requestBkYou",value:function(e,t,i,a,s){var d,l,c=void 0!==(null===(d=i.adSession)||void 0===d?void 0:d.adDataListener),h=r.A.addOrReplaceParam(e.url,"bk-ml","1.0");if(!1===i.options.get(o.H1.REQUEST_BKYOU_IF_BROADPEAK_CDN)&&a&&!c)return null!==(l=i.adTrackingManager)&&void 0!==l&&l.initBkYouSession(h,void 0,{},s,null==s?void 0:s.getNonce()),e;var v=r.A.addOrReplaceParam(e.url,"bk-ml",c?"2.0":"1.0");return this.adTracking(i,t,v,!1).then((function(t){var o,d,l;if(!(t.httpStatus>=200&&t.httpStatus<300))return n.g.d(u,"BkYou response unreadable (status code), falling back to legacy mode",i.id),null!==(o=i.adTrackingManager)&&void 0!==o&&o.notifyAdDataListener(!0),e;!a&&t.responseURL&&(n.g.d(u,"Using BkYou redirection: "+t.responseURL,i.id),e.url=t.responseURL);try{l=JSON.parse(t.content)}catch(t){var c;return n.g.d(u,"BkYou file unreadable (parsing), falling back to legacy mode",i.id),null!==(c=i.adTrackingManager)&&void 0!==c&&c.notifyAdDataListener(!0),e}var v=l.sessiontoken;if(void 0===v)return e;n.g.d(u,"BkYou session token: "+v,i.id);var p=l.playbackqueries;return Array.isArray(p)&&p.forEach((function(t){if(-1!==t.indexOf("=")){var i=t.split("=");e.url=r.A.addOrReplaceParam(e.url,i[0],i[1]),h=r.A.addOrReplaceParam(h,i[0],i[1])}})),null===(d=i.adTrackingManager)||void 0===d||d.initBkYouSession(h,v,l,s,null==s?void 0:s.getNonce()),e}))}},{key:"getRedirectionLocation",value:function(e,t,i,a){var o=this;return new Promise((function(d,l){var c=o.getHeaders(i);c.Connection="close";var h=s.A.getInstance().asyncGet(e,c,t,(function(t){var i=0,s="";void 0!==t.statusCode&&(i=parseInt(t.statusCode,10)),void 0!==t.headers&&void 0!==t.headers.location?s=t.headers.location:i>=200&&i<400&&n.g.e(u,'Location header cannot be read, please ensure "Access-Control-Expose-Headers: Location" is configured.',a.id),r.A.isValidUrl(s)&&r.A.isRelativeUrl(s)&&(n.g.i(u,"Redirected URL is relative, patching to absolute URL",a.id),s=r.A.patchRelativeUrl(s,e)),d({httpStatus:i,httpMessage:t.message,headers:t.headers,redirectedURL:s})}));a.jobs.push(h)}))}},{key:"requestSession",value:function(t,i,r,a){var d=this,l=i.sessionReport,c=l.redirectedURL,h=c.indexOf("?");h>0&&(c=c.substring(0,h)),c.endsWith("/")||(c+="/"),c+=t;var v=i.options.get(o.H1.SESSION_REPORTING_MODE),p=v===o.H1.SESSION_REPORTING_MODE_DEFAULT||v===o.H1.SESSION_REPORTING_MODE_KEEPALIVE_TEARDOWN;return!0===p?(c+="?",void 0!==l.timeline&&l.gdpr!==o.H1.GDPR_DELETE&&(c+=e.TEARDOWN_TIMELINE_QUERY+l.timeline.data().base64()+"&"),c+=e.TEARDOWN_METRICS_QUERY+l.toQuery(),!0===i.isPrecached()&&(c+="&precache="+(!0===i.sessionReport.precacheEnded?0:1)),n.g.i(u,"Sending session request using method "+a+" with metrics to "+c,i.id)):n.g.i(u,"Sending session request using method "+a+" without metrics to "+c,i.id),new Promise((function(o,h){var v=d.getHeaders(r),f=function(e){n.g.i(u,"Send session request ends with status code: "+e.statusCode,i.id);var s="";void 0!==e.body&&(s=e.body),"POST"===a&&(e.statusCode<200||e.statusCode>=300)?d.requestSession(t,i,r,"GET").then((function(e){o({httpStatus:e.httpStatus,body:e.body,method:"GET"})})):o({httpStatus:e.statusCode,body:s,method:a})};if("GET"===a)s.A.getInstance().asyncGet(c,v,e.BKS_REQUEST_TIMEOUT,f);else if("POST"===a){v["Content-Type"]="application/json";var y="";!0===p&&(y=JSON.stringify({Player:l.toEndSessionJSON()})),n.g.v(u,"Executing POST request with body: "+y),s.A.getInstance().asyncPost(c,v,y,e.BKS_REQUEST_TIMEOUT,f)}}))}},{key:"keepalive",value:function(t,i,n){return this.requestSession(e.KEEPALIVE_PATH,t,i,n)}},{key:"teardown",value:function(t,i,n){var r=e.TEARDOWN_PATH+t.sessionReport.statusCode;return this.requestSession(r,t,i,n)}},{key:"adEvent",value:function(e,t){var i=this.getHeaders(),r=e.options.get(o.H1.USERAGENT_AD_EVENT);void 0!==r&&(i["User-Agent"]=r),s.A.getInstance().asyncGet(t,i,2e4,(function(t){n.g.i(u,"Ad event request ends with status code: "+t.statusCode,e.id)}))}},{key:"adTracking",value:function(t,i,r){var a=this,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return n.g.d(u,"Sending request to the BkYou (async:"+o+"): "+r,t.id),new Promise((function(i,d){var l=a.getHeaders();if(!1===o){var c=s.A.getInstance().asyncGet(r,l,e.BKS_REQUEST_TIMEOUT,(function(e){n.g.d(u,"BkYou responded with status code "+e.statusCode,t.id),i({httpStatus:e.statusCode,content:e.body,responseURL:e.responseURL})}));t.jobs.push(c)}else s.A.getInstance().asyncGet(r,l,e.BKS_REQUEST_TIMEOUT,(function(e){n.g.d(u,"BkYou responded with status code "+e.statusCode,t.id),i({httpStatus:e.statusCode,content:e.body,responseURL:e.responseURL})}))}))}}],[{key:"getInstance",value:function(){return _classStaticPrivateFieldSpecGet(e,e,v)||_classStaticPrivateFieldSpecSet(e,e,v,new e),_classStaticPrivateFieldSpecGet(e,e,v)}},{key:"isBroadpeakDomainName",value:function(e,t){if(null===t||""===t)return!1;if("*"===t)return!0;var i=r.A.parseURL(e);return null!==i&&null!==i.hostname&&t.split(",").indexOf(i.hostname)>=0}}]),e}();_defineProperty(h,"TEARDOWN_PATH","teardown/"),_defineProperty(h,"TEARDOWN_METRICS_QUERY","metrics="),_defineProperty(h,"TEARDOWN_TIMELINE_QUERY","metricsTL="),_defineProperty(h,"KEEPALIVE_PATH","keepalive"),_defineProperty(h,"QUERY_PARAMETER_SESSION_ID","bk-session_id"),_defineProperty(h,"QUERY_PARAMETER_SESSION_CREATED","bk-session"),_defineProperty(h,"HEADER_CDN_DETAILED_ERROR_CODE","x-bpk-error"),_defineProperty(h,"HEADER_CDN_TIMING","x-bpk-timing"),_defineProperty(h,"CDN_REQUEST_TIMEOUT",2e4),_defineProperty(h,"BKS_REQUEST_TIMEOUT",5e3),_defineProperty(h,"KEEPALIVE_REQUEST_INTERVAL",5e3);var v={writable:!0,value:void 0}},3473:function(e,t,i){i.d(t,{A:function(){return a}});var n=i(1262),r="BpkAppStateMgr",a=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"listeners",void 0),_defineProperty(this,"bound",void 0),_defineProperty(this,"networkAvailable",void 0),_defineProperty(this,"appInBackground",void 0),_defineProperty(this,"networkType",99),_defineProperty(this,"mute",0),_defineProperty(this,"osName",""),_defineProperty(this,"osVersion",""),_defineProperty(this,"deviceType",""),this.bound=!1,this.release()}return _createClass(e,[{key:"init",value:function(e){n.gD.d(r,"Initializing core app state manager..."),this.smartLib=e,!1===this.bound?(this.networkAvailable=!0,this.appInBackground=!1):n.gD.d(r,"Core app state manager is bound to the target, skipping default values..."),this.release()}},{key:"release",value:function(){this.listeners=[]}},{key:"setDeviceInfo",value:function(e,t,i){this.osName=e,this.osVersion=t,this.deviceType=i}},{key:"bind",value:function(){n.gD.i(r,"Device:osName="+this.osName+", osVersion="+this.osVersion),n.gD.i(r,"Device:type="+this.deviceType),this.bound=!0}},{key:"isConnectedToWifi",value:function(){return this.networkType>=10&&this.networkType<=12}},{key:"addListener",value:function(e){void 0===e||this.listeners.includes(e)||this.listeners.push(e)}},{key:"removeListener",value:function(e){var t=this.listeners.indexOf(e);-1!==t&&this.listeners.splice(t,1)}},{key:"notifyEvent",value:function(e,t,i,n){"function"==typeof e[t]&&e[t](i,n)}},{key:"notifyForeground",value:function(){var e=this;n.gD.d(r,"App is in foreground"),this.appInBackground=!1,this.listeners.forEach((function(t){e.notifyEvent(t,"onForeground")}))}},{key:"notifyBackground",value:function(){var e=this;n.gD.d(r,"App is in background"),this.appInBackground=!0,this.listeners.forEach((function(t){e.notifyEvent(t,"onBackground")}))}},{key:"notifyNetworkAvailable",value:function(e){var t=this;void 0===e?(n.gD.d(r,"Network is available (networkType:"+e+")"),this.networkAvailable=!0,this.listeners.forEach((function(e){t.notifyEvent(e,"onNetworkAvailable",99)}))):this.networkType!==e&&(n.gD.d(r,"Network is available (networkType:"+e+")"),this.networkAvailable=!0,this.networkType=e,this.listeners.forEach((function(i){t.notifyEvent(i,"onNetworkAvailable",e)})))}},{key:"notifyNetworkLost",value:function(){var e=this;!1!==this.networkAvailable&&(n.gD.d(r,"Network is unavailable"),this.networkAvailable=!1,this.networkType=0,this.listeners.forEach((function(t){e.notifyEvent(t,"onNetworkLost")})))}},{key:"notifyMute",value:function(){var e=this;n.gD.d(r,"Volume is muted"),this.mute=1,this.listeners.forEach((function(t){e.notifyEvent(t,"onMute")}))}},{key:"notifyUnmute",value:function(){var e=this;n.gD.d(r,"Volume is unmuted"),this.mute=0,this.listeners.forEach((function(t){e.notifyEvent(t,"onUnmute")}))}}],[{key:"getInstance",value:function(){return _classStaticPrivateFieldSpecGet(e,e,s)||_classStaticPrivateFieldSpecSet(e,e,s,new e),_classStaticPrivateFieldSpecGet(e,e,s)}}]),e}(),s={writable:!0,value:void 0}},1134:function(e,t,i){i.d(t,{A:function(){return o}});var n=i(4943),r=i(1262),a=i(6506),s="BpkJobMgr",o=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"worker",void 0),_defineProperty(this,"jobs",void 0),this.jobs={}}return _createClass(e,[{key:"async",value:function(e,t,i,n){return this.start(e,0,-1,t,i,n)}},{key:"asyncGet",value:function(e,t,i,n){return this.start("get",0,-1,(function(){a.A.getInstance().requestHandler.get(e,t,i,n)}),(function(){return{url:e,headers:t,timeout:i}}),n)}},{key:"asyncPost",value:function(e,t,i,n,r){return this.start("post",0,-1,(function(){a.A.getInstance().requestHandler.post(e,t,i,n,r)}),(function(){return{url:e,headers:t,body:i,timeout:n}}),r)}},{key:"asyncDelay",value:function(e,t){var i,n=this.start("delay",e,-1,(function(t){i=setTimeout(t,e)}),void 0,t);return n.task=i,n}},{key:"asyncDelayPromise",value:function(t){return new Promise((function(i,n){e.getInstance().asyncDelay(t,(function(){i()}))}))}},{key:"asyncNonce",value:function(e,t,i){var n,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;void 0===a&&(a=function(){r.gD.d(s,"ios async nonce"),n=setTimeout(i,e)});var o=this.start("waitNonce",0,e,a,void 0,t,i,!0);return o.task=n,o}},{key:"waitNonce",value:function(e,t,i){var n,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;void 0===a&&(a=function(){r.gD.d(s,"ios wait nonce"),n=setTimeout(i,e)});var o=this.start("waitNonce",0,e,a,void 0,t,i,!1);return o.task=n,o}},{key:"start",value:function(e,t,i,n,a,o){var d=arguments.length>6&&void 0!==arguments[6]?arguments[6]:function(){},l=!(arguments.length>7&&void 0!==arguments[7])||arguments[7];if(void 0===this.worker&&("function"==typeof CoreWorker?this.worker=new CoreWorker:"object"==("undefined"==typeof CoreWorker?"undefined":_typeof(CoreWorker))&&(this.worker=CoreWorker)),void 0!==this.worker){var u=this.createJob();return u.active=!0,u.command=e,u.startDelay=t,u.timeoutDelay=i,u.requestInfo=a,u.result=o,u.timeout=d,u.async=l,r.gD.v(s,'Starting job "'+e+'" (id: '+u.id+", async: "+u.async+")..."),l?this.worker.async(u.id,u.command,u.startDelay,u.timeoutDelay):this.worker.sync(u.id,u.command,u.startDelay,u.timeoutDelay),u}return n(o),{command:e}}},{key:"interrupt",value:function(e){void 0!==e&&void 0!==this.worker&&(r.gD.v(s,'Interrupting job "'+e.command+'"...'),this.worker.interrupt(e.id))}},{key:"cancel",value:function(e){void 0!==e&&(void 0!==this.worker&&(r.gD.v(s,'Cancelling job "'+e.command+'"...'),e.active=!1,this.worker.cancel(e.id),delete this.jobs[e.id]),void 0!==e.task&&clearTimeout(e.task))}},{key:"requestInfo",value:function(e){var t=this.jobs[e];return void 0!==t&&"function"==typeof t.requestInfo?t.requestInfo():{}}},{key:"result",value:function(e,t){var i=this.jobs[e];void 0!==i&&"function"==typeof i.result&&!0===i.active&&(i.active=!1,i.result(t)),delete this.jobs[e]}},{key:"timeout",value:function(e){var t=this.jobs[e];void 0!==t&&"function"==typeof t.timeout&&!0===t.active&&(r.gD.v(s,'Job "'+t.command+'" timeout...'),t.active=!1,t.timeout()),delete this.jobs[e]}},{key:"createJob",value:function(){for(var e=n.A.randomIntFromInterval(1e6,9999999);void 0!==this.jobs[e];)e=n.A.randomIntFromInterval(1e6,9999999);return this.jobs[e]={id:e},this.jobs[e]}}],[{key:"getInstance",value:function(){return _classStaticPrivateFieldSpecGet(e,e,d)||_classStaticPrivateFieldSpecSet(e,e,d,new e),_classStaticPrivateFieldSpecGet(e,e,d)}}]),e}(),d={writable:!0,value:void 0}},2765:function(e,t,i){i.d(t,{A:function(){return d}});var n=i(8724),r=i(1262),a=i(5866),s=i(1142),o="BpkProxySessionHandler",d=function(e){_inherits(i,e);var t=_createSuper(i);function i(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return _classCallCheck(this,i),_defineProperty(_assertThisInitialized(n=t.call(this,e,r)),"startCalled",void 0),n.startCalled=!1,n}return _createClass(i,[{key:"getQuery",value:function(){var e,t=this,i=null===(e=this.smartLib.nanoCDNHostManager)||void 0===e?void 0:e.getCurrentNanoCDNDescr(),n=void 0!==i?"nanoCDN host: "+i.hostIP:"No nanoCDN found";r.gD.i(o,n+" ("+a.A+")",this.id),this.options.print(o,"",this.id),this.initPlayerAdapter(),this.initManagers(),this.notifyStart();var d="bk-ml=1";return void 0!==i?s.A.nanoCDNModule.NanoCDNRequestManager.getInstance().getQuery(i,this).then((function(e){return d+=e,r.gD.i(o,"getQuery result : "+d,t.id),d})):(r.gD.i(o,"getQuery result : "+d,this.id),Promise.resolve(d))}},{key:"start",value:function(e,t){if(this.startCalled)r.gD.e(o,"Implementation error: SmartLib.startStreamingSession(...) already called.",this.id);else{r.gD.i(o,"Starting streaming session...",this.id),this.startCalled=!0,this.sessionReport.startSessionDate=Date.now(),this.sessionReport.requestedURL=e,this.sessionReport.redirectedURL=t,this.sessionReport.precached=this.isPrecached(),this.sessionResult=r.Sy.success(t);var i=this.smartLib.getParameters(),n={broadpeakDomainNames:i.broadpeakDomainNames,forceTeardown:i.forceTeardown};this.handleRedirectedURL(n)}}}]),i}(n.A)},8724:function(e,t,i){i.d(t,{A:function(){return S}});var n=i(1142),r=i(8379),a=i(6506),s=i(7964),o=i(4943),d=i(5866),l=i(5305),u=i(5941),c=i(7832),h=i(3121),v=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"requestedURL",void 0),_defineProperty(this,"redirectedURL",void 0),_defineProperty(this,"sessionRunning",void 0),_defineProperty(this,"sessionCreated",void 0),_defineProperty(this,"sessionId",void 0),_defineProperty(this,"teardownActivated",void 0),_defineProperty(this,"smartLibParameters",void 0),_defineProperty(this,"customParameters",void 0),_defineProperty(this,"playerName",void 0),_defineProperty(this,"playerVersion",void 0),_defineProperty(this,"osName",void 0),_defineProperty(this,"osVersion",void 0),_defineProperty(this,"deviceType",void 0),_defineProperty(this,"smartLibVersion",void 0),_defineProperty(this,"networkTypeList",void 0),_defineProperty(this,"nanoCDNStatus",void 0),_defineProperty(this,"statusCode",void 0),_defineProperty(this,"cdnStatusCode",void 0),_defineProperty(this,"nanoCDNStatusCode",void 0),_defineProperty(this,"nanoCDNErrorCode",void 0),_defineProperty(this,"bpkhttpserverTiming",void 0),_defineProperty(this,"playerErrorCode",void 0),_defineProperty(this,"metrics",void 0),_defineProperty(this,"adMetrics",void 0),_defineProperty(this,"timeline",void 0),_defineProperty(this,"diversity",void 0),_defineProperty(this,"startSessionDate",void 0),_defineProperty(this,"endSessionDate",void 0),_defineProperty(this,"keepaliveRequestDate",void 0),_defineProperty(this,"teardownRequestDate",void 0),_defineProperty(this,"endSessionRequestDate",void 0),_defineProperty(this,"precached",void 0),_defineProperty(this,"precacheEnded",void 0),_defineProperty(this,"precacheDuration",void 0),this.requestedURL="",this.redirectedURL="",this.sessionRunning=!1,this.sessionCreated=!1,this.sessionId="",this.teardownActivated=!1,this.smartLibParameters={},this.customParameters={},this.playerName="",this.playerVersion="",this.osName="",this.osVersion="",this.deviceType="",this.smartLibVersion=d.A,this.networkTypeList=[],this.nanoCDNStatus=S.NANOCDN_STATUS_UNAVAILABLE,this.statusCode=n.A.BPSessionEndsNormally,this.cdnStatusCode=-1,this.nanoCDNStatusCode=-1,this.nanoCDNErrorCode=h.S.RESULT_NO_ERROR,this.bpkhttpserverTiming=-1,this.playerErrorCode="",this.gdpr=c.H.GDPR_CLEAR,void 0!==n.A.analyticsModule&&(this.timeline=new n.A.analyticsModule.SessionTrackerTimeline),this.startSessionDate=0,this.endSessionDate=0,this.keepaliveRequestDate=0,this.teardownRequestDate=0,this.endSessionRequestDate=0,this.precached=!1,this.precacheEnded=!1,this.precacheDuration=0}return _createClass(e,[{key:"toQuery",value:function(){var e,t=""===this.playerName?"Generic":this.playerName,i=this.customParameters.pre_startup_time;if(this.gdpr===c.H.GDPR_DELETE)return"+++++++++++++++++++++++++++++++++++++++1";if(e=void 0===this.metrics?"-1+"+encodeURIComponent(t)+"+"+encodeURIComponent(this.playerVersion)+"+"+encodeURIComponent(this.osName)+"+"+encodeURIComponent(this.osVersion)+"+"+(""!==this.smartLibParameters.deviceType?encodeURIComponent(this.smartLibParameters.deviceType):encodeURIComponent(this.deviceType))+"+-1+-1+-1++-1+-1+-1+-1+-1+-1+-1+-1+-1+-1+-1++":this.metrics.redirectionTime+"+"+encodeURIComponent(t)+"+"+encodeURIComponent(this.playerVersion)+"+"+encodeURIComponent(this.osName)+"+"+encodeURIComponent(this.osVersion)+"+"+(""!==this.smartLibParameters.deviceType?encodeURIComponent(this.smartLibParameters.deviceType):encodeURIComponent(this.deviceType))+"+"+this.metrics.startupTime+"+"+this.metrics.completion+"+"+Math.round(this.metrics.playbackDuration/1e3)+"+"+this.metrics.playbackType+"+"+this.metrics.stallsNumber+"+"+this.metrics.maxStallDuration+"+"+this.metrics.totalStallsDuration+"+"+this.metrics.rebufferingsNumber+"+"+this.metrics.maxRebufferingDuration+"+"+this.metrics.totalRebufferingDuration+"+"+Math.round(this.metrics.maxBitrate)+"+"+Math.round(this.metrics.minBitrate)+"+"+Math.round(this.metrics.averageBitrate)+"+"+this.metrics.layerSwitchesNumber+"+"+Math.round(this.metrics.sessionDuration/1e3)+"+"+this.getTimeSpentPerLayerQuery()+"+",e+=encodeURIComponent(this.smartLibParameters.uuid)+"+"+this.nanoCDNStatus+"+"+(void 0!==i?encodeURIComponent(i):"")+"+"+encodeURIComponent(this.smartLibVersion)+"+",void 0!==n.A.adModule&&void 0!==this.adMetrics&&this.adMetrics.length>0){var r=n.A.adModule.AdMetrics.merge(this.adMetrics);e+=(r.adSkippable?1:0)+"+"+(r.adSkipped?1:0)+"+"+(r.adProgress>=0?r.adProgress/25:r.adProgress)+"+"+Math.round(r.adDuration/1e3)+"+"+r.stallsNumber+"+"+Math.round(r.stallsDuration/1e3)+"+"+r.layerSwitchesNumber+"+"+r.averageBitrate}else e+="+++++++";return e+="+"+this.getNetworkTypeListStr()+"+"+encodeURIComponent(this.playerErrorCode),e+="+"+(this.cdnStatusCode>=0?this.cdnStatusCode:"")+"+"+(this.nanoCDNStatusCode>=0?this.nanoCDNStatusCode:"")+"+"+this.bpkhttpserverTiming+"+"+this.gdpr}},{key:"toEndSessionJSON",value:function(){var t={};if(t[e.IS_START]=!1,t[e.GDPR]=this.gdpr,this.gdpr===c.H.GDPR_DELETE)return t;if(t[e.SESSION_ID]=this.sessionId,t[e.STATUS_CODE]=this.statusCode,t[e.CONTENT_URL]=u.A.toBkAString(this.requestedURL),this.redirectedURL.includes(l.A.AD_NONCE_QUERY_PARAMETER)?t[e.REDIRECT_URL]=u.A.toBkAString(s.A.addOrReplaceParam(this.redirectedURL,l.A.AD_NONCE_QUERY_PARAMETER,"ignored")):t[e.REDIRECT_URL]=u.A.toBkAString(this.redirectedURL),void 0!==this.metrics?(t[e.REDIRECTION_TIME]=this.metrics.redirectionTime,t[e.STARTUP_TIME]=this.metrics.startupTime,t[e.COMPLETION]=this.metrics.completion,t[e.PLAYED_TIME]=Math.round(this.metrics.playbackDuration/1e3),t[e.DURATION]=Math.round(this.metrics.sessionDuration/1e3),t[e.PLAYBACK_TYPE]=this.metrics.playbackType,t[e.NUMBER_OF_STALLS]=this.metrics.stallsNumber,t[e.MAX_STALL_TIME]=this.metrics.maxStallDuration,t[e.TOTAL_STALL_TIME]=this.metrics.totalStallsDuration,t[e.NUMBER_OF_REBUFFERING]=this.metrics.rebufferingsNumber,t[e.MAX_REBUFFERING_TIME]=this.metrics.maxRebufferingDuration,t[e.TOTAL_REBUFFERING_TIME]=this.metrics.totalRebufferingDuration,t[e.MIN_BITRATE]=Math.round(this.metrics.minBitrate),t[e.MAX_BITRATE]=Math.round(this.metrics.maxBitrate),t[e.AVERAGE_BITRATE]=Math.round(this.metrics.averageBitrate),t[e.NUMBER_OF_LAYER_SWITCH]=this.metrics.layerSwitchesNumber,t[e.TIME_SPENT_PER_LAYER]=this.metrics.timeSpentPerLayer):(t[e.REDIRECTION_TIME]=-1,t[e.STARTUP_TIME]=-1,t[e.COMPLETION]=-1,t[e.PLAYED_TIME]=-1,t[e.DURATION]=-1,t[e.PLAYBACK_TYPE]="",t[e.NUMBER_OF_STALLS]=-1,t[e.MAX_STALL_TIME]=-1,t[e.TOTAL_STALL_TIME]=-1,t[e.NUMBER_OF_REBUFFERING]=-1,t[e.MAX_REBUFFERING_TIME]=-1,t[e.TOTAL_REBUFFERING_TIME]=-1,t[e.MIN_BITRATE]=-1,t[e.MAX_BITRATE]=-1,t[e.AVERAGE_BITRATE]=-1,t[e.NUMBER_OF_LAYER_SWITCH]=-1,t[e.TIME_SPENT_PER_LAYER]={}),t[e.PLAYER_NAME]=u.A.toBkAString(this.playerName),t[e.PLAYER_VERSION]=u.A.toBkAString(this.playerVersion),t[e.DEVICE_OS]=u.A.toBkAString(this.osName),t[e.DEVICE_VERSION]=u.A.toBkAString(this.osVersion),""!==this.smartLibParameters.deviceType?t[e.DEVICE_TYPE]=u.A.toBkAString(this.smartLibParameters.deviceType):t[e.DEVICE_TYPE]=u.A.toBkAString(this.deviceType),t[e.UUID]=u.A.toBkAString(this.smartLibParameters.uuid),t[e.NANOCDN_STATUS]=this.nanoCDNStatus,t[e.CUSTOM_PARAMETERS]=this.customParameters,void 0!==this.timeline?t[e.TIMELINE]=this.timeline.data().base64():t[e.TIMELINE]="",t[e.SMARTLIB_VERSION]=u.A.toBkAString(this.smartLibVersion),t[e.DIVERSITY]=this.diversity,t[e.PLAYER_ERROR_CODE]=u.A.toBkAString(this.playerErrorCode),t[e.AD_METRICS]=[],void 0!==this.adMetrics&&this.adMetrics.length>0)for(var i=0;i<this.adMetrics.length;i++){var n=this.adMetrics[i],r=this.cleanJson({ad_skippable:n.adSkippable?1:0,ad_skipped:n.adSkipped?1:0,ad_progress:n.adProgress>=0?n.adProgress/25:n.adProgress,ad_duration:Math.round(n.adDuration/1e3),stalls_number:n.stallsNumber,stalls_duration:Math.round(n.stallsDuration/1e3),layer_switches_number:n.layerSwitchesNumber,average_bitrate:n.averageBitrate,creative_id:n.creativeId,ad_id:n.adId.slice(0,n.adId.lastIndexOf("-")),ad_index:n.adIndex,ad_count:n.adCount,ad_format:n.adFormat,impression_date:n.impressionDate});t[e.AD_METRICS].push(r)}return t[e.NETWORK_TYPE]=this.getNetworkTypeListStr(),t[e.CDN_STATUS_CODE]=this.cdnStatusCode>=0?this.cdnStatusCode:"",t[e.NANOCDN_STATUS_CODE]=this.nanoCDNStatusCode>=0?this.nanoCDNStatusCode:"",t[e.BPKHTTPSERVER_TIMING]=this.bpkhttpserverTiming,t[e.PRECACHED]=this.precached,t[e.PRECACHE_ENDED]=this.precacheEnded,t[e.PRECACHE_DURATION]=this.precacheDuration,t}},{key:"cleanJson",value:function(e){var t=["ad_progress","ad_index","ad_count"];for(var i in e){var n=e[i];-1===t.indexOf(i)&&(n<=0||""===n)&&delete e[i]}return e}},{key:"getTimeSpentPerLayerQuery",value:function(){var e="";for(var t in this.metrics.timeSpentPerLayer)""!==e&&(e+=","),e+=t+":"+this.metrics.timeSpentPerLayer[t];return e}},{key:"addNetworkType",value:function(e){this.networkTypeList.includes(e)||this.networkTypeList.push(e)}},{key:"getNetworkTypeListStr",value:function(){return this.networkTypeList.length>0?this.networkTypeList.sort().join("-"):""}},{key:"toString",value:function(){return JSON.stringify(this,(function(e,t){return"timeline"===e&&void 0!==n.A.analyticsModule&&t instanceof n.A.analyticsModule.SessionTrackerTimeline?t.toString():void 0===t?"undefined":t}),"  ")}}]),e}();_defineProperty(v,"SESSION_ID","session_id"),_defineProperty(v,"IS_START","start"),_defineProperty(v,"STATUS_CODE","status_code"),_defineProperty(v,"STARTUP_TIME","startup_time"),_defineProperty(v,"REDIRECTION_TIME","redirection_time"),_defineProperty(v,"COMPLETION","completion"),_defineProperty(v,"PLAYED_TIME","played_time"),_defineProperty(v,"DURATION","duration"),_defineProperty(v,"PLAYBACK_TYPE","playback_type"),_defineProperty(v,"NUMBER_OF_STALLS","cnt_stall"),_defineProperty(v,"TOTAL_STALL_TIME","total_stall_time"),_defineProperty(v,"MAX_STALL_TIME","max_stall_time"),_defineProperty(v,"NUMBER_OF_REBUFFERING","cnt_rebuffering"),_defineProperty(v,"TOTAL_REBUFFERING_TIME","total_rebuffering_time"),_defineProperty(v,"MAX_REBUFFERING_TIME","max_rebuffering_time"),_defineProperty(v,"REDIRECT_URL","redirect_url"),_defineProperty(v,"CONTENT_URL","content_url"),_defineProperty(v,"MAX_BITRATE","max_bitrate"),_defineProperty(v,"MIN_BITRATE","min_bitrate"),_defineProperty(v,"AVERAGE_BITRATE","average_bitrate"),_defineProperty(v,"NUMBER_OF_LAYER_SWITCH","cnt_layer_switch"),_defineProperty(v,"TIME_SPENT_PER_LAYER","time_spent_per_layer"),_defineProperty(v,"PLAYER_NAME","player_name"),_defineProperty(v,"PLAYER_VERSION","player_version"),_defineProperty(v,"DEVICE_OS","device_os"),_defineProperty(v,"DEVICE_VERSION","device_version"),_defineProperty(v,"DEVICE_TYPE","device_type"),_defineProperty(v,"DIVERSITY","diversity"),_defineProperty(v,"UUID","uuid"),_defineProperty(v,"NANOCDN_STATUS","nano_status"),_defineProperty(v,"CUSTOM_PARAMETERS","custom_parameters"),_defineProperty(v,"TIMELINE","timeline"),_defineProperty(v,"SMARTLIB_VERSION","smartlib_version"),_defineProperty(v,"AD_METRICS","ad_metrics"),_defineProperty(v,"NETWORK_TYPE","network_type_list"),_defineProperty(v,"PLAYER_ERROR_CODE","player_error_code"),_defineProperty(v,"CDN_STATUS_CODE","cdn_status_code"),_defineProperty(v,"NANOCDN_STATUS_CODE","nanocdn_status_code"),_defineProperty(v,"BPKHTTPSERVER_TIMING","bpkhttpserver_timing"),_defineProperty(v,"GDPR","gdpr"),_defineProperty(v,"PRECACHED","precached"),_defineProperty(v,"PRECACHE_ENDED","precache_ended"),_defineProperty(v,"PRECACHE_DURATION","precache_duration");var p=i(7418),f=i(1262),y=i(1134),g="BpkSessionHandler",S=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"streamingSession",void 0),_defineProperty(this,"analyticsAddress",void 0),_defineProperty(this,"options",void 0),_defineProperty(this,"diversitySession",void 0),_defineProperty(this,"adSession",void 0),_defineProperty(this,"diversityFailoverSession",void 0),_defineProperty(this,"omSessionHandler",void 0),_defineProperty(this,"id",void 0),_defineProperty(this,"listeners",void 0),_defineProperty(this,"playerAdapterInitialized",void 0),_defineProperty(this,"playerAdapter",void 0),_defineProperty(this,"metricsManager",void 0),_defineProperty(this,"keepAliveManager",void 0),_defineProperty(this,"adMetricsManager",void 0),_defineProperty(this,"adTrackingManager",void 0),_defineProperty(this,"sessionResult",void 0),_defineProperty(this,"sessionReport",void 0),_defineProperty(this,"firstImageCalled",void 0),_defineProperty(this,"stopped",void 0),_defineProperty(this,"jobs",void 0),_defineProperty(this,"deviceInitDate",void 0),_defineProperty(this,"manifestInitDate",void 0),_defineProperty(this,"videoMulticastStatus",void 0),_defineProperty(this,"audioMulticastStatus",void 0),_defineProperty(this,"dataMulticastStatus",void 0),this.smartLib=t,this.streamingSession=i,this.diversitySession=void 0,this.adSession=void 0,this.listeners=[],this.playerAdapterInitialized=!1,this.sessionResult=void 0,this.sessionReport=new v,this.firstImageCalled=!1,this.stopped=!1,this.jobs=[],this.sessionReport.smartLibParameters=t.getParameters(),this.sessionReport.customParameters=this.getCustomParameters(),this.videoMulticastStatus=c.H.MULTICAST_INACTIVE,this.audioMulticastStatus=c.H.MULTICAST_INACTIVE,this.dataMulticastStatus=c.H.MULTICAST_INACTIVE,this.analyticsAddress=this.sessionReport.smartLibParameters.analyticsAddress,this.multicastStatusListener=void 0}return _createClass(e,[{key:"initPlayerAdapter",value:function(){if(!1===this.playerAdapterInitialized)if(this.playerAdapterInitialized=!0,void 0!==this.playerAdapter){var e;if(r.g.i(g,"Player:name="+this.playerAdapter.getName()+", version="+this.playerAdapter.getVersion(),this.id),r.g.i(g,"Player:capabilities="+JSON.stringify(this.playerAdapter.getCapabilities()),this.id),this.playerAdapter.attachSession(this),this.playerAdapter.initSessionPlayerObjects(),!0===(null===(e=this.diversitySession)||void 0===e?void 0:e.activated)){var t=this.playerAdapter.initDiversitySession(this.diversitySession.options);this.addListener(t)}}else r.g.i(g,"Player:no player attached",this.id)}},{key:"initManagers",value:function(){var e,t,i;void 0!==n.A.analyticsModule&&(this.metricsManager=new n.A.analyticsModule.MetricsManager(this,this.playerAdapter),this.addListener(this.sessionReport.timeline),f.CE.getInstance().addListener(this.sessionReport.timeline)),void 0!==n.A.adModule&&(this.adMetricsManager=new n.A.adModule.AdMetricsManager(this),this.adTrackingManager=new n.A.adModule.AdTrackingManager(this,this.playerAdapter),this.addListener(this.adMetricsManager),this.adTrackingManager.addListener(this.adMetricsManager)),void 0!==this.playerAdapter&&(this.addListener(this.metricsManager),this.addListener(this.playerAdapter)),void 0!==this.adSession&&this.adSession.isAdActivated()&&null!==(e=n.A.adModule)&&void 0!==e&&e.OMSDKManager.getInstance().isEnabled()&&(this.omSessionHandler=new n.A.adModule.OMSessionHandler(this,this.playerAdapter),this.addListener(this.omSessionHandler),null!==(t=this.adTrackingManager)&&void 0!==t&&t.addListener(this.omSessionHandler)),this.addListener(this),f.CE.getInstance().addListener(this),null===(i=this.smartLib.monitoringManager)||void 0===i||i.startSession(this)}},{key:"start",value:function(t){var i,s,o=this,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){};r.g.i(g,"Starting session...",this.id),this.sessionReport.startSessionDate=Date.now(),this.sessionReport.requestedURL=t,this.sessionReport.precached=this.isPrecached(),this.initPlayerAdapter(),this.initManagers(),this.notifyStart();var l=this.smartLib.getParameters(),u={analyticsAddress:this.analyticsAddress,nanoCDNDescr:null===(i=this.smartLib.nanoCDNHostManager)||void 0===i?void 0:i.getCurrentNanoCDNDescr(),nanoCDNDescrConfigured:null===(s=this.smartLib.nanoCDNHostManager)||void 0===s?void 0:s.getCurrentNanoCDNDescr(),broadpeakDomainNames:l.broadpeakDomainNames,forceTeardown:l.forceTeardown,userAgent:l.userAgent},h=a.A.isBroadpeakDomainName(t,u.broadpeakDomainNames)&&!0===this.options.get(c.H.REQUEST_BROADPEAK_CDN);return void 0!==u.nanoCDNDescr&&(this.sessionReport.nanoCDNStatus=e.NANOCDN_STATUS_DETECTED_NOT_USED),this.sessionReport.gdpr=this.options.get(c.H.GDPR_PREFERENCE),Promise.resolve().then((function(){return void 0!==n.A.diversityModule&&!0===o.options.get(c.H.REQUEST_DIVERSITY_DYNAMIC_FAILOVER)&&h?n.A.diversityModule.DiversityRequestManager.getInstance().processFailoverSession(t,u,o):a.A.getInstance().getURL(t,u,o)})).then((function(e){return o.sessionResult=e,e.isError()&&(o.sessionReport.statusCode=e.getErrorCode()),o.sessionReport.redirectedURL=o.patchBkmResult(e.getURL()),o.handleRedirectedURL(u),e.url=o.sessionReport.redirectedURL,o.stopped?(r.g.d(g,"The session has been stopped while creating it.",o.id),o.stop(),e=f.Sy.error(f.Sy.RESULT_SESSION_HAS_BEEN_STOPPED_DURING_REQUEST),r.g.i(g,"Session error code: "+e.getErrorMessage()+" (code "+e.getErrorCode()+", detail "+e.getDetailedErrorMessage()+")",o.id),r.g.i(g,"Session redirected URL: ",o.id),d(""),e):(r.g.i(g,"Session status: "+e.getErrorMessage()+" (code "+e.getErrorCode()+", detail "+e.getDetailedErrorMessage()+")",o.id),o.sessionReport.redirectedURL.length>0&&r.g.i(g,"Session redirected URL: "+o.sessionReport.redirectedURL,o.id),d(o.sessionReport.redirectedURL),e)}))}},{key:"handleRedirectedURL",value:function(t){var i,a,s;this.isNanoCDNUsed()&&(this.sessionReport.nanoCDNStatus=e.NANOCDN_STATUS_USED,this.notifyUnicastUsed());var o=this.sessionResult.nanoCDNDescr||(null===(i=this.smartLib.nanoCDNHostManager)||void 0===i?void 0:i.getCurrentNanoCDNDescr()),d=null==o?void 0:o.bka100;void 0!==d&&(r.g.i(g,"nanoCDN is overwriting the analytics address to "+d,this.id),this.analyticsAddress=d),0!==this.sessionReport.redirectedURL.length&&(this.sessionReport.sessionRunning=!0),this.sessionReport.sessionCreated=!0,this.sessionReport.sessionRunning&&(this.sessionReport.teardownActivated=this.isTeardownActivated(t)),this.sessionReport.redirectedURL=this.removeBroadpeakParameters(this.sessionReport.redirectedURL),this.sessionReport.sessionId=this.getTicket(),this.sessionReport.teardownActivated||null!==(a=this.sessionReport.timeline)&&void 0!==a&&a.encoder.extend(),this.notifyRedirectionEnd(),this.isPrecached()&&this.startKeepalive(),void 0===this.playerAdapter&&this.sessionReport.sessionRunning&&this.notifyFirstImage(0,0),null===(s=n.A.analyticsModule)||void 0===s||s.CacheManager.getInstance().clean()}},{key:"stop",value:function(e){var t,i;if(this.updatePrecacheDuration(),f.CE.getInstance().removeListener(this.sessionReport.timeline),f.CE.getInstance().removeListener(this),void 0===e||isNaN(e)||(this.sessionReport.statusCode=e),this.sessionReport.endSessionDate=Date.now(),this.notifyStop(this.sessionReport.statusCode),void 0!==this.keepAliveManager&&this.keepAliveManager.stop(),null!==(t=this.smartLib.monitoringManager)&&void 0!==t&&t.stopSession(this),this.sessionReport.sessionCreated){var s,o,d,l,u;r.g.d(g,"Session ends with status code: "+this.sessionReport.statusCode,this.id);var h=this.smartLib.getParameters();this.sessionReport.sessionRunning=!1,this.sessionReport.sessionCreated=!1,this.sessionReport.smartLibParameters=JSON.parse(JSON.stringify(this.sessionReport.smartLibParameters)),this.sessionReport.customParameters=JSON.parse(JSON.stringify(this.getCustomParameters())),this.notifyEndSessionReportUpdateRequested(this.sessionReport),r.g.d(g,"SessionReport: "+this.sessionReport.toString(),this.id),this.resetCustomParameters();var v={analyticsAddress:this.analyticsAddress,userAgent:h.userAgent},p=this.options.get(c.H.SESSION_TEARDOWN),S=this.options.get(c.H.SESSION_REPORTING_MODE),_=void 0===this.keepAliveManager||"POST"===(null===(s=this.keepAliveManager)||void 0===s?void 0:s.method)?"POST":"GET";r.g.d(g,"Keepalive manager method set to "+_,this.id),p===c.H.SESSION_ENABLED?(this.sessionReport.teardownRequestDate=Date.now(),a.A.getInstance().teardown(this,v,_),S===c.H.SESSION_REPORTING_MODE_METRICS_RECEIVER&&(this.sessionReport.endSessionRequestDate=Date.now(),null===(o=n.A.analyticsModule)||void 0===o||o.AnalyticsRequestManager.getInstance().endSession(this,v))):p===c.H.SESSION_ENABLED_IF_BROADPEAK_DOMAIN?this.sessionReport.teardownActivated?(this.sessionReport.teardownRequestDate=Date.now(),a.A.getInstance().teardown(this,v,_),S===c.H.SESSION_REPORTING_MODE_METRICS_RECEIVER&&(this.sessionReport.endSessionRequestDate=Date.now(),null===(d=n.A.analyticsModule)||void 0===d||d.AnalyticsRequestManager.getInstance().endSession(this,v))):S!==c.H.SESSION_REPORTING_MODE_DEFAULT&&S!==c.H.SESSION_REPORTING_MODE_METRICS_RECEIVER||(this.sessionReport.endSessionRequestDate=Date.now(),null===(l=n.A.analyticsModule)||void 0===l||l.AnalyticsRequestManager.getInstance().endSession(this,v)):p===c.H.SESSION_DISABLED&&(S!==c.H.SESSION_REPORTING_MODE_DEFAULT&&S!==c.H.SESSION_REPORTING_MODE_METRICS_RECEIVER||(this.sessionReport.endSessionRequestDate=Date.now(),null===(u=n.A.analyticsModule)||void 0===u||u.AnalyticsRequestManager.getInstance().endSession(this,v)))}void 0!==this.playerAdapter&&(!0===(null===(i=this.diversitySession)||void 0===i?void 0:i.activated)&&this.playerAdapter.releaseDiversitySession(),this.playerAdapter.releaseSessionPlayerObjects(),this.playerAdapter.detachSession());for(var E=0,m=0;m<this.jobs.length;m++)!0===this.jobs[m].active&&E++,y.A.getInstance().interrupt(this.jobs[m]);r.g.d(g,E+" active job(s)",this.id),this.jobs=[],this.listeners=[],this.stopped=!0}},{key:"onFirstImage",value:function(e,t){void 0===this.keepAliveManager&&this.startKeepalive()}},{key:"startKeepalive",value:function(){var e,t;this.notifyStartSessionReportUpdateRequested(this.sessionReport);var i=this.options.get(c.H.SESSION_KEEPALIVE),a=void 0!==n.A.analyticsModule&&this.options.get(c.H.SESSION_REPORTING_MODE)===c.H.SESSION_REPORTING_MODE_METRICS_RECEIVER,s=0===this.smartLib.getParameters().analyticsAddress.length;i===c.H.SESSION_ENABLED?(r.g.i(g,"Keepalive is enabled (SESSION_KEEPALIVE=SESSION_ENABLED)",this.id),this.keepAliveManager=a&&!s?new n.A.analyticsModule.BroadpeakCDNCacheKeepaliveManager(this):new p.q(this)):i===c.H.SESSION_ENABLED_IF_BROADPEAK_DOMAIN?this.sessionReport.teardownActivated?this.keepAliveManager=a&&!s?new n.A.analyticsModule.BroadpeakCDNCacheKeepaliveManager(this):new p.q(this):void 0===n.A.analyticsModule||s||(this.keepAliveManager=new n.A.analyticsModule.CacheKeepaliveManager(this)):i===c.H.SESSION_DISABLED&&(this.sessionReport.teardownActivated&&r.g.i(g,"Keepalive is disabled (SESSION_KEEPALIVE=SESSION_DISABLED)",this.id),void 0===n.A.analyticsModule||s||(this.keepAliveManager=new n.A.analyticsModule.CacheKeepaliveManager(this))),null!==(e=this.keepAliveManager)&&void 0!==e&&e.start(),void 0!==n.A.monitoringModule&&!0===this.isNanoCDNUsed()&&(null===(t=this.keepAliveManager)||void 0===t||t.process(!1))}},{key:"getMulticastStatus",value:function(e,t){if(void 0!==e&&"number"==typeof e.status){var i=e.status,n=e.daiperiod||0;if(1===i)return r.g.d(g,"Multicast status: active",this.id),c.H.MULTICAST_ACTIVE;if(0!==n||1!==i)return t!==c.H.MULTICAST_INACTIVE?(r.g.d(g,"Multicast status: inactive was active"+(n>=1?" (playing DAI period)":""),this.id),c.H.MULTICAST_INACTIVE_WAS_ACTIVE):(r.g.d(g,"Multicast status: inactive",this.id),c.H.MULTICAST_INACTIVE)}return r.g.d(g,"Multicast status: unknown (segment:"+JSON.stringify(e)+")",this.id),c.H.MULTICAST_UNKNOWN}},{key:"onKeepaliveRequestEnded",value:function(e,t){if(this.isNanoCDNUsed()){var i,n,a;if("object"!=_typeof(t))return r.g.d(g,"Data is not an object: (data:"+JSON.stringify(t)+")",this.id),this.videoMulticastStatus=c.H.MULTICAST_UNKNOWN,this.audioMulticastStatus=c.H.MULTICAST_UNKNOWN,void(this.dataMulticastStatus=c.H.MULTICAST_UNKNOWN);r.g.d(g,"nanoCDN data: "+JSON.stringify(t)+")",this.id);var s=this.videoMulticastStatus,o=this.audioMulticastStatus,d=this.dataMulticastStatus;r.g.d(g,"Getting multicast status for video",this.id),this.videoMulticastStatus=this.getMulticastStatus(t.last_video_segment,s),r.g.d(g,"Getting multicast status for audio",this.id),this.audioMulticastStatus=this.getMulticastStatus(t.last_audio_segment,o),r.g.d(g,"Getting multicast status for data",this.id),this.dataMulticastStatus=this.getMulticastStatus(t.last_data_segment,d),s!==this.videoMulticastStatus&&(null!==(i=this.streamingSession.multicastStatusListener)&&void 0!==i&&i.onVideoMulticastStatusChange(this.videoMulticastStatus),this.videoMulticastStatus===c.H.MULTICAST_ACTIVE?this.notifyMulticastUsed():this.notifyUnicastUsed()),o!==this.audioMulticastStatus&&null!==(n=this.streamingSession.multicastStatusListener)&&void 0!==n&&n.onAudioMulticastStatusChange(this.audioMulticastStatus),d!==this.dataMulticastStatus&&(null===(a=this.streamingSession.multicastStatusListener)||void 0===a||a.onDataMulticastStatusChange(this.dataMulticastStatus))}}},{key:"isNanoCDNUsed",value:function(){var e=this,t=!1;if(void 0===n.A.nanoCDNModule)return!1;if("string"==typeof this.sessionReport.redirectedURL&&0!==this.sessionReport.redirectedURL.length){var i,r=null===(i=this.smartLib.nanoCDNHostManager)||void 0===i?void 0:i.getNanoCDNDescrList();null==r||r.forEach((function(i){e.sessionReport.redirectedURL.indexOf(i.hostIP)>0&&(t=!0)}))}return t}},{key:"patchBkmResult",value:function(e){var t=s.A.parseURL(e);if(null!==t){var i=e,n=t.query.split("&");return n.length>=2&&-1===n[0].indexOf("bk-teardown=")&&(i=i.replace("?bk-teardown=","&bk-teardown=")),-1!==i.indexOf("//bpk-token")&&(i=i.replace("//bpk-token","/bpk-token")),i}return e}},{key:"getTicket",value:function(){var e,t="";if(void 0!==this.adTrackingManager&&!0===this.adTrackingManager.bkYouSession&&void 0!==this.adTrackingManager.sessionToken&&this.adTrackingManager.sessionToken.length>0&&(t=this.adTrackingManager.sessionToken),""===t&&(t=s.A.getParam(this.sessionReport.redirectedURL,a.A.QUERY_PARAMETER_SESSION_ID)),""===t&&(t=s.A.getParam(this.sessionReport.redirectedURL,"bpkio_sessionid")),""===t&&(e=this.sessionReport.redirectedURL.split("/")).length>=4&&(e[3].match("\\[(.*)\\]")?t=e[3].substring(1,e[3].length-1):e[3].match("\\%5B(.*)\\%5D")&&(t=e[3].substring(3,e[3].length-3))),""===t){var i=this.sessionReport.redirectedURL.split("//");(e=i[i.length-1].split("/")).length>=3&&(e[0].match(s.A.REGEX_IP)||e[0].match(s.A.REGEX_HOSTNAME))&&"bpk-token"===e[1]&&(e[2].match(s.A.REGEX_TICKET)||e[2].match(s.A.REGEX_TICKET_TRANSCODED))&&(t=e[2],r.g.v(g,"Ticket detected: "+t,this.id))}if(""===t){var n=this.sessionReport.redirectedURL.split("//");(e=n[n.length-1].split("/")).length>=2&&(e[0].match(s.A.REGEX_IP)||e[0].match(s.A.REGEX_HOSTNAME))&&(e[1].match(s.A.REGEX_TICKET)||e[1].match(s.A.REGEX_TICKET_TRANSCODED))&&(t=e[1],r.g.v(g,"Ticket detected: "+t,this.id))}return""===t&&(t=this.generateTicket()),""===t&&(r.g.w(g,"Failed getting ticket",this.id),r.g.w(g,"SmartLib won't be able to post metrics",this.id)),t}},{key:"generateTicket",value:function(){var e=Date.now(),t=o.A.randomIntFromInterval(1e6,9999999),i=o.A.randomIntFromInterval(1e6,9999999),n=o.A.stringToBase64(t+""+e+i);return null===n?(r.g.w(g,"Cannot encode base 64 ticket",this.id),""):(r.g.v(g,"Ticket generated: "+n,this.id),n)}},{key:"getTeardown",value:function(){var e=s.A.getParam(this.sessionReport.redirectedURL,"bk-teardown");return e.startsWith("T")||e.startsWith("t")||e.startsWith("1")}},{key:"isTeardownActivated",value:function(t){if(t.forceTeardown===n.A.FORCE_TEARDOWN_ENABLED)return r.g.i(g,"Session will start with keepalives/teardown (forceTeardown)",this.id),!0;if(t.forceTeardown===n.A.FORCE_TEARDOWN_DISABLED)return r.g.i(g,"Session will start without keepalives/teardown (forceTeardown)",this.id),!1;if(null!==this.sessionReport.redirectedURL&&"0"===s.A.getParam(this.sessionReport.redirectedURL,"bk-teardown"))return r.g.i(g,"Session will start without keepalives/teardown (bk-teardown=0)",this.id),!1;if(null!==this.sessionReport.redirectedURL&&this.getTeardown())return r.g.i(g,"Session will start with keepalives/teardown (bk-teardown=1)",this.id),!0;if(this.sessionReport.nanoCDNStatus===e.NANOCDN_STATUS_USED)return r.g.i(g,"Session will start with keepalives/teardown (nanoCDN used)",this.id),!0;if(!1===this.options.get(c.H.REQUEST_BROADPEAK_CDN))return r.g.i(g,"Session will start without keepalives/teardown (REQUEST_BROADPEAK_CDN=false)",this.id),!1;if(void 0===this.diversityFailoverSession){if(null!==this.sessionReport.requestedURL&&a.A.isBroadpeakDomainName(this.sessionReport.requestedURL,t.broadpeakDomainNames))return r.g.i(g,"Session will start with keepalives/teardown (in broadpeak domains list)",this.id),!0}else if(a.A.isBroadpeakDomainName(this.diversityFailoverSession.getMainURL(),t.broadpeakDomainNames))return r.g.i(g,"Diversity session will start with keepalives/teardown (in broadpeak domains list)",this.id),!0;return r.g.i(g,"Session will start without keepalives/teardown",this.id),!1}},{key:"removeBroadpeakParameters",value:function(e){var t=s.A.parseURL(e);if(null!==t){for(var i="",n=t.query.split("&"),a=1,o=0;o<n.length;o++){var d=n[o];d.startsWith("bk-")?r.g.d(g,"Remove query parameter '"+d+"' from url",this.id):(a||(i+="&"),i+=d,a=0)}return""===i?e.replace("?"+t.query,""):e.replace(t.query,i)}return e}},{key:"isSessionCreated",value:function(e){if(null===e)return!1;var t=s.A.getParam(e,a.A.QUERY_PARAMETER_SESSION_ID);return 0!==t.length||(t=s.A.getParam(e,a.A.QUERY_PARAMETER_SESSION_CREATED)).startsWith("T")||t.startsWith("t")||t.startsWith("1")}},{key:"isPrecached",value:function(){return!0===this.options.get(c.H.SESSION_PRECACHE)}},{key:"updatePrecacheDuration",value:function(){this.isPrecached()&&0===this.sessionReport.precacheDuration&&(this.sessionReport.precacheDuration=Date.now()-this.sessionReport.startSessionDate)}},{key:"getCustomParameters",value:function(){return void 0!==this.streamingSession?this.streamingSession.customParameters:this.smartLib.getParameters().customParameters}},{key:"resetCustomParameters",value:function(){void 0!==this.streamingSession||this.smartLib.resetCustomParameters()}},{key:"addListener",value:function(e){void 0===e||this.listeners.includes(e)||this.listeners.push(e)}},{key:"removeListener",value:function(e){var t=this.listeners.indexOf(e);-1!==t&&this.listeners.splice(t,1)}},{key:"notifyEvent",value:function(e,t,i,n){"function"==typeof e[t]&&e[t](i,n)}},{key:"notifyLoading",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onLoading")}))}},{key:"notifyStart",value:function(){var e,t,i=this;e=f.CE.getInstance().networkType,this.sessionReport.addNetworkType(e),t=f.CE.getInstance().mute,this.listeners.forEach((function(n){i.notifyEvent(n,"onStart",e,t)}))}},{key:"notifyRedirectionEnd",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onRedirectionEnd")}))}},{key:"notifyPrecacheEnded",value:function(){var e=this;this.isPrecached()&&!0!==this.sessionReport.precacheEnded&&(r.g.d(g,"Precache ended",this.id),this.sessionReport.precacheEnded=!0,this.updatePrecacheDuration(),this.listeners.forEach((function(t){e.notifyEvent(t,"onPrecacheEnded")})))}},{key:"notifyFirstImage",value:function(e,t){var i=this;this.firstImageCalled?r.g.e(g,"Implementation error: PlayerEventListener.onSessionStart() already called. If you don't attach any player, please remove this call, SmartLib is now handling it automatically.",this.id):(this.firstImageCalled=!0,void 0!==this.adSession&&this.adSession.isAdActivated()?void 0!==this.playerAdapter?!0===this.playerAdapter.getCapabilities().adTracking?this.addListener(this.adTrackingManager):r.g.d(g,"Ad tracking disabled (player not compatible)",this.id):r.g.d(g,"Ad tracking disabled (no player attached)",this.id):r.g.d(g,"Ad tracking disabled (advertising not activated)",this.id),this.listeners.forEach((function(n){i.notifyEvent(n,"onFirstImage",e,t)})))}},{key:"notifyLayerSwitch",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onLayerSwitch",e)}))}},{key:"notifyPause",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onPause")}))}},{key:"notifyResume",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onResume")}))}},{key:"notifyBufferingStart",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onBufferingStart")}))}},{key:"notifyBufferingEnd",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onBufferingEnd",e)}))}},{key:"notifyStallEnd",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onStallEnd")}))}},{key:"notifyRebufferingEnd",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onRebufferingEnd")}))}},{key:"notifySeek",value:function(e,t){var i=this;this.listeners.forEach((function(n){i.notifyEvent(n,"onSeek",e,t)}))}},{key:"notifyStop",value:function(e){var t,i,n=this;null!==(t=this.adSession)&&void 0!==t&&null!==(i=t.adPalSession)&&void 0!==i&&i.setAdView(void 0),this.listeners.forEach((function(t){n.notifyEvent(t,"onStop",e)}))}},{key:"notifyClose",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;this.listeners.forEach((function(i){e.notifyEvent(i,"onClose",t)}))}},{key:"notifyStartSessionReportUpdateRequested",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onStartSessionReportUpdateRequested",e)}))}},{key:"notifyKeepaliveSessionReportRequested",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onKeepaliveSessionReportUpdateRequested",e)}))}},{key:"notifyKeepaliveRequestEnded",value:function(e,t){var i=this;this.listeners.forEach((function(n){i.notifyEvent(n,"onKeepaliveRequestEnded",e,t)}))}},{key:"notifyEndSessionReportUpdateRequested",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onEndSessionReportUpdateRequested",e)}))}},{key:"notifyVolumeChanged",value:function(e){var t=this;this.listeners.forEach((function(i){t.notifyEvent(i,"onVolumeChanged",e)}))}},{key:"notifyPlayerError",value:function(e,t){var i=this;this.listeners.forEach((function(n){i.notifyEvent(n,"onPlayerError",e,t)}))}},{key:"notifyMulticastUsed",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onMulticastUsed")}))}},{key:"notifyUnicastUsed",value:function(){var e=this;this.listeners.forEach((function(t){e.notifyEvent(t,"onUnicastUsed")}))}},{key:"onNetworkAvailable",value:function(e){r.g.v(g,"Received event onNetworkAvailable type:"+e,this.id),this.sessionReport.addNetworkType(e)}}]),e}();_defineProperty(S,"NANOCDN_STATUS_UNAVAILABLE",1),_defineProperty(S,"NANOCDN_STATUS_DETECTED_NOT_USED",2),_defineProperty(S,"NANOCDN_STATUS_USED",3)},828:function(e,t,i){i.d(t,{f:function(){return v}});var n=i(5690),r=i(8379),a=i(7832),s=i(3121),o=i(2765),d=i(5941),l=i(4943),u=i(1105),c=i(1142),h="BpkStreamingSession",v=function(){function e(t,i){_classCallCheck(this,e),_defineProperty(this,"smartLib",void 0),_defineProperty(this,"options",void 0),_defineProperty(this,"customParameters",void 0),_defineProperty(this,"diversitySession",void 0),_defineProperty(this,"adSession",void 0),_defineProperty(this,"id",void 0),_defineProperty(this,"date",void 0),_defineProperty(this,"handler",void 0),_defineProperty(this,"playerAdapter",void 0),_defineProperty(this,"stopped",void 0),_defineProperty(this,"multicastStatusListener",void 0),this.smartLib=t,this.options=i,this.customParameters={},this.id=l.A.randomIntFromInterval(1e4,99999),this.date=Date.now(),this.stopped=!1}return _createClass(e,[{key:"attachPlayer",value:function(e,t){void 0!==e?void 0===this.handler?(this.playerAdapter=e,r.g.i(h,"Player "+this.playerAdapter.getName()+" attached",this.id)):r.g.e(h,"Exception: attachPlayer error, you cannot attach a player when a session is running.",this.id):void 0===c.A.analyticsModule?r.g.w(h,"Try to attach a player, but analytics module not loaded",this.id):r.g.w(h,"Try to attach a player, but it is not recognized",this.id)}},{key:"setOption",value:function(e,t){void 0===this.handler?(r.g.d(h,"Setting session option "+a.H.optionToString(e)+" to "+a.H.valueToString(e,t),this.id),this.options.set(e,t)):r.g.e(h,"Exception: setOption error, you cannot set an option when a session is running.",this.id)}},{key:"setAdEventsListener",value:function(e){void 0===this.adSession&&(this.adSession=new u.A),this.adSession.setAdEventsListener(e)}},{key:"setAdDataListener",value:function(e){void 0===this.adSession&&(this.adSession=new u.A),this.adSession.setAdDataListener(e)}},{key:"activateDiversity",value:function(e){void 0!==c.A.diversityModule.DiversityManager?void 0===this.diversitySession&&(this.diversitySession=new c.A.diversityModule.DiversitySession,this.diversitySession.activate(e)):r.g.e(h,"Diversity module not loaded, please import it",this.id)}},{key:"activateAdvertising",value:function(){void 0===this.adSession&&(this.adSession=new u.A),this.adSession.activateAdvertising()}},{key:"setAdParameter",value:function(e,t){void 0===this.adSession&&(this.adSession=new u.A),this.adSession.setAdParameter(e,t)}},{key:"setAdView",value:function(e){var t,i,n;void 0===this.adSession&&(this.adSession=new u.A),this.adSession.adView=e,null===(t=this.handler)||void 0===t||null===(i=t.omSessionHandler)||void 0===i||null===(n=i.omAdSession)||void 0===n||n.setAdView(e)}},{key:"setAdViewState",value:function(e){var t,i,a;void 0===this.adSession&&(this.adSession=new u.A),!0===n.up.isAdViewState(e)?(this.adSession.adViewState=e,null===(t=this.handler)||void 0===t||null===(i=t.omSessionHandler)||void 0===i||null===(a=i.omAdSession)||void 0===a||a.setAdViewState(e)):r.g.e(h,"Please use a defined state within the AdViewState enum.",this.id)}},{key:"registerAdFriendlyObstructionView",value:function(e,t,i){var a,s,o;void 0===this.adSession&&(this.adSession=new u.A),n.D6.isAdFriendlyObstructionPurpose(t)&&"string"==typeof i?(this.adSession.adFriendlyObstructionViews.push({view:e,purpose:t,reason:i}),null===(a=this.handler)||void 0===a||null===(s=a.omSessionHandler)||void 0===s||null===(o=s.omAdSession)||void 0===o||o.registerAdFriendlyObstructionView(e,t,i)):r.g.e(h,"Ad friendly objection view not registered, please check arguments types: purpose -> state within the AdFriendlyObstructionPurpose enum, reason -> string",this.id)}},{key:"unregisterFriendlyObstruction",value:function(e){var t,i,n;void 0!==this.adSession&&(this.adSession.adFriendlyObstructionViews=this.adSession.adFriendlyObstructionViews.filter((function(t){return t.view!==e})),null===(t=this.handler)||void 0===t||null===(i=t.omSessionHandler)||void 0===i||null===(n=i.omAdSession)||void 0===n||n.unregisterFriendlyObstruction(e))}},{key:"unregisterAllFriendlyObstructions",value:function(){var e,t,i;void 0!==this.adSession&&(this.adSession.adFriendlyObstructionViews=[],null===(e=this.handler)||void 0===e||null===(t=e.omSessionHandler)||void 0===t||null===(i=t.omAdSession)||void 0===i||i.unregisterAllFriendlyObstructions())}},{key:"setAdCustomReference",value:function(e){var t,i;void 0===this.adSession&&(this.adSession=new u.A),"string"==typeof e?(this.adSession.adCustomReference=e,void 0!==(null===(t=this.handler)||void 0===t||null===(i=t.omSessionHandler)||void 0===i?void 0:i.omAdSession)&&r.g.w(h,'An ad is already playing, the ad custom reference "'+e+'" will be set for the next ad',this.id)):r.g.e(h,"Ad custom reference not set, reference argument has to be a string",this.id)}},{key:"registerAdVerificationData",value:function(e){var t,i,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0===this.adSession&&(this.adSession=new u.A);var s={verificationVendor:n,verificationURL:e,verificationParameters:a};this.adSession.adVerificationData.push(s),void 0!==(null===(t=this.handler)||void 0===t||null===(i=t.omSessionHandler)||void 0===i?void 0:i.omAdSession)&&r.g.w(h,'An ad is already playing, the ad verification data "'+JSON.stringify(s)+'" will be set for the next ad',this.id)}},{key:"adUserInteraction",value:function(e){var t,i,n,a,s;void 0===this.adSession&&(this.adSession=new u.A),"string"==typeof e?(r.g.d(h,"Ad interaction: "+e,this.id),null!==(t=this.handler)&&void 0!==t&&null!==(i=t.adTrackingManager)&&void 0!==i&&i.adUserInteraction(e),null!==(n=this.adSession.adPalSession)&&void 0!==n&&n.sendAdClick(),void 0!==(null===(a=this.handler)||void 0===a||null===(s=a.omSessionHandler)||void 0===s?void 0:s.omAdSession)&&this.handler.omSessionHandler.omAdSession.adUserInteraction(e)):r.g.e(h,"Ad interaction type not triggered, interactionType argument has to be a string",this.id)}},{key:"adTouch",value:function(e){var t;void 0===this.adSession&&(this.adSession=new u.A),r.g.d(h,"Ad touch",this.id),null===(t=this.adSession.adPalSession)||void 0===t||t.sendTouch(e)}},{key:"setCustomParameter",value:function(e,t){"string"==typeof e?this.customParameters[e]=d.A.toBkAString(String(t)):r.g.e(h,"Error: Only string values are accepted for custom parameters",this.id)}},{key:"setEventCallbackEnabled",value:function(e){void 0===this.adSession&&(this.adSession=new u.A),this.adSession.setEventCallbackEnabled(e)}},{key:"getURL",value:function(e){return r.g.i(h,"getURL with "+e,this.id),void 0===this.handler?d.A.typeParameter(e,"string","Error: requestedURL should be a string")&&d.A.stringNonEmpty(e,"Error: requestedURL is empty")?(this.handler=this.smartLib.sessionManager.createSessionHandler(this),this.handler.diversitySession=this.diversitySession,void 0!==this.diversitySession&&(this.diversitySession.handler=this.handler),this.handler.adSession=this.adSession,void 0!==this.adSession&&(this.adSession.handler=this.handler),this.handler.start(e)):Promise.resolve(s.S.error(s.S.RESULT_API_PARAMETER_FORMAT_ERROR)):(r.g.e(h,"Exception: getURL error, the session is already running.",this.id),Promise.resolve(s.S.error(s.S.RESULT_REQUEST_ALREADY_DONE)))}},{key:"getQuery",value:function(){return r.g.i(h,"getQuery",this.id),void 0===this.handler?(this.handler=this.smartLib.sessionManager.createProxySessionHandler(this),this.handler.getQuery()):(r.g.e(h,"Exception: getQuery error, the session is already running.",this.id),"")}},{key:"precacheEnded",value:function(){var e;null===(e=this.handler)||void 0===e||e.notifyPrecacheEnded()}},{key:"startStreamingSession",value:function(e,t){return r.g.i(h,"startStreamingSession with "+e+" and "+t,this.id),!(!d.A.typeParameter(e,"string","Error: requestedURL should be a string")||!d.A.typeParameter(t,"string","Error: redirectedURL should be a string")||(void 0===this.handler?(r.g.e(h,"Exception: startSteamingSession error, getQuery() should be called prior to startStreamingSession(...)"),1):this.handler instanceof o.A?(this.handler.start(e,t),0):(r.g.e(h,"Exception: startSteamingSession error, startStreamingSession(...) cannot be called once getURL(...) has been called"),1)))}},{key:"stopStreamingSession",value:function(e){return void 0!==e?r.g.i(h,"Stopping streaming session with statusCode "+e+"...",this.id):r.g.i(h,"Stopping streaming session...",this.id),void 0!==this.handler&&this.handler.stop(e),void 0!==this.playerAdapter&&this.playerAdapter.detachPlayer(),this.stopped=!0,this.handler}},{key:"isNanoCDNUsed",value:function(){return void 0!==this.handler&&this.handler.isNanoCDNUsed()}},{key:"getVideoMulticastStatus",value:function(){return void 0!==this.handler?this.handler.videoMulticastStatus:a.H.MULTICAST_INACTIVE}},{key:"getAudioMulticastStatus",value:function(){return void 0!==this.handler?this.handler.audioMulticastStatus:a.H.MULTICAST_INACTIVE}},{key:"getDataMulticastStatus",value:function(){return void 0!==this.handler?this.handler.dataMulticastStatus:a.H.MULTICAST_INACTIVE}},{key:"setMulticastStatusListener",value:function(e){r.g.d(h,"Multicast status listener "+(void 0!==e?"set":"unset"),this.id),this.multicastStatusListener=e}},{key:"getAdList",value:function(){var e,t;return(null===(e=this.handler)||void 0===e||null===(t=e.adTrackingManager)||void 0===t?void 0:t.adList)||[]}},{key:"getCurrentAd",value:function(){var e,t;return null===(e=this.handler)||void 0===e||null===(t=e.adTrackingManager)||void 0===t?void 0:t.getCurrentAd()}},{key:"getCurrentAdBreak",value:function(){var e,t;return null===(e=this.handler)||void 0===e||null===(t=e.adTrackingManager)||void 0===t?void 0:t.getCurrentAdBreak()}},{key:"getPositionForBookmark",value:function(){var e,t,i=null===(e=this.handler)||void 0===e||null===(t=e.adTrackingManager)||void 0===t?void 0:t.getPositionForBookmark();return void 0!==i?i:-1}},{key:"getPositionForPlayback",value:function(e){var t,i,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=null===(t=this.handler)||void 0===t||null===(i=t.adTrackingManager)||void 0===i?void 0:i.getPositionForPlayback(Number(e),n);return void 0!==r?r:-1}}]),e}()},7832:function(e,t,i){i.d(t,{H:function(){return s}});var n=i(8379),r=i(6506),a="BpkStreamingSessionOptions",s=function(){function e(){_classCallCheck(this,e),_defineProperty(this,"options",void 0),_defineProperty(this,"updates",void 0),this.options={},this.options[e.MULTICAST_ONLY]=!1,this.options[e.ULTRA_LOW_LATENCY_SUPPORT]=!1,this.options[e.PIP_SESSION]=!1,this.options[e.GDPR_PREFERENCE]=e.GDPR_CLEAR,this.options[e.REQUEST_REDIRECT_THIRD_PARTY_CDN]=!1,this.options[e.REQUEST_DIVERSITY]=!1,this.options[e.REQUEST_NANO_CDN]=!0,this.options[e.REQUEST_BROADPEAK_CDN]=!0,this.options[e.REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI]=!0,this.options[e.REQUEST_DIVERSITY_DYNAMIC_FAILOVER]=!1,this.options[e.REQUEST_NANO_CDN_DURING_GET_QUERY]=!0,this.options[e.REQUEST_BKYOU_IF_BROADPEAK_CDN]=!1,this.options[e.REQUEST_NANO_CDN_PRIORITY]=e.PRIORITY_NANO_CDN_CONF,this.options[e.SESSION_KEEPALIVE]=e.SESSION_ENABLED_IF_BROADPEAK_DOMAIN,this.options[e.SESSION_TEARDOWN]=e.SESSION_ENABLED_IF_BROADPEAK_DOMAIN,this.options[e.SESSION_REPORTING_MODE]=e.SESSION_REPORTING_MODE_DEFAULT,this.options[e.SESSION_KEEPALIVE_FREQUENCY]=r.A.KEEPALIVE_REQUEST_INTERVAL,this.options[e.SESSION_PRECACHE]=!1,this.options[e.USERAGENT_AD_EVENT]=void 0,this.options[e.TIMEOUT_NANO_CDN_REQUEST_ROUTER]=2e3,this.options[e.TIMEOUT_NANO_CDN_STATIC_RESOLVING]=3e3,this.options[e.TIMEOUT_NANO_CDN_DISCOVER_RESOLVING]=3e3,this.options[e.EXPERIMENTAL_METRICS_LATENCY]=!1,this.options[e.LEGACY_MULTICAST_ONLY]=!1,this.updates=[]}return _createClass(e,[{key:"option",value:function(t,i){return this.validate(t,i)&&(this.options[t]=i,t===e.PIP_SESSION&&!0===i&&this.option(e.REQUEST_NANO_CDN,!1),-1===this.updates.indexOf(t)&&this.updates.push(t)),this}},{key:"get",value:function(t){var i=[e.USERAGENT_AD_EVENT];return void 0===this.options[t]&&-1===i.indexOf(t)&&n.g.w(a,"Warning: get option "+t+" not found"),this.options[t]}},{key:"set",value:function(e,t){this.option(e,t)}},{key:"validate",value:function(t,i){if(t===e.SESSION_REPORTING_MODE)return i===e.SESSION_REPORTING_MODE_DEFAULT||i===e.SESSION_REPORTING_MODE_KEEPALIVE_TEARDOWN||i===e.SESSION_REPORTING_MODE_METRICS_RECEIVER||i===e.SESSION_REPORTING_MODE_DISABLED||(n.g.e(a,"Error: SESSION_REPORTING_MODE should be SESSION_REPORTING_MODE_DEFAULT, SESSION_REPORTING_MODE_KEEPALIVE_TEARDOWN, SESSION_REPORTING_MODE_METRICS_RECEIVER or SESSION_REPORTING_MODE_DISABLED"),!1);if(t===e.SESSION_KEEPALIVE||t===e.SESSION_TEARDOWN)return i===e.SESSION_DISABLED||i===e.SESSION_ENABLED_IF_BROADPEAK_DOMAIN||i===e.SESSION_ENABLED||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be SESSION_ENABLED_IF_BROADPEAK_DOMAIN, SESSION_DISABLED or SESSION_ENABLED"),!1);if(t===e.SESSION_KEEPALIVE_FREQUENCY)return"number"==typeof i&&i>=5e3&&i<=6e4||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be a number within 5000ms and 60000ms"),!1);if(t===e.USERAGENT_AD_EVENT)return"string"==typeof i||void 0===i||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be a string or undefined"),!1);if(t===e.GDPR_PREFERENCE)return"number"==typeof i&&i>=1&&i<=4||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be a number within 1 and 4"),!1);if(t>=e.TIMEOUT_NANO_CDN_REQUEST_ROUTER&&t<=e.TIMEOUT_NANO_CDN_DISCOVER_RESOLVING)return"number"==typeof i||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be a number"),!1);if(t===e.REQUEST_NANO_CDN_PRIORITY)return i>=e.PRIORITY_NANO_CDN_CONF&&i<=e.PRIORITY_LOAD_BALANCING||(n.g.e(a,"Error: "+e.optionToString(t)+" value should be PRIORITY_NANO_CDN_CONF, PRIORITY_APP_CONF or PRIORITY_LOAD_BALANCING"),!1);if(void 0===t)return n.g.e(a,"Error: option should not be undefined"),!1;var r="boolean"==typeof i;return r||n.g.e(a,"Error: "+e.optionToString(t)+" value should be a boolean"),r}},{key:"print",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2?arguments[2]:void 0;n.g.i(t,i+"Options",r),n.g.i(t,i+"   MULTICAST_ONLY="+this.get(e.MULTICAST_ONLY),r),n.g.i(t,i+"   LEGACY_MULTICAST_ONLY="+this.get(e.LEGACY_MULTICAST_ONLY),r),n.g.i(t,i+"   REQUEST_REDIRECT_THIRD_PARTY_CDN="+this.get(e.REQUEST_REDIRECT_THIRD_PARTY_CDN),r),n.g.i(t,i+"   REQUEST_DIVERSITY="+this.get(e.REQUEST_DIVERSITY),r),n.g.i(t,i+"   REQUEST_NANO_CDN="+this.get(e.REQUEST_NANO_CDN),r),n.g.i(t,i+"   REQUEST_NANO_CDN_DURING_GET_QUERY="+this.get(e.REQUEST_NANO_CDN_DURING_GET_QUERY),r),n.g.i(t,i+"   REQUEST_BROADPEAK_CDN="+this.get(e.REQUEST_BROADPEAK_CDN),r),n.g.i(t,i+"   REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI="+this.get(e.REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI),r),n.g.i(t,i+"   REQUEST_DIVERSITY_DYNAMIC_FAILOVER="+this.get(e.REQUEST_DIVERSITY_DYNAMIC_FAILOVER),r),n.g.i(t,i+"   REQUEST_BKYOU_IF_BROADPEAK_CDN="+this.get(e.REQUEST_BKYOU_IF_BROADPEAK_CDN),r),n.g.i(t,i+"   REQUEST_NANO_CDN_PRIORITY="+e.nanoCDNPriorityToString(this.get(e.REQUEST_NANO_CDN_PRIORITY)),r),n.g.i(t,i+"   SESSION_KEEPALIVE="+e.stateToString(this.get(e.SESSION_KEEPALIVE)),r),n.g.i(t,i+"   SESSION_KEEPALIVE_FREQUENCY="+this.get(e.SESSION_KEEPALIVE_FREQUENCY)+"ms",r),n.g.i(t,i+"   SESSION_TEARDOWN="+e.stateToString(this.get(e.SESSION_TEARDOWN)),r),n.g.i(t,i+"   SESSION_REPORTING_MODE="+e.reportingModeToString(this.get(e.SESSION_REPORTING_MODE)),r),n.g.i(t,i+"   SESSION_PRECACHE="+this.get(e.SESSION_PRECACHE),r),n.g.i(t,i+"   TIMEOUT_NANO_CDN_REQUEST_ROUTER="+this.get(e.TIMEOUT_NANO_CDN_REQUEST_ROUTER)+"ms",r),void 0!==this.get(e.USERAGENT_AD_EVENT)&&n.g.i(t,i+"   USERAGENT_AD_EVENT="+this.get(e.USERAGENT_AD_EVENT),r),n.g.i(t,i+"   PIP_SESSION="+this.get(e.PIP_SESSION),r),n.g.i(t,i+"   ULTRA_LOW_LATENCY_SUPPORT="+this.get(e.ULTRA_LOW_LATENCY_SUPPORT),r),n.g.i(t,i+"   GDPR_PREFERENCE="+this.get(e.GDPR_PREFERENCE),r)}},{key:"printUpdates",value:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2?arguments[2]:void 0;this.updates.forEach((function(a){n.g.i(i,r+"   "+e.optionToString(a)+"="+e.valueToString(a,t.get(a)),s)}))}}],[{key:"create",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=new e;return t instanceof e&&(n.options=_objectSpread({},t.options)),void 0!==i&&i.updates.forEach((function(e){n.options[e]=i.options[e]})),n}},{key:"optionToString",value:function(e){switch(e){case this.MULTICAST_ONLY:return"MULTICAST_ONLY";case this.PIP_SESSION:return"PIP_SESSION";case this.ULTRA_LOW_LATENCY_SUPPORT:return"ULTRA_LOW_LATENCY_SUPPORT";case this.GDPR_PREFERENCE:return"GDPR_PREFERENCE";case this.REQUEST_REDIRECT_THIRD_PARTY_CDN:return"REQUEST_REDIRECT_THIRD_PARTY_CDN";case this.REQUEST_DIVERSITY:return"REQUEST_DIVERSITY";case this.REQUEST_NANO_CDN:return"REQUEST_NANO_CDN";case this.REQUEST_BROADPEAK_CDN:return"REQUEST_BROADPEAK_CDN";case this.REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI:return"REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI";case this.REQUEST_DIVERSITY_DYNAMIC_FAILOVER:return"REQUEST_DIVERSITY_DYNAMIC_FAILOVER";case this.REQUEST_NANO_CDN_DURING_GET_QUERY:return"REQUEST_NANO_CDN_DURING_GET_QUERY";case this.REQUEST_BKYOU_IF_BROADPEAK_CDN:return"REQUEST_BKYOU_IF_BROADPEAK_CDN";case this.REQUEST_NANO_CDN_PRIORITY:return"REQUEST_NANO_CDN_PRIORITY";case this.SESSION_KEEPALIVE:return"SESSION_KEEPALIVE";case this.SESSION_TEARDOWN:return"SESSION_TEARDOWN";case this.SESSION_REPORTING_MODE:return"SESSION_REPORTING_MODE";case this.SESSION_KEEPALIVE_FREQUENCY:return"SESSION_KEEPALIVE_FREQUENCY";case this.SESSION_PRECACHE:return"SESSION_PRECACHE";case this.USERAGENT_AD_EVENT:return"USERAGENT_AD_EVENT";case this.TIMEOUT_NANO_CDN_REQUEST_ROUTER:return"TIMEOUT_NANO_CDN_REQUEST_ROUTER";case this.TIMEOUT_NANO_CDN_STATIC_RESOLVING:return"TIMEOUT_NANO_CDN_STATIC_RESOLVING";case this.TIMEOUT_NANO_CDN_DISCOVER_RESOLVING:return"TIMEOUT_NANO_CDN_DISCOVER_RESOLVING";case this.PRIORITY_NANO_CDN_CONF:return"PRIORITY_NANO_CDN_CONF";case this.PRIORITY_APP_CONF:return"PRIORITY_APP_CONF";case this.PRIORITY_LOAD_BALANCING:return"PRIORITY_LOAD_BALANCING";case this.LEGACY_MULTICAST_ONLY:return"LEGACY_MULTICAST_ONLY"}return"unknown"}},{key:"valueToString",value:function(e,t){switch(e){case this.SESSION_KEEPALIVE:case this.SESSION_TEARDOWN:return this.stateToString(t);case this.SESSION_REPORTING_MODE:return this.reportingModeToString(t);case this.REQUEST_NANO_CDN_PRIORITY:return this.nanoCDNPriorityToString(t)}return String(t)}},{key:"stateToString",value:function(t){switch(t){case e.SESSION_DISABLED:return"disabled";case e.SESSION_ENABLED_IF_BROADPEAK_DOMAIN:return"enabled if broadpeak domain name";case e.SESSION_ENABLED:return"enabled"}return"unknown"}},{key:"reportingModeToString",value:function(t){switch(t){case e.SESSION_REPORTING_MODE_DEFAULT:return"default";case e.SESSION_REPORTING_MODE_KEEPALIVE_TEARDOWN:return"keepalive/teardown";case e.SESSION_REPORTING_MODE_METRICS_RECEIVER:return"metricsReceiver";case e.SESSION_REPORTING_MODE_DISABLED:return"disabled"}return"unknown"}},{key:"nanoCDNPriorityToString",value:function(t){switch(t){case e.PRIORITY_NANO_CDN_CONF:return"nanoCDN conf";case e.PRIORITY_APP_CONF:return"init order";case e.PRIORITY_LOAD_BALANCING:return"load balancing"}return"unknown"}}]),e}();_defineProperty(s,"MULTICAST_ONLY",0),_defineProperty(s,"ULTRA_LOW_LATENCY_SUPPORT",1),_defineProperty(s,"PIP_SESSION",2),_defineProperty(s,"GDPR_PREFERENCE",3),_defineProperty(s,"REQUEST_REDIRECT_THIRD_PARTY_CDN",100),_defineProperty(s,"REQUEST_DIVERSITY",101),_defineProperty(s,"REQUEST_NANO_CDN",102),_defineProperty(s,"REQUEST_BROADPEAK_CDN",103),_defineProperty(s,"REQUEST_LOCAL_NANO_CDN_IF_CONNECTED_TO_WIFI",104),_defineProperty(s,"REQUEST_DIVERSITY_DYNAMIC_FAILOVER",105),_defineProperty(s,"REQUEST_NANO_CDN_DURING_GET_QUERY",106),_defineProperty(s,"REQUEST_BKYOU_IF_BROADPEAK_CDN",107),_defineProperty(s,"REQUEST_NANO_CDN_PRIORITY",108),_defineProperty(s,"SESSION_KEEPALIVE",200),_defineProperty(s,"SESSION_TEARDOWN",201),_defineProperty(s,"SESSION_REPORTING_MODE",202),_defineProperty(s,"SESSION_KEEPALIVE_FREQUENCY",203),_defineProperty(s,"SESSION_PRECACHE",204),_defineProperty(s,"USERAGENT_AD_EVENT",300),_defineProperty(s,"TIMEOUT_NANO_CDN_REQUEST_ROUTER",500),_defineProperty(s,"TIMEOUT_NANO_CDN_STATIC_RESOLVING",501),_defineProperty(s,"TIMEOUT_NANO_CDN_DISCOVER_RESOLVING",502),_defineProperty(s,"PRIORITY_NANO_CDN_CONF",600),_defineProperty(s,"PRIORITY_APP_CONF",601),_defineProperty(s,"PRIORITY_LOAD_BALANCING",602),_defineProperty(s,"EXPERIMENTAL_METRICS_LATENCY",900),_defineProperty(s,"LEGACY_MULTICAST_ONLY",1e3),_defineProperty(s,"SESSION_DISABLED",-1),_defineProperty(s,"SESSION_ENABLED_IF_BROADPEAK_DOMAIN",0),_defineProperty(s,"SESSION_ENABLED",1),_defineProperty(s,"SESSION_REPORTING_MODE_DEFAULT",0),_defineProperty(s,"SESSION_REPORTING_MODE_KEEPALIVE_TEARDOWN",1),_defineProperty(s,"SESSION_REPORTING_MODE_METRICS_RECEIVER",2),_defineProperty(s,"SESSION_REPORTING_MODE_DISABLED",-1),_defineProperty(s,"MULTICAST_UNKNOWN",-1),_defineProperty(s,"MULTICAST_INACTIVE",0),_defineProperty(s,"MULTICAST_INACTIVE_WAS_ACTIVE",1),_defineProperty(s,"MULTICAST_ACTIVE",2),_defineProperty(s,"GDPR_DELETE",1),_defineProperty(s,"GDPR_ANONYMIZED",2),_defineProperty(s,"GDPR_ENCRYPTED",3),_defineProperty(s,"GDPR_CLEAR",4)},3121:function(e,t,i){i.d(t,{S:function(){return n}});var n=function(){function e(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";_classCallCheck(this,e),_defineProperty(this,"url",void 0),_defineProperty(this,"diversityFailoverSession",void 0),_defineProperty(this,"errorCode",void 0),_defineProperty(this,"detailedErrorCode",void 0),_defineProperty(this,"detailedErrorMessage",void 0),_defineProperty(this,"nanoCDNUsed",void 0),_defineProperty(this,"nanoCDNDescr",void 0),this.url=t,this.errorCode=i,this.detailedErrorMessage=n.trim();var r=n.match("^([0-9]+).*");null!==r&&void 0!==r[1]?this.detailedErrorCode=parseInt(r[1],10):""===this.detailedErrorMessage?this.detailedErrorCode=e.RESULT_NO_DETAILED_ERROR:this.detailedErrorCode=e.RESULT_DETAILED_ERROR_PARSING_ERROR,this.nanoCDNUsed=!1,this.nanoCDNDescr=void 0}return _createClass(e,[{key:"getURL",value:function(){return this.url}},{key:"getFallbackURLList",value:function(){return void 0!==this.diversityFailoverSession?this.diversityFailoverSession.getFallbackURLList():[]}},{key:"isError",value:function(){return this.errorCode!==e.RESULT_NO_ERROR}},{key:"getErrorCode",value:function(){return this.errorCode}},{key:"getErrorMessage",value:function(){return e.getErrorMessage(this.errorCode)}},{key:"getDetailedErrorCode",value:function(){return this.detailedErrorCode}},{key:"getDetailedErrorMessage",value:function(){return this.detailedErrorMessage}},{key:"isNanoCDNUsed",value:function(){return this.nanoCDNUsed}}],[{key:"success",value:function(t){return new e(t,e.RESULT_NO_ERROR)}},{key:"error",value:function(t){return new e("",t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:"")}},{key:"getErrorMessage",value:function(t){switch(t){case e.RESULT_NO_ERROR:return"Result success. The session has been created.";case e.RESULT_NO_STREAMING_URL_FOUND:return"Request error. No streaming URL has been found. Default error when requesting the CDN (BkM/uCDN).";case e.RESULT_CDN_RESPONSE_UNREADABLE:return"Request error. The CDN response is unreadable (timeout error, CORS error, connection error).";case e.RESULT_REQUESTED_URL_NOT_FOUND_ON_CDN:return"Request error. Content not found on the CDN (error 4XX).";case e.RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN:return"Request error. Content not found on the nanoCDN (error 404, timeout error, CORS error or connection error).";case e.RESULT_REQUESTED_URL_CDN_ERROR:return"Request error. The CDN returned an unknown error (error 5XX).";case e.RESULT_NANOCDN_SESSION_LIMIT_REACHED:return"nanoCDN error. All nanoCDN on the network responded with session limit reached error.";case e.RESULT_NANOCDN_SESSION_LIMIT_REACHED_ON_REQUESTED_CHANNEL:return"nanoCDN error. All nanoCDN on the network responded with session limit reached error for the requested channel.";case e.RESULT_NANOCDN_SERVICE_UNAVAILABLE:return"nanoCDN error. All nanoCDN on the network have their service unavailable.";case e.RESULT_NANOCDN_CHANNEL_UNAVAILABLE:return"nanoCDN error. The requested channel is unavailable on all nanoCDN of the network.";case e.RESULT_NANOCDN_REQUEST_LOCAL_IF_CONNECTED_TO_WIFI_NOT_ALLOWED:return"nanoCDN error. A nanoCDN on 127.0.0.1 has been found and is connected to Wi-Fi but it is not allowed.";case e.RESULT_API_PARAMETER_FORMAT_ERROR:return"Api error. A method parameter does not have the right format.";case e.RESULT_API_NOT_INITIALIZED:return"Api error. SmartLib init not called";case e.RESULT_SESSION_HAS_BEEN_STOPPED_DURING_REQUEST:return"Workflow error. The session has been stopped during request.";case e.RESULT_REQUEST_ALREADY_DONE:return"Workflow error. The request has already been done for that session."}return"No error message for the error code "+t}}]),e}();_defineProperty(n,"RESULT_NO_ERROR",0),_defineProperty(n,"RESULT_NO_STREAMING_URL_FOUND",3100),_defineProperty(n,"RESULT_CDN_RESPONSE_UNREADABLE",3101),_defineProperty(n,"RESULT_REQUESTED_URL_NOT_FOUND_ON_CDN",3102),_defineProperty(n,"RESULT_REQUESTED_URL_NOT_FOUND_ON_NANOCDN",3103),_defineProperty(n,"RESULT_REQUESTED_URL_CDN_ERROR",3104),_defineProperty(n,"RESULT_NANOCDN_SESSION_LIMIT_REACHED",3201),_defineProperty(n,"RESULT_NANOCDN_SESSION_LIMIT_REACHED_ON_REQUESTED_CHANNEL",3202),_defineProperty(n,"RESULT_NANOCDN_SERVICE_UNAVAILABLE",3203),_defineProperty(n,"RESULT_NANOCDN_CHANNEL_UNAVAILABLE",3204),_defineProperty(n,"RESULT_NANOCDN_REQUEST_LOCAL_IF_CONNECTED_TO_WIFI_NOT_ALLOWED",3205),_defineProperty(n,"RESULT_NANOCDN_RESPONSE_UNREADABLE",3206),_defineProperty(n,"RESULT_API_PARAMETER_FORMAT_ERROR",3400),_defineProperty(n,"RESULT_API_NOT_INITIALIZED",3401),_defineProperty(n,"RESULT_SESSION_HAS_BEEN_STOPPED_DURING_REQUEST",3402),_defineProperty(n,"RESULT_REQUEST_ALREADY_DONE",3403),_defineProperty(n,"RESULT_NO_DETAILED_ERROR",0),_defineProperty(n,"RESULT_DETAILED_ERROR_PARSING_ERROR",-1)},3445:function(e,t,i){i.d(t,{A:function(){return n}});var n=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"formatDate",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t="";e<0&&(e*=-1,t="-");var i=e.getDate()<=9?"0"+e.getDate():e.getDate(),n=e.getMonth()+1<=9?"0"+(e.getMonth()+1):e.getMonth()+1,r=e.getHours()<=9?"0"+e.getHours():e.getHours(),a=e.getMinutes()<=9?"0"+e.getMinutes():e.getMinutes(),s=e.getSeconds()<=9?"0"+e.getSeconds():e.getSeconds(),o=String(e.getMilliseconds());return 1===o.length?o="00"+o:2===o.length&&(o="0"+o),t+n+"-"+i+" "+r+":"+a+":"+s+"."+o}},{key:"formatTime",value:function(e){var t="";e<0&&(e*=-1,t="-");var i=e%1e3,n=Math.floor(e/1e3%60),r=Math.floor(e/6e4%60),a=Math.floor(e/36e5%24),s=a<=9?"0"+a:a,o=r<=9?"0"+r:r,d=n<=9?"0"+n:n,l=String(i);return 1===l.length?l="00"+l:2===l.length&&(l="0"+l),t+s+":"+o+":"+d+"."+l}}]),e}()},8379:function(e,t,i){i.d(t,{$:function(){return a},g:function(){return c}});var n=i(3445),r=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"log",value:function(e,t,i){}}]),e}(),a=_createClass((function e(){_classCallCheck(this,e)}));_defineProperty(a,"VERBOSE",0),_defineProperty(a,"DEBUG",1),_defineProperty(a,"INFO",2),_defineProperty(a,"WARN",3),_defineProperty(a,"ERROR",4);var s=_createClass((function e(){_classCallCheck(this,e)}));_defineProperty(s,"NONE",-1),_defineProperty(s,"BASIC",0),_defineProperty(s,"VERBOSE",1);var o=new WeakMap,d=new WeakMap,l=new WeakMap,u=new WeakMap,c=function(){function e(){_classCallCheck(this,e),_classPrivateFieldInitSpec(this,o,{writable:!0,value:r}),_classPrivateFieldInitSpec(this,d,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,l,{writable:!0,value:void 0}),_classPrivateFieldInitSpec(this,u,{writable:!0,value:void 0}),_classPrivateFieldSet(this,d,[]),_classPrivateFieldSet(this,l,s.VERBOSE)}return _createClass(e,[{key:"init",value:function(e){_classPrivateFieldSet(this,o,e)}},{key:"release",value:function(){_classPrivateFieldSet(this,o,void 0)}},{key:"printLogs",value:function(e,t,i,r){if(_classPrivateFieldGet(this,d).length>100&&_classPrivateFieldSet(this,d,[]),void 0!==r&&(i="["+r+"] "+i),_classPrivateFieldGet(this,d).push(t+": "+i),void 0!==_classPrivateFieldGet(this,u)){var s,l=n.A.formatDate()+" ";switch(e){case a.VERBOSE:l+="V/";break;case a.DEBUG:l+="D/";break;case a.INFO:l+="I/";break;case a.WARN:l+="W/";break;case a.ERROR:l+="E/"}l+=t+": "+i,null===(s=_classPrivateFieldGet(this,u))||void 0===s||s.onLog(l)}else _classPrivateFieldGet(this,o).log(e,t,i,r)}},{key:"printVerboseLogs",value:function(e,t,i){_classPrivateFieldGet(this,l)<s.VERBOSE||this.printLogs(a.VERBOSE,e,t,i)}},{key:"printDebugLogs",value:function(e,t,i){_classPrivateFieldGet(this,l)<s.VERBOSE||this.printLogs(a.DEBUG,e,t,i)}},{key:"printInfoLogs",value:function(e,t,i){_classPrivateFieldGet(this,l)<=s.NONE||this.printLogs(a.INFO,e,t,i)}},{key:"printWarnLogs",value:function(e,t,i){_classPrivateFieldGet(this,l)<=s.NONE||this.printLogs(a.WARN,e,t,i)}},{key:"printErrorLogs",value:function(e,t,i){_classPrivateFieldGet(this,l)<=s.NONE||this.printLogs(a.ERROR,e,t,i)}},{key:"getLogs",value:function(){return _classPrivateFieldGet(this,d).join("\n")}},{key:"resetLogs",value:function(){_classPrivateFieldSet(this,d,[])}},{key:"getLogLevel",value:function(e){return _classPrivateFieldGet(this,l)}},{key:"setLogLevel",value:function(e){_classPrivateFieldSet(this,l,e)}},{key:"setLogRedirectListener",value:function(e){_classPrivateFieldSet(this,u,e)}}],[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}},{key:"v",value:function(t,i,n){e.getInstance().printVerboseLogs(t,i,n)}},{key:"d",value:function(t,i,n){e.getInstance().printDebugLogs(t,i,n)}},{key:"i",value:function(t,i,n){e.getInstance().printInfoLogs(t,i,n)}},{key:"w",value:function(t,i,n){e.getInstance().printWarnLogs(t,i,n)}},{key:"e",value:function(t,i,n){e.getInstance().printErrorLogs(t,i,n)}}]),e}();_defineProperty(c,"instance",void 0)},4943:function(e,t,i){i.d(t,{A:function(){return n}});var n=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"randomIntFromInterval",value:function(e,t){return Math.floor(Math.random()*(t-e+1)+e)}},{key:"bufferToBase64Lookup",value:function(e){return e<26?String.fromCharCode(e+"A".charCodeAt(0)):e<52?String.fromCharCode(e-26+"a".charCodeAt(0)):e<62?String.fromCharCode(e-52+"0".charCodeAt(0)):62===e?"-":63===e?"_":""}},{key:"bufferToBase64",value:function(t){var i,n=t.data(),r=t.length(),a="";for(i=0;i<r;i+=3){var s=[void 0,void 0,void 0,void 0];s[0]=n[i]>>2,s[1]=(3&n[i])<<4,r>i+1&&(s[1]|=n[i+1]>>4,s[2]=(15&n[i+1])<<2),r>i+2&&(s[2]|=n[i+2]>>6,s[3]=63&n[i+2]);for(var o=0;o<s.length;o++)void 0===s[o]||(a+=e.bufferToBase64Lookup(s[o]))}return a}},{key:"stringToBase64",value:function(t){var i="",n="",r=t.length%3;if(r>0)for(;r<3;r++)n+="=",t+="\0";for(r=0;r<t.length;r+=3){var a=(t.charCodeAt(r)<<16)+(t.charCodeAt(r+1)<<8)+t.charCodeAt(r+2);a=[a>>>18&63,a>>>12&63,a>>>6&63,63&a],i+=e.BASE64_CHARS[a[0]]+e.BASE64_CHARS[a[1]]+e.BASE64_CHARS[a[2]]+e.BASE64_CHARS[a[3]]}return i.substring(0,i.length-n.length)+n}},{key:"base64ToString",value:function(t){var i="="===(t=t.replace(new RegExp("[^"+e.BASE64_CHARS.split("")+"=]","g"),"")).charAt(t.length-1)?"="===t.charAt(t.length-2)?"AA":"A":"",n="";t=t.substr(0,t.length-i.length)+i;for(var r=0;r<t.length;r+=4){var a=(e.BASE64_CHARS.indexOf(t.charAt(r))<<18)+(e.BASE64_CHARS.indexOf(t.charAt(r+1))<<12)+(e.BASE64_CHARS.indexOf(t.charAt(r+2))<<6)+e.BASE64_CHARS.indexOf(t.charAt(r+3));n+=String.fromCharCode(a>>>16&255,a>>>8&255,255&a)}return n.substring(0,n.length-i.length)}},{key:"floor",value:function(e){return e>>>0}},{key:"bufferToString",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.length,i="[0x",n=0;n<t;n++){var r=e[n];i+=("00"+Number(r).toString(16)).slice(-2),n<t-1&&(i+=",0x")}return i+="] (capacity:"+e.length+")"}}]),e}();_defineProperty(n,"BASE64_CHARS","ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/")},7964:function(e,t,i){i.d(t,{A:function(){return n}});var n=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"parseURL",value:function(e){if(!this.isValidUrl(e))return null;var t=e.match("^(https?)\\:\\/\\/(([^:\\/?#]*)(?:\\:([0-9]+))?)([\\/]{0,1}[^?#]*)(\\?[^#]*|)(#.*|)$"),i=t&&{href:e,protocol:t[1],host:t[2],hostname:t[3],port:t[4],path:t[5],query:t[6],hash:t[7]};return null!==i&&null!==i.query&&i.query.length>0&&(i.query=i.query.substring(1)),i}},{key:"getParam",value:function(e,t){if(!this.isValidUrl(e))return"";t=t.replace(/[\[\]]/g,"\\$&");var i=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)").exec(e);return i&&i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):""}},{key:"addOrReplaceParam",value:function(e,t,i){var n=e.indexOf("?"+t+"=");if(n<0&&(n=e.indexOf("&"+t+"=")),n>=0){var r=e.substring(0,e.indexOf(t)),a=e.substring(e.indexOf(t));return r+t+"="+i+(a=a.indexOf("&")>=0?a.substring(a.indexOf("&")):"")}return(e.indexOf("?")<0?e+"?":e+"&")+t+"="+i}},{key:"extractHostnamePort",value:function(e){return this.isValidUrl(e)?(e.indexOf("://")>-1?e.split("/")[2]:e.split("/")[0]).split("?")[0]:""}},{key:"extractProtocol",value:function(e){return this.isValidUrl(e)?e.split("://")[0]:""}},{key:"isValidUrl",value:function(e){return null!==e&&""!==e&&void 0!==e}},{key:"isRelativeUrl",value:function(e){return e.startsWith("/")||e.startsWith("../")}},{key:"patchRelativeUrl",value:function(e,t){var i=this.parseURL(t),n=i.path;if(e.startsWith("../")){n=n.slice(0,n.lastIndexOf("/")+1)+e;var r=[];n.replace(/^(\.\.?(\/|$))+/,"").replace(/\/(\.(\/|$))+/g,"/").replace(/\/\.\.$/,"/../").replace(/\/?[^\/]*/g,(function(e){"/.."===e?r.pop():r.push(e)})),n=r.join("").replace(/^\//,"/")}else n=e;return i.protocol+"://"+i.host+n}}]),e}();_defineProperty(n,"REGEX_IP","^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}(:[0-9]+)?$"),_defineProperty(n,"REGEX_HOSTNAME","^([A-Za-z0-9\\_\\-\\.]+)(:[0-9]+)?$"),_defineProperty(n,"REGEX_TICKET","^[0-5a-z]{30,50}$"),_defineProperty(n,"REGEX_TICKET_TRANSCODED","^.*@[0-5a-z]{30,50}$")},5941:function(e,t,i){i.d(t,{A:function(){return a}});var n=i(8379),r="BpkValidator",a=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"typeParameter",value:function(e,t,i){return _typeof(e)===t||(n.g.e(r,i),!1)}},{key:"typeOrUndefinedParameter",value:function(e,t,i){return _typeof(e)===t||void 0===e||(n.g.e(r,i),!1)}},{key:"typeOrNullParameter",value:function(e,t,i){return _typeof(e)===t||null===e||(n.g.e(r,i),!1)}},{key:"stringNonEmpty",value:function(e,t){return"string"==typeof e&&e.length>0||(n.g.e(r,t),!1)}},{key:"toBkAString",value:function(e){return void 0!==e&&"string"==typeof e?e.replace(/\n/g,"").replace(/\t/g,"").replace(/\r/g,""):e}}]),e}()}},__webpack_module_cache__={},deferred;function __nested_webpack_require_274918__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var i=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](i,i.exports,__nested_webpack_require_274918__),i.exports}__nested_webpack_require_274918__.m=__webpack_modules__,deferred=[],__nested_webpack_require_274918__.O=function(e,t,i,n){if(!t){var r=1/0;for(d=0;d<deferred.length;d++){t=deferred[d][0],i=deferred[d][1],n=deferred[d][2];for(var a=!0,s=0;s<t.length;s++)(!1&n||r>=n)&&Object.keys(__nested_webpack_require_274918__.O).every((function(e){return __nested_webpack_require_274918__.O[e](t[s])}))?t.splice(s--,1):(a=!1,n<r&&(r=n));if(a){deferred.splice(d--,1);var o=i();void 0!==o&&(e=o)}}return e}n=n||0;for(var d=deferred.length;d>0&&deferred[d-1][2]>n;d--)deferred[d]=deferred[d-1];deferred[d]=[t,i,n]},__nested_webpack_require_274918__.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return __nested_webpack_require_274918__.d(t,{a:t}),t},__nested_webpack_require_274918__.d=function(e,t){for(var i in t)__nested_webpack_require_274918__.o(t,i)&&!__nested_webpack_require_274918__.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},__nested_webpack_require_274918__.g=function(){if("object"==("undefined"==typeof globalThis?"undefined":_typeof(globalThis)))return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==("undefined"==typeof window?"undefined":_typeof(window)))return window}}(),__nested_webpack_require_274918__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__nested_webpack_require_274918__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e={152:0};__nested_webpack_require_274918__.O.j=function(t){return 0===e[t]};var t=function(t,i){var n,r,a=i[0],s=i[1],o=i[2],d=0;if(a.some((function(t){return 0!==e[t]}))){for(n in s)__nested_webpack_require_274918__.o(s,n)&&(__nested_webpack_require_274918__.m[n]=s[n]);if(o)var l=o(__nested_webpack_require_274918__)}for(t&&t(i);d<a.length;d++)r=a[d],__nested_webpack_require_274918__.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return __nested_webpack_require_274918__.O(l)},i=("undefined"!=typeof self?self:__webpack_require__.g).webpackChunkSmartLibModule=("undefined"!=typeof self?self:__webpack_require__.g).webpackChunkSmartLibModule||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))}();var __nested_webpack_exports__={};__nested_webpack_require_274918__.r(__nested_webpack_exports__),__nested_webpack_require_274918__.d(__nested_webpack_exports__,{AdInteractionType:function(){return core__WEBPACK_IMPORTED_MODULE_0__.AdInteractionType},AdManager:function(){return core__WEBPACK_IMPORTED_MODULE_0__.X2},LoggerManager:function(){return core__WEBPACK_IMPORTED_MODULE_0__.gD},RequestManager:function(){return core__WEBPACK_IMPORTED_MODULE_0__.Je},SmartLib:function(){return core__WEBPACK_IMPORTED_MODULE_0__.x6},StreamingSession:function(){return core__WEBPACK_IMPORTED_MODULE_0__.fF},StreamingSessionOptions:function(){return core__WEBPACK_IMPORTED_MODULE_0__.H1},StreamingSessionResult:function(){return core__WEBPACK_IMPORTED_MODULE_0__.Sy}});var core__WEBPACK_IMPORTED_MODULE_0__=__nested_webpack_require_274918__(1262),_engine_CoreEngine__WEBPACK_IMPORTED_MODULE_1__=__nested_webpack_require_274918__(8100);return _engine_CoreEngine__WEBPACK_IMPORTED_MODULE_1__.A.getInstance().init(),__nested_webpack_exports__=__nested_webpack_require_274918__.O(__nested_webpack_exports__),__nested_webpack_exports__}()},"object"==_typeof(exports)&&"object"==_typeof(module)?module.exports=t():(__WEBPACK_AMD_DEFINE_ARRAY__=[],void 0===(__WEBPACK_AMD_DEFINE_RESULT__="function"==typeof(__WEBPACK_AMD_DEFINE_FACTORY__=t)?__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__):__WEBPACK_AMD_DEFINE_FACTORY__)||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__))},9397:function(e,t,i){"use strict";function n(){n=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",d=r.toStringTag||"@@toStringTag";function l(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,i){return e[t]=i}}function u(e,t,i,n){var r=t&&t.prototype instanceof v?t:v,a=Object.create(r.prototype),s=new D(n||[]);return a._invoke=function(e,t,i){var n="suspendedStart";return function(r,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===r)throw a;return R()}for(i.method=r,i.arg=a;;){var s=i.delegate;if(s){var o=A(s,i);if(o){if(o===h)continue;return o}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var d=c(e,t,i);if("normal"===d.type){if(n=i.done?"completed":"suspendedYield",d.arg===h)continue;return{value:d.arg,done:i.done}}"throw"===d.type&&(n="completed",i.method="throw",i.arg=d.arg)}}}(e,i,s),a}function c(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var h={};function v(){}function p(){}function f(){}var y={};l(y,a,(function(){return this}));var g=Object.getPrototypeOf,S=g&&g(g(P([])));S&&S!==t&&i.call(S,a)&&(y=S);var _=f.prototype=v.prototype=Object.create(y);function E(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function m(e,t){function n(r,a,o,d){var l=c(e[r],e,a);if("throw"!==l.type){var u=l.arg,h=u.value;return h&&"object"==s(h)&&i.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,o,d)}),(function(e){n("throw",e,o,d)})):t.resolve(h).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,d)}))}d(l.arg)}var r;this._invoke=function(e,i){function a(){return new t((function(t,r){n(e,i,t,r)}))}return r=r?r.then(a,a):a()}}function A(e,t){var i=e.iterator[t.method];if(void 0===i){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,A(e,t),"throw"===t.method))return h;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var n=c(i,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,h;var r=n.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function P(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:R}}function R(){return{value:void 0,done:!0}}return p.prototype=f,l(_,"constructor",f),l(f,"constructor",p),p.displayName=l(f,d,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,l(e,d,"GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},E(m.prototype),l(m.prototype,o,(function(){return this})),e.AsyncIterator=m,e.async=function(t,i,n,r,a){void 0===a&&(a=Promise);var s=new m(u(t,i,n,r),a);return e.isGeneratorFunction(i)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},E(_),l(_,d,"Generator"),l(_,a,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var i in e)t.push(i);return t.reverse(),function i(){for(;t.length;){var n=t.pop();if(n in e)return i.value=n,i.done=!1,i}return i.done=!0,i}},e.values=P,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(N),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(i,n){return s.type="throw",s.arg=e,t.next=i,n&&(t.method="next",t.arg=void 0),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],s=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var o=i.call(a,"catchLoc"),d=i.call(a,"finallyLoc");if(o&&d){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!d)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var s=a?a.completion:{};return s.type=e,s.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),N(i),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var r=n.arg;N(i)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:P(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),h}},e}function r(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,d=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return o=e.done,e},e:function(e){d=!0,s=e},f:function(){try{o||null==i.return||i.return()}finally{if(d)throw s}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function o(){return o="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=f(e)););return e}(e,t);if(n){var r=Object.getOwnPropertyDescriptor(n,t);return r.get?r.get.call(arguments.length<3?e:i):r.value}},o.apply(this,arguments)}function d(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t,i){return t&&d(e.prototype,t),i&&d(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=f(e);if(t){var r=f(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return function(e,t){if(t&&("object"===s(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return p(e)}(this,i)}}function p(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}var y=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(r,a){function s(e){try{d(n.next(e))}catch(e){a(e)}}function o(e){try{d(n.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,o)}d((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerBPKExtended=void 0;var g=i(7875),S=i(4695),_=i(5417),E=i(5102),m=i(4154);i(3846),i(4154),i(4239);var A=function(e){c(i,e);var t=v(i);function i(e){var n;return u(this,i),(n=t.call(this)).getPlayerName=function(){return"VOPlayer"},n.getVersion=function(){return _.Player.version},n.getOSName=function(){return n.client.getOS()},n.getDeviceVersion=function(){return n.client.getOSVersion()},n.getDeviceType=function(){return _.Player.isSmartTv()?"smarttv":"browser"},n.getTotalDuration=function(){return n.player?n.player.isLive?0:1e3*n.player.duration:-1},n.getCurrentPosition=function(){return n.player?1e3*n.player.currentTime:-1},n.getCurrentBitrate=function(){return n.player&&n.player.quality&&n.player.quality>=0&&n.player.qualities&&n.player.qualities.length>0&&n.player.qualities[n.player.quality]?n.player.qualities[n.player.quality].bandwidth/1e3:-1},n.getCapabilities=function(){return n.playerConfiguration_.enableSmartLibAdvertisement?{adTracking:!0}:{adTracking:!1}},n.client=new g.ClientJS,n.player=e,n.playerConfiguration_=e.getConfiguration(),n}return l(i)}(m.GenericPlayerApi),k=function(e){c(i,e);var t=v(i);function i(e){var n;return u(this,i),(n=t.call(this,e)).handlePlaying_=function(){return n._playerWraper.notifyFirstImage()},n.onerror_=function(e){var t=E.SmartLib.BPUnspecifiedError;switch(e.category){case 1:t=E.SmartLib.BPNetworkingError;break;case 2:case 3:case 4:case 5:t=E.SmartLib.BPDecodingError;break;case 6:t=E.SmartLib.BPAccessRightError;break;default:t=E.SmartLib.BPUnspecifiedError}n._sessionStarted=!1,null!==n.smartlibSession_&&(n.smartlibSession_.stopStreamingSession(t),n.smartlibSession_=null)},n.onreset_=function(){n._sessionStarted&&(n._sessionStarted=!1,null!==n.smartlibSession_&&(n.smartlibSession_.stopStreamingSession(),n.smartlibSession_=null))},n.onloaded_=function(){voplayer.Log.debug("[PlayerBPKExtended] loaded"),n._sessionStarted=!0},n.onended_=function(){n.onreset_()},E.SmartLib?(n.smartlib_=E.SmartLib.getInstance(),n._playerWraper=new A(p(n))):(n.smartlib_=null,voplayer.Log.alwaysWarn("[PlayerBPKExtended] constructor: Impossible to find Broadpeak Smartlib library in current window context!")),n.playerConfiguration_=n.getConfiguration(),n._sessionStarted=!1,n.attached_=!1,n.smartlibSession_=null,n._registerForDashEvents(),i.smartlibInitializedStatic_&&n._attachSmartLib(),n}return l(i,[{key:"initSmartLib",value:function(e,t,n){o(f(i.prototype),"isVideoElementProvided",this).call(this)&&!i.smartlibInitializedStatic_&&(voplayer.Log.log("[PlayerBPKExtended] initSmartLib: analyticsAddress,nanoCDNHost,broadpeakDomainNames "+e+", "+t+", "+n),this.smartlib_&&(this.smartlib_.init(e,t,n),this._attachSmartLib()))}},{key:"releaseSmartLib",value:function(){o(f(i.prototype),"isVideoElementProvided",this).call(this)&&(voplayer.Log.log("[PlayerBPKExtended] releaseSmartLib"),this.off("dashevent",this._onDashEvent.bind(this)),this.off("error",this.onerror_),this.off("reset",this.onreset_),this.off("loaded",this.onloaded_),this.off("ended",this.onended_),this.videoElement.removeEventListener("playing",this.handlePlaying_),null!==this.smartlibSession_&&(this.smartlibSession_.stopStreamingSession(),this.smartlibSession_=null),this.smartlib_&&!i.smartlibInitializedStatic_&&this.smartlib_.release(),this.attached_=!1)}},{key:"smartlibInstance",get:function(){return o(f(i.prototype),"isVideoElementProvided",this).call(this)?this.smartlib_:null}},{key:"smartlibSession",get:function(){return o(f(i.prototype),"isVideoElementProvided",this).call(this)?this.smartlibSession_:null}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return o(f(i.prototype),"reset",this).call(this,e)}},{key:"destroy",value:function(){if(o(f(i.prototype),"isVideoElementProvided",this).call(this))return this.smartlib_&&!i.smartlibInitializedStatic_&&(this.releaseSmartLib(),this.smartlib_=null),o(f(i.prototype),"destroy",this).call(this)}},{key:"load",value:function(e,t){var a=this,d=Object.create(null,{isVideoElementProvided:{get:function(){return o(f(i.prototype),"isVideoElementProvided",a)}},load:{get:function(){return o(f(i.prototype),"load",a)}}});return y(this,void 0,void 0,n().mark((function i(){var a,o,l,u,c,h,v,p,f,y,g,S,_,E,m,A=this;return n().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(d.isVideoElementProvided.call(this)){i.next=2;break}return i.abrupt("return");case 2:if(null!==this.smartlibSession_&&(this.smartlibSession_.stopStreamingSession(),this.smartlibSession_=null),this.initPsrIfNeeded(),e&&"object"===s(e)){i.next=6;break}return i.abrupt("return",d.load.call(this,e,t));case 6:if(this.attached_&&this.smartlib_&&(this.smartlibSession_=this.smartlib_.createStreamingSession(),voplayer.Log.debug("[PlayerBPKExtended] load: created smartlib session",this.smartlibSession_),void 0===this.smartlibSession_?(this.smartlibSession_=null,voplayer.Log.warning("PlayerBPKExtended : load - smartlib session creation failed")):this.smartlibSession_.attachPlayer(this._playerWraper)),this.smartlibSession_&&this.playerConfiguration_.enableSmartLibAdvertisement&&(this.smartlibSession_.setAdEventsListener({onAdBreakBegin:function(e){voplayer.Log.debug("[PlayerBPKExtended] onAdBreakBegin event received",e),A.parseSmartInfo("onAdBreakBegin",null,e)},onAdBegin:function(e,t){voplayer.Log.debug("[PlayerBPKExtended] onAdBegin event received",t,e),A.parseSmartInfo("onAdBegin",e,t)},onAdSkippable:function(e,t,i,n,r){voplayer.Log.debug("[PlayerBPKExtended] onAdSkippable event received",t,e,i,n,r)},onAdEnd:function(e,t){voplayer.Log.debug("[PlayerBPKExtended] onAdEnd event received",t,e),A.parseSmartInfo("onAdEnd",e,t)},onAdBreakEnd:function(e){voplayer.Log.debug("[PlayerBPKExtended] onAdBreakBegin event received",e),A.parseSmartInfo("onAdBreakEnd",null,e)}}),this.smartlibSession_.activateAdvertising()),a=this.filterAndSanitizeMediaUrl(e.url),o=a.sanitizedUrl,l=e.adRequestUrl,u=a.voVMAPUrl||e.vmapUrl,c=a.voVASTUrl||e.vastUrl,h=e.mastUrl,this.setStartTime(e.startTime,t,o),!l){i.next=28;break}return voplayer.Log.debug("[PlayerBPKExtended] load: player load calling loadAdrequest_ with adRequestUrl, vmapUrl, vastUrl, mastUrl ",l,u,c,h),i.prev=17,i.next=20,this.loadAdrequest_(l,!1);case 20:i.next=25;break;case 22:i.prev=22,i.t0=i.catch(17),voplayer.Log.debug("[PlayerBPKExtended]  load: loadAdRequest error",i.t0);case 25:voplayer.Log.debug("[PlayerBPKExtended]  load: player load return loadAdrequest_"),i.next=70;break;case 28:if(!u){i.next=44;break}return voplayer.Log.debug("[PlayerBPKExtended]  load: load with vmap"),voplayer.Log.debug("[PlayerBPKExtended]  load: player load calling loadAdrequest_ with adRequestUrl, vmapUrl, vastUrl, mastUrl",l,u,c,h),i.prev=31,i.next=34,this.loadAdrequest_(u,!1,"VMAP");case 34:i.next=39;break;case 36:i.prev=36,i.t1=i.catch(31),voplayer.Log.debug("[PlayerBPKExtended] load: loadAdRequest error",i.t1);case 39:if(voplayer.Log.debug("[PlayerBPKExtended] load: player load return loadAdrequest_"),v=null,this.adManager&&this.adManager.vmap&&(p=this.adManager.vmap.extensions,voplayer.Log.debug("[PlayerBPKExtended] load: vmap exts",p),p.forEach((function(e){var t=A._parseVOVMAPExtension(e);voplayer.Log.debug("[PlayerBPKExtended] load: ext",e),null!==t&&(voplayer.Log.debug("[PlayerBPKExtended] load: Found vo extenstion",t),v=t.vovmapurl)})),v&&this.smartlibSession_)){this.adManager.reset(),(f=new URL(v).search).length>0&&(f=f.substring(1)),y=f.split("&"),g=r(y);try{for(g.s();!(S=g.n()).done;)_=S.value,E=_.split("="),voplayer.Log.debug("[PlayerBPKExtended] load: smartlib adParameter "+E[0]+" = "+E[1]),this.smartlibSession_.setAdParameter(E[0],E[1])}catch(e){g.e(e)}finally{g.f()}}i.next=70;break;case 44:if(!c){i.next=58;break}return voplayer.Log.debug("[PlayerBPKExtended] load: load with vast URL"),voplayer.Log.debug("[PlayerBPKExtended] load: player load calling loadAdrequest_ with adRequestUrl, vmapUrl, vastUrl, mastUrl",l,u,c,h),i.prev=47,i.next=50,this.loadAdrequest_(c,!1,"VAST");case 50:i.next=55;break;case 52:i.prev=52,i.t2=i.catch(47),voplayer.Log.debug("[PlayerBPKExtended] load: loadAdRequest error",i.t2);case 55:voplayer.Log.debug("[PlayerBPKExtended] load: player load return loadAdrequest_"),i.next=70;break;case 58:if(!h){i.next=70;break}return voplayer.Log.debug("[PlayerBPKExtended] load: load with mast URL"),voplayer.Log.debug("[PlayerBPKExtended] load: player load calling loadAdrequest_ with adRequestUrl, vmapUrl, vastUrl, mastUrl ",l,u,c,h),i.prev=61,i.next=64,this.loadAdrequest_(h,!1,"MAST");case 64:i.next=69;break;case 66:i.prev=66,i.t3=i.catch(61),voplayer.Log.debug("[PlayerBPKExtended] load: loadAdRequest error",i.t3);case 69:voplayer.Log.debug("[PlayerBPKExtended] load: player load return loadAdrequest_");case 70:return i.prev=70,i.next=73,this.getSmartLibUrl(o);case 73:o=i.sent,voplayer.Log.debug("[PlayerBPKExtended] load: final URL after smartlib getURL = "+o),i.next=81;break;case 77:return i.prev=77,i.t4=i.catch(70),voplayer.Log.warning("[PlayerBPKExtended] load: smartlib getURL error",i.t4),i.abrupt("return",Promise.reject(i.t4));case 81:return m={url:o,mimeType:e.mimeType,startTime:e.startTime,imageDuration:e.imageDuration?e.imageDuration:null,preserveImageAspectRatio:e.preserveImageAspectRatio?e.preserveImageAspectRatio:null,imageAspectRatio:e.imageAspectRatio?e.imageAspectRatio:null},i.abrupt("return",d.load.call(this,m,t));case 83:case"end":return i.stop()}}),i,this,[[17,22],[31,36],[47,52],[61,66],[70,77]])})))}},{key:"_registerForDashEvents",value:function(){this.on("dashevent",this._onDashEvent.bind(this))}},{key:"_onDashEvent",value:function(e){if("timelineregionenter"===e.type&&e.detail&&"urn:mpeg:dash:event:callback:2015"===e.detail.schemeIdUri&&e.detail.eventElement){var t=e.detail.eventElement;if(t.textContent){var i=t.textContent;(i.indexOf("smartlib.broadpeak.tv")>0||i.indexOf("smartadserver.com")>0)&&(voplayer.Log.debug("[PlayerBPKExtended] _onDashEvent: timelineregionenter, found smartlib callback URL",i),this.adManager.sendNotificationUrlRequest(i))}}}},{key:"_attachSmartLib",value:function(){this.attached_?voplayer.Log.warning("[PlayerBPKExtended] _attachSmartLib: Smartlib already attached"):(this.attached_=!0,this.on("error",this.onerror_),this.on("reset",this.onreset_),this.on("loaded",this.onloaded_),this.on("ended",this.onended_),this.videoElement.addEventListener("playing",this.handlePlaying_))}},{key:"_parseVOVMAPExtension",value:function(e){try{if("vomode"===e.attributes.type){var t=e.children["voextension:Mode"],i={mode:null,vovmapurl:null};return i.mode=t.attributes.mode,i.vovmapurl=t.children["voextension:Vovmapurl"].value,i}}catch(e){voplayer.Log.debug("[PlayerBPKExtended] _parseVOVMAPExtension: error parsing vovmap extension",e)}return null}},{key:"getSmartLibUrl",value:function(e){var t=this;return voplayer.Log.info("[PlayerBPKExtended] getSmartLibUrl: START url,typeof url"+e+"  "+s(e)),new Promise((function(i,n){voplayer.Log.info("[PlayerBPKExtended] getSmartLibUrl: start getURL"),t.smartlibSession_?t.smartlibSession_.getURL(e).then(function(e){voplayer.Log.info("[PlayerBPKExtended] getSmartLibUrl: smartlibSession_.getURL res ",e),e.isError()?n("Smartlib error - no redirected URL "+e.errorCode):i(e.getURL())}.bind(t)):(voplayer.Log.warning("[PlayerBPKExtended] getSmartLibUrl: no SmartlibSession > return same URL"),i(e))}))}},{key:"parseSmartInfo",value:function(e,t,i,n,r,a){var s,o,d,l,u,c,h,v,p=[],f=null;if("onAdBreakBegin"===e&&i){this.adInfoArray_=[],this.adBreakInfo_={activeAdIdx:null,duration:null,nbAds:null,id:null,position:null,start:null,type:null,adIds:null,adInfos:null,url:null},this.adBreakInfo_.duration=0,this.adBreakInfo_.nbAds=i.ads.length,this.adBreakInfo_.id=i.id,this.adBreakInfo_.position=S.AdvertisementPosition.MIDROLL,this.adBreakInfo_.start=i.startPosition,this.adBreakInfo_.type=S.AdvertisementType.LINEAR,i.url&&(this.adBreakInfo_.url=i.url);for(var y=0;y<i.ads.length;y++){p.push(i.ads[y].adId);var g={id:null,mimeType:null,start:null,duration:null,type:null,media:null,clickThroughUrl:null,skipDelay:null,url:null};g.id=null!==(s=i.ads[y].adId)&&void 0!==s?s:null,g.start=null!==(o=i.ads[y].startPosition)&&void 0!==o?o:null,g.duration=null!==(d=i.ads[y].duration)&&void 0!==d?d:null,g.type=S.AdvertisementType.LINEAR,null!==i.ads[y].mimeType&&(g.mimeType=i.ads[y].mimeType),null!==i.ads[y].media&&(g.media=i.ads[y].media),null!==i.ads[y].clickThroughUrl&&(g.clickThroughUrl=i.ads[y].clickThroughUrl),null!==i.ads[y].skipDelay&&(g.skipDelay=i.ads[y].skipDelay),null!==i.ads[y].url&&(g.url=i.ads[y].url),this.adBreakInfo_.duration+=null!==(l=i.ads[y].duration)&&void 0!==l?l:0,this.adInfoArray_.push(g)}this.adBreakInfo_.adIds=p,this.adBreakInfo_.adInfos=this.adInfoArray_}var E=this.adBreakInfo_;switch(t&&(f=this.adInfoArray_[t.index+1],E.activeAdIdx=t.index+1),e){case"onAdBreakBegin":this.emitEvent(_.Player.AD_BREAK_STARTED,{adBreakInfo:this.adBreakInfo_,breakId:i.id,breakAdIds:p,position:null,breakType:S.AdvertisementType.LINEAR});break;case"onAdBegin":this.emitEvent(_.Player.AD_STARTED,{adBreakInfo:this.adBreakInfo_,adInfo:this.adInfoArray_,id:null!==(u=null==f?void 0:f.adId)&&void 0!==u?u:null,clickThroughUrl:null!==(c=null==f?void 0:f.clickThroughUrl)&&void 0!==c?c:null,adType:S.AdvertisementType.LINEAR,media:null!==(h=null==f?void 0:f.media)&&void 0!==h?h:null});break;case"onAdSkippable":default:break;case"onAdEnd":this.emitEvent(_.Player.AD_ENDED,{adBreakInfo:this.adBreakInfo_,adInfo:this.adInfoArray_,id:null!==(v=null==f?void 0:f.adId)&&void 0!==v?v:null,adType:S.AdvertisementType.LINEAR});break;case"onAdBreakEnd":this.emitEvent(_.Player.AD_BREAK_ENDED,{adBreakInfo:this.adBreakInfo_,breakId:i.id,breakType:S.AdvertisementType.LINEAR})}}}],[{key:"initSmartLib",value:function(e,t,n){E.SmartLib?(E.SmartLib.getInstance().init(e,t,n),i.smartlibInitializedStatic_=!0):voplayer.Log.alwaysWarn("VO Player with Smartlib extension - Impossible to find Broadpeak Smartlib library in current window context!")}},{key:"releaseSmartLib",value:function(){E.SmartLib?(E.SmartLib.getInstance().release(),i.smartlibInitializedStatic_=!1):voplayer.Log.alwaysWarn("VO Player with Smartlib extension - Impossible to find Broadpeak Smartlib library in current window context!")}}]),i}(_.Player);t.PlayerBPKExtended=k,k.smartlibInitializedStatic_=!1},4530:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PlayerBPKExtended=void 0;var n=i(9397);Object.defineProperty(t,"PlayerBPKExtended",{enumerable:!0,get:function(){return n.PlayerBPKExtended}});var r="undefined"!=typeof window&&window||i.g,a=r.voplayer;a||(a=r.voplayer={}),a.smartlib={},a.smartlib.PlayerBPKExtended=n.PlayerBPKExtended,t.default=a}},function(e){var t;return t=4530,e(e.s=t)}])}));