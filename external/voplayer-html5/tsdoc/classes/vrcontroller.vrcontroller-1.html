<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>VRController | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Class VRController</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/VRController.html">VRController</a>
				</li>
				<li>
					<a href="VRController.VRController-1.html">VRController</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p> Controller object for VR / 360 Video feature for VO Player<br><br>
							Note: The VR/360 Video is an extension for the VO Player and is available only with additional integration steps.<br>
						Please see the dedicated tutorial section <a href="../pages/Documentation/virtual-reality.html">VR / Video 360°</a> for more details.</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">VRController</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel">
				<h3>Implements</h3>
				<ul class="tsd-hierarchy">
					<li><span class="tsd-signature-type">IVRController</span></li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="VRController.VRController-1.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="VRController.VRController-1.html#immersiveModeAvailable" class="tsd-kind-icon">immersive<wbr>Mode<wbr>Available</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="VRController.VRController-1.html#immersiveModeOn" class="tsd-kind-icon">immersive<wbr>Mode<wbr>On</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="VRController.VRController-1.html#is180" class="tsd-kind-icon">is180</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="VRController.VRController-1.html#isHorizontalSplit" class="tsd-kind-icon">is<wbr>Horizontal<wbr>Split</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="VRController.VRController-1.html#isMonoscopic" class="tsd-kind-icon">is<wbr>Monoscopic</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="VRController.VRController-1.html#isPrepared" class="tsd-kind-icon">is<wbr>Prepared</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="VRController.VRController-1.html#vrInUse" class="tsd-kind-icon">vr<wbr>InUse</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#enterVR" class="tsd-kind-icon">enterVR</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#exitVR" class="tsd-kind-icon">exitVR</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#prepareVR" class="tsd-kind-icon">prepareVR</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#resetVR" class="tsd-kind-icon">resetVR</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#resetVRcameraPosition" class="tsd-kind-icon">resetVRcamera<wbr>Position</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#setVRcameraPosition" class="tsd-kind-icon">setVRcamera<wbr>Position</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="VRController.VRController-1.html#switchVR" class="tsd-kind-icon">switchVR</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new VRController<span class="tsd-signature-symbol">(</span>player<span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="VRController.VRController-1.html" class="tsd-signature-type">VRController</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Creates a new instance of VRController for HTML 5 player.<br/>
									<b>Note</b>:<br/>Attached to the player by default.<br/>There is no need to create one by yourself; the controller shall be accessed via player.vrController.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>player: <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h5>
									<div class="tsd-comment tsd-typography">
										<div class="lead">
											<p>Reference to the player it is attached to.</p>
										</div>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="VRController.VRController-1.html" class="tsd-signature-type">VRController</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="immersiveModeAvailable" class="tsd-anchor"></a>
					<h3>immersive<wbr>Mode<wbr>Available</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> immersiveModeAvailable<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current navigator supports VR immersive mode</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="immersiveModeOn" class="tsd-anchor"></a>
					<h3>immersive<wbr>Mode<wbr>On</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> immersiveModeOn<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current video is currently rendering in immersive VR mode</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="is180" class="tsd-anchor"></a>
					<h3>is180</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> is180<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> is180<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current video is a 180° representation (true) or a 360° representation (false).<br>
										Shall be set between call to prepareVR() and loading the content.<br/>
									<b>Note:</b><br/>Applies to both stereoscopic and monoscopic representations.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current video is a 180° representation (true) or a 360° representation (false).<br>
										Shall be set between call to prepareVR() and loading the content.<br/>
									<b>Note:</b><br/>Applies to both stereoscopic and monoscopic representations.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="isHorizontalSplit" class="tsd-anchor"></a>
					<h3>is<wbr>Horizontal<wbr>Split</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> isHorizontalSplit<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> isHorizontalSplit<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>For stereoscopic videos, indicates if the current video is horizontally split (true: top-bottom) or vertically (false: left-right).<br/>
										Shall be set between call to prepareVR() and loading the content.<br>
										<b>Notes:</b><br>
										For horizontally split video, left eye shall be on top and right eye at bottom.<br/>
									Only applies to stereoscopic representations.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>For stereoscopic videos, indicates if the current video is horizontally split (true: top-bottom) or vertically (false: left-right).<br/>
										Shall be set between call to prepareVR() and loading the content.<br>
										<b>Notes:</b><br>
										For horizontally split video, left eye shall be on top and right eye at bottom.<br/>
									Only applies to stereoscopic representations.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="isMonoscopic" class="tsd-anchor"></a>
					<h3>is<wbr>Monoscopic</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> isMonoscopic<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> isMonoscopic<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current video a monoscopic representation (true) or a stereoscopic representation (false).<br/>
									Shall be set between call to prepareVR() and loading the content.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the current video a monoscopic representation (true) or a stereoscopic representation (false).<br/>
									Shall be set between call to prepareVR() and loading the content.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>value: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="isPrepared" class="tsd-anchor"></a>
					<h3>is<wbr>Prepared</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> isPrepared<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the VR controller is prepared for a VR/360 representation.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="vrInUse" class="tsd-anchor"></a>
					<h3>vr<wbr>InUse</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> vrInUse<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Indicates if the VR controller is currently in use for a VR/360 representation.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="enterVR" class="tsd-anchor"></a>
					<h3>enterVR</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">enterVR<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Implementation of IVRController.enterVR</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>To be called when the playback of the VR/360 content shall be rendered immersively within a VR headset.<br/>
									<b>Only applicable with Firefox browser.</b></p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="exitVR" class="tsd-anchor"></a>
					<h3>exitVR</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">exitVR<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Implementation of IVRController.exitVR</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>To be called when the playback of the VR/360 content shall be exited from an immersive playback within a VR headset.<br/>
									<b>Only applicable with Firefox browser.</b></p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="prepareVR" class="tsd-anchor"></a>
					<h3>prepareVR</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">prepareVR<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Implementation of IVRController.prepareVR</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Initializes the VRController for the playback.<br/>
										<b>Note:</b><br/>Shall be called only before playing VR/360 content or after a resetVR() is called and a new VR/360 content shall be played.<br/>
									Required when switching from a non VR/360 content to a VR/360 content playback.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="resetVR" class="tsd-anchor"></a>
					<h3>resetVR</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">resetVR<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Implementation of IVRController.resetVR</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Resets the VRController for the player.<br/>
										<b>Note:</b><br/>Shall be called when no more VR/360 content will be played.<br>
										Required when switching from a VR/360 content to a non VR/360 content playback.<br>
									The resetVR() is called automatically by the player during the Player.reset() phase; it is therefore rarely necessary to call it directly.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="resetVRcameraPosition" class="tsd-anchor"></a>
					<h3>resetVRcamera<wbr>Position</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">resetVRcamera<wbr>Position<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>To be called to reset the camera position to its initial position</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setVRcameraPosition" class="tsd-anchor"></a>
					<h3>setVRcamera<wbr>Position</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">setVRcamera<wbr>Position<span class="tsd-signature-symbol">(</span>xAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, yAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>To be called to set the camera position to a specific x and y angle</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>xAngle: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5>yAngle: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="switchVR" class="tsd-anchor"></a>
					<h3>switchVR</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">switchVR<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Implementation of IVRController.switchVR</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Toggle between exitVR() and enterVR()</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>