<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>PlayerPlatform | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Class PlayerPlatform</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/PlayerPlatforms.html">PlayerPlatforms</a>
				</li>
				<li>
					<a href="PlayerPlatforms.PlayerPlatform.html">PlayerPlatform</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">PlayerPlatform</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class"><a href="PlayerPlatforms.PlayerPlatform.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#Brand" class="tsd-kind-icon">Brand</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#Name" class="tsd-kind-icon">Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#PHILIPS-2" class="tsd-kind-icon">PHILIPS</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getAllSupportedAndUnsupportedConfigurations" class="tsd-kind-icon">get<wbr>All<wbr>Supported<wbr>And<wbr>Unsupported<wbr>Configurations</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getDeviceId" class="tsd-kind-icon">get<wbr>Device<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getDeviceModel" class="tsd-kind-icon">get<wbr>Device<wbr>Model</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getDeviceName" class="tsd-kind-icon">get<wbr>Device<wbr>Name</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getDeviceVersion" class="tsd-kind-icon">get<wbr>Device<wbr>Version</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getSmartTVAppId" class="tsd-kind-icon">get<wbr>SmartTVApp<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getSupportedCodecs" class="tsd-kind-icon">get<wbr>Supported<wbr>Codecs</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#getSupportedRobustnessLevels" class="tsd-kind-icon">get<wbr>Supported<wbr>Robustness<wbr>Levels</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#isCodecSupported" class="tsd-kind-icon">is<wbr>Codec<wbr>Supported</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#isMobile" class="tsd-kind-icon">is<wbr>Mobile</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="PlayerPlatforms.PlayerPlatform.html#setExternalDeviceId" class="tsd-kind-icon">set<wbr>External<wbr>Device<wbr>Id</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Player<wbr>Platform<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="PlayerPlatforms.PlayerPlatform.html" class="tsd-signature-type">PlayerPlatform</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <a href="PlayerPlatforms.PlayerPlatform.html" class="tsd-signature-type">PlayerPlatform</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="Brand" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> Brand</h3>
					<div class="tsd-signature tsd-kind-icon">Brand<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>APPLE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>ARCELIK<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>GOOGLE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>HISENSE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>LG<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>MICROSOFT<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>MOZILLA<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>PHILIPS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>SAMSUNG<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>UNKNOWN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>VESTEL<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = ...</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>APPLE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>ARCELIK<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>GOOGLE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>HISENSE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>LG<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>MICROSOFT<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>MOZILLA<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>PHILIPS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>SAMSUNG<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>UNKNOWN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>VESTEL<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="Name" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> Name</h3>
					<div class="tsd-signature tsd-kind-icon">Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>ARCELIK<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>CHROME<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>EDGE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>FIREFOX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>HISENSE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>IE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>PHILIPS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>SAFARI<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>TIZEN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>UNKNOWN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>VESTEL<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>WEBOS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = ...</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5>ARCELIK<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>CHROME<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>EDGE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>FIREFOX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>HISENSE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>IE<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>PHILIPS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>SAFARI<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>TIZEN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>UNKNOWN<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>VESTEL<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5>WEBOS<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="PHILIPS-2" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> PHILIPS</h3>
					<div class="tsd-signature tsd-kind-icon">PHILIPS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;philips&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getAllSupportedAndUnsupportedConfigurations" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>All<wbr>Supported<wbr>And<wbr>Unsupported<wbr>Configurations</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>All<wbr>Supported<wbr>And<wbr>Unsupported<wbr>Configurations<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>supported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>unSupported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that retrieves all supported and unsupported configurations for a given platform.
										This method combines various video and audio codec pairs with robustness levels, encryption schemes,
									and initialization data types to determine their compatibility with different key systems.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>supported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>unSupported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>A promise that resolves to an object containing:</p>
							<ul>
								<li><code>supported</code>: An object where each key is a key system name (e.g., &quot;com.widevine.alpha&quot;),
									and each value is an array of supported configurations. Each configuration includes:<ul>
										<li><code>codecs</code>: An object containing <code>video</code> and <code>audio</code> codec strings (e.g., <code>{ video: &#39;video/mp4; codecs=&quot;avc1.42E01E&quot;&#39;, audio: &#39;audio/mp4; codecs=&quot;mp4a.40.2&quot;&#39; }</code>).</li>
										<li><code>robustness</code>: An object containing <code>video</code> and <code>audio</code> robustness levels.</li>
										<li><code>encryptionScheme</code>: The encryption scheme used (e.g., &quot;cenc&quot; or &quot;cbcs&quot;).</li>
										<li><code>initDataType</code>: The initialization data type used (e.g., &quot;cenc&quot;).</li>
									</ul>
								</li>
								<li><code>unSupported</code>: An object where each key is a key system name,
								and each value is an array of unsupported configurations, with the same properties as <code>supported</code>.</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getDeviceId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Device<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Device<wbr>Id<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that provides a unique device ID.
										Unicity of the device ID is only applicable for SmartTVs.
									If an external device ID has been set by the application, it will be returned.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>a string via a promise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getDeviceModel" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Device<wbr>Model</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Device<wbr>Model<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that tells the model of the device where the player is installed and running.
									By device is meant either smartTV OS or browser name.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>a string via a promise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getDeviceName" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Device<wbr>Name</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Device<wbr>Name<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells the name of the device where the player is installed and running.
									By device is meant either smartTV OS or browser name.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
							<p>a string</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getDeviceVersion" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Device<wbr>Version</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Device<wbr>Version<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that tells the version of the device where the player is installed and running
									By device is meant either smartTV OS or browser name.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>a string via a promise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getSmartTVAppId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>SmartTVApp<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>SmartTVApp<wbr>Id<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static  method that tells the id of the application where the player embedded.
									Only applicable to SmartTVs.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
							<p>a string</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getSupportedCodecs" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Supported<wbr>Codecs</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Supported<wbr>Codecs<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that returns the supported codecs for a platform.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h4>
							<p>object containing the supported video and audio codecs.</p>
							<ul class="tsd-parameters">
								<li class="tsd-parameter">
									<h5>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
								<li class="tsd-parameter">
									<h5>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getSupportedRobustnessLevels" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Supported<wbr>Robustness<wbr>Levels</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Supported<wbr>Robustness<wbr>Levels<span class="tsd-signature-symbol">(</span>videoCodec<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, audioCodec<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>supported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>unSupported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static async method that retrieves the supported and unsupported robustness levels for a given platform.
									The method tests various robustness levels, encryption schemes, and initialization data types for specified video and audio codecs.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>videoCodec: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The MIME type and codec string for the video codec to be tested (e.g., &#39;video/mp4; codecs=&quot;avc1.42E01E&quot;&#39;).</p>
									</div>
								</li>
								<li>
									<h5>audioCodec: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The MIME type and codec string for the audio codec to be tested (e.g., &#39;audio/mp4; codecs=&quot;mp4a.40.2&quot;&#39;).</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-symbol">{ </span>supported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>unSupported<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">{ </span>encryptionScheme<span class="tsd-signature-symbol">: </span><a href="../enums/Types.EncryptionScheme.html" class="tsd-signature-type">EncryptionScheme</a><span class="tsd-signature-symbol">; </span>initDataType<span class="tsd-signature-symbol">: </span><a href="../enums/Types.InitDataType.html" class="tsd-signature-type">InitDataType</a><span class="tsd-signature-symbol">; </span>robustness<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>audio<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>A promise that resolves to an object containing:</p>
							<ul>
								<li><code>supported</code>: An object where each key is a key system name (e.g., &quot;com.widevine.alpha&quot;),
									and each value is an array of supported configurations. Each configuration includes:<ul>
										<li><code>robustness</code>: An object containing <code>video</code> and <code>audio</code> robustness levels.</li>
										<li><code>encryptionScheme</code>: The encryption scheme used.</li>
										<li><code>initDataType</code>: The initialization data type used.</li>
									</ul>
								</li>
								<li><code>unsupported</code>: An object where each key is a key system name,
								and each value is an array of unsupported configurations, with the same properties as <code>supported</code>.</li>
							</ul>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isCodecSupported" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Codec<wbr>Supported</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Codec<wbr>Supported<span class="tsd-signature-symbol">(</span>codec<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Check codec support using the MSE</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>codec: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>codec to be tested</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the codec is supported by the platform</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isMobile" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Mobile</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Mobile<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that returns true if the we are on a mobile device, false otherwise</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if we are on a mobile device, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="setExternalDeviceId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> set<wbr>External<wbr>Device<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">set<wbr>External<wbr>Device<wbr>Id<span class="tsd-signature-symbol">(</span>deviceId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that set a unique device ID external to the application.
										If set the device ID will be used instead of the one generated by the library.
										To reset to the default behavior, set the device ID to null.
									In particular useful in the case of web player where unicity of the ID cannot be acchieved.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>deviceId: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>