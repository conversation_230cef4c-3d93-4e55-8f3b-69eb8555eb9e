<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Player | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Class Player</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Player.html">Player</a>
				</li>
				<li>
					<a href="Player.Player-1.html">Player</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p>The main player object for VO Player.</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="tsd-signature-type">EventEmitter</span>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">Player</span>
								<ul class="tsd-hierarchy">
									<li>
										<a href="PlayerBPKExtended.PlayerBPKExtended-1.html" class="tsd-signature-type">PlayerBPKExtended</a>
									</li>
								</ul>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="Player.Player-1.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Events</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_ACTIVATED" class="tsd-kind-icon">AD_<wbr>ACTIVATED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_BREAKS_LOADED" class="tsd-kind-icon">AD_<wbr>BREAKS_<wbr>LOADED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_BREAK_ENDED" class="tsd-kind-icon">AD_<wbr>BREAK_<wbr>ENDED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_BREAK_STARTED" class="tsd-kind-icon">AD_<wbr>BREAK_<wbr>STARTED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_ENDED" class="tsd-kind-icon">AD_<wbr>ENDED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_LOADED" class="tsd-kind-icon">AD_<wbr>LOADED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_PAUSE" class="tsd-kind-icon">AD_<wbr>PAUSE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_PLAY" class="tsd-kind-icon">AD_<wbr>PLAY</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_SEEKED" class="tsd-kind-icon">AD_<wbr>SEEKED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_SEEKING" class="tsd-kind-icon">AD_<wbr>SEEKING</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_STARTED" class="tsd-kind-icon">AD_<wbr>STARTED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_TIMEUPDATED" class="tsd-kind-icon">AD_<wbr>TIMEUPDATED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#AD_VOLUME_CHANGE" class="tsd-kind-icon">AD_<wbr>VOLUME_<wbr>CHANGE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#BUFFERING" class="tsd-kind-icon">BUFFERING</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#DASH_EVENT" class="tsd-kind-icon">DASH_<wbr>EVENT</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#DESTROYED" class="tsd-kind-icon">DESTROYED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#DRM_SESSION_CREATED" class="tsd-kind-icon">DRM_<wbr>SESSION_<wbr>CREATED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#DURATION_CHANGE" class="tsd-kind-icon">DURATION_<wbr>CHANGE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#ENDED" class="tsd-kind-icon">ENDED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#END_OF_LIVE_REACHED" class="tsd-kind-icon">END_<wbr>OF_<wbr>LIVE_<wbr>REACHED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#ENTER_VR_IMMERSIVE" class="tsd-kind-icon">ENTER_<wbr>VR_<wbr>IMMERSIVE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#ERROR" class="tsd-kind-icon">ERROR</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#EXIT_VR_IMMERSIVE" class="tsd-kind-icon">EXIT_<wbr>VR_<wbr>IMMERSIVE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#LOADED" class="tsd-kind-icon">LOADED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#PAUSE" class="tsd-kind-icon">PAUSE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#PLAY" class="tsd-kind-icon">PLAY</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#PLAYING" class="tsd-kind-icon">PLAYING</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#QUALITY_CHANGED" class="tsd-kind-icon">QUALITY_<wbr>CHANGED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#READY" class="tsd-kind-icon">READY</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#RESET" class="tsd-kind-icon">RESET</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SCTE35_CUE_DETECTED" class="tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>DETECTED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SCTE35_CUE_END" class="tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>END</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SCTE35_CUE_START" class="tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>START</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SECTION_END" class="tsd-kind-icon">SECTION_<wbr>END</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SECTION_IN" class="tsd-kind-icon">SECTION_<wbr>IN</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SECTION_OUT" class="tsd-kind-icon">SECTION_<wbr>OUT</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SECTION_START" class="tsd-kind-icon">SECTION_<wbr>START</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SEEKED" class="tsd-kind-icon">SEEKED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SEEKING" class="tsd-kind-icon">SEEKING</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SKIP_AD" class="tsd-kind-icon">SKIP_<wbr>AD</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SUBTITLE_RENDERING_NODE_ADJUSTED" class="tsd-kind-icon">SUBTITLE_<wbr>RENDERING_<wbr>NODE_<wbr>ADJUSTED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#TIMEUPDATE" class="tsd-kind-icon">TIMEUPDATE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#TRACKS_CHANGED" class="tsd-kind-icon">TRACKS_<wbr>CHANGED</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#VOLUME_CHANGE" class="tsd-kind-icon">VOLUME_<wbr>CHANGE</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#WAITING" class="tsd-kind-icon">WAITING</a></li>
								<li class="tsd-kind-event tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#WARNING" class="tsd-kind-icon">WARNING</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#SRT" class="tsd-kind-icon">SRT</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#VTT" class="tsd-kind-icon">VTT</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#captureRejectionSymbol" class="tsd-kind-icon">capture<wbr>Rejection<wbr>Symbol</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#captureRejections" class="tsd-kind-icon">capture<wbr>Rejections</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#defaultMaxListeners" class="tsd-kind-icon">default<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#errorMonitor" class="tsd-kind-icon">error<wbr>Monitor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Accessors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#audioTrack" class="tsd-kind-icon">audio<wbr>Track</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#audioTracks" class="tsd-kind-icon">audio<wbr>Tracks</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#currentBandwidth" class="tsd-kind-icon">current<wbr>Bandwidth</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#currentTime" class="tsd-kind-icon">current<wbr>Time</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#duration" class="tsd-kind-icon">duration</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#ended" class="tsd-kind-icon">ended</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#estimatedBandwidth" class="tsd-kind-icon">estimated<wbr>Bandwidth</a></li>
								<li class="tsd-kind-set-signature tsd-parent-kind-class"><a href="Player.Player-1.html#license" class="tsd-kind-icon">license</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#muted" class="tsd-kind-icon">muted</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#playbackRate" class="tsd-kind-icon">playback<wbr>Rate</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#playing" class="tsd-kind-icon">playing</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#prftNtpOffset" class="tsd-kind-icon">prft<wbr>Ntp<wbr>Offset</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#qualities" class="tsd-kind-icon">qualities</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#quality" class="tsd-kind-icon">quality</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#textTrack" class="tsd-kind-icon">text<wbr>Track</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#textTracks" class="tsd-kind-icon">text<wbr>Tracks</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Player.Player-1.html#version" class="tsd-kind-icon">version</a></li>
								<li class="tsd-kind-accessor tsd-parent-kind-class"><a href="Player.Player-1.html#volume" class="tsd-kind-icon">volume</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#fmaManager" class="tsd-kind-icon">fma<wbr>Manager</a></li>
								<li class="tsd-kind-get-signature tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#version-1" class="tsd-kind-icon">version</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#addExternalTextTrack" class="tsd-kind-icon">add<wbr>External<wbr>Text<wbr>Track</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#addExternalThumbnail" class="tsd-kind-icon">add<wbr>External<wbr>Thumbnail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#addListener" class="tsd-kind-icon">add<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#addSRTTextTrack" class="tsd-kind-icon">addSRTText<wbr>Track</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#configure" class="tsd-kind-icon">configure</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#destroy" class="tsd-kind-icon">destroy</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#emit" class="tsd-kind-icon">emit</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#eventNames" class="tsd-kind-icon">event<wbr>Names</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#getAllThumbnail" class="tsd-kind-icon">get<wbr>All<wbr>Thumbnail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#getConfiguration" class="tsd-kind-icon">get<wbr>Configuration</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#getMaxListeners" class="tsd-kind-icon">get<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#getSections" class="tsd-kind-icon">get<wbr>Sections</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#getThumbnail" class="tsd-kind-icon">get<wbr>Thumbnail</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#hasSections" class="tsd-kind-icon">has<wbr>Sections</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#hasThumbnails" class="tsd-kind-icon">has<wbr>Thumbnails</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#isEvent" class="tsd-kind-icon">is<wbr>Event</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#isLive" class="tsd-kind-icon">is<wbr>Live</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#listenerCount" class="tsd-kind-icon">listener<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#listeners" class="tsd-kind-icon">listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#load" class="tsd-kind-icon">load</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#off" class="tsd-kind-icon">off</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#on" class="tsd-kind-icon">on</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#once" class="tsd-kind-icon">once</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#pause" class="tsd-kind-icon">pause</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#play" class="tsd-kind-icon">play</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#prependListener" class="tsd-kind-icon">prepend<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#prependOnceListener" class="tsd-kind-icon">prepend<wbr>Once<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#programInfo" class="tsd-kind-icon">program<wbr>Info</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#rawListeners" class="tsd-kind-icon">raw<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#registerCapabilitiesFilter" class="tsd-kind-icon">register<wbr>Capabilities<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#registerLicenseRequestFilter" class="tsd-kind-icon">register<wbr>License<wbr>Request<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#registerLicenseResponseFilter" class="tsd-kind-icon">register<wbr>License<wbr>Response<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#registerRequestFilter" class="tsd-kind-icon">register<wbr>Request<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#removeAllListeners" class="tsd-kind-icon">remove<wbr>All<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#removeListener" class="tsd-kind-icon">remove<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#reset" class="tsd-kind-icon">reset</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#seekRange" class="tsd-kind-icon">seek<wbr>Range</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#seekToEndOfSection" class="tsd-kind-icon">seek<wbr>ToEnd<wbr>OfSection</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#seekToSection" class="tsd-kind-icon">seek<wbr>ToSection</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#setAnalyticsMetadata" class="tsd-kind-icon">set<wbr>Analytics<wbr>Metadata</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#setBufferingWatchdog_" class="tsd-kind-icon">set<wbr>Buffering<wbr>Watchdog_</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="Player.Player-1.html#setMaxListeners" class="tsd-kind-icon">set<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#signalFullscreen" class="tsd-kind-icon">signal<wbr>Fullscreen</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#skipCurrentSection" class="tsd-kind-icon">skip<wbr>Current<wbr>Section</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#unregisterCapabilitiesFilter" class="tsd-kind-icon">unregister<wbr>Capabilities<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#unregisterLicenseRequestFilter" class="tsd-kind-icon">unregister<wbr>License<wbr>Request<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#unregisterLicenseResponseFilter" class="tsd-kind-icon">unregister<wbr>License<wbr>Response<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#unregisterRequestFilter" class="tsd-kind-icon">unregister<wbr>Request<wbr>Filter</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#updateVideoArea" class="tsd-kind-icon">update<wbr>Video<wbr>Area</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="Player.Player-1.html#wasLive" class="tsd-kind-icon">was<wbr>Live</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#getDeviceId" class="tsd-kind-icon">get<wbr>Device<wbr>Id</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#getEventListeners" class="tsd-kind-icon">get<wbr>Event<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#getSupportedTypes" class="tsd-kind-icon">get<wbr>Supported<wbr>Types</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#init" class="tsd-kind-icon">init</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserChromiumBased" class="tsd-kind-icon">is<wbr>Browser<wbr>Chromium<wbr>Based</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserEdgeOrIE11" class="tsd-kind-icon">is<wbr>Browser<wbr>Edge<wbr>OrIE11</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserSafariIphoneIpadIpod" class="tsd-kind-icon">is<wbr>Browser<wbr>Safari<wbr>Iphone<wbr>Ipad<wbr>Ipod</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserSafariMacOS" class="tsd-kind-icon">is<wbr>Browser<wbr>Safari<wbr>MacOS</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserSafariiOS" class="tsd-kind-icon">is<wbr>Browser<wbr>SafariiOS</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserSafariiPadOS" class="tsd-kind-icon">is<wbr>Browser<wbr>Safarii<wbr>PadOS</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isBrowserSupported" class="tsd-kind-icon">is<wbr>Browser<wbr>Supported</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isPackagedSmartTVApp" class="tsd-kind-icon">is<wbr>Packaged<wbr>SmartTVApp</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#isSmartTv" class="tsd-kind-icon">is<wbr>Smart<wbr>Tv</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#listenerCount-1" class="tsd-kind-icon">listener<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#on-1" class="tsd-kind-icon">on</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#once-1" class="tsd-kind-icon">once</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="Player.Player-1.html#setLevel" class="tsd-kind-icon">set<wbr>Level</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="Player.Player-1.html#setMaxListeners-1" class="tsd-kind-icon">set<wbr>Max<wbr>Listeners</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite">
						<li class="tsd-signature tsd-kind-icon">new <wbr>Player<span class="tsd-signature-symbol">(</span>video<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLVideoElement</span>, isAdPlayer<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Overrides EventEmitter.constructor</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Creates a new instance of VOPlayer HTML 5 player</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>alias</dt>
									<dd><p>Player</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>video: <span class="tsd-signature-type">HTMLVideoElement</span></h5>
									<div class="tsd-comment tsd-typography">
										<div class="lead">
											<p>Any existing TextTracks attached to this
											element that were not created by VOPlayer will be discarded.</p>
										</div>
									</div>
								</li>
								<li>
									<h5>isAdPlayer: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Events</h2>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_ACTIVATED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>ACTIVATED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>ACTIVATED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adActivated&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the user activates (i.e: clicks or presses ENTER on it) the ad
							<br/>See <a href="../interfaces/Events.AdActivatedEvent.html">AdActivatedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_BREAKS_LOADED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>BREAKS_<wbr>LOADED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>BREAKS_<wbr>LOADED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adBreaksLoaded&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when adBreaks have been loaded (i.e: a VMAP file has been loaded)
							<br/>See <a href="../interfaces/Events.AdBreaksLoadedEvent.html">AdBreaksLoadedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_BREAK_ENDED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>BREAK_<wbr>ENDED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>BREAK_<wbr>ENDED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adBreakEnded&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a linear adbreak has completed playing
							<br/>See <a href="../interfaces/Events.AdBreakEndedEvent.html">AdBreakEndedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_BREAK_STARTED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>BREAK_<wbr>STARTED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>BREAK_<wbr>STARTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adBreakStarted&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a linear adBreak starts playing.
							<br/>See <a href="../interfaces/Events.AdBreakStartedEvent.html">AdBreakStartedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_ENDED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>ENDED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>ENDED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adEnded&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when an ad completes playing
							<br/>See <a href="../interfaces/Events.AdEndedEvent.html">AdEndedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_LOADED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>LOADED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>LOADED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adLoaded&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when an ad starts playing
							<br/>See <a href="../interfaces/Events.AdLoadedEvent.html">AdLoadedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_PAUSE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>PAUSE</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>PAUSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adpause&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when advertisement is paused
							<br/>See <a href="../interfaces/Events.AdPauseEvent.html">AdPauseEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_PLAY" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>PLAY</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>PLAY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adplay&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when advertisement plays or is resumed
							<br/>See <a href="../interfaces/Events.AdPlayEvent.html">AdPlayEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_SEEKED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>SEEKED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>SEEKED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adSeeked&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the user stops seeking within the ad
							<br/>See <a href="../interfaces/Events.AdSeekedEvent.html">AdSeekedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_SEEKING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>SEEKING</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>SEEKING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adSeeking&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the user starts seeking within the ad
							<br/>See <a href="../interfaces/Events.AdSeekingEvent.html">AdSeekingEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_STARTED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>STARTED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>STARTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adStarted&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when an ad starts playing
							<br/>See <a href="../interfaces/Events.AdStartedEvent.html">AdStartedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_TIMEUPDATED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>TIMEUPDATED</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>TIMEUPDATED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;adTimeUpdated&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the head position changes in the current ad
							<br/>See <a href="../interfaces/Events.AdTimeUpdatedEvent.html">AdTimeUpdatedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="AD_VOLUME_CHANGE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> AD_<wbr>VOLUME_<wbr>CHANGE</h3>
					<div class="tsd-signature tsd-kind-icon">AD_<wbr>VOLUME_<wbr>CHANGE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;advolumechange&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when ad volume is changed or muted
							<br/>See <a href="../interfaces/Events.AdVolumeChangeEvent.html">AdVolumeChangeEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="BUFFERING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> BUFFERING</h3>
					<div class="tsd-signature tsd-kind-icon">BUFFERING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;buffering&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player enters or leave a buffering period
							<br/>See <a href="../interfaces/Events.BufferingEvent.html">BufferingEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="DASH_EVENT" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> DASH_<wbr>EVENT</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;dashevent&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a dash event is detected within a dash manifest
							<br/>See <a href="../interfaces/Events.DashEventEvent.html">DashEventEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="DESTROYED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> DESTROYED</h3>
					<div class="tsd-signature tsd-kind-icon">DESTROYED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;destroyed&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player has been destroyed
							<br/>See <a href="../interfaces/Events.DestroyedEvent.html">DestroyedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="DRM_SESSION_CREATED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> DRM_<wbr>SESSION_<wbr>CREATED</h3>
					<div class="tsd-signature tsd-kind-icon">DRM_<wbr>SESSION_<wbr>CREATED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;drmSessionCreated&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a DRM key session has been created
							<br/>See <a href="../interfaces/Events.DrmSessionCreatedEvent.html">DrmSessionCreatedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="DURATION_CHANGE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> DURATION_<wbr>CHANGE</h3>
					<div class="tsd-signature tsd-kind-icon">DURATION_<wbr>CHANGE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;durationchange&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the duration of the stream has been modified
							<br/>See <a href="../interfaces/Events.DurationChangeEvent.html">DurationChangeEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="ENDED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> ENDED</h3>
					<div class="tsd-signature tsd-kind-icon">ENDED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;ended&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Repeated event from &lt;video&gt; tag
							<br/>See <a href="../interfaces/Events.EndedEvent.html">EndedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="END_OF_LIVE_REACHED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> END_<wbr>OF_<wbr>LIVE_<wbr>REACHED</h3>
					<div class="tsd-signature tsd-kind-icon">END_<wbr>OF_<wbr>LIVE_<wbr>REACHED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;endoflivereached&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a startover stream has reached its end (now static)
							<br/>See <a href="../interfaces/Events.EndOfLiveReachedEvent.html">EndOfLiveReachedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="ENTER_VR_IMMERSIVE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> ENTER_<wbr>VR_<wbr>IMMERSIVE</h3>
					<div class="tsd-signature tsd-kind-icon">ENTER_<wbr>VR_<wbr>IMMERSIVE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;entervrimmersive&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player enter a VR session in a VR headset
							<br/>See <a href="../interfaces/Events.EnterVRImmersiveEvent.html">EnterVRImmersiveEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="ERROR" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;error&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when an error has occured
							<br/>See <a href="../interfaces/Events.ErrorEvent.html">ErrorEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="EXIT_VR_IMMERSIVE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> EXIT_<wbr>VR_<wbr>IMMERSIVE</h3>
					<div class="tsd-signature tsd-kind-icon">EXIT_<wbr>VR_<wbr>IMMERSIVE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;exitvrimmersive&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player exit a VR session from a VR headset
							<br/>See <a href="../interfaces/Events.ExitVRImmersiveEvent.html">ExitVRImmersiveEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="LOADED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> LOADED</h3>
					<div class="tsd-signature tsd-kind-icon">LOADED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;loaded&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player has been loaded
							<br/>See <a href="../interfaces/Events.LoadedEvent.html">LoadedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="PAUSE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> PAUSE</h3>
					<div class="tsd-signature tsd-kind-icon">PAUSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;pause&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player is paused
							<br/>See <a href="../interfaces/Events.PauseEvent.html">PauseEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="PLAY" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> PLAY</h3>
					<div class="tsd-signature tsd-kind-icon">PLAY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;play&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player starts playing
							<br/>See <a href="../interfaces/Events.PlayEvent.html">PlayEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="PLAYING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> PLAYING</h3>
					<div class="tsd-signature tsd-kind-icon">PLAYING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;playing&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player leaves a buffering period
							<br/>See <a href="../interfaces/Events.PlayingEvent.html">PlayingEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="QUALITY_CHANGED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> QUALITY_<wbr>CHANGED</h3>
					<div class="tsd-signature tsd-kind-icon">QUALITY_<wbr>CHANGED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;qualitychanged&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the quality of the played streaming representation has changed
							<br/>See <a href="../interfaces/Events.QualityChangedEvent.html">QualityChangedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="READY" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> READY</h3>
					<div class="tsd-signature tsd-kind-icon">READY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;ready&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the UI controller is ready and may be used
								<br/>Only applicable in the case where the player is created using function <a href="../modules/UIController.html#createPlayerWithControls">createPlayerWithControls</a>
							<br/>See <a href="../interfaces/Events.ReadyEvent.html">ReadyEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="RESET" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> RESET</h3>
					<div class="tsd-signature tsd-kind-icon">RESET<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;reset&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player has been reset
							<br/>See <a href="../interfaces/Events.ResetEvent.html">ResetEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SCTE35_CUE_DETECTED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SCTE35_<wbr>CUE_<wbr>DETECTED</h3>
					<div class="tsd-signature tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>DETECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;scte35cuedetected&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a scte35 cue is detected within a manifest
							<br/>See <a href="../modules/Events.html#Scte35CueDetectedEvent">Scte35CueDetectedEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SCTE35_CUE_END" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SCTE35_<wbr>CUE_<wbr>END</h3>
					<div class="tsd-signature tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>END<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;scte35cueend&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a scte35 cue is ending
							<br/>See <a href="../modules/Events.html#Scte35CueEndEvent">Scte35CueEndEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SCTE35_CUE_START" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SCTE35_<wbr>CUE_<wbr>START</h3>
					<div class="tsd-signature tsd-kind-icon">SCTE35_<wbr>CUE_<wbr>START<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;scte35cuestart&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when a scte35 cue is starting
							<br/>See <a href="../modules/Events.html#Scte35CueStartEvent">Scte35CueStartEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SECTION_END" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SECTION_<wbr>END</h3>
					<div class="tsd-signature tsd-kind-icon">SECTION_<wbr>END<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;sectionend&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player leave a vodata section
							<br/>See <a href="../interfaces/Events.SectionEndEvent.html">SectionEndEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SECTION_IN" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SECTION_<wbr>IN</h3>
					<div class="tsd-signature tsd-kind-icon">SECTION_<wbr>IN<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;sectionin&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player land in a vodata section (e.g. start time not null or seek within a section)
							<br/>See <a href="../interfaces/Events.SectionInEvent.html">SectionInEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SECTION_OUT" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SECTION_<wbr>OUT</h3>
					<div class="tsd-signature tsd-kind-icon">SECTION_<wbr>OUT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;sectionout&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player leave a vodata section (e.g. end time not null or seek outside a section)
							<br/>See <a href="../interfaces/Events.SectionOutEvent.html">SectionOutEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SECTION_START" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SECTION_<wbr>START</h3>
					<div class="tsd-signature tsd-kind-icon">SECTION_<wbr>START<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;sectionstart&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player enter a vodata section
							<br/>See <a href="../interfaces/Events.SectionStartEvent.html">SectionStartEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SEEKED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SEEKED</h3>
					<div class="tsd-signature tsd-kind-icon">SEEKED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;seeked&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when seeking in content is completed
							<br/>See <a href="../interfaces/Events.SeekedEvent.html">SeekedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SEEKING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SEEKING</h3>
					<div class="tsd-signature tsd-kind-icon">SEEKING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;seeking&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player.currentTime is set to a new value
							<br/>See <a href="../interfaces/Events.SeekingEvent.html">SeekingEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SKIP_AD" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SKIP_<wbr>AD</h3>
					<div class="tsd-signature tsd-kind-icon">SKIP_<wbr>AD<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;skipAd&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the user skips an ad.
							<br/>See <a href="../interfaces/Events.SkipAdEvent.html">SkipAdEvent</a> for callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="SUBTITLE_RENDERING_NODE_ADJUSTED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SUBTITLE_<wbr>RENDERING_<wbr>NODE_<wbr>ADJUSTED</h3>
					<div class="tsd-signature tsd-kind-icon">SUBTITLE_<wbr>RENDERING_<wbr>NODE_<wbr>ADJUSTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;subtitlerenderingnodeadjusted&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the HTML node used for subtitles rendering is adjusted to the video size.
								May be used by the UI to externally adjust the node so that the UI controls does not cover the subtitles.
							<br/>See <a href="../interfaces/Events.SubtitleRenderingNodeAdjustedEvent.html">SubtitleRenderingNodeAdjustedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="TIMEUPDATE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> TIMEUPDATE</h3>
					<div class="tsd-signature tsd-kind-icon">TIMEUPDATE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;timeupdate&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the playback time of the stream has been modified
							<br/>See <a href="../interfaces/Events.TimeUpdateEvent.html">TimeUpdateEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="TRACKS_CHANGED" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> TRACKS_<wbr>CHANGED</h3>
					<div class="tsd-signature tsd-kind-icon">TRACKS_<wbr>CHANGED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;trackschanged&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the list of tracks (video, audio and subtitles) have changed
							<br/>See <a href="../interfaces/Events.TracksChangedEvent.html">TracksChangedEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="VOLUME_CHANGE" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> VOLUME_<wbr>CHANGE</h3>
					<div class="tsd-signature tsd-kind-icon">VOLUME_<wbr>CHANGE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;volumechange&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Repeated event from &lt;video&gt; tag
							<br/>See <a href="../interfaces/Events.VolumeChangeEvent.html">VolumeChangeEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="WAITING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> WAITING</h3>
					<div class="tsd-signature tsd-kind-icon">WAITING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;waiting&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when the player enters a buffering period
							<br/>See <a href="../interfaces/Events.WaitingEvent.html">WaitingEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-event tsd-parent-kind-class tsd-is-static">
					<a name="WARNING" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> WARNING</h3>
					<div class="tsd-signature tsd-kind-icon">WARNING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;warning&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Fired when an error has occured, without breaking playback
							<br/>See <a href="../interfaces/Events.WarningEvent.html">WarningEvent</a> for event callback/handler parameter properties</p>
						</div>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="SRT" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> SRT</h3>
					<div class="tsd-signature tsd-kind-icon">SRT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;srt&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="VTT" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> VTT</h3>
					<div class="tsd-signature tsd-kind-icon">VTT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &#x27;vtt&#x27;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="captureRejectionSymbol" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> capture<wbr>Rejection<wbr>Symbol</h3>
					<div class="tsd-signature tsd-kind-icon">capture<wbr>Rejection<wbr>Symbol<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">typeof </span><a href="Player.Player-1.html#captureRejectionSymbol" class="tsd-signature-type">captureRejectionSymbol</a></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.captureRejectionSymbol</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="captureRejections" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> capture<wbr>Rejections</h3>
					<div class="tsd-signature tsd-kind-icon">capture<wbr>Rejections<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.captureRejections</p>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Sets or gets the default captureRejection value for all emitters.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="defaultMaxListeners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> default<wbr>Max<wbr>Listeners</h3>
					<div class="tsd-signature tsd-kind-icon">default<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.defaultMaxListeners</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="errorMonitor" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> error<wbr>Monitor</h3>
					<div class="tsd-signature tsd-kind-icon">error<wbr>Monitor<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">typeof </span><a href="Player.Player-1.html#errorMonitor" class="tsd-signature-type">errorMonitor</a></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.errorMonitor</p>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>This symbol shall be used to install a listener for only monitoring <code>&#39;error&#39;</code>
								events. Listeners installed using this symbol are called before the regular
							<code>&#39;error&#39;</code> listeners are called.</p>
						</div>
						<p>Installing a listener using this symbol does not change the behavior once an
							<code>&#39;error&#39;</code> event is emitted, therefore the process will still crash if no
						regular <code>&#39;error&#39;</code> listener is installed.</p>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Accessors</h2>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="audioTrack" class="tsd-anchor"></a>
					<h3>audio<wbr>Track</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> audioTrack<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> audioTrack<span class="tsd-signature-symbol">(</span>trackId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current audio track in the list of available audio tracks<br>
									Assign this value to set a new audio track</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current audio track in the list of available audio tracks<br>
									Assign this value to set a new audio track</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>trackId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="audioTracks" class="tsd-anchor"></a>
					<h3>audio<wbr>Tracks</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> audioTracks<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">AudioTrack</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>List of audio tracks (read-only)</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">AudioTrack</span><span class="tsd-signature-symbol">[]</span></h4>
							<p>Array of <a href="../interfaces/Types.AudioTrack.html">AudioTrack</a></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="currentBandwidth" class="tsd-anchor"></a>
					<h3>current<wbr>Bandwidth</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> currentBandwidth<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Instantaneous current download speed (last video segment) in bits per seconds</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="currentTime" class="tsd-anchor"></a>
					<h3>current<wbr>Time</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> currentTime<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> currentTime<span class="tsd-signature-symbol">(</span>time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Current position in the video in seconds.<br>
									Assign this value to jump in the video stream</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Current position in the video in seconds.<br>
									Assign this value to jump in the video stream</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>time: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="duration" class="tsd-anchor"></a>
					<h3>duration</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> duration<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Duration of the current stream in seconds.<br>
									Duration is always 0 for Live streams.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="ended" class="tsd-anchor"></a>
					<h3>ended</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> ended<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns true if the playback has ended</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="estimatedBandwidth" class="tsd-anchor"></a>
					<h3>estimated<wbr>Bandwidth</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> estimatedBandwidth<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Estimated current download speed in bits per seconds</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-set-signature tsd-parent-kind-class">
					<a name="license" class="tsd-anchor"></a>
					<h3>license</h3>
					<ul class="tsd-signatures tsd-kind-set-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> license<span class="tsd-signature-symbol">(</span>l<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Set the player license</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>l: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="muted" class="tsd-anchor"></a>
					<h3>muted</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> muted<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> muted<span class="tsd-signature-symbol">(</span>m<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Boolean representing the muted state of the player.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Boolean that will mute the player</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>m: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="playbackRate" class="tsd-anchor"></a>
					<h3>playback<wbr>Rate</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> playbackRate<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> playbackRate<span class="tsd-signature-symbol">(</span>rate<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Playaback rate of the current stream.<br>
									1.0 represents a normal playback.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Playaback rate of the current stream.<br>
									1.0 represents a normal playback.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>rate: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="playing" class="tsd-anchor"></a>
					<h3>playing</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> playing<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns true if the player is currently playing (including re-buffering phases), false otherwise</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="prftNtpOffset" class="tsd-anchor"></a>
					<h3>prft<wbr>Ntp<wbr>Offset</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> prftNtpOffset<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Property providing the offset between NTP time and UTC time, when available in the stream (MP4 ProducerReferenceTimeBox).
									Only applies to dash streams</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="qualities" class="tsd-anchor"></a>
					<h3>qualities</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> qualities<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Quality</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>List of qualities available for playback <br>
										The list is read-only, no actions on it will register.<br>
									The Array is empty on platforms where video quality management is not available.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Quality</span><span class="tsd-signature-symbol">[]</span></h4>
							<p>Array of <a href="../interfaces/Types.Quality.html">Quality</a></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="quality" class="tsd-anchor"></a>
					<h3>quality</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> quality<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> quality<span class="tsd-signature-symbol">(</span>levelId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current Quality in the list of available qualities<br>
										Assign this value to set a specific video quality for playback. Use the index of the desired quality from the list available qualities.<br>
										Setting a quality disables the ABR.<br>
									This feature may not be available on all platforms.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current Quality in the list of available qualities<br>
										Assign this value to set a specific video quality for playback. Use the index of the desired quality from the list available qualities.<br>
										Setting a quality disables the ABR.<br>
									This feature may not be available on all platforms.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>levelId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="textTrack" class="tsd-anchor"></a>
					<h3>text<wbr>Track</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> textTrack<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> textTrack<span class="tsd-signature-symbol">(</span>trackId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current text track in the list of available text tracks (-1 if no text track is selected)<br>
									Assign this value to set a new text track</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Index of the current text track in the list of available text tracks (-1 if no text track is selected)<br>
									Assign this value to set a new text track</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>trackId: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="textTracks" class="tsd-anchor"></a>
					<h3>text<wbr>Tracks</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> textTracks<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">TextTrack</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>List of available text tracks (read-only)</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">TextTrack</span><span class="tsd-signature-symbol">[]</span></h4>
							<p>Array of <a href="../interfaces/Types.TextTrack.html">TextTrack</a></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class">
					<a name="version" class="tsd-anchor"></a>
					<h3>version</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> version<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class">
					<a name="volume" class="tsd-anchor"></a>
					<h3>volume</h3>
					<ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> volume<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> volume<span class="tsd-signature-symbol">(</span>v<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Volume of the player.
									Value is between 0.0 and 1.0.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Number that will change the player volume</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>v: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-static">
					<a name="fmaManager" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> fma<wbr>Manager</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> fmaManager<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">VOFmaManager</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a singleton instance of the VO FMA manager</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">VOFmaManager</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class tsd-is-static">
					<a name="version-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> version</h3>
					<ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> version<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Version of the player</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addExternalTextTrack" class="tsd-anchor"></a>
					<h3>add<wbr>External<wbr>Text<wbr>Track</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>External<wbr>Text<wbr>Track<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, language<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Add a new subtitle track with an SRT/VTT file downloaded from the url.<br>
										It is possible to add multiple SRT/VTT files by using different names.<br>
										<b>Warning: this function should not be called before the player is loaded</b><br>
									<b>Note: This feature is only available in VOD.</b></p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>name of the track</p>
									</div>
								</li>
								<li>
									<h5>language: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>language of the track</p>
									</div>
								</li>
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>url of the SRT/VTT file to be downloaded</p>
									</div>
								</li>
								<li>
									<h5>type: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>subtitle type :&#39;SRT&#39; or &#39;VTT&#39;</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addExternalThumbnail" class="tsd-anchor"></a>
					<h3>add<wbr>External<wbr>Thumbnail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">add<wbr>External<wbr>Thumbnail<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, type<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Add a new thumbnail track with a VTT file downloaded from the url.<br></p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>url of the VTT file to be downloaded</p>
									</div>
								</li>
								<li>
									<h5>type: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>Thumbnail file type :&#39;VTT&#39;</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="addListener" class="tsd-anchor"></a>
					<h3>add<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.addListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Alias for <code>emitter.on(eventName, listener)</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="addSRTTextTrack" class="tsd-anchor"></a>
					<h3>addSRTText<wbr>Track</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">addSRTText<wbr>Track<span class="tsd-signature-symbol">(</span>name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, language<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Add a new subtitle track with an SRT/VTT file downloaded from the url.<br>
										It is possible to add multiple SRT/VTT files by using different names.<br>
										<b>Warning: this function should not be called before the player is loaded</b><br>
									<b>Note: This feature is only available in VOD.</b></p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>deprecated</dt>
									<dd><p>use <a href="Player.Player-1.html#addExternalTextTrack">addExternalTextTrack</a>  instead</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>name: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>name of the track</p>
									</div>
								</li>
								<li>
									<h5>language: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>language of the track</p>
									</div>
								</li>
								<li>
									<h5>url: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>url of the SRT/VTT file to be downloaded</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="configure" class="tsd-anchor"></a>
					<h3>configure</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">configure<span class="tsd-signature-symbol">(</span>config<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Configure the Player instance.
										The config object passed in needs not be complete. It will be merged with the existing Player configuration.
									Config keys and types will be checked.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>config: <a href="../interfaces/Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>This should follow the form of <a href="../interfaces/Types.PlayerConfiguration.html">PlayerConfiguration</a>, but you may omit any field you do not wish to change.</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="destroy" class="tsd-anchor"></a>
					<h3>destroy</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Destroys the player. The instance will not be usable after the call.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="emit" class="tsd-anchor"></a>
					<h3>emit</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">emit<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, <span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.emit</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Synchronously calls each of the listeners registered for the event named<code>eventName</code>, in the order they were registered, passing the supplied arguments
									to each.</p>
								</div>
								<p>Returns <code>true</code> if the event had listeners, <code>false</code> otherwise.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #008000">// First listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">firstListener</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Helloooo! first listener&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #008000">// Second listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">secondListener</span><span style="color: #000000">(</span><span style="color: #001080">arg1</span><span style="color: #000000">, </span><span style="color: #001080">arg2</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">`event with parameters </span><span style="color: #0000FF">${</span><span style="color: #001080">arg1</span><span style="color: #0000FF">}</span><span style="color: #A31515">, </span><span style="color: #0000FF">${</span><span style="color: #001080">arg2</span><span style="color: #0000FF">}</span><span style="color: #A31515"> in second listener`</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #008000">// Third listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">thirdListener</span><span style="color: #000000">(...</span><span style="color: #001080">args</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">parameters</span><span style="color: #000000"> = </span><span style="color: #001080">args</span><span style="color: #000000">.</span><span style="color: #795E26">join</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;, &#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">`event with parameters </span><span style="color: #0000FF">${</span><span style="color: #001080">parameters</span><span style="color: #0000FF">}</span><span style="color: #A31515"> in third listener`</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>

<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">listeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">));</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #098658">1</span><span style="color: #000000">, </span><span style="color: #098658">2</span><span style="color: #000000">, </span><span style="color: #098658">3</span><span style="color: #000000">, </span><span style="color: #098658">4</span><span style="color: #000000">, </span><span style="color: #098658">5</span><span style="color: #000000">);</span>

<span style="color: #008000">// Prints:</span>
<span style="color: #008000">// [</span>
<span style="color: #008000">//   [Function: firstListener],</span>
<span style="color: #008000">//   [Function: secondListener],</span>
<span style="color: #008000">//   [Function: thirdListener]</span>
<span style="color: #008000">// ]</span>
<span style="color: #008000">// Helloooo! first listener</span>
<span style="color: #008000">// event with parameters 1, 2 in second listener</span>
<span style="color: #008000">// event with parameters 1, 2, 3, 4, 5 in third listener</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="eventNames" class="tsd-anchor"></a>
					<h3>event<wbr>Names</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">event<wbr>Names<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.eventNames</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns an array listing the events for which the emitter has registered
									listeners. The values in the array are strings or <code>Symbol</code>s.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;bar&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">sym</span><span style="color: #000000"> = </span><span style="color: #267F99">Symbol</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;symbol&#039;</span><span style="color: #000000">);</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #001080">sym</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>

<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">eventNames</span><span style="color: #000000">());</span>
<span style="color: #008000">// Prints: [ &#039;foo&#039;, &#039;bar&#039;, Symbol(symbol) ]</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getAllThumbnail" class="tsd-anchor"></a>
					<h3>get<wbr>All<wbr>Thumbnail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>All<wbr>Thumbnail<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/Types.ParsedThumbnails.html" class="tsd-signature-type">ParsedThumbnails</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/Types.ParsedThumbnails.html" class="tsd-signature-type">ParsedThumbnails</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getConfiguration" class="tsd-anchor"></a>
					<h3>get<wbr>Configuration</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Configuration<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Return a copy of the current configuration. Modifications of the returned value will not affect the Player&#39;s active configuration.
									You must call player.configure() to make changes.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="../interfaces/Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="getMaxListeners" class="tsd-anchor"></a>
					<h3>get<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.getMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the current max listener value for the <code>EventEmitter</code> which is either
									set by <code>emitter.setMaxListeners(n)</code> or defaults to <a href="Player.Player-1.html#defaultMaxListeners">defaultMaxListeners</a>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v1.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getSections" class="tsd-anchor"></a>
					<h3>get<wbr>Sections</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Sections<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Section</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Retrieves the sections of the current content.
									This is an instance method that returns an array of Section objects, each representing a section in the currently loaded content. If no content is loaded, or if the content has no sections, returns an empty array.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Section</span><span class="tsd-signature-symbol">[]</span></h4>
							<p>An array of Section objects representing the sections of the current content. If no content is loaded, or if the content has no sections, returns an empty array.</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getThumbnail" class="tsd-anchor"></a>
					<h3>get<wbr>Thumbnail</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Thumbnail<span class="tsd-signature-symbol">(</span>time<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/Types.Thumbnail.html" class="tsd-signature-type">Thumbnail</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the thumbnail at time position.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>time: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>the time position, in seconds, based on the return value of the <a href="Player.Player-1.html#duration">duration</a> property for VOD streams or based on the <code>start</code>and <code>end</code> values of the <a href="Player.Player-1.html#seekRange">seekRange</a> method for LIVE streams.</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/Types.Thumbnail.html" class="tsd-signature-type">Thumbnail</a><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>a promise which is resolved when thumbnail is retrieved for the given time position. Thumbnail value can be null if there is no thumbnails representation or
							if there is no thumbnail at given time position</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="hasSections" class="tsd-anchor"></a>
					<h3>has<wbr>Sections</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">has<wbr>Sections<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Determines if the current content has sections.
									This is an instance method that checks if the currently loaded content has associated sections. These sections are typically used to divide the content into meaningful parts.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>Returns true if the current content has sections. Returns false if the current content does not have sections, or if no content is loaded.</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="hasThumbnails" class="tsd-anchor"></a>
					<h3>has<wbr>Thumbnails</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">has<wbr>Thumbnails<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns true if current content has thumbnails.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if current content has thumbnails</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="isEvent" class="tsd-anchor"></a>
					<h3>is<wbr>Event</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Event<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the current stream is a StartOver / HLS Event stream.</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="isLive" class="tsd-anchor"></a>
					<h3>is<wbr>Live</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Live<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the current stream is a Live one, false otherwise (VOD)</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="listenerCount" class="tsd-anchor"></a>
					<h3>listener<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listener<wbr>Count<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listenerCount</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the number of listeners listening to the event named <code>eventName</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v3.2.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event being listened for</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="listeners" class="tsd-anchor"></a>
					<h3>listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listeners<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">util</span><span style="color: #000000">.</span><span style="color: #795E26">inspect</span><span style="color: #000000">(</span><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">listeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">)));</span>
<span style="color: #008000">// Prints: [ [Function] ]</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="load" class="tsd-anchor"></a>
					<h3>load</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">load<span class="tsd-signature-symbol">(</span>mediaObj<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Media</span>, opt_startTime<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Loads a stream&#39;s manifest</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>mediaObj: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Media</span></h5>
									<div class="tsd-comment tsd-typography">
										<p><a href="../interfaces/Types.Media.html">Media</a></p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> opt_startTime: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>start time, in seconds, to begin playback. Defaults to 0 for VOD and to the live edge for live.</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>Promise that resolves when the loading is done</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="off" class="tsd-anchor"></a>
					<h3>off</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">off<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.off</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Alias for <code>emitter.removeListener()</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v10.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="on" class="tsd-anchor"></a>
					<h3>on</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.on</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds the <code>listener</code> function to the end of the listeners array for the
										event named <code>eventName</code>. No checks are made to see if the <code>listener</code> has
										already been added. Multiple calls passing the same combination of <code>eventName</code>and <code>listener</code> will result in the <code>listener</code> being added, and called, multiple
									times.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<p>By default, event listeners are invoked in the order they are added. The<code>emitter.prependListener()</code> method can be used as an alternative to add the
								event listener to the beginning of the listeners array.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;a&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">prependListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;b&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   b</span>
<span style="color: #008000">//   a</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.101</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="once" class="tsd-anchor"></a>
					<h3>once</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds a <strong>one-time</strong><code>listener</code> function for the event named <code>eventName</code>. The
									next time <code>eventName</code> is triggered, this listener is removed and then invoked.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Ah, we have our first user!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<p>By default, event listeners are invoked in the order they are added. The<code>emitter.prependOnceListener()</code> method can be used as an alternative to add the
								event listener to the beginning of the listeners array.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;a&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">prependOnceListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;b&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   b</span>
<span style="color: #008000">//   a</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.3.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="pause" class="tsd-anchor"></a>
					<h3>pause</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">pause<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Pauses the video playback (equivalent to video.pause() on the original video element)</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="play" class="tsd-anchor"></a>
					<h3>play</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">play<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Starts the video playback (equivalent to video.play() on the original video element)</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>the play promise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="prependListener" class="tsd-anchor"></a>
					<h3>prepend<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">prepend<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.prependListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds the <code>listener</code> function to the <em>beginning</em> of the listeners array for the
										event named <code>eventName</code>. No checks are made to see if the <code>listener</code> has
										already been added. Multiple calls passing the same combination of <code>eventName</code>and <code>listener</code> will result in the <code>listener</code> being added, and called, multiple
									times.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">prependListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="prependOnceListener" class="tsd-anchor"></a>
					<h3>prepend<wbr>Once<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">prepend<wbr>Once<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.prependOnceListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds a <strong>one-time</strong><code>listener</code> function for the event named <code>eventName</code> to the_beginning_ of the listeners array. The next time <code>eventName</code> is triggered, this
									listener is removed, and then invoked.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">prependOnceListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Ah, we have our first user!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="programInfo" class="tsd-anchor"></a>
					<h3>program<wbr>Info</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">program<wbr>Info<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.TimeRange.html" class="tsd-signature-type">TimeRange</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Return the program information as reported in the stream manifest, if any,
									with program start time (<code>start</code>) and end time  (<code>end</code>) number properties in seconds</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="../interfaces/Types.TimeRange.html" class="tsd-signature-type">TimeRange</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="rawListeners" class="tsd-anchor"></a>
					<h3>raw<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">raw<wbr>Listeners<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.rawListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>,
									including any wrappers (such as those created by <code>.once()</code>).</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">emitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log once&#039;</span><span style="color: #000000">));</span>

<span style="color: #008000">// Returns a new Array with a function `onceWrapper` which has a property</span>
<span style="color: #008000">// `listener` which contains the original listener bound above</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">listeners</span><span style="color: #000000"> = </span><span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">rawListeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">logFnWrapper</span><span style="color: #000000"> = </span><span style="color: #001080">listeners</span><span style="color: #000000">[</span><span style="color: #098658">0</span><span style="color: #000000">];</span>

<span style="color: #008000">// Logs &quot;log once&quot; to the console and does not unbind the `once` event</span>
<span style="color: #001080">logFnWrapper</span><span style="color: #000000">.</span><span style="color: #795E26">listener</span><span style="color: #000000">();</span>

<span style="color: #008000">// Logs &quot;log once&quot; to the console and removes the listener</span>
<span style="color: #795E26">logFnWrapper</span><span style="color: #000000">();</span>

<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log persistently&#039;</span><span style="color: #000000">));</span>
<span style="color: #008000">// Will return a new Array with a single function bound by `.on()` above</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">newListeners</span><span style="color: #000000"> = </span><span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">rawListeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>

<span style="color: #008000">// Logs &quot;log persistently&quot; twice</span>
<span style="color: #001080">newListeners</span><span style="color: #000000">[</span><span style="color: #098658">0</span><span style="color: #000000">]();</span>
<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v9.4.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registerCapabilitiesFilter" class="tsd-anchor"></a>
					<h3>register<wbr>Capabilities<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>Capabilities<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Registers a filter for player capabilities.
									This method allows you to register a callback function that can select dash representations that can be used by the player. The callback function should take a dash representation object as its argument, modify it as necessary, and then return a boolean value indicating whether the representation should be used.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">boolean</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function to register. This function should take a dash representation object as its argument.</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>representation: <span class="tsd-signature-type">any</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registerLicenseRequestFilter" class="tsd-anchor"></a>
					<h3>register<wbr>License<wbr>Request<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>License<wbr>Request<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Register a filter to be applied on DRM license requests<br>
									See <a href="../pages/Documentation/playback-drm/license-wrapping.html">DRM License Wrapping</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>request: <a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registerLicenseResponseFilter" class="tsd-anchor"></a>
					<h3>register<wbr>License<wbr>Response<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>License<wbr>Response<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Register a filter to be applied on DRM license responses<br>
									See <a href="../pages/Documentation/playback-drm/license-wrapping.html">DRM License Wrapping</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>response: <a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registerRequestFilter" class="tsd-anchor"></a>
					<h3>register<wbr>Request<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>Request<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Register a filter to be applied on streaming request<br>
									See <a href="../pages/Documentation/player-configuration/custom-request.html">Custom HTTP request</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>xhr: <span class="tsd-signature-type">XMLHttpRequest</span></h5>
														</li>
														<li>
															<h5>url: <span class="tsd-signature-type">string</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="removeAllListeners" class="tsd-anchor"></a>
					<h3>remove<wbr>All<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>All<wbr>Listeners<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.removeAllListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Removes all listeners, or those of the specified <code>eventName</code>.</p>
								</div>
								<p>It is bad practice to remove listeners added elsewhere in the code,
									particularly when the <code>EventEmitter</code> instance was created by some other
								component or module (e.g. sockets or file streams).</p>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> event: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="removeListener" class="tsd-anchor"></a>
					<h3>remove<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.removeListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Removes the specified <code>listener</code> from the listener array for the event named<code>eventName</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callback</span><span style="color: #000000"> = (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>
<span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callback</span><span style="color: #000000">);</span>
<span style="color: #008000">// ...</span>
<span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callback</span><span style="color: #000000">);</span>
</code></pre>
								<p><code>removeListener()</code> will remove, at most, one instance of a listener from the
									listener array. If any single listener has been added multiple times to the
									listener array for the specified <code>eventName</code>, then <code>removeListener()</code> must be
								called multiple times to remove each instance.</p>
								<p>Once an event is emitted, all listeners attached to it at the
									time of emitting are called in order. This implies that any<code>removeListener()</code> or <code>removeAllListeners()</code> calls <em>after</em> emitting and_before_ the last listener finishes execution will
								not remove them from<code>emit()</code> in progress. Subsequent events behave as expected.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">MyEmitter</span><span style="color: #000000">();</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callbackA</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;A&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackB</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callbackB</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;B&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackA</span><span style="color: #000000">);</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackB</span><span style="color: #000000">);</span>

<span style="color: #008000">// callbackA removes listener callbackB but it will still be called.</span>
<span style="color: #008000">// Internal listener array at time of emit [callbackA, callbackB]</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   A</span>
<span style="color: #008000">//   B</span>

<span style="color: #008000">// callbackB is now removed.</span>
<span style="color: #008000">// Internal listener array [callbackA]</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   A</span>
</code></pre>
								<p>Because listeners are managed using an internal array, calling this will
									change the position indices of any listener registered <em>after</em> the listener
									being removed. This will not impact the order in which listeners are called,
									but it means that any copies of the listener array as returned by
								the <code>emitter.listeners()</code> method will need to be recreated.</p>
								<p>When a single function has been added as a handler multiple times for a single
									event (as in the example below), <code>removeListener()</code> will remove the most
								recently added instance. In the example the <code>once(&#39;ping&#39;)</code>listener is removed:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">pong</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;pong&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">);</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="reset" class="tsd-anchor"></a>
					<h3>reset</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">reset<span class="tsd-signature-symbol">(</span>resetForAds<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Stop/reset current stream playback.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>resetForAds: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="seekRange" class="tsd-anchor"></a>
					<h3>seek<wbr>Range</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">seek<wbr>Range<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.TimeRange.html" class="tsd-signature-type">TimeRange</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Return the available seek range in absolute video time values.
									The range with start and end number properties are expressed in seconds</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="../interfaces/Types.TimeRange.html" class="tsd-signature-type">TimeRange</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="seekToEndOfSection" class="tsd-anchor"></a>
					<h3>seek<wbr>ToEnd<wbr>OfSection</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">seek<wbr>ToEnd<wbr>OfSection<span class="tsd-signature-symbol">(</span>sectionType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Seeks to the end of the specified section in the current content.
									This is an instance method that seeks to the end of the specified section in the currently loaded content. If no content is loaded, or if the content has no sections, this method does nothing.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>sectionType: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The type of the section to seek to. This is a string that represents the type of the section to seek to as returned via the getSections API.</p>
									</div>
								</li>
								<li>
									<h5>sectionName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the section to seek to. This is a string that represents the name of the section to seek to as returned via the getSections API.</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="seekToSection" class="tsd-anchor"></a>
					<h3>seek<wbr>ToSection</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">seek<wbr>ToSection<span class="tsd-signature-symbol">(</span>sectionType<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, sectionName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Seeks to the start of the specified section in the current content.
									This is an instance method that seeks to the start of the specified section in the currently loaded content. If no content is loaded, or if the content has no sections, or if the specified section does not exist, this method does nothing.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>sectionType: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The type of the section to seek to. This is a string that represents the type of the section to seek to as returned via the getSections API.</p>
									</div>
								</li>
								<li>
									<h5>sectionName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the section to seek to. This is a string that represents the name of the section to seek to as returned via the getSections API.</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setAnalyticsMetadata" class="tsd-anchor"></a>
					<h3>set<wbr>Analytics<wbr>Metadata</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Analytics<wbr>Metadata<span class="tsd-signature-symbol">(</span>config<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ApplicationAnalytics.html" class="tsd-signature-type">ApplicationAnalytics</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Function to update the ApplicationAnalytics metadata after the stream has started
										It can be used on Live to notify that a new program has started by passing the sesContentProgramId and sesContentProgramName
									The sessions will then be splitted by program</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>config: <a href="../interfaces/Types.ApplicationAnalytics.html" class="tsd-signature-type">ApplicationAnalytics</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>the updated ApplicationAnalytics metadatas</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setBufferingWatchdog_" class="tsd-anchor"></a>
					<h3>set<wbr>Buffering<wbr>Watchdog_</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Buffering<wbr>Watchdog_<span class="tsd-signature-symbol">(</span>enable<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>enable: <span class="tsd-signature-type">boolean</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="setMaxListeners" class="tsd-anchor"></a>
					<h3>set<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span>n<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.setMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>By default <code>EventEmitter</code>s will print a warning if more than <code>10</code> listeners are
										added for a particular event. This is a useful default that helps finding
										memory leaks. The <code>emitter.setMaxListeners()</code> method allows the limit to be
									modified for this specific <code>EventEmitter</code> instance. The value can be set to<code>Infinity</code> (or <code>0</code>) to indicate an unlimited number of listeners.</p>
								</div>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.3.5</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>n: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="signalFullscreen" class="tsd-anchor"></a>
					<h3>signal<wbr>Fullscreen</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">signal<wbr>Fullscreen<span class="tsd-signature-symbol">(</span>mode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Inform the player that the videoContext div was set fullscreen.<br>
									Will be used by the player to trigger VAST fullscreen tracking events.<br></p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>mode: <span class="tsd-signature-type">boolean</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>set to true to signal player is in fullscreen false otherwise</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="skipCurrentSection" class="tsd-anchor"></a>
					<h3>skip<wbr>Current<wbr>Section</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">skip<wbr>Current<wbr>Section<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Skips the current section in the current content.
									This is an instance method that skips the current section in the currently loaded content. If no content is loaded, or if the content has no sections, this method does nothing.</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="unregisterCapabilitiesFilter" class="tsd-anchor"></a>
					<h3>unregister<wbr>Capabilities<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>Capabilities<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Unregisters a filter for player capabilities.
									This method allows you to unregister a callback function that can select dash representations that can be used by the player. The callback function should take a dash representation object as its argument, modify it as necessary, and then return a boolean value indicating whether the representation should be used.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">boolean</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function to unregister. This function should take a dash representation object as its argument.</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>representation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>representation: <span class="tsd-signature-type">any</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="unregisterLicenseRequestFilter" class="tsd-anchor"></a>
					<h3>unregister<wbr>License<wbr>Request<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>License<wbr>Request<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Unregister a filter applied on DRM license requests<br>
									See <a href="../pages/Documentation/playback-drm/license-wrapping.html">DRM License Wrapping</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>request<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>request: <a href="../interfaces/Types.RequestFilterData.html" class="tsd-signature-type">RequestFilterData</a></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="unregisterLicenseResponseFilter" class="tsd-anchor"></a>
					<h3>unregister<wbr>License<wbr>Response<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>License<wbr>Response<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Unregister a filter applied on DRM license responses<br>
									See <a href="../pages/Documentation/playback-drm/license-wrapping.html">DRM License Wrapping</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>response<span class="tsd-signature-symbol">: </span><a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>response: <a href="../interfaces/Types.ResponseFilterData.html" class="tsd-signature-type">ResponseFilterData</a></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="unregisterRequestFilter" class="tsd-anchor"></a>
					<h3>unregister<wbr>Request<wbr>Filter</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">unregister<wbr>Request<wbr>Filter<span class="tsd-signature-symbol">(</span>filter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Unregister a filter to be applies on streaming request<br>
									See <a href="../pages/Documentation/player-configuration/custom-request.html">Custom HTTP request</a> tutorial section for detailed use of this API</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>filter: <span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>xhr<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">XMLHttpRequest</span>, url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5>xhr: <span class="tsd-signature-type">XMLHttpRequest</span></h5>
														</li>
														<li>
															<h5>url: <span class="tsd-signature-type">string</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="updateVideoArea" class="tsd-anchor"></a>
					<h3>update<wbr>Video<wbr>Area</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">update<wbr>Video<wbr>Area<span class="tsd-signature-symbol">(</span>left<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, top<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, width<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, height<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, method<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Update Video Area. Should be call with 4 parameters to update the display area for video content playback.
										The 4 parameters specify the left video area position, top video area position, video area width, and video area height based on a 1920 x 1080 resolution virtual screen, regardless of the actual application resolution.
									This method should be call with no parameter to update the subtitle rendering area.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>left: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = ...</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>position in pixels of the display area.</p>
									</div>
								</li>
								<li>
									<h5>top: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = ...</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>position in pixels of the display area.</p>
									</div>
								</li>
								<li>
									<h5>width: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = ...</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>display area width in pixels.</p>
									</div>
								</li>
								<li>
									<h5>height: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = ...</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>display area height in pixels.</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> method: <span class="tsd-signature-type">string</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="wasLive" class="tsd-anchor"></a>
					<h3>was<wbr>Live</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">was<wbr>Live<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the current stream was Live before ending (Start Over Streal that reaches end of Program)</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getDeviceId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Device<wbr>Id</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Device<wbr>Id<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method to get the deviceId, it is reliable on SmartTVs and unreliable on Browsers (no permanent unicity)</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>deviceId</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="getEventListeners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Event<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Event<wbr>Listeners<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span>, name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.getEventListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>.</p>
								</div>
								<p>For <code>EventEmitter</code>s this behaves exactly the same as calling <code>.listeners</code> on
								the emitter.</p>
								<p>For <code>EventTarget</code>s this is the only way to get the event listeners for the
								event target. This is useful for debugging and diagnostic purposes.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">getEventListeners</span><span style="color: #000000">, </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">listener</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Events are fun&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">listener</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #795E26">getEventListeners</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// [listener]</span>
<span style="color: #000000">}</span>
<span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">et</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventTarget</span><span style="color: #000000">();</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">listener</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Events are fun&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">et</span><span style="color: #000000">.</span><span style="color: #795E26">addEventListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">listener</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #795E26">getEventListeners</span><span style="color: #000000">(</span><span style="color: #001080">et</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// [listener]</span>
<span style="color: #000000">}</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v15.2.0, v14.17.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span></h5>
								</li>
								<li>
									<h5>name: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="getSupportedTypes" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Supported<wbr>Types</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Supported<wbr>Types<span class="tsd-signature-symbol">(</span>types<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that returns a map of MediaSource support for well-known types or the types given in parameter if any.</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>types: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>to probe support of</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="init" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> init</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">init<span class="tsd-signature-symbol">(</span>parameters<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">VOFmaParameters</span>, initListener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">VOFmaManagerInitListener</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Init the FMA.
									It will fetch the config only if the previous one has expired (the expiration time can be set in the remote config, usually it is 1 day)</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>parameters: <span class="tsd-signature-type">VOFmaParameters</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>the initial parameter structure required for initialization.</p>
									</div>
								</li>
								<li>
									<h5>initListener: <span class="tsd-signature-type">VOFmaManagerInitListener</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>listener to get notified if the initializetion was successful or not (meaning we could get the config from the url from VOFmaParameters)</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>a promise which is resolved to true if the FMA was successfuly initialized and is ready to be used</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserChromiumBased" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Chromium<wbr>Based</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Chromium<wbr>Based<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is based on Chromium</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is based on Chromium, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserEdgeOrIE11" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Edge<wbr>OrIE11</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Edge<wbr>OrIE11<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is Microsoft IE11 or Edge</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is Microsoft IE11 or Edge, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserSafariIphoneIpadIpod" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Safari<wbr>Iphone<wbr>Ipad<wbr>Ipod</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Safari<wbr>Iphone<wbr>Ipad<wbr>Ipod<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is Safari running on Iphone or Ipad or Ipod</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is Safari on Iphone or Ipad or Ipod, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserSafariMacOS" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Safari<wbr>MacOS</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Safari<wbr>MacOS<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is Safari running on MacOS</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is Safari on MacOS, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserSafariiOS" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>SafariiOS</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>SafariiOS<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is Safari running on iOS</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is Safari on iOS, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserSafariiPadOS" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Safarii<wbr>PadOS</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Safarii<wbr>PadOS<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if the current browser is Safari running on iOS</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the browser is Safari on iPadOS, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isBrowserSupported" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Browser<wbr>Supported</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Browser<wbr>Supported<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that returns true if the player is supported in the current environment, false otherwise</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if the current browser is supported, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isPackagedSmartTVApp" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Packaged<wbr>SmartTVApp</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Packaged<wbr>SmartTVApp<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if we are on Smart TV packaged application</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if Smart TV packaged application, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="isSmartTv" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> is<wbr>Smart<wbr>Tv</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">is<wbr>Smart<wbr>Tv<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method that tells if we are on Smart TV environment</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
							<p>true if SmartTV, false otherwise</p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="listenerCount-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> listener<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listener<wbr>Count<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">EventEmitter</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listenerCount</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A class method that returns the number of listeners for the given <code>eventName</code>registered on the given <code>emitter</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">listenerCount</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #795E26">listenerCount</span><span style="color: #000000">(</span><span style="color: #001080">myEmitter</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">));</span>
<span style="color: #008000">// Prints: 2</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.9.12</p>
									</dd>
									<dt>deprecated</dt>
									<dd><p>Since v3.2.0 - Use <code>listenerCount</code> instead.</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">EventEmitter</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The emitter to query</p>
									</div>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The event name</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="on-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> on</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">EventEmitter</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">AsyncIterableIterator</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.on</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>```js
									const { on, EventEmitter } = require(&#39;events&#39;);</p>
								</div>
								<p>(async () =&gt; {
								const ee = new EventEmitter();</p>
								<p>  // Emit later on
									process.nextTick(() =&gt; {
									ee.emit(&#39;foo&#39;, &#39;bar&#39;);
									ee.emit(&#39;foo&#39;, 42);
								});</p>
								<p>  for await (const event of on(ee, &#39;foo&#39;)) {
									// The execution of this inner block is synchronous and it
									// processes one event at a time (even with await). Do not use
									// if concurrent execution is required.
									console.log(event); // prints [&#39;bar&#39;] [42]
									}
									// Unreachable here
								})();</p>
								<pre><code>
<span style="color: #001080">Returns</span><span style="color: #000000"> </span><span style="color: #001080">an</span><span style="color: #000000"> </span><span style="color: #A31515">`AsyncIterator`</span><span style="color: #000000"> </span><span style="color: #001080">that</span><span style="color: #000000"> </span><span style="color: #001080">iterates</span><span style="color: #000000"> </span><span style="color: #A31515">`eventName`</span><span style="color: #000000"> </span><span style="color: #001080">events</span><span style="color: #000000">. </span><span style="color: #001080">It</span><span style="color: #000000"> </span><span style="color: #001080">will</span><span style="color: #000000"> </span><span style="color: #AF00DB">throw</span>
<span style="color: #AF00DB">if</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #A31515">`EventEmitter`</span><span style="color: #000000"> </span><span style="color: #001080">emits</span><span style="color: #000000"> </span><span style="color: #A31515">`&#039;error&#039;`</span><span style="color: #000000">. </span><span style="color: #001080">It</span><span style="color: #000000"> </span><span style="color: #001080">removes</span><span style="color: #000000"> </span><span style="color: #001080">all</span><span style="color: #000000"> </span><span style="color: #001080">listeners</span><span style="color: #000000"> </span><span style="color: #001080">when</span>
<span style="color: #001080">exiting</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #001080">loop</span><span style="color: #000000">. </span><span style="color: #001080">The</span><span style="color: #000000"> </span><span style="color: #A31515">`value`</span><span style="color: #000000"> </span><span style="color: #001080">returned</span><span style="color: #000000"> </span><span style="color: #001080">by</span><span style="color: #000000"> </span><span style="color: #001080">each</span><span style="color: #000000"> </span><span style="color: #001080">iteration</span><span style="color: #000000"> </span><span style="color: #001080">is</span><span style="color: #000000"> </span><span style="color: #001080">an</span><span style="color: #000000"> </span><span style="color: #001080">array</span>
<span style="color: #001080">composed</span><span style="color: #000000"> </span><span style="color: #0000FF">of</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #001080">emitted</span><span style="color: #000000"> </span><span style="color: #001080">event</span><span style="color: #000000"> </span><span style="color: #0000FF">arguments</span><span style="color: #000000">.</span>

<span style="color: #001080">An</span><span style="color: #000000"> </span><span style="color: #A31515">`AbortSignal`</span><span style="color: #000000"> </span><span style="color: #001080">can</span><span style="color: #000000"> </span><span style="color: #001080">be</span><span style="color: #000000"> </span><span style="color: #001080">used</span><span style="color: #000000"> </span><span style="color: #001080">to</span><span style="color: #000000"> </span><span style="color: #001080">cancel</span><span style="color: #000000"> </span><span style="color: #001080">waiting</span><span style="color: #000000"> </span><span style="color: #001080">on</span><span style="color: #000000"> events:</span>

<span style="color: #A31515">```js</span>
<span style="color: #A31515">const { on, EventEmitter } = require(&#039;events&#039;);</span>
<span style="color: #A31515">const ac = new AbortController();</span>

<span style="color: #A31515">(async () =&gt; {</span>
<span style="color: #A31515">  const ee = new EventEmitter();</span>

<span style="color: #A31515">  // Emit later on</span>
<span style="color: #A31515">  process.nextTick(() =&gt; {</span>
<span style="color: #A31515">    ee.emit(&#039;foo&#039;, &#039;bar&#039;);</span>
<span style="color: #A31515">    ee.emit(&#039;foo&#039;, 42);</span>
<span style="color: #A31515">  });</span>

<span style="color: #A31515">  for await (const event of on(ee, &#039;foo&#039;, { signal: ac.signal })) {</span>
<span style="color: #A31515">    // The execution of this inner block is synchronous and it</span>
<span style="color: #A31515">    // processes one event at a time (even with await). Do not use</span>
<span style="color: #A31515">    // if concurrent execution is required.</span>
<span style="color: #A31515">    console.log(event); // prints [&#039;bar&#039;] [42]</span>
<span style="color: #A31515">  }</span>
<span style="color: #A31515">  // Unreachable here</span>
<span style="color: #A31515">})();</span>

<span style="color: #A31515">process.nextTick(() =&gt; ac.abort());</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v13.6.0, v12.16.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">EventEmitter</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event being listened for</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">AsyncIterableIterator</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>that iterates <code>eventName</code> events emitted by the <code>emitter</code></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="once-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> once</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">NodeEventTarget</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">DOMEventTarget</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Creates a <code>Promise</code> that is fulfilled when the <code>EventEmitter</code> emits the given
										event or that is rejected if the <code>EventEmitter</code> emits <code>&#39;error&#39;</code> while waiting.
										The <code>Promise</code> will resolve with an array of all the arguments emitted to the
									given event.</p>
								</div>
								<p>This method is intentionally generic and works with the web platform <a href="https://dom.spec.whatwg.org/#interface-eventtarget">EventTarget</a> interface, which has no special<code>&#39;error&#39;</code> event
								semantics and does not listen to the <code>&#39;error&#39;</code> event.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">once</span><span style="color: #000000">, </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">async</span><span style="color: #000000"> </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">run</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #000000">  </span><span style="color: #001080">process</span><span style="color: #000000">.</span><span style="color: #795E26">nextTick</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">, </span><span style="color: #098658">42</span><span style="color: #000000">);</span>
<span style="color: #000000">  });</span>

<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> [</span><span style="color: #0070C1">value</span><span style="color: #000000">] = </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">value</span><span style="color: #000000">);</span>

<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">err</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;kaboom&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">process</span><span style="color: #000000">.</span><span style="color: #795E26">nextTick</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">);</span>
<span style="color: #000000">  });</span>

<span style="color: #000000">  </span><span style="color: #AF00DB">try</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  } </span><span style="color: #AF00DB">catch</span><span style="color: #000000"> (</span><span style="color: #001080">err</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error happened&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">);</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}</span>

<span style="color: #795E26">run</span><span style="color: #000000">();</span>
</code></pre>
								<p>The special handling of the <code>&#39;error&#39;</code> event is only used when <code>events.once()</code>is used to wait for another event. If <code>events.once()</code> is used to wait for the
									&#39;<code>error&#39;</code> event itself, then it is treated as any other kind of event without
								special handling:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">once</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">)</span>
<span style="color: #000000">  .</span><span style="color: #795E26">then</span><span style="color: #000000">(([</span><span style="color: #001080">err</span><span style="color: #000000">]) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ok&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">))</span>
<span style="color: #000000">  .</span><span style="color: #795E26">catch</span><span style="color: #000000">((</span><span style="color: #001080">err</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">));</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;boom&#039;</span><span style="color: #000000">));</span>

<span style="color: #008000">// Prints: ok boom</span>
</code></pre>
								<p>An <code>AbortSignal</code> can be used to cancel waiting for the event:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">once</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ac</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">AbortController</span><span style="color: #000000">();</span>

<span style="color: #0000FF">async</span><span style="color: #000000"> </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">foo</span><span style="color: #000000">(</span><span style="color: #001080">emitter</span><span style="color: #000000">, </span><span style="color: #001080">event</span><span style="color: #000000">, </span><span style="color: #001080">signal</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">try</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">emitter</span><span style="color: #000000">, </span><span style="color: #001080">event</span><span style="color: #000000">, { </span><span style="color: #001080">signal</span><span style="color: #000000"> });</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event emitted!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  } </span><span style="color: #AF00DB">catch</span><span style="color: #000000"> (</span><span style="color: #001080">error</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">error</span><span style="color: #000000">.</span><span style="color: #001080">name</span><span style="color: #000000"> === </span><span style="color: #A31515">&#039;AbortError&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Waiting for the event was canceled!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">    } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;There was an error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">error</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}</span>

<span style="color: #795E26">foo</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">ac</span><span style="color: #000000">.</span><span style="color: #001080">signal</span><span style="color: #000000">);</span>
<span style="color: #001080">ac</span><span style="color: #000000">.</span><span style="color: #795E26">abort</span><span style="color: #000000">(); </span><span style="color: #008000">// Abort waiting for the event</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// Prints: Waiting for the event was canceled!</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v11.13.0, v10.16.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">NodeEventTarget</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">DOMEventTarget</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static">
					<a name="setLevel" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> set<wbr>Level</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Level<span class="tsd-signature-symbol">(</span>level<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A static method to set the log level,to call it : voplayer.Player.setLevel(level)</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>level: <span class="tsd-signature-type">number</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>Log level number</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="setMaxListeners-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> set<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span>n<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, <span class="tsd-signature-symbol">...</span>eventTargets<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.setMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>By default <code>EventEmitter</code>s will print a warning if more than <code>10</code> listeners are
										added for a particular event. This is a useful default that helps finding
										memory leaks. The <code>EventEmitter.setMaxListeners()</code> method allows the default limit to be
										modified (if eventTargets is empty) or modify the limit specified in every <code>EventTarget</code> | <code>EventEmitter</code> passed as arguments.
									The value can be set to<code>Infinity</code> (or <code>0</code>) to indicate an unlimited number of listeners.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">setMaxListeners</span><span style="color: #000000">(</span><span style="color: #098658">20</span><span style="color: #000000">);</span>
<span style="color: #008000">// Equivalent to</span>
<span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #001080">defaultMaxListeners</span><span style="color: #000000"> = </span><span style="color: #098658">20</span><span style="color: #000000">;</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">eventTarget</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventTarget</span><span style="color: #000000">();</span>
<span style="color: #008000">// Only way to increase limit for `EventTarget` instances</span>
<span style="color: #008000">// as these doesn&#039;t expose its own `setMaxListeners` method</span>
<span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">setMaxListeners</span><span style="color: #000000">(</span><span style="color: #098658">20</span><span style="color: #000000">, </span><span style="color: #001080">eventTarget</span><span style="color: #000000">);</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v15.3.0, v14.17.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> n: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>eventTargets: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Player.html">Player</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
				<li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static property</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>