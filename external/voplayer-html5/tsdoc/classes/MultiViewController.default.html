<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>default | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Class default</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/MultiViewController.html">MultiViewController</a>
				</li>
				<li>
					<a href="MultiViewController.default.html">default</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="tsd-signature-type">EventEmitter</span>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">default</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Constructors</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="MultiViewController.default.html#constructor" class="tsd-kind-icon">constructor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="MultiViewController.default.html#MAX_NUMBER_OF_PLAYER_INSTANCES" class="tsd-kind-icon">MAX_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><a href="MultiViewController.default.html#MIN_NUMBER_OF_PLAYER_INSTANCES" class="tsd-kind-icon">MIN_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#captureRejectionSymbol" class="tsd-kind-icon">capture<wbr>Rejection<wbr>Symbol</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#captureRejections" class="tsd-kind-icon">capture<wbr>Rejections</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#defaultMaxListeners" class="tsd-kind-icon">default<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#errorMonitor" class="tsd-kind-icon">error<wbr>Monitor</a></li>
							</ul>
						</section>
						<section class="tsd-index-section ">
							<h3>Methods</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#addListener" class="tsd-kind-icon">add<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#emit" class="tsd-kind-icon">emit</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#eventNames" class="tsd-kind-icon">event<wbr>Names</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#getLeaderPlayer" class="tsd-kind-icon">get<wbr>Leader<wbr>Player</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#getMaxListeners" class="tsd-kind-icon">get<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#getPlayers" class="tsd-kind-icon">get<wbr>Players</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#listenerCount" class="tsd-kind-icon">listener<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#listeners" class="tsd-kind-icon">listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#load" class="tsd-kind-icon">load</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#off" class="tsd-kind-icon">off</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#on" class="tsd-kind-icon">on</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#once" class="tsd-kind-icon">once</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#pauseAllPlayers" class="tsd-kind-icon">pause<wbr>All<wbr>Players</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#prependListener" class="tsd-kind-icon">prepend<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#prependOnceListener" class="tsd-kind-icon">prepend<wbr>Once<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#rawListeners" class="tsd-kind-icon">raw<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#registerPlayer" class="tsd-kind-icon">register<wbr>Player</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#removeAllListeners" class="tsd-kind-icon">remove<wbr>All<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#removeListener" class="tsd-kind-icon">remove<wbr>Listener</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#reset" class="tsd-kind-icon">reset</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#setAudio" class="tsd-kind-icon">set<wbr>Audio</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#setDebugMode" class="tsd-kind-icon">set<wbr>Debug<wbr>Mode</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external"><a href="MultiViewController.default.html#setMaxListeners" class="tsd-kind-icon">set<wbr>Max<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#setSyncConfig" class="tsd-kind-icon">set<wbr>Sync<wbr>Config</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#startAllPlayers" class="tsd-kind-icon">start<wbr>All<wbr>Players</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class"><a href="MultiViewController.default.html#swapLeaderPlayer" class="tsd-kind-icon">swap<wbr>Leader<wbr>Player</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#getEventListeners" class="tsd-kind-icon">get<wbr>Event<wbr>Listeners</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#listenerCount-1" class="tsd-kind-icon">listener<wbr>Count</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#on-1" class="tsd-kind-icon">on</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#once-1" class="tsd-kind-icon">once</a></li>
								<li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external"><a href="MultiViewController.default.html#setMaxListeners-1" class="tsd-kind-icon">set<wbr>Max<wbr>Listeners</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Constructors</h2>
				<section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite">
					<a name="constructor" class="tsd-anchor"></a>
					<h3>constructor</h3>
					<ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite">
						<li class="tsd-signature tsd-kind-icon">new default<span class="tsd-signature-symbol">(</span>multiViewConfig<span class="tsd-signature-symbol">: </span><a href="MultiViewTypes.MultiViewControllerConfig.html" class="tsd-signature-type">MultiViewControllerConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Overrides EventEmitter.constructor</p>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>multiViewConfig: <a href="MultiViewTypes.MultiViewControllerConfig.html" class="tsd-signature-type">MultiViewControllerConfig</a></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="MAX_NUMBER_OF_PLAYER_INSTANCES" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> MAX_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES</h3>
					<div class="tsd-signature tsd-kind-icon">MAX_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 4</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Maximum number of player instance to use the MultiviewController</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-static">
					<a name="MIN_NUMBER_OF_PLAYER_INSTANCES" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> MIN_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES</h3>
					<div class="tsd-signature tsd-kind-icon">MIN_<wbr>NUMBER_<wbr>OF_<wbr>PLAYER_<wbr>INSTANCES<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 2</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Minimum number of player instance to use the MultiviewController</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="captureRejectionSymbol" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> capture<wbr>Rejection<wbr>Symbol</h3>
					<div class="tsd-signature tsd-kind-icon">capture<wbr>Rejection<wbr>Symbol<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">typeof </span><a href="Player.Player-1.html#captureRejectionSymbol" class="tsd-signature-type">captureRejectionSymbol</a></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.captureRejectionSymbol</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="captureRejections" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> capture<wbr>Rejections</h3>
					<div class="tsd-signature tsd-kind-icon">capture<wbr>Rejections<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.captureRejections</p>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Sets or gets the default captureRejection value for all emitters.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="defaultMaxListeners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> default<wbr>Max<wbr>Listeners</h3>
					<div class="tsd-signature tsd-kind-icon">default<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.defaultMaxListeners</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="errorMonitor" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> <span class="tsd-flag ts-flagReadonly">Readonly</span> error<wbr>Monitor</h3>
					<div class="tsd-signature tsd-kind-icon">error<wbr>Monitor<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">typeof </span><a href="Player.Player-1.html#errorMonitor" class="tsd-signature-type">errorMonitor</a></div>
					<aside class="tsd-sources">
						<p>Inherited from EventEmitter.errorMonitor</p>
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>This symbol shall be used to install a listener for only monitoring <code>&#39;error&#39;</code>
								events. Listeners installed using this symbol are called before the regular
							<code>&#39;error&#39;</code> listeners are called.</p>
						</div>
						<p>Installing a listener using this symbol does not change the behavior once an
							<code>&#39;error&#39;</code> event is emitted, therefore the process will still crash if no
						regular <code>&#39;error&#39;</code> listener is installed.</p>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Methods</h2>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="addListener" class="tsd-anchor"></a>
					<h3>add<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">add<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.addListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Alias for <code>emitter.on(eventName, listener)</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="emit" class="tsd-anchor"></a>
					<h3>emit</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">emit<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, <span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.emit</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Synchronously calls each of the listeners registered for the event named<code>eventName</code>, in the order they were registered, passing the supplied arguments
									to each.</p>
								</div>
								<p>Returns <code>true</code> if the event had listeners, <code>false</code> otherwise.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #008000">// First listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">firstListener</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Helloooo! first listener&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #008000">// Second listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">secondListener</span><span style="color: #000000">(</span><span style="color: #001080">arg1</span><span style="color: #000000">, </span><span style="color: #001080">arg2</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">`event with parameters </span><span style="color: #0000FF">${</span><span style="color: #001080">arg1</span><span style="color: #0000FF">}</span><span style="color: #A31515">, </span><span style="color: #0000FF">${</span><span style="color: #001080">arg2</span><span style="color: #0000FF">}</span><span style="color: #A31515"> in second listener`</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #008000">// Third listener</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">thirdListener</span><span style="color: #000000">(...</span><span style="color: #001080">args</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">parameters</span><span style="color: #000000"> = </span><span style="color: #001080">args</span><span style="color: #000000">.</span><span style="color: #795E26">join</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;, &#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">`event with parameters </span><span style="color: #0000FF">${</span><span style="color: #001080">parameters</span><span style="color: #0000FF">}</span><span style="color: #A31515"> in third listener`</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>

<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">listeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">));</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #098658">1</span><span style="color: #000000">, </span><span style="color: #098658">2</span><span style="color: #000000">, </span><span style="color: #098658">3</span><span style="color: #000000">, </span><span style="color: #098658">4</span><span style="color: #000000">, </span><span style="color: #098658">5</span><span style="color: #000000">);</span>

<span style="color: #008000">// Prints:</span>
<span style="color: #008000">// [</span>
<span style="color: #008000">//   [Function: firstListener],</span>
<span style="color: #008000">//   [Function: secondListener],</span>
<span style="color: #008000">//   [Function: thirdListener]</span>
<span style="color: #008000">// ]</span>
<span style="color: #008000">// Helloooo! first listener</span>
<span style="color: #008000">// event with parameters 1, 2 in second listener</span>
<span style="color: #008000">// event with parameters 1, 2, 3, 4, 5 in third listener</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="eventNames" class="tsd-anchor"></a>
					<h3>event<wbr>Names</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">event<wbr>Names<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.eventNames</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns an array listing the events for which the emitter has registered
									listeners. The values in the array are strings or <code>Symbol</code>s.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;bar&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">sym</span><span style="color: #000000"> = </span><span style="color: #267F99">Symbol</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;symbol&#039;</span><span style="color: #000000">);</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #001080">sym</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>

<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">eventNames</span><span style="color: #000000">());</span>
<span style="color: #008000">// Prints: [ &#039;foo&#039;, &#039;bar&#039;, Symbol(symbol) ]</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getLeaderPlayer" class="tsd-anchor"></a>
					<h3>get<wbr>Leader<wbr>Player</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Leader<wbr>Player<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the current leader player</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="getMaxListeners" class="tsd-anchor"></a>
					<h3>get<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.getMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the current max listener value for the <code>EventEmitter</code> which is either
									set by <code>emitter.setMaxListeners(n)</code> or defaults to <a href="MultiViewController.default.html#defaultMaxListeners">defaultMaxListeners</a>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v1.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="getPlayers" class="tsd-anchor"></a>
					<h3>get<wbr>Players</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Players<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Player</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the list of registered players</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Player</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="listenerCount" class="tsd-anchor"></a>
					<h3>listener<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listener<wbr>Count<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listenerCount</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns the number of listeners listening to the event named <code>eventName</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v3.2.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event being listened for</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="listeners" class="tsd-anchor"></a>
					<h3>listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listeners<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">util</span><span style="color: #000000">.</span><span style="color: #795E26">inspect</span><span style="color: #000000">(</span><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">listeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">)));</span>
<span style="color: #008000">// Prints: [ [Function] ]</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="load" class="tsd-anchor"></a>
					<h3>load</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">load<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Call load on all the registered players and start the playback on all players
										Should be called only once after all the players have been registrered
									Emits an error it was already called or is the minimum number of player has not been reached</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="off" class="tsd-anchor"></a>
					<h3>off</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">off<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.off</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Alias for <code>emitter.removeListener()</code>.</p>
								</div>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v10.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="on" class="tsd-anchor"></a>
					<h3>on</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.on</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds the <code>listener</code> function to the end of the listeners array for the
										event named <code>eventName</code>. No checks are made to see if the <code>listener</code> has
										already been added. Multiple calls passing the same combination of <code>eventName</code>and <code>listener</code> will result in the <code>listener</code> being added, and called, multiple
									times.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<p>By default, event listeners are invoked in the order they are added. The<code>emitter.prependListener()</code> method can be used as an alternative to add the
								event listener to the beginning of the listeners array.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;a&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">prependListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;b&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   b</span>
<span style="color: #008000">//   a</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.101</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="once" class="tsd-anchor"></a>
					<h3>once</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds a <strong>one-time</strong><code>listener</code> function for the event named <code>eventName</code>. The
									next time <code>eventName</code> is triggered, this listener is removed and then invoked.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Ah, we have our first user!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<p>By default, event listeners are invoked in the order they are added. The<code>emitter.prependOnceListener()</code> method can be used as an alternative to add the
								event listener to the beginning of the listeners array.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEE</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;a&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">prependOnceListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;b&#039;</span><span style="color: #000000">));</span>
<span style="color: #001080">myEE</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   b</span>
<span style="color: #008000">//   a</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.3.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="pauseAllPlayers" class="tsd-anchor"></a>
					<h3>pause<wbr>All<wbr>Players</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">pause<wbr>All<wbr>Players<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Call pause on all the registered players</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="prependListener" class="tsd-anchor"></a>
					<h3>prepend<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">prepend<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.prependListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds the <code>listener</code> function to the <em>beginning</em> of the listeners array for the
										event named <code>eventName</code>. No checks are made to see if the <code>listener</code> has
										already been added. Multiple calls passing the same combination of <code>eventName</code>and <code>listener</code> will result in the <code>listener</code> being added, and called, multiple
									times.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">prependListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="prependOnceListener" class="tsd-anchor"></a>
					<h3>prepend<wbr>Once<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">prepend<wbr>Once<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.prependOnceListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Adds a <strong>one-time</strong><code>listener</code> function for the event named <code>eventName</code> to the_beginning_ of the listeners array. The next time <code>eventName</code> is triggered, this
									listener is removed, and then invoked.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">prependOnceListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Ah, we have our first user!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">});</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v6.0.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event.</p>
									</div>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The callback function</p>
									</div>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="rawListeners" class="tsd-anchor"></a>
					<h3>raw<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">raw<wbr>Listeners<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.rawListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>,
									including any wrappers (such as those created by <code>.once()</code>).</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">emitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log once&#039;</span><span style="color: #000000">));</span>

<span style="color: #008000">// Returns a new Array with a function `onceWrapper` which has a property</span>
<span style="color: #008000">// `listener` which contains the original listener bound above</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">listeners</span><span style="color: #000000"> = </span><span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">rawListeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">logFnWrapper</span><span style="color: #000000"> = </span><span style="color: #001080">listeners</span><span style="color: #000000">[</span><span style="color: #098658">0</span><span style="color: #000000">];</span>

<span style="color: #008000">// Logs &quot;log once&quot; to the console and does not unbind the `once` event</span>
<span style="color: #001080">logFnWrapper</span><span style="color: #000000">.</span><span style="color: #795E26">listener</span><span style="color: #000000">();</span>

<span style="color: #008000">// Logs &quot;log once&quot; to the console and removes the listener</span>
<span style="color: #795E26">logFnWrapper</span><span style="color: #000000">();</span>

<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log persistently&#039;</span><span style="color: #000000">));</span>
<span style="color: #008000">// Will return a new Array with a single function bound by `.on()` above</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">newListeners</span><span style="color: #000000"> = </span><span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">rawListeners</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>

<span style="color: #008000">// Logs &quot;log persistently&quot; twice</span>
<span style="color: #001080">newListeners</span><span style="color: #000000">[</span><span style="color: #098658">0</span><span style="color: #000000">]();</span>
<span style="color: #001080">emitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;log&#039;</span><span style="color: #000000">);</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v9.4.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="registerPlayer" class="tsd-anchor"></a>
					<h3>register<wbr>Player</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">register<wbr>Player<span class="tsd-signature-symbol">(</span>videoElementId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, mediaObj<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Media</span>, config<span class="tsd-signature-symbol">: </span><a href="../interfaces/MultiViewTypes.IMultiViewPlayerConfig.html" class="tsd-signature-type">IMultiViewPlayerConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Add a new player to the MultiviewController
									Emits an error if the load() was already called or if the maximum number of player has been reached</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>videoElementId: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>(string) id of the html video element</p>
									</div>
								</li>
								<li>
									<h5>mediaObj: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Media</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>(string | Media) media object to pass to the player</p>
									</div>
								</li>
								<li>
									<h5>config: <a href="../interfaces/MultiViewTypes.IMultiViewPlayerConfig.html" class="tsd-signature-type">IMultiViewPlayerConfig</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>(IMultiViewPlayerConfig) synchronisation configuration</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="removeAllListeners" class="tsd-anchor"></a>
					<h3>remove<wbr>All<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>All<wbr>Listeners<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.removeAllListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Removes all listeners, or those of the specified <code>eventName</code>.</p>
								</div>
								<p>It is bad practice to remove listeners added elsewhere in the code,
									particularly when the <code>EventEmitter</code> instance was created by some other
								component or module (e.g. sockets or file streams).</p>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> event: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="removeListener" class="tsd-anchor"></a>
					<h3>remove<wbr>Listener</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">remove<wbr>Listener<span class="tsd-signature-symbol">(</span>eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, listener<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.removeListener</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Removes the specified <code>listener</code> from the listener array for the event named<code>eventName</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callback</span><span style="color: #000000"> = (</span><span style="color: #001080">stream</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;someone connected!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>
<span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callback</span><span style="color: #000000">);</span>
<span style="color: #008000">// ...</span>
<span style="color: #001080">server</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;connection&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callback</span><span style="color: #000000">);</span>
</code></pre>
								<p><code>removeListener()</code> will remove, at most, one instance of a listener from the
									listener array. If any single listener has been added multiple times to the
									listener array for the specified <code>eventName</code>, then <code>removeListener()</code> must be
								called multiple times to remove each instance.</p>
								<p>Once an event is emitted, all listeners attached to it at the
									time of emitting are called in order. This implies that any<code>removeListener()</code> or <code>removeAllListeners()</code> calls <em>after</em> emitting and_before_ the last listener finishes execution will
								not remove them from<code>emit()</code> in progress. Subsequent events behave as expected.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">MyEmitter</span><span style="color: #000000">();</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callbackA</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;A&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackB</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">callbackB</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;B&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">};</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackA</span><span style="color: #000000">);</span>

<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, </span><span style="color: #001080">callbackB</span><span style="color: #000000">);</span>

<span style="color: #008000">// callbackA removes listener callbackB but it will still be called.</span>
<span style="color: #008000">// Internal listener array at time of emit [callbackA, callbackB]</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   A</span>
<span style="color: #008000">//   B</span>

<span style="color: #008000">// callbackB is now removed.</span>
<span style="color: #008000">// Internal listener array [callbackA]</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// Prints:</span>
<span style="color: #008000">//   A</span>
</code></pre>
								<p>Because listeners are managed using an internal array, calling this will
									change the position indices of any listener registered <em>after</em> the listener
									being removed. This will not impact the order in which listeners are called,
									but it means that any copies of the listener array as returned by
								the <code>emitter.listeners()</code> method will need to be recreated.</p>
								<p>When a single function has been added as a handler multiple times for a single
									event (as in the example below), <code>removeListener()</code> will remove the most
								recently added instance. In the example the <code>once(&#39;ping&#39;)</code>listener is removed:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">pong</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;pong&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">removeListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">, </span><span style="color: #001080">pong</span><span style="color: #000000">);</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">);</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ping&#039;</span><span style="color: #000000">);</span>
</code></pre>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.1.26</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5>listener: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5>
									<ul class="tsd-parameters">
										<li class="tsd-parameter-signature">
											<ul class="tsd-signatures tsd-kind-type-literal tsd-is-external">
												<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">...</span>args<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
											</ul>
											<ul class="tsd-descriptions">
												<li class="tsd-description">
													<h4 class="tsd-parameters-title">Parameters</h4>
													<ul class="tsd-parameters">
														<li>
															<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>args: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></h5>
														</li>
													</ul>
													<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
												</li>
											</ul>
										</li>
									</ul>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="reset" class="tsd-anchor"></a>
					<h3>reset</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">reset<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Reset the MultiviewController</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setAudio" class="tsd-anchor"></a>
					<h3>set<wbr>Audio</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Audio<span class="tsd-signature-symbol">(</span>playerRef<span class="tsd-signature-symbol">?: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Set the audio to be used
									If set, when swaping the player it will stay on this audio</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> playerRef: <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>(Player) player ref of the player from which we want to use the audio</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setDebugMode" class="tsd-anchor"></a>
					<h3>set<wbr>Debug<wbr>Mode</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Debug<wbr>Mode<span class="tsd-signature-symbol">(</span>isDebug<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Set debug mode for the MultiviewController</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>isDebug: <span class="tsd-signature-type">boolean</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>(boolean) if true, the multiview controller will emit some debug events MultiViewEvent.ON_DEBUG and MultiViewEvent.ON_DEBUG_ACTION</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
					<a name="setMaxListeners" class="tsd-anchor"></a>
					<h3>set<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span>n<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="MultiViewController.default.html" class="tsd-signature-type">default</a></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.setMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>By default <code>EventEmitter</code>s will print a warning if more than <code>10</code> listeners are
										added for a particular event. This is a useful default that helps finding
										memory leaks. The <code>emitter.setMaxListeners()</code> method allows the limit to be
									modified for this specific <code>EventEmitter</code> instance. The value can be set to<code>Infinity</code> (or <code>0</code>) to indicate an unlimited number of listeners.</p>
								</div>
								<p>Returns a reference to the <code>EventEmitter</code>, so that calls can be chained.</p>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.3.5</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>n: <span class="tsd-signature-type">number</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <a href="MultiViewController.default.html" class="tsd-signature-type">default</a></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="setSyncConfig" class="tsd-anchor"></a>
					<h3>set<wbr>Sync<wbr>Config</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Sync<wbr>Config<span class="tsd-signature-symbol">(</span>multiViewSyncConfig<span class="tsd-signature-symbol">: </span><a href="MultiViewTypes.MultiViewSyncConfig.html" class="tsd-signature-type">MultiViewSyncConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Set the Multiview synchronisation configuration</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>multiViewSyncConfig: <a href="MultiViewTypes.MultiViewSyncConfig.html" class="tsd-signature-type">MultiViewSyncConfig</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>synchronisation configuration (MultiViewSyncConfig) can be set during playback to update the configuration</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="startAllPlayers" class="tsd-anchor"></a>
					<h3>start<wbr>All<wbr>Players</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">start<wbr>All<wbr>Players<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Call start on all the registered players</p>
								</div>
							</div>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class">
					<a name="swapLeaderPlayer" class="tsd-anchor"></a>
					<h3>swap<wbr>Leader<wbr>Player</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class">
						<li class="tsd-signature tsd-kind-icon">swap<wbr>Leader<wbr>Player<span class="tsd-signature-symbol">(</span>playerRef<span class="tsd-signature-symbol">: </span><a href="Player.Player-1.html" class="tsd-signature-type">Player</a>, shouldSwapVideoElement<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Swap the playerRef player passed as parameter with the current leader player
										By default it will set the audio to the new leader player
									If possible, it will set lower qualities to the secondary player and higher quality to the leader player</p>
								</div>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>playerRef: <a href="Player.Player-1.html" class="tsd-signature-type">Player</a></h5>
									<div class="tsd-comment tsd-typography">
										<p>(Player) player ref of the leader player</p>
									</div>
								</li>
								<li>
									<h5>shouldSwapVideoElement: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>(boolean) if true, it will emit MultiViewEvent.ON_SWAP_VIDEO_ELEMENT after the players have been swaped</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="getEventListeners" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> get<wbr>Event<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">get<wbr>Event<wbr>Listeners<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span>, name<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.getEventListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Returns a copy of the array of listeners for the event named <code>eventName</code>.</p>
								</div>
								<p>For <code>EventEmitter</code>s this behaves exactly the same as calling <code>.listeners</code> on
								the emitter.</p>
								<p>For <code>EventTarget</code>s this is the only way to get the event listeners for the
								event target. This is useful for debugging and diagnostic purposes.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">getEventListeners</span><span style="color: #000000">, </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">listener</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Events are fun&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">listener</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #795E26">getEventListeners</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// [listener]</span>
<span style="color: #000000">}</span>
<span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">et</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventTarget</span><span style="color: #000000">();</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">listener</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Events are fun&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">et</span><span style="color: #000000">.</span><span style="color: #795E26">addEventListener</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">listener</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #795E26">getEventListeners</span><span style="color: #000000">(</span><span style="color: #001080">et</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// [listener]</span>
<span style="color: #000000">}</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v15.2.0, v14.17.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span></h5>
								</li>
								<li>
									<h5>name: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Function</span><span class="tsd-signature-symbol">[]</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="listenerCount-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> listener<wbr>Count</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">listener<wbr>Count<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">EventEmitter</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.listenerCount</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>A class method that returns the number of listeners for the given <code>eventName</code>registered on the given <code>emitter</code>.</p>
								</div>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">listenerCount</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">myEmitter</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">myEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">, () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {});</span>
<span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #795E26">listenerCount</span><span style="color: #000000">(</span><span style="color: #001080">myEmitter</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;event&#039;</span><span style="color: #000000">));</span>
<span style="color: #008000">// Prints: 2</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v0.9.12</p>
									</dd>
									<dt>deprecated</dt>
									<dd><p>Since v3.2.0 - Use <code>listenerCount</code> instead.</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">EventEmitter</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The emitter to query</p>
									</div>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The event name</p>
									</div>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="on-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> on</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">EventEmitter</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">AsyncIterableIterator</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.on</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>```js
									const { on, EventEmitter } = require(&#39;events&#39;);</p>
								</div>
								<p>(async () =&gt; {
								const ee = new EventEmitter();</p>
								<p>  // Emit later on
									process.nextTick(() =&gt; {
									ee.emit(&#39;foo&#39;, &#39;bar&#39;);
									ee.emit(&#39;foo&#39;, 42);
								});</p>
								<p>  for await (const event of on(ee, &#39;foo&#39;)) {
									// The execution of this inner block is synchronous and it
									// processes one event at a time (even with await). Do not use
									// if concurrent execution is required.
									console.log(event); // prints [&#39;bar&#39;] [42]
									}
									// Unreachable here
								})();</p>
								<pre><code>
<span style="color: #001080">Returns</span><span style="color: #000000"> </span><span style="color: #001080">an</span><span style="color: #000000"> </span><span style="color: #A31515">`AsyncIterator`</span><span style="color: #000000"> </span><span style="color: #001080">that</span><span style="color: #000000"> </span><span style="color: #001080">iterates</span><span style="color: #000000"> </span><span style="color: #A31515">`eventName`</span><span style="color: #000000"> </span><span style="color: #001080">events</span><span style="color: #000000">. </span><span style="color: #001080">It</span><span style="color: #000000"> </span><span style="color: #001080">will</span><span style="color: #000000"> </span><span style="color: #AF00DB">throw</span>
<span style="color: #AF00DB">if</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #A31515">`EventEmitter`</span><span style="color: #000000"> </span><span style="color: #001080">emits</span><span style="color: #000000"> </span><span style="color: #A31515">`&#039;error&#039;`</span><span style="color: #000000">. </span><span style="color: #001080">It</span><span style="color: #000000"> </span><span style="color: #001080">removes</span><span style="color: #000000"> </span><span style="color: #001080">all</span><span style="color: #000000"> </span><span style="color: #001080">listeners</span><span style="color: #000000"> </span><span style="color: #001080">when</span>
<span style="color: #001080">exiting</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #001080">loop</span><span style="color: #000000">. </span><span style="color: #001080">The</span><span style="color: #000000"> </span><span style="color: #A31515">`value`</span><span style="color: #000000"> </span><span style="color: #001080">returned</span><span style="color: #000000"> </span><span style="color: #001080">by</span><span style="color: #000000"> </span><span style="color: #001080">each</span><span style="color: #000000"> </span><span style="color: #001080">iteration</span><span style="color: #000000"> </span><span style="color: #001080">is</span><span style="color: #000000"> </span><span style="color: #001080">an</span><span style="color: #000000"> </span><span style="color: #001080">array</span>
<span style="color: #001080">composed</span><span style="color: #000000"> </span><span style="color: #0000FF">of</span><span style="color: #000000"> </span><span style="color: #001080">the</span><span style="color: #000000"> </span><span style="color: #001080">emitted</span><span style="color: #000000"> </span><span style="color: #001080">event</span><span style="color: #000000"> </span><span style="color: #0000FF">arguments</span><span style="color: #000000">.</span>

<span style="color: #001080">An</span><span style="color: #000000"> </span><span style="color: #A31515">`AbortSignal`</span><span style="color: #000000"> </span><span style="color: #001080">can</span><span style="color: #000000"> </span><span style="color: #001080">be</span><span style="color: #000000"> </span><span style="color: #001080">used</span><span style="color: #000000"> </span><span style="color: #001080">to</span><span style="color: #000000"> </span><span style="color: #001080">cancel</span><span style="color: #000000"> </span><span style="color: #001080">waiting</span><span style="color: #000000"> </span><span style="color: #001080">on</span><span style="color: #000000"> events:</span>

<span style="color: #A31515">```js</span>
<span style="color: #A31515">const { on, EventEmitter } = require(&#039;events&#039;);</span>
<span style="color: #A31515">const ac = new AbortController();</span>

<span style="color: #A31515">(async () =&gt; {</span>
<span style="color: #A31515">  const ee = new EventEmitter();</span>

<span style="color: #A31515">  // Emit later on</span>
<span style="color: #A31515">  process.nextTick(() =&gt; {</span>
<span style="color: #A31515">    ee.emit(&#039;foo&#039;, &#039;bar&#039;);</span>
<span style="color: #A31515">    ee.emit(&#039;foo&#039;, 42);</span>
<span style="color: #A31515">  });</span>

<span style="color: #A31515">  for await (const event of on(ee, &#039;foo&#039;, { signal: ac.signal })) {</span>
<span style="color: #A31515">    // The execution of this inner block is synchronous and it</span>
<span style="color: #A31515">    // processes one event at a time (even with await). Do not use</span>
<span style="color: #A31515">    // if concurrent execution is required.</span>
<span style="color: #A31515">    console.log(event); // prints [&#039;bar&#039;] [42]</span>
<span style="color: #A31515">  }</span>
<span style="color: #A31515">  // Unreachable here</span>
<span style="color: #A31515">})();</span>

<span style="color: #A31515">process.nextTick(() =&gt; ac.abort());</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v13.6.0, v12.16.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">EventEmitter</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span></h5>
									<div class="tsd-comment tsd-typography">
										<p>The name of the event being listened for</p>
									</div>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">AsyncIterableIterator</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h4>
							<p>that iterates <code>eventName</code> events emitted by the <code>emitter</code></p>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="once-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> once</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">NodeEventTarget</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
						<li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>emitter<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">DOMEventTarget</span>, eventName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, options<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">StaticEventEmitterOptions</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>Creates a <code>Promise</code> that is fulfilled when the <code>EventEmitter</code> emits the given
										event or that is rejected if the <code>EventEmitter</code> emits <code>&#39;error&#39;</code> while waiting.
										The <code>Promise</code> will resolve with an array of all the arguments emitted to the
									given event.</p>
								</div>
								<p>This method is intentionally generic and works with the web platform <a href="https://dom.spec.whatwg.org/#interface-eventtarget">EventTarget</a> interface, which has no special<code>&#39;error&#39;</code> event
								semantics and does not listen to the <code>&#39;error&#39;</code> event.</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">once</span><span style="color: #000000">, </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">async</span><span style="color: #000000"> </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">run</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #000000">  </span><span style="color: #001080">process</span><span style="color: #000000">.</span><span style="color: #795E26">nextTick</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">, </span><span style="color: #098658">42</span><span style="color: #000000">);</span>
<span style="color: #000000">  });</span>

<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> [</span><span style="color: #0070C1">value</span><span style="color: #000000">] = </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">value</span><span style="color: #000000">);</span>

<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">err</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;kaboom&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #001080">process</span><span style="color: #000000">.</span><span style="color: #795E26">nextTick</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">);</span>
<span style="color: #000000">  });</span>

<span style="color: #000000">  </span><span style="color: #AF00DB">try</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;myevent&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  } </span><span style="color: #AF00DB">catch</span><span style="color: #000000"> (</span><span style="color: #001080">err</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error happened&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">);</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}</span>

<span style="color: #795E26">run</span><span style="color: #000000">();</span>
</code></pre>
								<p>The special handling of the <code>&#39;error&#39;</code> event is only used when <code>events.once()</code>is used to wait for another event. If <code>events.once()</code> is used to wait for the
									&#39;<code>error&#39;</code> event itself, then it is treated as any other kind of event without
								special handling:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">once</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>

<span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">)</span>
<span style="color: #000000">  .</span><span style="color: #795E26">then</span><span style="color: #000000">(([</span><span style="color: #001080">err</span><span style="color: #000000">]) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ok&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">))</span>
<span style="color: #000000">  .</span><span style="color: #795E26">catch</span><span style="color: #000000">((</span><span style="color: #001080">err</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">err</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">));</span>

<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;boom&#039;</span><span style="color: #000000">));</span>

<span style="color: #008000">// Prints: ok boom</span>
</code></pre>
								<p>An <code>AbortSignal</code> can be used to cancel waiting for the event:</p>
								<pre><code class="language-js"><span style="color: #0000FF">const</span><span style="color: #000000"> { </span><span style="color: #0070C1">EventEmitter</span><span style="color: #000000">, </span><span style="color: #0070C1">once</span><span style="color: #000000"> } = </span><span style="color: #795E26">require</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;events&#039;</span><span style="color: #000000">);</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ee</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventEmitter</span><span style="color: #000000">();</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">ac</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">AbortController</span><span style="color: #000000">();</span>

<span style="color: #0000FF">async</span><span style="color: #000000"> </span><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">foo</span><span style="color: #000000">(</span><span style="color: #001080">emitter</span><span style="color: #000000">, </span><span style="color: #001080">event</span><span style="color: #000000">, </span><span style="color: #001080">signal</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">try</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">await</span><span style="color: #000000"> </span><span style="color: #795E26">once</span><span style="color: #000000">(</span><span style="color: #001080">emitter</span><span style="color: #000000">, </span><span style="color: #001080">event</span><span style="color: #000000">, { </span><span style="color: #001080">signal</span><span style="color: #000000"> });</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;event emitted!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  } </span><span style="color: #AF00DB">catch</span><span style="color: #000000"> (</span><span style="color: #001080">error</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">error</span><span style="color: #000000">.</span><span style="color: #001080">name</span><span style="color: #000000"> === </span><span style="color: #A31515">&#039;AbortError&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Waiting for the event was canceled!&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">    } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;There was an error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">error</span><span style="color: #000000">.</span><span style="color: #001080">message</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}</span>

<span style="color: #795E26">foo</span><span style="color: #000000">(</span><span style="color: #001080">ee</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">, </span><span style="color: #001080">ac</span><span style="color: #000000">.</span><span style="color: #001080">signal</span><span style="color: #000000">);</span>
<span style="color: #001080">ac</span><span style="color: #000000">.</span><span style="color: #795E26">abort</span><span style="color: #000000">(); </span><span style="color: #008000">// Abort waiting for the event</span>
<span style="color: #001080">ee</span><span style="color: #000000">.</span><span style="color: #795E26">emit</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;foo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// Prints: Waiting for the event was canceled!</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v11.13.0, v10.16.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">NodeEventTarget</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">symbol</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.once</p>
							</aside>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5>emitter: <span class="tsd-signature-type">DOMEventTarget</span></h5>
								</li>
								<li>
									<h5>eventName: <span class="tsd-signature-type">string</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> options: <span class="tsd-signature-type">StaticEventEmitterOptions</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">&gt;</span></h4>
						</li>
					</ul>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
					<a name="setMaxListeners-1" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagStatic">Static</span> set<wbr>Max<wbr>Listeners</h3>
					<ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-static tsd-is-external">
						<li class="tsd-signature tsd-kind-icon">set<wbr>Max<wbr>Listeners<span class="tsd-signature-symbol">(</span>n<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, <span class="tsd-signature-symbol">...</span>eventTargets<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li>
					</ul>
					<ul class="tsd-descriptions">
						<li class="tsd-description">
							<aside class="tsd-sources">
								<p>Inherited from EventEmitter.setMaxListeners</p>
							</aside>
							<div class="tsd-comment tsd-typography">
								<div class="lead">
									<p>By default <code>EventEmitter</code>s will print a warning if more than <code>10</code> listeners are
										added for a particular event. This is a useful default that helps finding
										memory leaks. The <code>EventEmitter.setMaxListeners()</code> method allows the default limit to be
										modified (if eventTargets is empty) or modify the limit specified in every <code>EventTarget</code> | <code>EventEmitter</code> passed as arguments.
									The value can be set to<code>Infinity</code> (or <code>0</code>) to indicate an unlimited number of listeners.</p>
								</div>
								<pre><code class="language-js"><span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">setMaxListeners</span><span style="color: #000000">(</span><span style="color: #098658">20</span><span style="color: #000000">);</span>
<span style="color: #008000">// Equivalent to</span>
<span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #001080">defaultMaxListeners</span><span style="color: #000000"> = </span><span style="color: #098658">20</span><span style="color: #000000">;</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">eventTarget</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #795E26">EventTarget</span><span style="color: #000000">();</span>
<span style="color: #008000">// Only way to increase limit for `EventTarget` instances</span>
<span style="color: #008000">// as these doesn&#039;t expose its own `setMaxListeners` method</span>
<span style="color: #001080">EventEmitter</span><span style="color: #000000">.</span><span style="color: #795E26">setMaxListeners</span><span style="color: #000000">(</span><span style="color: #098658">20</span><span style="color: #000000">, </span><span style="color: #001080">eventTarget</span><span style="color: #000000">);</span>
</code></pre>
								<dl class="tsd-comment-tags">
									<dt>since</dt>
									<dd><p>v15.3.0, v14.17.0</p>
									</dd>
								</dl>
							</div>
							<h4 class="tsd-parameters-title">Parameters</h4>
							<ul class="tsd-parameters">
								<li>
									<h5><span class="tsd-flag ts-flagOptional">Optional</span> n: <span class="tsd-signature-type">number</span></h5>
								</li>
								<li>
									<h5><span class="tsd-flag ts-flagRest">Rest</span> <span class="tsd-signature-symbol">...</span>eventTargets: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-type">DOMEventTarget</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventEmitter</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></h5>
								</li>
							</ul>
							<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4>
						</li>
					</ul>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
				<li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li>
				<li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-property tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static property</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>