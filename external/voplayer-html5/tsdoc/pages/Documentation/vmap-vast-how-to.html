<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Advertisement with VMAP and VAST | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Advertisement with VMAP and VAST</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>Support for VAST/VMAP/MAST ad insertion comes as an extension to VO Player for Web.</p>
				<a href="#include-the-extention-in-the-web-page" id="include-the-extention-in-the-web-page" style="color: inherit; text-decoration: none;">
					<h2>Include the extention in the web page</h2>
				</a>
				<p>To make use of the extension, the dedicated library file <em>voplayer-asdext.min.js</em> must be included <strong>after</strong> the core library (<em>voplayer.min.js</em>) in the page, before creating the player object.</p>
				<pre><code class="language-html"><span style="color: #800000">&lt;!DOCTYPE</span><span style="color: #000000"> </span><span style="color: #FF0000">html</span><span style="color: #800000">&gt;</span>
<span style="color: #800000">&lt;head&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;meta</span><span style="color: #000000"> </span><span style="color: #FF0000">http-equiv</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;content-type&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">content</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;text/html; charset=UTF-8&quot;</span><span style="color: #800000">&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;title&gt;</span><span style="color: #000000">VO Player for Web (HTML5) Demo</span><span style="color: #800000">&lt;/title&gt;</span>

<span style="color: #000000">  </span><span style="color: #800000">&lt;link</span><span style="color: #000000"> </span><span style="color: #FF0000">rel</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;stylesheet&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">href</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;html5player/css/voplayer.css&quot;</span><span style="color: #800000">&gt;</span>

<span style="color: #000000">  </span><span style="color: #008000">&lt;!-- include the VO Player library --&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>
<span style="color: #000000">  </span><span style="color: #008000">&lt;!-- include the VR extension for VO Player (must be loaded AFTER the core library) --&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer-adsext.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #800000">&lt;/head&gt;</span>
<span style="color: #800000">&lt;body&gt;</span>
</code></pre>
				<p>Support of advertisement is also subject to business agreement with Viaccess-Orca and you will need a player license that enable this feature.</p>
				<a href="#extension-use" id="extension-use" style="color: inherit; text-decoration: none;">
					<h2>Extension use</h2>
				</a>
				<p>Player load method gets a <code>media</code> as primary parameter. The <code>media</code> is either a <code>string</code> or a <a href="../../interfaces/Types.Media.html">Media</a> object.
					In case VAST or VMAP needs to be requested, a <code>vastUrl</code> property (or <code>vmapUrl</code>/<code>mastUrl</code> property) shall be set in the <code>media</code> object as depicted below.
				Another option is to set an <code>adRequestUrl</code> property in the <code>media</code> object, the player will then automatically detect the ad protocol (VAST, MAST or VMAP)</p>
				<pre><code class="language-javascript"><span style="color: #008000">//this function assumes the player is already newly created or properly reset and configured</span>
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">loadMediaWithAd</span><span style="color: #000000">(</span><span style="color: #001080">streamUrl</span><span style="color: #000000">, </span><span style="color: #001080">adUrl</span><span style="color: #000000">, </span><span style="color: #001080">adProtocol</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">media</span><span style="color: #000000"> = </span><span style="color: #0000FF">null</span><span style="color: #000000">;</span>

<span style="color: #000000">  </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">adUrl</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #001080">media</span><span style="color: #000000"> = {};</span>
<span style="color: #000000">    </span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">url</span><span style="color: #000000"> = </span><span style="color: #001080">streamUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000">(!</span><span style="color: #001080">adProtocol</span><span style="color: #000000"> || </span><span style="color: #001080">adProtocol</span><span style="color: #000000"> === </span><span style="color: #0000FF">undefined</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">adRequestUrl</span><span style="color: #000000"> = </span><span style="color: #001080">adUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">    } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">adProtocol</span><span style="color: #000000"> === </span><span style="color: #A31515">&#039;VAST&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">vastUrl</span><span style="color: #000000"> = </span><span style="color: #001080">adUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">    } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">adProtocol</span><span style="color: #000000"> === </span><span style="color: #A31515">&#039;VMAP&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">vmapUrl</span><span style="color: #000000"> = </span><span style="color: #001080">adUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">    } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">adProtocol</span><span style="color: #000000"> === </span><span style="color: #A31515">&#039;MAST&#039;</span><span style="color: #000000">) { </span>
<span style="color: #000000">       </span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">mastUrl</span><span style="color: #000000"> = </span><span style="color: #001080">adUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  } </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">media</span><span style="color: #000000"> = </span><span style="color: #001080">streamUrl</span><span style="color: #000000">;</span>
<span style="color: #000000">  }</span>

<span style="color: #000000">  </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #001080">media</span><span style="color: #000000">).</span><span style="color: #795E26">then</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Media loaded, playback can start&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">play</span><span style="color: #000000">();</span>
<span style="color: #000000">  })</span>
<span style="color: #000000">}</span>
</code></pre>
				<a href="#advertisement-errors" id="advertisement-errors" style="color: inherit; text-decoration: none;">
					<h2>Advertisement errors</h2>
				</a>
				<p>In case where an error occurs while retrieving an advertisement, being either and among other cases, the impossibility to connect to the advertisement server or the response to the advertisement server being incorrect or the media file not being supported by the player, the playback of an advertisement may be interrupted. </p>
				<p>In such a case, however the playback of the main content will not be interrupted. Consequently, in such cases, the player will trigger a Warning (see <a href="../../interfaces/Events.WarningEvent.html">WarningEvent</a>) to the application, informing the reason of the encountered advertisement error. Warning event will include in its <code>data</code> part, when relevant, information related to the AdBreak <a href="../../interfaces/Types.AdvertisementAdBreakInfo.html">AdvertisementAdBreakInfo</a> and/or on the AdInfo <a href="../../interfaces/Types.AdvertisementInfo.html">AdvertisementInfo</a> where the error occurs.</p>
				<p>When possible and if available, advertisement error, including IAB VAST and VMAP standard code, will also be triggered toward the advertisement server via VAST error uri or VMAP error uri.</p>
				<p>In some cases, an advertisement error may lead to several player warnings. For instance if a VAST from a VMAP has a failed request then it will generate two warnings.</p>
				<ul>
					<li>One related to VAST request failing: ADS_REQUEST_FAILED</li>
					<li>One related to VMAP: ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR</li>
				</ul>
				<p>If an error occurs on an advertisement within an advertisement break that contains several advertisements then all consecutive advertisements will be disregarded.</p>
				<p>List of possible error codes for advertisement warning are listed in <a href="../../enums/Errors.ErrorCodes.html">ErrorCodes</a> and are part of error category ADVERTISEMENT_CATEGORY (20) and have their values in the range 20xxx.</p>
				<a href="#vmap-auto-refresh" id="vmap-auto-refresh" style="color: inherit; text-decoration: none;">
					<h2>VMAP auto refresh</h2>
				</a>
				<p>VMAP auto refresh is a Viaccess-orca features that enables you to schedule mid-roll application on Live or Start-Over streams.</p>
				<p>For Live or Start-Over streams, if you want your ads&#39; times to be based on when the player started playback
				rather than actual player time codes, you should call the <code>startVMAPAutoRefresh</code> API once the stream is loaded.</p>
				<p>The <code>startVMAPAutoRefresh</code> expects a <a href="../../interfaces/Types.VmapAutrefreshConfig.html">VmapAutrefreshConfig</a> object, which includes a <code>requestUrl</code> and a <code>duration</code> in seconds, corresponding to the vmap refresh period.</p>
				<pre><code class="language-javascript"><span style="color: #008000">//this function assumes the player is already newly created or properly reset and configured</span>
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">loadLiveMediaWithAd</span><span style="color: #000000">(</span><span style="color: #001080">liveStreamUrl</span><span style="color: #000000">, </span><span style="color: #001080">vmapURL</span><span style="color: #000000">, </span><span style="color: #001080">vmapRefreshPeriod</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// here we load a VMAP url with timing indication relative to now() (the clock time at which playback starts)</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #001080">liveStreamUrl</span><span style="color: #000000">).</span><span style="color: #795E26">then</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>
<span style="color: #000000">      </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Media loaded, playback can start&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">      </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">startVMAPAutoRefresh</span><span style="color: #000000">({ </span><span style="color: #001080">requestURL:</span><span style="color: #000000"> </span><span style="color: #001080">vmapURL</span><span style="color: #000000">, </span><span style="color: #001080">duration:</span><span style="color: #000000"> </span><span style="color: #001080">vmapRefreshPeriod</span><span style="color: #000000"> });</span>
<span style="color: #000000">      </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">play</span><span style="color: #000000">();</span>
<span style="color: #000000">  })</span>
<span style="color: #000000">}</span>
</code></pre>
				<a href="#note-on-usemainplayervideoelement-advertisment-option" id="note-on-usemainplayervideoelement-advertisment-option" style="color: inherit; text-decoration: none;">
					<h2>Note on <code>useMainPlayerVideoElement</code> advertisment option</h2>
				</a>
				<p>The player may set the option <code>useMainPlayerVideoElement</code> to use the same video element for the main content and the video advertisement to be played.</p>
				<p>This is required on low end platforms that supports only one video element (e.g. Tizen devices).
				This may be also required on iOS, iPad device when advertisement requires to be played in full screen, since the iOS framework only allows to fullscreen a given video element and not a DIV that would be a container of the video element.</p>
				<p>In case where the same video is used and that the playback would be encrypted and would require license calls for token renewal, the integration shall implement the necessary callbacks (<code>mainPlayerResumeCallback</code> and <code>mainPlayerReloadCallback</code>) to restarts the main content after its interruption for the playback of an advertisment.</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>