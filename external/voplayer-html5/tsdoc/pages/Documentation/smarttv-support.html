<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Support for SmartTVs | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Support for SmartTVs</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="smarttv-support.html">Support for SmartTVs</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>The VO Player for Web requires additional integration steps to work properly on SmartTV platforms.</p>
				<a href="#polyffils" id="polyffils" style="color: inherit; text-decoration: none;">
					<h2>Polyffils</h2>
				</a>
				<p>To ensure a  full playback support, the following script must be added on your html (before including VO Player library) as following :</p>
				<pre><code class="language-html"><span style="color: #800000">&lt;html&gt;</span>
<span style="color: #800000">&lt;head&gt;</span>

<span style="color: #000000">    [...]</span>

<span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;https://polyfill.io/v3/polyfill.min.js?features=default,Map,Array.prototype.find,Array.prototype.findIndex&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- include the HTML5 player library --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;voplayer.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #800000">&lt;/head&gt;</span>
<span style="color: #800000">&lt;body&gt;</span>
<span style="color: #000000">    [...]</span>
</code></pre>
				<a href="#ads-library-tizen-versionlt4-and-webos-versionlt4" id="ads-library-tizen-versionlt4-and-webos-versionlt4" style="color: inherit; text-decoration: none;">
					<h2>Ads library (Tizen version&lt;4 and Webos version&lt;4)</h2>
				</a>
				<p>To play ads proprly  on Tizen (version&lt;4) or Webos (version&lt;4), you must babelify  <code>voplayer-adsext.min.js</code> lib before use it , following an example on how you can do that  : </p>
				<pre><code class="language-js"><span style="color: #001080">browserify</span><span style="color: #000000"> -</span><span style="color: #001080">t</span><span style="color: #000000"> [</span><span style="color: #001080">babelify</span><span style="color: #000000">] -</span><span style="color: #001080">p</span><span style="color: #000000"> </span><span style="color: #001080">browserify</span><span style="color: #000000">-</span><span style="color: #001080">derequire</span><span style="color: #000000"> -</span><span style="color: #001080">p</span><span style="color: #000000"> </span><span style="color: #001080">bundle</span><span style="color: #000000">-</span><span style="color: #001080">collapser</span><span style="color: #000000">/</span><span style="color: #001080">plugin</span><span style="color: #000000"> -</span><span style="color: #001080">s</span><span style="color: #000000"> </span><span style="color: #001080">votest</span><span style="color: #000000"> </span><span style="color: #001080">dist</span><span style="color: #000000">/</span><span style="color: #001080">voplayer</span><span style="color: #000000">-</span><span style="color: #001080">adsext</span><span style="color: #000000">.</span><span style="color: #001080">minjs</span><span style="color: #000000"> &gt; </span><span style="color: #001080">out</span><span style="color: #000000">/</span><span style="color: #001080">voplayer</span><span style="color: #000000">-</span><span style="color: #001080">adsext</span><span style="color: #000000">.</span><span style="color: #001080">min</span><span style="color: #000000">.</span><span style="color: #001080">js</span><span style="color: #000000"> </span>

</code></pre>
				<a href="#tizen-webapi-availability" id="tizen-webapi-availability" style="color: inherit; text-decoration: none;">
					<h2>Tizen <em>webapi</em> availability</h2>
				</a>
				<p>To ensure proper platform detection for tizen , the Tizen <em>webapi</em> library must be loaded prior to load the VO Player library:</p>
				<pre><code class="language-html"><span style="color: #800000">&lt;html&gt;</span>
<span style="color: #800000">&lt;head&gt;</span>

<span style="color: #000000">    [...]</span>

<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">type</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;text/javascript&quot;</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;$WEBAPIS/webapis/webapis.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- include the HTML5 player library --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;voplayer.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #800000">&lt;/head&gt;</span>
<span style="color: #800000">&lt;body&gt;</span>
<span style="color: #000000">    [...]</span>
</code></pre>
				<a href="#privileges-in-tizen-project-configuration-file" id="privileges-in-tizen-project-configuration-file" style="color: inherit; text-decoration: none;">
					<h2>Privileges in Tizen project configuration file</h2>
				</a>
				<p>To ensure full playback support, the configuration file of the integrating Tizen project <em>config.xml</em> must also include the following privileges:</p>
				<pre><code class="language-xml"><span style="color: #800000">&lt;access</span><span style="color: #000000"> </span><span style="color: #FF0000">origin</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;*&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">subdomains</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;true&quot;</span><span style="color: #800000">&gt;&lt;/access&gt;</span>
<span style="color: #800000">&lt;tizen:privilege</span><span style="color: #000000"> </span><span style="color: #FF0000">name</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;http://developer.samsung.com/privilege/drmplay&quot;</span><span style="color: #800000">/&gt;</span>
<span style="color: #800000">&lt;tizen:privilege</span><span style="color: #000000"> </span><span style="color: #FF0000">name</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;http://tizen.org/privilege/internet&quot;</span><span style="color: #800000">/&gt;</span>
<span style="color: #800000">&lt;tizen:privilege</span><span style="color: #000000"> </span><span style="color: #FF0000">name</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;http://developer.samsung.com/privilege/productinfo&quot;</span><span style="color: #800000">/&gt;</span>
</code></pre>
				<a href="#enable-4k-playback-on-tizen" id="enable-4k-playback-on-tizen" style="color: inherit; text-decoration: none;">
					<h2>Enable 4K playback on Tizen</h2>
				</a>
				<p>4K is supported on Tizen (for devices that supported this resolution) using the native player, you shall simply update the player configuration with <code>enable4K=true</code> to enable 4K playback as following :</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">tizenNativeConfiguration:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">enable4K:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;true&#039;</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<a href="#enable-8k-playback-on-tizen" id="enable-8k-playback-on-tizen" style="color: inherit; text-decoration: none;">
					<h2>Enable 8K playback on Tizen</h2>
				</a>
				<p>8K resolution is supported on Tizen (for devices that supported this resolution) using the native player, you shall simply update the player configuration with <code>enable8K=true</code> to enable 8K playback as following :</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">tizenNativeConfiguration:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">enable8K:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;true&#039;</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>