<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Video quality management | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Video quality management</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../quality-and-tracks.html">Tracks and Video Quality management</a>
				</li>
				<li>
					<a href="quality.html">Video quality management</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>When playing adaptive streams, multiple video qualities (variants) can be available for the same content, allowing the VO Player to choose from them.
					By default, VO Player uses an ABR mechanism to choose the best suited quality based on network conditions and potential restrictions.
				It is however possible to force the player to use a specific variant, regardless of external conditions.</p>
				<a href="#player-apis-for-quality-management" id="player-apis-for-quality-management" style="color: inherit; text-decoration: none;">
					<h2>Player APIs for quality management</h2>
				</a>
				<p>The support for video qualities tracks in VO Player is available through the following APIs:</p>
				<ul>
					<li><code>player.qualities</code> returns the list of all available video qualities tracks to choose from.</li>
					<li><code>player.quality</code> is the index of the quality currently playing in the list of all available qualities.</li>
				</ul>
				<p>In addition, the configuration paramater <code>abr.enabled</code> indicates if the quality currently playing is selected by the player ABR mechanism or was forced by the user (via the API).</p>
				<p>To force the selection of a specific quality, you shall simply assign a new index <code>player.quality = qualityId;</code>.<br>
					Setting an index outside the range of existing tracks will have not impact.
				Setting specifically <code>player.quality = -1;</code> enables back the player ABR mechanism (the <code>player.quality</code> will not remain at -1).</p>
				<p><em><strong>Example</strong></em></p>
				<pre><code class="language-javascript"><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">listQualities</span><span style="color: #000000">() {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">qualities</span><span style="color: #000000"> = </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">qualities</span><span style="color: #000000">;</span>

<span style="color: #000000">    </span><span style="color: #AF00DB">for</span><span style="color: #000000">(</span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">q</span><span style="color: #000000">=</span><span style="color: #098658">0</span><span style="color: #000000">; </span><span style="color: #001080">q</span><span style="color: #000000">&lt;</span><span style="color: #001080">qualities</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000">; </span><span style="color: #001080">q</span><span style="color: #000000">++) {</span>
<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Details for video quality [&#039;</span><span style="color: #000000"> + </span><span style="color: #001080">q</span><span style="color: #000000"> + </span><span style="color: #A31515">&#039;]: &#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;- active: &#039;</span><span style="color: #000000"> + </span><span style="color: #001080">qualities</span><span style="color: #000000">[</span><span style="color: #001080">q</span><span style="color: #000000">].</span><span style="color: #001080">active</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;- bandwidth: &#039;</span><span style="color: #000000"> + </span><span style="color: #001080">qualities</span><span style="color: #000000">[</span><span style="color: #001080">q</span><span style="color: #000000">].</span><span style="color: #001080">bandwidth</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #008000">// the width and height of a quality object may not always be available (stream dependent)</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">qualities</span><span style="color: #000000">[</span><span style="color: #001080">q</span><span style="color: #000000">].</span><span style="color: #001080">width</span><span style="color: #000000">) {</span>
<span style="color: #000000">            </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;- resolution: &#039;</span><span style="color: #000000"> + </span><span style="color: #001080">qualities</span><span style="color: #000000">[</span><span style="color: #001080">q</span><span style="color: #000000">].</span><span style="color: #001080">width</span><span style="color: #000000"> + </span><span style="color: #A31515">&#039;x&#039;</span><span style="color: #000000"> + </span><span style="color: #001080">qualities</span><span style="color: #000000">[</span><span style="color: #001080">q</span><span style="color: #000000">].</span><span style="color: #001080">height</span><span style="color: #000000">);</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">forceQuality</span><span style="color: #000000">(</span><span style="color: #001080">qualityId</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// this will set player.textTracks[trackId] as the active track</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">quality</span><span style="color: #000000"> = </span><span style="color: #001080">qualityId</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">resetToABR</span><span style="color: #000000">() {</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">quality</span><span style="color: #000000"> = -</span><span style="color: #098658">1</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>
</code></pre>
				<p><strong>WARNING:</strong> The ability to force a chosen video quality is not available on all platforms. When the feature is not available, the list of available qualities is empty and <code>player.quality</code> is always -1.</p>
				<a href="#video-quality-change-events" id="video-quality-change-events" style="color: inherit; text-decoration: none;">
					<h2>Video quality change events</h2>
				</a>
				<p>Each time a change occurs on video quality being played, the player triggers a <code>qualitychanged</code> event.
				This event is trigger whether the change was triggered by the internal ABR mechanism or forced by the user through the API.</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="tracks.html">Audio and subtitle tracks</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="quality.html">Video quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>