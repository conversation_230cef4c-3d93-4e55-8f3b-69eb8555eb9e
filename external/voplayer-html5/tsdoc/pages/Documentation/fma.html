<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Front Monitoring Agent (FMA) | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Front Monitoring Agent (FMA)</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="fma.html">Front Monitoring Agent (FMA)</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#vo-player-fma-web" id="vo-player-fma-web" style="color: inherit; text-decoration: none;">
					<h1>VO Player FMA Web</h1>
				</a>
				<a href="#installation" id="installation" style="color: inherit; text-decoration: none;">
					<h2>Installation</h2>
				</a>
				<p>If you are already using the VOPlayer Web, the FMA is already provided via the VOPlayer Web</p>
				<p>Throught the VOPlayer Web, you can get a Singleton instance of the FMA Manager by calling the static accessor <em>fmaManager</em> on the <em>Player</em>
				eg: voplayer.Player.fmaManager.Instance</p>
				<p>If you are not using the VOPlayer Web, you can also use this module as a standalone module and it can be installed with this command:
					<code>$ npm install git+https://&lt;your token&gt;:<EMAIL>/voplayerteam/vofma-web.git</code>
					or
				<code>$ yarn add git+https://&lt;your token&gt;:<EMAIL>/voplayerteam/vofma-web.git</code></p>
				<a href="#usage" id="usage" style="color: inherit; text-decoration: none;">
					<h2>Usage</h2>
				</a>
				<a href="#initialization" id="initialization" style="color: inherit; text-decoration: none;">
					<h3>Initialization</h3>
				</a>
				<p>To work, the FMA should be initialized by calling the <code>init</code> function, we should provide the URL from which we will recover the config, and some information regarding the app and device that is using the FMA.</p>
				<p>When calling this function for the first time, the FMA will try to fetch the config from the URL passed as parameter and store it in the browser&#39;s local storage.</p>
				<p>This function can be call as many time as we want to ensure that it is initialized (it will return true in case of success). If we already fetched the config, it will return it from the cache or from the local storage or try to refetch a new config if the previous one has expired (the config expiration depends on the config but usually it is 1 day).</p>
				<pre><code class="language-javascript"><span style="color: #AF00DB">import</span><span style="color: #000000"> { </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000"> } </span><span style="color: #AF00DB">from</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;fma&#039;</span><span style="color: #000000">;</span>
<span style="color: #008000">// voplayer.Player.init // when calling the FMA through the VOPlayer</span>

<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">init</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">configurationUrl:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;the url to your config&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">applicationName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">applicationVersion:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">deviceId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">deviceCategory:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">playerId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">firmwareVersion:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #001080">terminalId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">});</span>
</code></pre>
				<p>If you are using the FMA via the VOPlayer Web, you can also call the static <em>init</em> method on the <em>Player</em> with the same parameters</p>
				<a href="#example" id="example" style="color: inherit; text-decoration: none;">
					<h4>Example</h4>
				</a>
				<p>For example, it can be called higher in your application in your App.js to make sure it was always called</p>
				<pre><code class="language-javascript"><span style="color: #008000">// App.js</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">isFirstRun</span><span style="color: #000000"> = </span><span style="color: #795E26">useRef</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>
<span style="color: #795E26">useEffect</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">isFirstRun</span><span style="color: #000000">.</span><span style="color: #001080">current</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">init</span><span style="color: #000000">({</span>
<span style="color: #000000">      </span><span style="color: #008000">// ...</span>
<span style="color: #000000">    });</span>
<span style="color: #000000">    </span><span style="color: #001080">isFirstRun</span><span style="color: #000000">.</span><span style="color: #001080">current</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">return</span><span style="color: #000000">;</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}, []);</span>
</code></pre>
				<p>You can also call <code>isInitialized()</code> to check if the <code>VOFmaManager</code> was initialized before using it</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">isInitialized</span><span style="color: #000000">();</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.isInitialized(); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#save-and-send-the-events" id="save-and-send-the-events" style="color: inherit; text-decoration: none;">
					<h3>Save and send the events</h3>
				</a>
				<p>You can call <code>terminate()</code> and it will flush the events to the local storage or send them to the API if one of the threshold is reached.
					It isn’t mandatory to call it.
				It will be automatically called when the tab is reloaded or closed and when the browser is closed.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">terminate</span><span style="color: #000000">();</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.terminate(); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#threshold-policy" id="threshold-policy" style="color: inherit; text-decoration: none;">
					<h3>Threshold policy</h3>
				</a>
				<p>The FMA temporarilly save the events to the local storage.
				The events collected by the FMA are sent by batch when at least one of those 2 conditions is met:</p>
				<ul>
					<li>10 or more events have been collected</li>
					<li>or more than 5 minutes have elapsed since the last POST to the backend</li>
				</ul>
				<p>After being sent to the backend, the events are cleared from the local storage.
				But if we got an error response from the backend, the events will be kept and another POST to the backend will occur when the next event will be added. We will never keep more than 50 events, if we got more than 50 unsent events, the oldest one will be removed when we receive the next one.</p>
				<a href="#navigation-session-report" id="navigation-session-report" style="color: inherit; text-decoration: none;">
					<h2>Navigation session report</h2>
				</a>
				<p>Currently, the Web FMA only implements the navigation session report (and the search which is part of the navigation session report)</p>
				<a href="#vofmauilogger" id="vofmauilogger" style="color: inherit; text-decoration: none;">
					<h3>VoFmaUiLogger</h3>
				</a>
				<p>To log some UI events, you need to get the <code>VoFmaUiLogger</code> which is a singleton</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">();</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.getUiLogger(); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#log-a-navigation-event" id="log-a-navigation-event" style="color: inherit; text-decoration: none;">
					<h3>Log a navigation event</h3>
				</a>
				<p>When landing on a new page, if you want to log this page navigation event you have to call <code>startLoadingUiView</code> with the page ID and which kind of event triggered this navigation (UI_BUTTON, RC_SHORTCUT_BUTTON or UNDEFINED)</p>
				<p>This will returns you a <code>VoFmaUiView</code> instance. On which you will have to call <code>onLoaded</code> to indicate if the page loaded successfully, it will help to calculate how long the page took to load.</p>
				<pre><code class="language-javascript"><span style="color: #008000">// voplayer.Player.fmaManager.Instance.getUiLogger().startLoadingUiView; // when calling the FMA through the VOPlayer</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">view</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;SETTING_PAGE&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// The ID allowing to identify a UI Element in a non-ambiguous way.</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">UNDEFINED</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">});</span>
<span style="color: #001080">view</span><span style="color: #000000">.</span><span style="color: #795E26">onLoaded</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>
</code></pre>
				<p>You can call <code>terminate()</code> to explicitly indicate that the user left the current page, it will help to calculate how long the user stayed on the page. If not called, the page will be closed when the next <code>startLoadingUiView</code> is called on the <code>VoFmaUiView</code> singleton</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">terminate</span><span style="color: #000000">(); </span><span style="color: #008000">// will close the current view</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.getUiLogger().terminate(); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#example-1" id="example-1" style="color: inherit; text-decoration: none;">
					<h4>Example</h4>
				</a>
				<p>For example, on your page it could look like</p>
				<pre><code class="language-javascript"><span style="color: #008000">// Page1.js</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;PAGE_1&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">});</span>

<span style="color: #008000">// Page2.js</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #008000">// will close page 1</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;PAGE_2&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">});</span>
</code></pre>
				<p>or</p>
				<pre><code class="language-javascript"><span style="color: #795E26">useEffect</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">isInitialized</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">isInitialized</span><span style="color: #000000">();</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">isInitialized</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">view</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">      </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;PLAYBACK_PAGE&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">    });</span>

<span style="color: #000000">    </span><span style="color: #008000">// called when page is fully loaded</span>
<span style="color: #000000">    </span><span style="color: #001080">view</span><span style="color: #000000">.</span><span style="color: #795E26">onLoaded</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>

<span style="color: #000000">    </span><span style="color: #AF00DB">return</span><span style="color: #000000"> () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #008000">// to make sure that it is called when the page is left</span>
<span style="color: #000000">      </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">terminate</span><span style="color: #000000">();</span>
<span style="color: #000000">    };</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">}, []);</span>
</code></pre>
				<a href="#navigation-to-a-playback-page" id="navigation-to-a-playback-page" style="color: inherit; text-decoration: none;">
					<h3>Navigation to a playback page</h3>
				</a>
				<p>If the user navigate to a page where a content is played, you can call <code>startLoadingUiView</code> with an additional parameter to specify the content ID that is being played</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">view</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;PLAYBACK_PAGE&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">  </span><span style="color: #001080">contentInfo:</span><span style="color: #000000"> { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James_Bond_GoldenEye&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James Bond GoldenEye&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// The content info that contains the content id and the content name</span>
<span style="color: #000000">});</span>
<span style="color: #001080">view</span><span style="color: #000000">.</span><span style="color: #795E26">onLoaded</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#navigation-inside-a-page-when-using-a-remote-control" id="navigation-inside-a-page-when-using-a-remote-control" style="color: inherit; text-decoration: none;">
					<h3>Navigation inside a page when using a remote control</h3>
				</a>
				<p>To log navigation events inside a page, you have to call <code>setFocus</code> on <code>VoFmaUiView</code> returned by <code>startLoadingUiView</code> function to notify that the focus moved to another UI element</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">view</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;PLAYBACK_PAGE&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">});</span>

<span style="color: #001080">view</span><span style="color: #000000">.</span><span style="color: #795E26">setFocus</span><span style="color: #000000">(</span>
<span style="color: #000000">  { </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;Page_Thumbnail#17&#039;</span><span style="color: #000000">, </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #0070C1">FMA</span><span style="color: #000000">.</span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">UI_BUTTON</span><span style="color: #000000"> },</span>
<span style="color: #000000">  { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;Avatar_2&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;Avatar 2&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// the content info containing the content id and the content name not mandatory if the element focused is not a stream</span>
<span style="color: #000000">);</span>
</code></pre>
				<a href="#login-logout-and-profile-selection" id="login-logout-and-profile-selection" style="color: inherit; text-decoration: none;">
					<h3>Login, logout and profile selection</h3>
				</a>
				<p>When a user login and select a profile, all the event recorded will be linked to this account</p>
				<a href="#login" id="login" style="color: inherit; text-decoration: none;">
					<h4>Login</h4>
				</a>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">login</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;accountId 123&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// user ID or household ID depending on the integration</span>
</code></pre>
				<a href="#logout" id="logout" style="color: inherit; text-decoration: none;">
					<h4>Logout</h4>
				</a>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">login</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;accountId 123&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// login this user (specify the account if of the household ID depending on the integration)</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">login</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;accountId 1234&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// logout the previous user and login the new user</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">logout</span><span style="color: #000000">(); </span><span style="color: #008000">// logout the user</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">login</span><span style="color: #000000">(</span><span style="color: #0000FF">null</span><span style="color: #000000"> | </span><span style="color: #0000FF">undefined</span><span style="color: #000000">); </span><span style="color: #008000">// logout the user</span>
</code></pre>
				<a href="#selecting-a-user-profile" id="selecting-a-user-profile" style="color: inherit; text-decoration: none;">
					<h4>Selecting a user profile</h4>
				</a>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setProfile</span><span style="color: #000000">(</span>
<span style="color: #000000">  </span><span style="color: #A31515">&#039;profileId&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// the profile ID</span>
<span style="color: #000000">  </span><span style="color: #A31515">&#039;kids&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// the type of profile selected and it is optional</span>
<span style="color: #000000">);</span>
</code></pre>
				<a href="#content-rating" id="content-rating" style="color: inherit; text-decoration: none;">
					<h3>Content rating</h3>
				</a>
				<p>The <code>VoFmaUiLogger</code> can be used to rate a content by calling <code>setContentRating</code> and passing a content ID, content name and rating.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setContentRating</span><span style="color: #000000">(</span>
<span style="color: #000000">  { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James_Bond_GoldenEye&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James Bond GoldenEye&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// the content to rate (mandatory)</span>
<span style="color: #000000">  </span><span style="color: #098658">3</span><span style="color: #000000">, </span><span style="color: #008000">// the rating we want to give for this content (we recommend a rating from 1 to 3)</span>
<span style="color: #000000">);</span>

<span style="color: #008000">// voplayer.Player.fmaManager.Instance.getUiLogger()setContentRating(</span>
<span style="color: #008000">// { contentId: &#039;James_Bond_GoldenEye&#039;, contentName: &#039;James Bond GoldenEye&#039; },</span>
<span style="color: #008000">// 3,</span>
<span style="color: #008000">// ); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#search" id="search" style="color: inherit; text-decoration: none;">
					<h3>Search</h3>
				</a>
				<p>Search events are a subtype of navigation session report.
				When the user land on a search page, to log search results we can call the <code>searchTerm</code> from the <code>VoFmaUiLogger</code> and it will start a search session (each search session has a search session ID)</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">searchTerm</span><span style="color: #000000">(</span>
<span style="color: #000000">  </span><span style="color: #A31515">&#039;james b&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// term searched by the user (mandatory)</span>
<span style="color: #000000">  </span><span style="color: #098658">7</span><span style="color: #000000">, </span><span style="color: #008000">// the number of results returned for this search term (mandatory)</span>
<span style="color: #000000">  </span><span style="color: #0070C1">FMA</span><span style="color: #000000">.</span><span style="color: #001080">VoFmaUiLogger</span><span style="color: #000000">.</span><span style="color: #001080">SearchType</span><span style="color: #000000">.</span><span style="color: #0070C1">CONTENT_PROGRAM</span><span style="color: #000000">, </span><span style="color: #008000">// the type of search (not mandatory), if not specified it will be marked as UNDEFINED</span>
<span style="color: #000000">);</span>
</code></pre>
				<p>When we call the <code>searchTerm</code> function, we will get an instance of <code>SearchResult</code>.
				It can be used to log events linked to this search (result clicked, content purchased, content viewed).</p>
				<a href="#search-result-content-clicked" id="search-result-content-clicked" style="color: inherit; text-decoration: none;">
					<h4>Search result content clicked</h4>
				</a>
				<p>After the user click on a search result you can call the <code>registerClick</code> function of the <code>SearchResult</code> you got previously to register this event under this search session.</p>
				<pre><code class="language-javascript"><span style="color: #001080">searchResult</span><span style="color: #000000">.</span><span style="color: #795E26">registerClick</span><span style="color: #000000">(</span>
<span style="color: #000000">  { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James_Bond_GoldenEye&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James Bond GoldenEye&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// the search result item clicked (mandatory)</span>
<span style="color: #000000">  </span><span style="color: #001080">VoFmaUiLogger</span><span style="color: #000000">.</span><span style="color: #001080">SearchType</span><span style="color: #000000">, </span><span style="color: #008000">// the type of content that was clicked (mandatory)</span>
<span style="color: #000000">);</span>
</code></pre>
				<a href="#search-result-content-purchased" id="search-result-content-purchased" style="color: inherit; text-decoration: none;">
					<h4>Search result content purchased</h4>
				</a>
				<p>Following a search, it the user decide to purchase the content, you can call the <code>registerOrder</code> function of the <code>SearchResult</code> you got previously to register this event under this search session.</p>
				<pre><code class="language-javascript"><span style="color: #001080">searchResult</span><span style="color: #000000">.</span><span style="color: #795E26">registerOrder</span><span style="color: #000000">(</span>
<span style="color: #000000">  { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James_Bond_GoldenEye&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James Bond GoldenEye&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// the search result item that was purchased (mandatory)</span>
<span style="color: #000000">  </span><span style="color: #001080">VoFmaUiLogger</span><span style="color: #000000">.</span><span style="color: #001080">SearchType</span><span style="color: #000000">, </span><span style="color: #008000">// the type of content that was purchased (mandatory)</span>
<span style="color: #000000">);</span>
</code></pre>
				<a href="#start-a-playback-session-following-a-search" id="start-a-playback-session-following-a-search" style="color: inherit; text-decoration: none;">
					<h4>Start a playback session following a search</h4>
				</a>
				<p>If the user decide to play a content following a search, you can register this event by calling <code>startLoadingUiView</code> (same as in the <strong>Navigation to a playback page</strong> section) and add the search session ID as the last parameter.</p>
				<pre><code class="language-javascript"><span style="color: #008000">// you can get your search session ID by calling getSessionId() on your SearchResult</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">searchSessionId</span><span style="color: #000000"> = </span><span style="color: #001080">searchResult</span><span style="color: #000000">.</span><span style="color: #795E26">getSessionId</span><span style="color: #000000">();</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">view</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startLoadingUiView</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">uiViewInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">uiElementId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;SETTING_PAGE&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">uiNavigationTrigger:</span><span style="color: #000000"> </span><span style="color: #001080">VoFmaUiElementInfo</span><span style="color: #000000">.</span><span style="color: #001080">UiNavigationTrigger</span><span style="color: #000000">.</span><span style="color: #0070C1">RC_SHORTCUT_BUTTON</span><span style="color: #000000">,</span>
<span style="color: #000000">  },</span>
<span style="color: #000000">  </span><span style="color: #001080">contentInfo:</span><span style="color: #000000"> { </span><span style="color: #001080">contentId:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James_Bond_GoldenEye&#039;</span><span style="color: #000000">, </span><span style="color: #001080">contentName:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;James Bond GoldenEye&#039;</span><span style="color: #000000"> }, </span><span style="color: #008000">// The content info that contains the content id and the content name</span>
<span style="color: #000000">  </span><span style="color: #001080">searchSessionId:</span><span style="color: #000000"> </span><span style="color: #001080">searchSessionId</span><span style="color: #000000">, </span><span style="color: #008000">// your searche session ID</span>
<span style="color: #000000">});</span>
<span style="color: #001080">view</span><span style="color: #000000">.</span><span style="color: #795E26">onLoaded</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#start-a-new-search-session" id="start-a-new-search-session" style="color: inherit; text-decoration: none;">
					<h4>Start a new search session</h4>
				</a>
				<p>By calling <code>searchTerm</code> again, it will close the previous search session and start a new one.</p>
				<a href="#recover-a-search-result" id="recover-a-search-result" style="color: inherit; text-decoration: none;">
					<h4>Recover a search result</h4>
				</a>
				<p>If you lose the reference to the <code>SearchResult</code> (like for example if the page was reloaded or if the user opened a result on a new tab) and want to recover it to link the click, purchase, playback event to this search session.</p>
				<p>With <code>getLastSearchResult</code> you can recover the latest search result that was done on this tab if it was not closed.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">..</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">getLastSearchResult</span><span style="color: #000000">(); </span><span style="color: #008000">// return a SearchResult or undefined</span>
</code></pre>
				<p>With <code>restoreSearchResult</code> you can recover a <code>SearchResult</code> by passing it the <code>searchSessionId</code>.
				It can be useful if the user opens a new tab, and you want to continue to log the events for this search session on this new tab.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getUiLogger</span><span style="color: #000000">().</span><span style="color: #795E26">restoreSearchResult</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;searchSessionId&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// return a SearchResult or undefined</span>
</code></pre>
				<a href="#end-a-search-session" id="end-a-search-session" style="color: inherit; text-decoration: none;">
					<h4>End a search session</h4>
				</a>
				<p>Your search session will be closed the next time you call <code>searchTerm</code> and it will start a new one.
				You can also explicitly close it by calling <code>close</code> on the <code>SearchResult</code>.</p>
				<pre><code class="language-javascript"><span style="color: #001080">searchResult</span><span style="color: #000000">.</span><span style="color: #795E26">close</span><span style="color: #000000">();</span>
</code></pre>
				<a href="#playback-session-report" id="playback-session-report" style="color: inherit; text-decoration: none;">
					<h2>Playback session report</h2>
				</a>
				<a href="#vofmaapppbksessionlogger" id="vofmaapppbksessionlogger" style="color: inherit; text-decoration: none;">
					<h3>VoFmaAppPbkSessionLogger</h3>
				</a>
				<p>To log some playback events, you need to get the <code>VoFmaAppPbkSessionLogger</code> which is a singleton</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">();</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.getAppPbkSessionLogger(); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#standard-playback-flow" id="standard-playback-flow" style="color: inherit; text-decoration: none;">
					<h3>Standard playback flow</h3>
				</a>
				<p>During a playback session the application must call these API in the following order.</p>
				<a href="#open" id="open" style="color: inherit; text-decoration: none;">
					<h4>Open</h4>
				</a>
				<p>The application calls this function when starting a new playback session with the player.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">open</span><span style="color: #000000">();</span>
</code></pre>
				<a href="#start-sdp-request" id="start-sdp-request" style="color: inherit; text-decoration: none;">
					<h4>Start SDP request</h4>
				</a>
				<p>The application calls this function when sending a request to the SDP (Service Delivery Platform).</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startSdpRequest</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;GetLivePlayingInfo&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// the name of the SDP API</span>
</code></pre>
				<a href="#end-sdp-request" id="end-sdp-request" style="color: inherit; text-decoration: none;">
					<h4>End SDP request</h4>
				</a>
				<p>The application calls this function when receiving a response from the SDP server.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">endSdpRequest</span><span style="color: #000000">(</span><span style="color: #098658">200</span><span style="color: #000000">); </span><span style="color: #008000">// the status code returned by the API</span>
</code></pre>
				<a href="#start-prepare-player" id="start-prepare-player" style="color: inherit; text-decoration: none;">
					<h4>Start prepare player</h4>
				</a>
				<p>The application calls this function just after calling the prepare of the player.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startPreparePlayer</span><span style="color: #000000">();</span>
</code></pre>
				<a href="#end-prepare-player" id="end-prepare-player" style="color: inherit; text-decoration: none;">
					<h4>End prepare player</h4>
				</a>
				<p>The application calls this function after receiving from the player the notification with the prepare status.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">startPreparePlayer</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">); </span><span style="color: #008000">// true if prepare was successful, false if failed</span>
</code></pre>
				<a href="#set-playback-error" id="set-playback-error" style="color: inherit; text-decoration: none;">
					<h4>Set playback error</h4>
				</a>
				<p>The application calls this function when an error occurs during the playback session.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setPlaybackSessionError</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Error while trying to change alternate&#039;</span><span style="color: #000000">); </span><span style="color: #008000">// string description of the error</span>
</code></pre>
				<a href="#close" id="close" style="color: inherit; text-decoration: none;">
					<h4>Close</h4>
				</a>
				<p>The application calls this function when the current playback session is closed (reset or release of the player).</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">close</span><span style="color: #000000">();</span>
</code></pre>
				<a href="#analytics" id="analytics" style="color: inherit; text-decoration: none;">
					<h3>Analytics</h3>
				</a>
				<p>The application can specify analytics with each playback session. These analytics are used to generate the Viewership dashboards.
				All analytics specified below should be specifiedare needed when available for a fully fonctionnal dashboard.</p>
				<p>Note that not all have to be specified at the same time, any field can be specified later on and will overwrite the existing value.</p>
				<p>Analytics can be specified at any point during the playback, i.e. anytime between the call to <code>open</code> and the call to <code>close</code>.</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">metadata</span><span style="color: #000000">: </span><span style="color: #267F99">VoFmaAnalyticsMetadata</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    </span><span style="color: #001080">mdaApplicationLanguage:</span><span style="color: #000000">     </span><span style="color: #A31515">&quot;English&quot;</span><span style="color: #000000">,          </span><span style="color: #008000">// Language setting of the application</span>
<span style="color: #000000">    </span><span style="color: #001080">mdaDeviceOperator:</span><span style="color: #000000">          </span><span style="color: #A31515">&quot;Samsung&quot;</span><span style="color: #000000">,          </span><span style="color: #008000">// Operator of the device</span>
<span style="color: #000000">    </span><span style="color: #001080">sesSessionAccountId:</span><span style="color: #000000">        </span><span style="color: #A31515">&quot;joe1234&quot;</span><span style="color: #000000">,          </span><span style="color: #008000">// Unique account ID of the user or &quot;guest&quot; if not connected</span>
<span style="color: #000000">    </span><span style="color: #001080">sesSessionUserId:</span><span style="color: #000000">           </span><span style="color: #A31515">&quot;joe&quot;</span><span style="color: #000000">,              </span><span style="color: #008000">// Profile identifier the user is using or &quot;guest&quot; if not connected</span>
<span style="color: #000000">    </span><span style="color: #001080">sesSessionSubscriptionType:</span><span style="color: #000000"> </span><span style="color: #A31515">&quot;Tier1&quot;</span><span style="color: #000000">,            </span><span style="color: #008000">// Subscription type of tier of the user</span>
<span style="color: #000000">    </span><span style="color: #001080">sesSessionType:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;VOD&quot;</span><span style="color: #000000">,              </span><span style="color: #008000">// Type of playback session can be more precise than the open given to open (e.g. SVOD, TVOD are subset of VOD)</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentId:</span><span style="color: #000000">               </span><span style="color: #A31515">&quot;content_4823&quot;</span><span style="color: #000000">,     </span><span style="color: #008000">// Unique ID of the content or live channel</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentName:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;TheContent&quot;</span><span style="color: #000000">,       </span><span style="color: #008000">// Name of the content or channel</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramId:</span><span style="color: #000000">        </span><span style="color: #A31515">&quot;program_47320&quot;</span><span style="color: #000000">,    </span><span style="color: #008000">// Unique program ID if on a live channel</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramName:</span><span style="color: #000000">      </span><span style="color: #A31515">&quot;TheProgram&quot;</span><span style="color: #000000">,       </span><span style="color: #008000">// Program name if on a live channel</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentSeasonNb:</span><span style="color: #000000">         </span><span style="color: #A31515">&quot;3&quot;</span><span style="color: #000000">,                </span><span style="color: #008000">// Season number if content is episodic</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentEpisodeNb:</span><span style="color: #000000">        </span><span style="color: #A31515">&quot;8&quot;</span><span style="color: #000000">,                </span><span style="color: #008000">// Episode number if content is episodic</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentParentalRating:</span><span style="color: #000000">   </span><span style="color: #A31515">&quot;10+&quot;</span><span style="color: #000000">,              </span><span style="color: #008000">// Parental rating of content</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentLanguage:</span><span style="color: #000000">         </span><span style="color: #A31515">&quot;English&quot;</span><span style="color: #000000">,          </span><span style="color: #008000">// Content audio language</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentGenre:</span><span style="color: #000000">            </span><span style="color: #A31515">&quot;Musical&quot;</span><span style="color: #000000">,          </span><span style="color: #008000">// Genre of the content if any</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentType:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;Show&quot;</span><span style="color: #000000">,             </span><span style="color: #008000">// Type of the content if any (Series, Show, ...)</span>
<span style="color: #000000">    </span><span style="color: #001080">sesCdnSessionId:</span><span style="color: #000000">            </span><span style="color: #A31515">&quot;4237890HD312&quot;</span><span style="color: #000000">,     </span><span style="color: #008000">// CDN session ID</span>
<span style="color: #000000">    </span><span style="color: #001080">appPauseNbUser:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;3&quot;</span><span style="color: #000000">,                </span><span style="color: #008000">// Number of times the user paused the playback</span>
<span style="color: #000000">    </span><span style="color: #001080">appRetryNumber:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;1&quot;</span><span style="color: #000000">,                </span><span style="color: #008000">// Number of times the getting the content had to be retried because of an error</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProvider:</span><span style="color: #000000"> </span><span style="color: #A31515">&quot;content provider&quot;</span><span style="color: #000000">, </span><span style="color: #008000">// This field is used to specify  The owner/producer of the content</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentBdcastStart:</span><span style="color: #000000"> </span><span style="color: #098658">1717763792000</span><span style="color: #000000">, </span><span style="color: #008000">// EPG brocast start timestamp for a program in milliseconds</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentBdcastStop:</span><span style="color: #000000"> </span><span style="color: #098658">1717770992000</span><span style="color: #000000">, </span><span style="color: #008000">// EPG brocast end timestamp for a program in milliseconds</span>
<span style="color: #000000">}</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setAnalyticsMetadata</span><span style="color: #000000">(</span><span style="color: #001080">metadata</span><span style="color: #000000">);</span>
</code></pre>
				<p>When the program changes during playback of a live channel, the application must call the API again with the new program name and ID.
				This will result in the logging of a child playback session so that all program views can be counted properly on the dashboards.</p>
				<pre><code class="language-javascript"><span style="color: #008000">// Example: ser launches live channel 1, program A is playing</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">metadata</span><span style="color: #000000">: </span><span style="color: #267F99">VoFmaAnalyticsMetadata</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    </span><span style="color: #008000">// ...</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentId:</span><span style="color: #000000">               </span><span style="color: #A31515">&quot;chan1&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentName:</span><span style="color: #000000">             </span><span style="color: #A31515">&quot;Channel 1&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramId:</span><span style="color: #000000">        </span><span style="color: #A31515">&quot;progA&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramName:</span><span style="color: #000000">      </span><span style="color: #A31515">&quot;Program A&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #008000">// ...</span>
<span style="color: #000000">}</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setAnalyticsMetadata</span><span style="color: #000000">(</span><span style="color: #001080">metadata</span><span style="color: #000000">);</span>

<span style="color: #008000">// While still on the same playback and channel, the program changes to program B</span>
<span style="color: #008000">// A new call to setAnalyticsMetadata is done, only the program ID and names are needed</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">metadata2</span><span style="color: #000000">: </span><span style="color: #267F99">VoFmaAnalyticsMetadata</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramId:</span><span style="color: #000000">        </span><span style="color: #A31515">&quot;progB&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">sesContentProgramName:</span><span style="color: #000000">      </span><span style="color: #A31515">&quot;Program B&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">}</span>
<span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">setAnalyticsMetadata</span><span style="color: #000000">(</span><span style="color: #001080">metadata2</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#user-action-latency" id="user-action-latency" style="color: inherit; text-decoration: none;">
					<h3>User action latency</h3>
				</a>
				<p>The playback logger can also be used to measure user action latency. Two functions are available to do so, they must be called one after the other.</p>
				<a href="#user-action-called" id="user-action-called" style="color: inherit; text-decoration: none;">
					<h4>User action called</h4>
				</a>
				<p>The application calls this function when the user calls an action during playback such as pause, play, seek...
				This function returns an action ID to be used later with API <code>userActionEffective</code>.</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">actionId</span><span style="color: #000000"> = </span><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">userActionCalled</span><span style="color: #000000">(</span><span style="color: #001080">UserActionType</span><span style="color: #000000">.</span><span style="color: #0070C1">SEEK</span><span style="color: #000000">); </span><span style="color: #008000">// the type of user action</span>
</code></pre>
				<a href="#user-action-effective" id="user-action-effective" style="color: inherit; text-decoration: none;">
					<h4>User action effective</h4>
				</a>
				<p>The application calls this function after a call to serActionCalled when a user action has taken effect.</p>
				<p>If using alongside VOPlayer, this function should be called when receiving the following event</p>
				<ul>
					<li>UserActionType.PAUSE : when receiving Player.PAUSE</li>
					<li>UserActionType.PLAY : when receiving Player.PLAY</li>
					<li>UserActionType.SEEK : when receiving Player.BUFFERING end event</li>
				</ul>
				<p>Note: user actions are not saved between playback so calling this function with an ID from a previous playback will not do anything.
				Calling this function multiple times on the same ID will also not do anything after the first time.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">getAppPbkSessionLogger</span><span style="color: #000000">().</span><span style="color: #795E26">userActionEffective</span><span style="color: #000000">(</span><span style="color: #001080">actionId</span><span style="color: #000000">); </span><span style="color: #008000">// the action ID returned by userActionCalled</span>
</code></pre>
				<a href="#log-events" id="log-events" style="color: inherit; text-decoration: none;">
					<h2>Log events</h2>
				</a>
				<p>The <code>logEvent</code> API of the <code>VOFmaManager</code> is a generic API that can be used to log generic events.
					The first parameter is the severity of the event, it can be <code>SEVERITY_INFO</code>, <code>SEVERITY_WARNING</code> or <code>SEVERITY_ERROR</code>.
					The second parameter is a title to give for the event.
				The third parameter is the event message itself, it needs to be sent as string but it can contains a stringified JSON.</p>
				<pre><code class="language-javascript"><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">logEvent</span><span style="color: #000000">(</span><span style="color: #001080">VOEventSeverity</span><span style="color: #000000">.</span><span style="color: #0070C1">SEVERITY_INFO</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;title&#039;</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;message&#039;</span><span style="color: #000000">);</span>
<span style="color: #008000">// voplayer.Player.fmaManager.Instance.logEvent(VOEventSeverity.SEVERITY_INFO, &#039;title&#039;, &#039;message&#039;); // when calling the FMA through the VOPlayer</span>
</code></pre>
				<a href="#debugging" id="debugging" style="color: inherit; text-decoration: none;">
					<h2>Debugging</h2>
				</a>
				<p>Log are disabled by default, they can be enabled by calling:</p>
				<pre><code><span style="color: #001080">VOFmaManager</span><span style="color: #000000">.</span><span style="color: #001080">Instance</span><span style="color: #000000">.</span><span style="color: #795E26">enableLog</span><span style="color: #000000">(</span><span style="color: #0000FF">true</span><span style="color: #000000">);</span>
</code></pre>
				<p>Log will remain enabled until they are explicitely disabled or browser local storage is cleared.</p>
				<a href="#limitations" id="limitations" style="color: inherit; text-decoration: none;">
					<h2>Limitations</h2>
				</a>
				<blockquote>
					<ul>
						<li>If the application opens a link in a new tab, the FMA isn’t able to determine the page origin ID.</li>
						<li>after a search, when the user decides to open some search results in new tab / window, the FMA isn’t able to automatically link this new page to the search session id. To link the page to the search session ID , please refer to the <strong>Recover a search result</strong> section above</li>
					</ul>
				</blockquote>
				<p>project used node js v16.20.1</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>