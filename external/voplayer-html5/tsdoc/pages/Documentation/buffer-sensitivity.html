<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Adaptive Bitrate Control | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Adaptive Bitrate Control</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="buffer-sensitivity.html">Adaptive Bitrate Control</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>This tutorial aims to explain how to adjust buffer sensitivity
				To make buffer sensitivity configuration simpler we define 3 levels of sensitivity: low, medium, high</p>
				<a href="#select-the-buffer-sensitivity-level" id="select-the-buffer-sensitivity-level" style="color: inherit; text-decoration: none;">
					<h2>Select the buffer sensitivity level</h2>
				</a>
				<p>The configuration is done through the config object passed to the player via the <em>configure</em> method.
				If it is not set or if the value is not recognized, the default value is <em>medium</em>.</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">abr:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">bufferSensitivity:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;medium&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// one of: low, medium, high</span>
<span style="color: #000000">    </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<a href="#configuration-for-hls" id="configuration-for-hls" style="color: inherit; text-decoration: none;">
					<h2>Configuration for Hls</h2>
				</a>
				<p>Below are the parameters that will be applied to the Hls player depending the buffer sensitivity selected.</p>
				<a href="#maxbuffersize" id="maxbuffersize" style="color: inherit; text-decoration: none;">
					<h3><code>maxBufferSize</code></h3>
				</a>
				<p>(default: 60 MB)</p>
				<p>&#39;Minimum&#39; maximum buffer size in bytes. If buffer size upfront is bigger than this value, no fragment will be loaded.</p>
				<a href="#abrewmafastlive" id="abrewmafastlive" style="color: inherit; text-decoration: none;">
					<h3><code>abrEwmaFastLive</code></h3>
				</a>
				<p>(default: <code>3.0</code>)</p>
				<p>Fast bitrate Exponential moving average half-life, used to compute average bitrate for Live streams.
					Half of the estimate is based on the last abrEwmaFastLive seconds of sample history.
				Each of the sample is weighted by the fragment loading duration.</p>
				<p>parameter should be a float greater than 0</p>
				<a href="#abrewmaslowlive" id="abrewmaslowlive" style="color: inherit; text-decoration: none;">
					<h3><code>abrEwmaSlowLive</code></h3>
				</a>
				<p>(default: <code>9.0</code>)</p>
				<p>Slow bitrate Exponential moving average half-life, used to compute average bitrate for Live streams.
					Half of the estimate is based on the last abrEwmaSlowLive seconds of sample history.
				Each of the sample is weighted by the fragment loading duration.</p>
				<p>parameter should be a float greater than <a href="#abrewmafastlive">abrEwmaFastLive</a></p>
				<a href="#abrewmafastvod" id="abrewmafastvod" style="color: inherit; text-decoration: none;">
					<h3><code>abrEwmaFastVoD</code></h3>
				</a>
				<p>(default: <code>3.0</code>)</p>
				<p>Fast bitrate Exponential moving average half-life, used to compute average bitrate for VoD streams.
					Half of the estimate is based on the last abrEwmaFastVoD seconds of sample history.
				Each of the sample is weighted by the fragment loading duration.</p>
				<p>parameter should be a float greater than 0</p>
				<a href="#abrewmaslowvod" id="abrewmaslowvod" style="color: inherit; text-decoration: none;">
					<h3><code>abrEwmaSlowVoD</code></h3>
				</a>
				<p>(default: <code>9.0</code>)</p>
				<p>Slow bitrate Exponential moving average half-life, used to compute average bitrate for VoD streams.
					Half of the estimate is based on the last abrEwmaSlowVoD seconds of sample history.
				Each of the sample is weighted by the fragment loading duration.</p>
				<p>parameter should be a float greater than <a href="#abrewmafastvod">abrEwmaFastVoD</a></p>
				<a href="#abrbandwidthfactor" id="abrbandwidthfactor" style="color: inherit; text-decoration: none;">
					<h3><code>abrBandWidthFactor</code></h3>
				</a>
				<p>(default: <code>0.95</code>)</p>
				<p>Scale factor to be applied against measured bandwidth average, to determine whether we can stay on current or lower quality level.
				If <code>abrBandWidthFactor * bandwidth average &gt; level.bitrate</code> then ABR can switch to that level providing that it is equal or less than current level.</p>
				<a href="#abrbandwidthupfactor" id="abrbandwidthupfactor" style="color: inherit; text-decoration: none;">
					<h3><code>abrBandWidthUpFactor</code></h3>
				</a>
				<p>(default: <code>0.7</code>)</p>
				<p>Scale factor to be applied against measured bandwidth average, to determine whether we can switch up to a higher quality level.
				If <code>abrBandWidthUpFactor * bandwidth average &gt; level.bitrate</code> then ABR can switch up to that quality level.</p>
				<a href="#maxstarvationdelay" id="maxstarvationdelay" style="color: inherit; text-decoration: none;">
					<h3><code>maxStarvationDelay</code></h3>
				</a>
				<p>(default 4s)</p>
				<p>ABR algorithm will always try to choose a quality level that should avoid rebuffering.
					In case no quality level with this criteria can be found (lets say for example that buffer length is 1s,
					but fetching a fragment at lowest quality is predicted to take around 2s ... ie we can forecast around 1s of rebuffering ...)
				then ABR algorithm will try to find a level that should guarantee less than <code>maxStarvationDelay</code> of buffering.</p>
				<a href="#startlevel" id="startlevel" style="color: inherit; text-decoration: none;">
					<h3><code>startLevel</code></h3>
				</a>
				<p>(default: <code>undefined</code>)</p>
				<p>When set, use this level as the default hls.startLevel. Keep in mind that the startLevel set with the API takes precedence over config.startLevel configuration parameter.</p>
				<ul>
					<li>low:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        maxBufferSize: </span><span style="color: #098658">80000000</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaFastLive: </span><span style="color: #098658">5</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaSlowLive: </span><span style="color: #098658">12</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaFastVoD: </span><span style="color: #098658">5</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaSlowVoD: </span><span style="color: #098658">12</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrBandWidthFactor: </span><span style="color: #098658">0.85</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrBandWidthUpFactor: </span><span style="color: #098658">0.7</span><span style="color: #000000">,</span>
<span style="color: #000000">        maxStarvationDelay: </span><span style="color: #098658">6</span><span style="color: #000000">,</span>
<span style="color: #000000">        startLevel: </span><span style="color: #0000FF">undefined</span><span style="color: #000000"> </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">    }</span>
</code></pre>
				<ul>
					<li>medium:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        maxBufferSize: </span><span style="color: #098658">60000000</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrEwmaFastLive: </span><span style="color: #098658">3</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrEwmaSlowLive: </span><span style="color: #098658">9</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrEwmaFastVoD: </span><span style="color: #098658">3</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrEwmaSlowVoD: </span><span style="color: #098658">9</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrBandWidthFactor: </span><span style="color: #098658">0.95</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        abrBandWidthUpFactor: </span><span style="color: #098658">0.7</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        maxStarvationDelay: </span><span style="color: #098658">4</span><span style="color: #000000">, </span><span style="color: #008000">// (default Hls value)</span>
<span style="color: #000000">        startLevel: -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    }</span>
</code></pre>
				<ul>
					<li>high:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        maxBufferSize: </span><span style="color: #098658">40000000</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaFastLive: </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaSlowLive: </span><span style="color: #098658">7</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaFastVoD: </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrEwmaSlowVoD: </span><span style="color: #098658">7</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrBandWidthFactor: </span><span style="color: #098658">0.95</span><span style="color: #000000">,</span>
<span style="color: #000000">        abrBandWidthUpFactor: </span><span style="color: #098658">0.9</span><span style="color: #000000">,</span>
<span style="color: #000000">        maxStarvationDelay: </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        startLevel: -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    }</span>
</code></pre>
				<a href="#configuration-for-dash" id="configuration-for-dash" style="color: inherit; text-decoration: none;">
					<h2>Configuration for Dash</h2>
				</a>
				<p>Below are the parameters that will be applied to the Dash player depending the buffer sensitivity selected.</p>
				<a href="#livedelay" id="livedelay" style="color: inherit; text-decoration: none;">
					<h3><code>liveDelay</code></h3>
				</a>
				<p>(default: NaN)</p>
				<p>Value in seconds that will lower or increase live stream latency.</p>
				<p>Lowering this value will lower latency but may decrease the player&#39;s ability to build a stable buffer.</p>
				<p>This value should be less than the manifest duration by a couple of segment durations to avoid playback issues.</p>
				<a href="#stablebuffertime" id="stablebuffertime" style="color: inherit; text-decoration: none;">
					<h3><code>stableBufferTime</code></h3>
				</a>
				<p>(default: 12)</p>
				<p>The time that the internal buffer target will be set to post startup/seeks (NOT top quality).</p>
				<p>When the time is set higher than the default you will have to wait longer to see automatic bitrate switches but will have a larger buffer which will increase stability.</p>
				<a href="#buffertimeattopquality" id="buffertimeattopquality" style="color: inherit; text-decoration: none;">
					<h3><code>bufferTimeAtTopQuality</code></h3>
				</a>
				<p>(default: 30)
				The time that the internal buffer target will be set to once playing the top quality.</p>
				<p>If there are multiple bitrates in your adaptation, and the media is playing at the highest bitrate, then we try to build a larger buffer at the top quality to increase stability and to maintain media quality.</p>
				<a href="#movingaveragemethod" id="movingaveragemethod" style="color: inherit; text-decoration: none;">
					<h3><code>movingAverageMethod</code></h3>
				</a>
				<p>(default: &#39;slidingWindow&#39;)
					Sets the moving average method used for smoothing throughput estimates.
					Valid methods are &quot;slidingWindow&quot; and &quot;ewma&quot;.
					The call has no effect if an invalid method is passed.
					The sliding window moving average method computes the average throughput using the last four segments downloaded.
					If the stream is live (as opposed to VOD), then only the last three segments are used.
					If wide variations in throughput are detected, the number of segments can be dynamically increased to avoid oscillations.
					The exponentially weighted moving average (EWMA) method computes the average using exponential smoothing.
					Two separate estimates are maintained, a fast one with a three-second half life and a slow one with an eight-second half life.
					The throughput estimate at any time is the minimum of the fast and slow estimates.
				This allows a fast reaction to a bandwidth drop and prevents oscillations on bandwidth spikes.</p>
				<a href="#abrstrategy" id="abrstrategy" style="color: inherit; text-decoration: none;">
					<h3><code>ABRStrategy</code></h3>
				</a>
				<p>(default: &#39;abrDynamic&#39;)
				The ABR strategy to use: &quot;abrDynamic&quot;, &quot;abrBola&quot; or &quot;abrThroughput&quot;.</p>
				<a href="#bandwidthsafetyfactor" id="bandwidthsafetyfactor" style="color: inherit; text-decoration: none;">
					<h3><code>bandwidthSafetyFactor</code></h3>
				</a>
				<p>(default: 0.9)
					Standard ABR throughput rules multiply the throughput by this value.
				It should be between 0 and 1, with lower values giving less rebuffering (but also lower quality).</p>
				<ul>
					<li>low:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        streaming: {</span>
<span style="color: #000000">            delay :{</span>
<span style="color: #000000">                liveDelay :</span><span style="color: #098658">20</span><span style="color: #000000"> </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            buffer :{</span>
<span style="color: #000000">                stableBufferTime: </span><span style="color: #098658">20</span><span style="color: #000000">, </span>
<span style="color: #000000">                bufferTimeAtTopQuality: </span><span style="color: #098658">45</span><span style="color: #000000">, </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            abr: {</span>
<span style="color: #000000">                movingAverageMethod: </span><span style="color: #A31515">&#039;ewma&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">                ABRStrategy: </span><span style="color: #A31515">&#039;abrBola&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">                bandwidthSafetyFactor: </span><span style="color: #098658">0.85</span><span style="color: #000000">,</span>
<span style="color: #000000">            }</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>
</code></pre>
				<ul>
					<li>medium:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        streaming: {</span>
<span style="color: #000000">            delay :{</span>
<span style="color: #000000">                liveDelay :</span><span style="color: #098658">12</span><span style="color: #000000"> </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            buffer :{</span>
<span style="color: #000000">                stableBufferTime: </span><span style="color: #098658">12</span><span style="color: #000000">, </span>
<span style="color: #000000">                bufferTimeAtTopQuality: </span><span style="color: #098658">30</span><span style="color: #000000">, </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            abr: {</span>
<span style="color: #000000">                movingAverageMethod: </span><span style="color: #A31515">&#039;slidingWindow&#039;</span><span style="color: #000000">, </span>
<span style="color: #000000">                ABRStrategy: </span><span style="color: #A31515">&#039;abrDynamic&#039;</span><span style="color: #000000">, </span>
<span style="color: #000000">                bandwidthSafetyFactor: </span><span style="color: #098658">0.9</span><span style="color: #000000">, </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>
</code></pre>
				<ul>
					<li>high:</li>
				</ul>
				<pre><code><span style="color: #000000">    {</span>
<span style="color: #000000">        streaming: {</span>
<span style="color: #000000">            delay :{</span>
<span style="color: #000000">                liveDelay :</span><span style="color: #098658">8</span><span style="color: #000000"> </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            buffer :{</span>
<span style="color: #000000">                stableBufferTime: </span><span style="color: #098658">8</span><span style="color: #000000">, </span>
<span style="color: #000000">                bufferTimeAtTopQuality: </span><span style="color: #098658">20</span><span style="color: #000000">, </span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            abr: {</span>
<span style="color: #000000">                movingAverageMethod: </span><span style="color: #A31515">&#039;slidingWindow&#039;</span><span style="color: #000000">, </span>
<span style="color: #000000">                ABRStrategy: </span><span style="color: #A31515">&#039;abrThroughput&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">                bandwidthSafetyFactor: </span><span style="color: #098658">0.95</span><span style="color: #000000">,</span>
<span style="color: #000000">            }</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>
</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>