<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Core player configuration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Core player configuration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../player-configuration.html">Player configuration</a>
				</li>
				<li>
					<a href="core-player-configuration.html">Core player configuration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#override-default-configuration-for-hls" id="override-default-configuration-for-hls" style="color: inherit; text-decoration: none;">
					<h2>Override default configuration for Hls</h2>
				</a>
				<p>Hls Core player can be adjusted using the <em>hlsConfiguration</em> object passed to the player via the config object using the <em>configure</em> method.</p>
				<p>All the parameters that can be adjusted are listed below with their default value. (See hls.js project for detailed information related to these parameters)</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">hlsConfiguration:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">autoStartLoad:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">startPosition:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">debug:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">capLevelOnFPSDrop:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">capLevelToPlayerSize:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">defaultAudioCodec:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">initialLiveManifestSize:</span><span style="color: #000000"> </span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxBufferLength:</span><span style="color: #000000"> </span><span style="color: #098658">30</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxMaxBufferLength:</span><span style="color: #000000"> </span><span style="color: #098658">600</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">backBufferLength:</span><span style="color: #000000"> </span><span style="color: #0000FF">Infinity</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxBufferSize:</span><span style="color: #000000"> </span><span style="color: #098658">60</span><span style="color: #000000"> * </span><span style="color: #098658">1000</span><span style="color: #000000"> * </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxBufferHole:</span><span style="color: #000000"> </span><span style="color: #098658">0.5</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">highBufferWatchdogPeriod:</span><span style="color: #000000"> </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">nudgeOffset:</span><span style="color: #000000"> </span><span style="color: #098658">0.1</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">nudgeMaxRetry:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxFragLookUpTolerance:</span><span style="color: #000000"> </span><span style="color: #098658">0.25</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">liveSyncDurationCount:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">liveMaxLatencyDurationCount:</span><span style="color: #000000"> </span><span style="color: #0000FF">Infinity</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">liveDurationInfinity:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">enableWorker:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">enableSoftwareAES:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">manifestLoadingTimeOut:</span><span style="color: #000000"> </span><span style="color: #098658">10000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">manifestLoadingMaxRetry:</span><span style="color: #000000"> </span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">manifestLoadingRetryDelay:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">manifestLoadingMaxRetryTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">64000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">startLevel:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">levelLoadingTimeOut:</span><span style="color: #000000"> </span><span style="color: #098658">10000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">levelLoadingMaxRetry:</span><span style="color: #000000"> </span><span style="color: #098658">4</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">levelLoadingRetryDelay:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">levelLoadingMaxRetryTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">64000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fragLoadingTimeOut:</span><span style="color: #000000"> </span><span style="color: #098658">20000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fragLoadingMaxRetry:</span><span style="color: #000000"> </span><span style="color: #098658">6</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fragLoadingRetryDelay:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fragLoadingMaxRetryTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">64000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">startFragPrefetch:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">testBandwidth:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">progressive:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">lowLatencyMode:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fpsDroppedMonitoringPeriod:</span><span style="color: #000000"> </span><span style="color: #098658">5000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fpsDroppedMonitoringThreshold:</span><span style="color: #000000"> </span><span style="color: #098658">0.2</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">appendErrorMaxRetry:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">loader:</span><span style="color: #000000"> </span><span style="color: #001080">customLoader</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fLoader:</span><span style="color: #000000"> </span><span style="color: #001080">customFragmentLoader</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">pLoader:</span><span style="color: #000000"> </span><span style="color: #001080">customPlaylistLoader</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">xhrSetup:</span><span style="color: #000000"> </span><span style="color: #001080">XMLHttpRequestSetupCallback</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fetchSetup:</span><span style="color: #000000"> </span><span style="color: #001080">FetchSetupCallback</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrController:</span><span style="color: #000000"> </span><span style="color: #001080">AbrController</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">bufferController:</span><span style="color: #000000"> </span><span style="color: #001080">BufferController</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">capLevelController:</span><span style="color: #000000"> </span><span style="color: #001080">CapLevelController</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">fpsController:</span><span style="color: #000000"> </span><span style="color: #001080">FPSController</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">timelineController:</span><span style="color: #000000"> </span><span style="color: #001080">TimelineController</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">enableWebVTT:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">enableIMSC1:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">enableCEA708Captions:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">stretchShortVideoTrack:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxAudioFramesDrift:</span><span style="color: #000000"> </span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">forceKeyFrameOnDiscontinuity:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEwmaFastLive:</span><span style="color: #000000"> </span><span style="color: #098658">3.0</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEwmaSlowLive:</span><span style="color: #000000"> </span><span style="color: #098658">9.0</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEwmaFastVoD:</span><span style="color: #000000"> </span><span style="color: #098658">3.0</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEwmaSlowVoD:</span><span style="color: #000000"> </span><span style="color: #098658">9.0</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEwmaDefaultEstimate:</span><span style="color: #000000"> </span><span style="color: #098658">500000</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrBandWidthFactor:</span><span style="color: #000000"> </span><span style="color: #098658">0.95</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrBandWidthUpFactor:</span><span style="color: #000000"> </span><span style="color: #098658">0.7</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">abrMaxWithRealBitrate:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxStarvationDelay:</span><span style="color: #000000"> </span><span style="color: #098658">4</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">maxLoadingDelay:</span><span style="color: #000000"> </span><span style="color: #098658">4</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">minAutoBitrate:</span><span style="color: #000000"> </span><span style="color: #098658">0</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">emeEnabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">widevineLicenseUrl:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">licenseXhrSetup:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">drmSystemOptions:</span><span style="color: #000000"> {},</span>
<span style="color: #000000">    </span><span style="color: #001080">requestMediaKeySystemAccessFunc:</span><span style="color: #000000"> </span><span style="color: #001080">requestMediaKeySystemAccess</span><span style="color: #000000">,</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<a href="#override-default-configuration-for-dash" id="override-default-configuration-for-dash" style="color: inherit; text-decoration: none;">
					<h2>Override default configuration for Dash</h2>
				</a>
				<p>Dash core player configuration can be adjusted using the <em>dashConfiguration</em> object passed to the player via the config object using the <em>configure</em> method.</p>
				<p>All the parameters that can be adjusted are listed below with their default value. (See dash.js project for detailed information related to these parameters)</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">dashConfiguration:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">debug:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">logLevel:</span><span style="color: #000000"> </span><span style="color: #001080">Debug</span><span style="color: #000000">.</span><span style="color: #0070C1">LOG_LEVEL_WARNING</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">dispatchEvent:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span>
<span style="color: #000000">    },</span>
<span style="color: #000000">    </span><span style="color: #001080">streaming:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">abandonLoadTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">10000</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">wallclockTimeUpdateInterval:</span><span style="color: #000000"> </span><span style="color: #098658">100</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">lowLatencyEnabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">manifestUpdateRetryInterval:</span><span style="color: #000000"> </span><span style="color: #098658">100</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">cacheInitSegments:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">eventControllerRefreshDelay:</span><span style="color: #000000"> </span><span style="color: #098658">150</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">capabilities:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">filterUnsupportedEssentialProperties:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">useMediaCapabilitiesApi:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">timeShiftBuffer:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">calcFromSegmentTimeline:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">fallbackToSegmentTimeline:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">metrics:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">maxListDepth:</span><span style="color: #000000"> </span><span style="color: #098658">100</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">delay:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">liveDelayFragmentCount:</span><span style="color: #000000"> </span><span style="color: #0000FF">NaN</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">liveDelay:</span><span style="color: #000000"> </span><span style="color: #0000FF">NaN</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">useSuggestedPresentationDelay:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">applyServiceDescription:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">protection:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">ignoreEmeEncryptedEvent:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">keepProtectionMediaKeys:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">buffer:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">fastSwitchEnabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">flushBufferAtTrackSwitch:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">reuseExistingSourceBuffers:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">bufferPruningInterval:</span><span style="color: #000000"> </span><span style="color: #098658">10</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">bufferToKeep:</span><span style="color: #000000"> </span><span style="color: #098658">20</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">bufferTimeAtTopQuality:</span><span style="color: #000000"> </span><span style="color: #098658">30</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">bufferTimeAtTopQualityLongForm:</span><span style="color: #000000"> </span><span style="color: #098658">60</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">initialBufferLevel:</span><span style="color: #000000"> </span><span style="color: #0000FF">NaN</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">stableBufferTime:</span><span style="color: #000000"> </span><span style="color: #098658">12</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">longFormContentDurationThreshold:</span><span style="color: #000000"> </span><span style="color: #098658">600</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">stallThreshold:</span><span style="color: #000000"> </span><span style="color: #098658">0.3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">useAppendWindow:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">setStallState:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">gaps:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">jumpGaps:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">jumpLargeGaps:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">smallGapLimit:</span><span style="color: #000000"> </span><span style="color: #098658">1.5</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">threshold:</span><span style="color: #000000"> </span><span style="color: #098658">0.3</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">utcSynchronization:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">, </span><span style="color: #008000">// forced to false for live MSS streams</span>
<span style="color: #000000">        </span><span style="color: #001080">useManifestDateHeaderTimeSource:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">backgroundAttempts:</span><span style="color: #000000"> </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">timeBetweenSyncAttempts:</span><span style="color: #000000"> </span><span style="color: #098658">30</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">maximumTimeBetweenSyncAttempts:</span><span style="color: #000000"> </span><span style="color: #098658">600</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">minimumTimeBetweenSyncAttempts:</span><span style="color: #000000"> </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">timeBetweenSyncAttemptsAdjustmentFactor:</span><span style="color: #000000"> </span><span style="color: #098658">2</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">maximumAllowedDrift:</span><span style="color: #000000"> </span><span style="color: #098658">100</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">enableBackgroundSyncAfterSegmentDownloadError:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">defaultTimingSource:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">scheme:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;urn:mpeg:dash:utc:http-xsdate:2014&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">value:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://time.akamai.com/?iso&amp;ms&#039;</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">scheduling:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">defaultTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">500</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">lowLatencyTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">0</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">scheduleWhilePaused:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">text:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">defaultEnabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">liveCatchup:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">minDrift:</span><span style="color: #000000"> </span><span style="color: #098658">0.02</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">maxDrift:</span><span style="color: #000000"> </span><span style="color: #098658">0</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">playbackRate:</span><span style="color: #000000"> </span><span style="color: #098658">0.5</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">latencyThreshold:</span><span style="color: #000000"> </span><span style="color: #098658">60</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">playbackBufferMin:</span><span style="color: #000000"> </span><span style="color: #098658">0.5</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">mode:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">LIVE_CATCHUP_MODE_DEFAULT</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">lastBitrateCachingInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">ttl:</span><span style="color: #000000"> </span><span style="color: #098658">360000</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">lastMediaSettingsCachingInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">ttl:</span><span style="color: #000000"> </span><span style="color: #098658">360000</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">cacheLoadThresholds:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">video:</span><span style="color: #000000"> </span><span style="color: #098658">50</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">audio:</span><span style="color: #000000"> </span><span style="color: #098658">5</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">trackSwitchMode:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">audio:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">TRACK_SWITCH_MODE_ALWAYS_REPLACE</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">video:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">TRACK_SWITCH_MODE_NEVER_REPLACE</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">selectionModeForInitialTrack:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">TRACK_SELECTION_MODE_HIGHEST_BITRATE</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">fragmentRequestTimeout:</span><span style="color: #000000"> </span><span style="color: #098658">20000</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #001080">retryIntervals:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MPD_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">500</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">XLINK_EXPANSION_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">500</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MEDIA_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">INIT_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">BITSTREAM_SWITCHING_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">INDEX_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MSS_FRAGMENT_INFO_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">LICENSE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">OTHER_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1000</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">lowLatencyReductionFactor:</span><span style="color: #000000"> </span><span style="color: #098658">10</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">retryAttempts:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MPD_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">XLINK_EXPANSION_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MEDIA_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">INIT_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">BITSTREAM_SWITCHING_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">INDEX_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">MSS_FRAGMENT_INFO_SEGMENT_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">LICENSE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">[HTTPRequest.</span><span style="color: #0070C1">OTHER_TYPE</span><span style="color: #001080">]:</span><span style="color: #000000"> </span><span style="color: #098658">3</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">lowLatencyMultiplyFactor:</span><span style="color: #000000"> </span><span style="color: #098658">5</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">abr:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">movingAverageMethod:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">MOVING_AVERAGE_SLIDING_WINDOW</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">ABRStrategy:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">ABR_STRATEGY_DYNAMIC</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">additionalAbrRules:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">insufficientBufferRule:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">switchHistoryRule:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">droppedFramesRule:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">abandonRequestsRule:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">bandwidthSafetyFactor:</span><span style="color: #000000"> </span><span style="color: #098658">0.9</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">useDefaultABRRules:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">useDeadTimeLatency:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">limitBitrateByPortal:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">usePixelRatioInLimitBitrateByPortal:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">maxBitrate:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">minBitrate:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">maxRepresentationRatio:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> </span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> </span><span style="color: #098658">1</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">initialBitrate:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">initialRepresentationRatio:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> -</span><span style="color: #098658">1</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">autoSwitchBitrate:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">audio:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">video:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">fetchThroughputCalculationMode:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">ABR_FETCH_THROUGHPUT_CALCULATION_MOOF_PARSING</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">cmcd:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">enabled:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">sid:</span><span style="color: #000000"> </span><span style="color: #0000FF">null</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">cid:</span><span style="color: #000000"> </span><span style="color: #0000FF">null</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">rtp:</span><span style="color: #000000"> </span><span style="color: #0000FF">null</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">rtpSafetyFactor:</span><span style="color: #000000"> </span><span style="color: #098658">5</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">mode:</span><span style="color: #000000"> </span><span style="color: #001080">Constants</span><span style="color: #000000">.</span><span style="color: #0070C1">CMCD_MODE_QUERY</span>
<span style="color: #000000">      }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="main-player-configuration.html">Main player configuration</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="core-player-configuration.html">Core player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="custom-request.html">Custom HTTP request</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>