<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Braodpeak Smartlib pre-Integration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Braodpeak Smartlib pre-Integration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="smart-lib.html">Braodpeak Smartlib pre-Integration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>The use of BroadPeak&#39;s SmartLib is pre-integrated with VO Player and is available as an extension in dedicated library file <em>voplayer-smartlib.min.js</em></p>
				<p>This file must be included <em>after</em> the core library (<em>voplayer.min.js</em>) in the page before creating the player object.</p>
				<p>From version 3.5 of the VO Player, it is not required to load the smarlib.js library from BraodBeak. It is now fully included in the extension.</p>
				<p>The broadpeak smartlib pre-integration is currently limited to: Braodpeak CDN Integration and nanoCDN discovery and session handling. The following integration are not supported:</p>
				<ul>
					<li>Player metrics</li>
					<li>Ad insertion and ad tracking</li>
					<li>Real-time Monitoring</li>
					<li>Diversity failover</li>
				</ul>
				<a href="#general-use-of-the-smartlib-extension" id="general-use-of-the-smartlib-extension" style="color: inherit; text-decoration: none;">
					<h2>General use of the Smartlib extension</h2>
				</a>
				<p>The <code>createPlayer</code> API and the <code>createPlayerWithControls</code> API automatically detect the presence of the smartlib extension and will create a player that supports the broadpeak smartlib. When the Smartlib extension for VO Player is detected, the player instance created by the UI controller is the extended class of VO Player: PlayerBPKExtended .</p>
				<p>If you want to create the player instance with the extended class of VO Player directly from its class, you will have to create it as follows:</p>
				<pre><code class="language-javascript"><span style="color: #001080">player</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #001080">voplayer</span><span style="color: #000000">.</span><span style="color: #001080">smartlib</span><span style="color: #000000">.</span><span style="color: #795E26">PlayerBPKExtended</span><span style="color: #000000">(</span><span style="color: #001080">videoElement</span><span style="color: #000000">);</span>
</code></pre>
				<p>Whatever player creation you follow, you will then have to initialise the SmartLib by calling:</p>
				<pre><code class="language-javascript"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">initSmartLib</span><span style="color: #000000">(</span><span style="color: #001080">analyticsAddress</span><span style="color: #000000">, </span><span style="color: #001080">nanoCDNHost</span><span style="color: #000000">, </span><span style="color: #001080">broadpeakDomainNames</span><span style="color: #000000">)</span>
</code></pre>
				<p>Once initialized, all calls to player.load() will first run the URL through the Smartlib to retrieve a potential redirected URL.</p>
				<p>If you wish to return to the default player loading sequence without the call to the Smartlib on the same instance of the player, you can destroy the Smartlib session of the player by calling</p>
				<pre><code class="language-javascript"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">releaseSmartLib</span><span style="color: #000000">();</span>
</code></pre>
				<p>Instance of the smartlib is available via <code>player.smartlibInstance</code> and smartlib session is available via <code>player.smartlibSession</code>.
					You may use <code>player.smartlibInstance</code> and <code>player.smartlibSession</code> to further control your application, using the smartlib APIs.
				Please refer to <a href="https://delivery-platform.broadpeak.tv/smartlib/home">https://delivery-platform.broadpeak.tv/smartlib/home</a> for further detailed controll of the smartlib.</p>
				<a href="#smartlib-initialization-with-nanocdn" id="smartlib-initialization-with-nanocdn" style="color: inherit; text-decoration: none;">
					<h2>smartlib initialization with nanocdn</h2>
				</a>
				<p>In case the nano cdn shall be used and that the player is created just before playing a content, it may occur that the nanocdn is not fully initialized for the playback of the content. Consequently the stream will not play via an mABR session but through a normal OTT session.</p>
				<p>In such case the player provides static APIs to initialize and release the smartlib independently from the player creation/destruction.</p>
				<p>To initialize the smartlib you may then use :</p>
				<pre><code class="language-javascript"><span style="color: #001080">voplayer</span><span style="color: #000000">.</span><span style="color: #001080">smartlib</span><span style="color: #000000">.</span><span style="color: #001080">PlayerBPKExtended</span><span style="color: #000000">.</span><span style="color: #795E26">initSmartLib</span><span style="color: #000000">(</span><span style="color: #001080">analyticsAddress</span><span style="color: #000000">, </span><span style="color: #001080">nanoCDNHost</span><span style="color: #000000">, </span><span style="color: #001080">broadpeakDomainNames</span><span style="color: #000000">)</span>
</code></pre>
				<p>and to release the smarlib you may then use : </p>
				<pre><code class="language-javascript"><span style="color: #001080">voplayer</span><span style="color: #000000">.</span><span style="color: #001080">smartlib</span><span style="color: #000000">.</span><span style="color: #001080">PlayerBPKExtended</span><span style="color: #000000">.</span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">releaseSmartLib</span><span style="color: #000000">();</span>
</code></pre>
				<p>Note that static and non static APIs for initialization and release of the smartlib shall not be mixed together in the life cycle of the player.</p>
				<a href="#impact-on-player-api" id="impact-on-player-api" style="color: inherit; text-decoration: none;">
					<h2>impact on player API</h2>
				</a>
				<p>To use the player.load() API, the following changes are required in the integration when loading the playback:</p>
				<p>instead of providing a string to the load player API:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">streamURL</span><span style="color: #000000"> = </span><span style="color: #A31515">&quot;...&quot;</span><span style="color: #000000">; </span><span style="color: #008000">//string</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #001080">streamURL</span><span style="color: #000000">);</span>
</code></pre>
				<p>you shall provide to the load player API a media object containing the string url:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">streamURL</span><span style="color: #000000"> = </span><span style="color: #A31515">&quot;...&quot;</span><span style="color: #000000">; </span><span style="color: #008000">//string</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">({</span><span style="color: #001080">url:</span><span style="color: #000000"> </span><span style="color: #001080">streamURL</span><span style="color: #000000">});</span>
</code></pre>
				<a href="#smartlib-ad-feature-enablement" id="smartlib-ad-feature-enablement" style="color: inherit; text-decoration: none;">
					<h2>smartlib ad feature enablement</h2>
				</a>
				<p>The support of smarlib ad feature shall be explicitely enabled via player configuration  with the following paremeter :</p>
				<pre><code class="language-javascript"><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">enableSmartLibAdvertisement</span><span style="color: #000000"> = </span><span style="color: #0000FF">true</span><span style="color: #000000">;</span>
</code></pre>
				<p>Additionnaly the feature request that the type of stream (STOV or Live) is explicitely set to Live prior to the start of the stream.
				Consequently in this case the following parameters shall be set in the player configuration:</p>
				<pre><code class="language-javascript"><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">isEventAutomatic</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;</span>
<span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">isEventStream</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;  </span><span style="color: #008000">// indicate that the stream is a Live stream </span>
</code></pre>
				<p>With this configuration set, if advertisements are present in the stream, the smartlib ad event will be translated in VO Player ad events and emitted to the application.</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>