<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DRM configuration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>DRM configuration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../playback-drm.html">Playback of DRM protected streams</a>
				</li>
				<li>
					<a href="drm-config.html">DRM configuration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#license-servers" id="license-servers" style="color: inherit; text-decoration: none;">
					<h2>License Servers</h2>
				</a>
				<p>Without DRM configuration, Viaccess-Orca player only plays clear content.
					To play protected content, the application needs to tell the player at least one
				basic thing: the URL(s) of its license server(s).</p>
				<p>This can be simply done through <code>player.configure()</code>.  The field <code>drm.servers</code> is
					an object mapping key system IDs to server URLs.  For example, to set license
				servers for both Widevine and Playready:</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">servers:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/widevine&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;com.microsoft.playready&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/playready&#039;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<p>Assuming your manifest uses the standard UUIDs for those key systems, that&#39;s
				all you need to do.</p>
				<a href="#choosing-a-key-system" id="choosing-a-key-system" style="color: inherit; text-decoration: none;">
					<h2>Choosing a Key System</h2>
				</a>
				<p>Viaccess-Orca Player is key-system-agnostic, meaning we don&#39;t prefer any key systems
					over any others.  We use EME to ask the browser what it supports, and make no
					assumptions.  If your browser supports multiple key systems, the first supported
				key system in the manifest is used.</p>
				<p>The interoperable encryption standard that DRM vendors are implementing is
					called Common Encryption (CENC).  Some DASH manifests don&#39;t specify any
				particular key system at all, but instead state that any CENC system will do:</p>
				<pre><code class="language-xml"><span style="color: #800000">&lt;ContentProtection</span><span style="color: #000000"> </span><span style="color: #FF0000">schemeIdUri</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;urn:mpeg:dash:mp4protection:2011&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">value</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;cenc&quot;</span><span style="color: #800000">/&gt;</span>
</code></pre>
				<p>If this is the only <code>&lt;ContentProtection&gt;</code> element in the manifest, Viaccess-Orca
					will try all key systems it knows.  If the browser supports it and you configured
				a license server URL for it, we&#39;ll use it.</p>
				<p>VO Player supports the following DRM:</p>
				<ul>
					<li>Widevine: <code>com.widevine.alpha</code>, for DASH content on Chrome and Firefox browsers</li>
					<li>PlayReady: <code>com.microsoft.playready</code>, for DASH content Edge browser</li>
					<li>Fairplay: <code>com.apple.fps</code>, for HLS content on Safari MacOS browser</li>
				</ul>
				<a href="#clear-key" id="clear-key" style="color: inherit; text-decoration: none;">
					<h3>Clear Key</h3>
				</a>
				<p>The EME spec requires browsers to support a common key system called &quot;Clear
					Key&quot;.  <em>(At the time of this writing (June 2020), only Chrome and Firefox
					have implemented &quot;Clear Key&quot;.)</em>
					Clear Key uses unencrypted keys to decrypt CENC content, and can be useful
					for diagnosing problems and testing integrations.  To configure Clear Key,
					use the configuration field <code>drm.clearKeys</code> and provide a map of key IDs to
				content keys (both in hex):</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">clearKeys:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;deadbeefdeadbeefdeadbeefdeadbeef&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;18675309186753091867530918675309&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;02030507011013017019023029031037&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;03050701302303204201080425098033&#039;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<p>This will force the use of Clear Key for decryption, regardless of what is in
				your manifest.  Use this when you need to confirm that your keys are correct.</p>
				<a href="#clear-key-licenses" id="clear-key-licenses" style="color: inherit; text-decoration: none;">
					<h3>Clear Key Licenses</h3>
				</a>
				<p>If your manifest actually specifies Clear Key, you can also use the normal
					license request mechanism to retrieve keys based on key IDs.  The EME spec
					defines a JSON-based <a href="https://w3c.github.io/encrypted-media/#clear-key-request-format">license request format</a> and <a href="https://w3c.github.io/encrypted-media/#clear-key-license-format">license format</a> for the
					Clear Key CDM.  If you have a server that understands these, just configure
				a license server as normal:</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">servers:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;org.w3.clearkey&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;http://foo.bar/drm/clearkey&#039;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<a href="#advanced-drm-configuration" id="advanced-drm-configuration" style="color: inherit; text-decoration: none;">
					<h2>Advanced DRM Configuration</h2>
				</a>
				<p>We have several <a href="../../../interfaces/Types.AdvancedDrmConfiguration.html">advanced options</a>
					available to give you access to the full EME configuration.  The config field
					<code>drm.advanced</code> is an object mapping key system IDs to their advanced settings.
				For example, to require hardware security in Widevine:</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #001080">servers:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/widevine&#039;</span>
<span style="color: #000000">    },</span>
<span style="color: #000000">    </span><span style="color: #001080">advanced:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;videoRobustness&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;HW_SECURE_ALL&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;audioRobustness&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;HW_SECURE_ALL&#039;</span>
<span style="color: #000000">      }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  }</span>
<span style="color: #000000">});</span>
</code></pre>
				<p>If you don&#39;t need them, you can leave these at their default settings.</p>
				<a href="#drm-server-certificate" id="drm-server-certificate" style="color: inherit; text-decoration: none;">
					<h3>DRM Server Certificate</h3>
				</a>
				<p>For Widevine, you very often need to provide the VO Player with the server certificate to use to properly secure the DRM license exchanges.</p>
				<p>This is even <strong>mandatory</strong> for Fairplay DRM on Safari MacOS.</p>
				<p>You can provide the server certificate:</p>
				<ul>
					<li>directly in data format using <code>config.drm.advanced[&#39;&lt;DRM_KEY&gt;&#39;].serverCertificate = certificateData;</code> with <code>serverCertificate</code> in <code>Uint8Array</code> format,</li>
					<li>or using a URL from which the player will download the certificate using <code>config.drm.advanced[&#39;&lt;DRM_KEY&gt;&#39;].serverCertificateUrl = certificateUrl;</code> with <code>serverCertificate</code> in <code>string</code> format.</li>
				</ul>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="drm-config.html">DRM configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="license-wrapping.html">DRM <wbr>License <wbr>Wrapping</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="drm-persistent-license.html">DRM <wbr>Persistent <wbr>License</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>