<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DRM Persistent License | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>DRM Persistent License</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../playback-drm.html">Playback of DRM protected streams</a>
				</li>
				<li>
					<a href="drm-persistent-license.html">DRM Persistent License</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#support-of-drm-persistent-license" id="support-of-drm-persistent-license" style="color: inherit; text-decoration: none;">
					<h2>Support of DRM persistent license</h2>
				</a>
				<p>If the license server allows, it is possible to query a persistent DRM license. Persistent license are required in use cases such as: playing content offline or minimizing calls to the license backend server. The player supports license persistency for both Widevine and Playready.</p>
				<p>Enabling a persistent license for a media is performed in three steps</p>
				<ul>
					<li>First DRM license acquisition,</li>
					<li>Associate DRM metadata received from the player to the media,</li>
					<li>Enabling persistency on subsequent playback of the media.</li>
				</ul>
				<a href="#first-drm-license-acquisition" id="first-drm-license-acquisition" style="color: inherit; text-decoration: none;">
					<h3>First DRM license acquisition</h3>
				</a>
				<p>In this first step, querying for a persistent license should be set when configuring the DRM parameters of the media on its first playback.</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">drmAgentType:media</span><span style="color: #000000">.</span><span style="color: #001080">drmAgentType</span><span style="color: #000000">, </span><span style="color: #008000">//widevine or playready</span>
<span style="color: #000000">      </span><span style="color: #001080">servers:</span><span style="color: #000000">  {</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/widevine&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.microsoft.playready&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/playready&#039;</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">advanced:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">persistentStateRequired:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.microsoft.playready&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">persistentStateRequired:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">      }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">})</span>
</code></pre>
				<a href="#associate-drm-metadata-with-media-for-future-playback" id="associate-drm-metadata-with-media-for-future-playback" style="color: inherit; text-decoration: none;">
					<h3>Associate DRM metadata with media for future playback</h3>
				</a>
				<p>Then the application should listen to the Player event <code>drmSessionCreated</code> to verify that the session is persistent (property <code>sessionType</code> being equal to <code>persistent-license</code>).
				The application shall then retrieve the <code>sessionId</code> and the <code>expirationTime</code> of the license and associate them with the played media.</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;drmSessionCreated&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000"> (</span><span style="color: #001080">e</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">theMedia</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (!</span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">drmSessionIds</span><span style="color: #000000">) {</span>
<span style="color: #000000">        </span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">drmSessionIds</span><span style="color: #000000"> = [</span><span style="color: #001080">e</span><span style="color: #000000">.</span><span style="color: #001080">sessionId</span><span style="color: #000000">]; </span><span style="color: #008000">// shall be saved as an array of sessions</span>
<span style="color: #000000">        </span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">expirationTime</span><span style="color: #000000"> = </span><span style="color: #001080">e</span><span style="color: #000000">.</span><span style="color: #001080">expirationTime</span><span style="color: #000000">; </span><span style="color: #008000">// The expiration date as epoch timecode</span>
<span style="color: #000000">        </span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">sessionType</span><span style="color: #000000"> = </span><span style="color: #001080">e</span><span style="color: #000000">.</span><span style="color: #001080">sessionType</span><span style="color: #000000">; </span><span style="color: #008000">// &#039;temporary&#039; or &#039;persistent-license&#039;</span>
<span style="color: #000000">      }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  })</span>
</code></pre>
				<a href="#subsequent-playback-of-media" id="subsequent-playback-of-media" style="color: inherit; text-decoration: none;">
					<h3>Subsequent playback of media</h3>
				</a>
				<p>In order to activate the persistency of the license for subsequent playbacks, the sessionId should be provided when configuring the DRM parameters of the media to play.</p>
				<pre><code class="language-js"><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">  </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">drmAgentType:media</span><span style="color: #000000">.</span><span style="color: #001080">drmAgentType</span><span style="color: #000000">, </span><span style="color: #008000">//widevine or playready</span>
<span style="color: #000000">      </span><span style="color: #001080">servers:</span><span style="color: #000000">  {</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/widevine&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.microsoft.playready&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;https://foo.bar/drm/playready&#039;</span>
<span style="color: #000000">      },</span>
<span style="color: #000000">      </span><span style="color: #001080">advanced:</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">persistentStateRequired:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">sessionIds:</span><span style="color: #000000"> </span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">drmSessionIds</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #A31515">&#039;com.microsoft.playready&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> {</span>
<span style="color: #000000">          </span><span style="color: #001080">persistentStateRequired:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000">,</span>
<span style="color: #000000">          </span><span style="color: #001080">sessionIds:</span><span style="color: #000000"> </span><span style="color: #001080">theMedia</span><span style="color: #000000">.</span><span style="color: #001080">drmSessionIds</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">      }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">})</span>
</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="drm-config.html">DRM configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="license-wrapping.html">DRM <wbr>License <wbr>Wrapping</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="drm-persistent-license.html">DRM <wbr>Persistent <wbr>License</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>