<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>DRM License Wrapping | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>DRM License Wrapping</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../playback-drm.html">Playback of DRM protected streams</a>
				</li>
				<li>
					<a href="license-wrapping.html">DRM License Wrapping</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>Applications sometimes need to communicate extra information to or from its
					license server or use a specific format to do so.
					EME, the API that browsers provide for DRM, does not offer a
					direct way to include extra information in a license request or extract extra
					information from a license response. To pass extra information, applications
				must &quot;wrap&quot; requests and &quot;unwrap&quot; responses as they pass through JavaScript.</p>
				<p><em>Please note that the license server we are using in this tutorial is a Widevine
				license server, so you will need to use Chrome to follow along.</em></p>
				<a href="#wrapping-license-requests" id="wrapping-license-requests" style="color: inherit; text-decoration: none;">
					<h2>Wrapping License Requests</h2>
				</a>
				<p>If your application needs to communicate complex information to the license server along with the request or use a non-standard way of formating the resquest or response, the solution is to &quot;wrap&quot; the platform&#39;s license request and &quot;unwrap&quot; it at the license server.
				In Viaccess-Orca Player, this is accomplished with a network request filter.</p>
				<p>In practice, you may wrap the request in any format that can be constructed on
					the client and parsed on the server.  For simplicity, the server used in this
				tutorial expects a JSON format that looks like this:</p>
				<pre><code class="language-json"><span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0451A5">&quot;rawLicenseRequestBase64&quot;</span><span style="color: #000000">:</span>
<span style="color: #000000">      </span><span style="color: #A31515">&quot;VGhlIHJhdyBsaWNlbnNlIHJlcXVlc3QgZ2VuZXJhdGVkIGJ5IHRoZSBDRE0=&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #CD3131">...</span>
<span style="color: #000000">}</span>
</code></pre>
				<p>To start, we&#39;re going to use the code from {@page Basic usage}, but use this
				manifest and license server:</p>
				<pre><code class="language-js"><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">manifestUri</span><span style="color: #000000"> =</span>
<span style="color: #000000">    </span><span style="color: #A31515">&#039;//storage.googleapis.com/shaka-demo-assets/sintel-widevine/dash.mpd&#039;</span><span style="color: #000000">;</span>
<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">licenseServer</span><span style="color: #000000"> = </span><span style="color: #A31515">&#039;//cwip-shaka-proxy.appspot.com/wrapped_request&#039;</span><span style="color: #000000">;</span>
</code></pre>
				<p>We&#39;ll also need to configure the player to use this license server before it
				loads the manifest:</p>
				<pre><code class="language-js"><span style="color: #000000">  </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">({</span>
<span style="color: #000000">    </span><span style="color: #001080">drm:</span><span style="color: #000000"> {</span>
<span style="color: #000000">      </span><span style="color: #001080">servers:</span><span style="color: #000000"> { </span><span style="color: #A31515">&#039;com.widevine.alpha&#039;</span><span style="color: #001080">:</span><span style="color: #000000"> </span><span style="color: #001080">licenseServer</span><span style="color: #000000"> }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  });</span>

<span style="color: #000000">  </span><span style="color: #008000">// Try to load a manifest.</span>
<span style="color: #000000">  </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #001080">manifestUri</span><span style="color: #000000">).</span><span style="color: #795E26">then</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>
<span style="color: #000000">    </span><span style="color: #008000">// The video should now be playing!</span>
<span style="color: #000000">  }).</span><span style="color: #795E26">catch</span><span style="color: #000000">(</span><span style="color: #001080">onError</span><span style="color: #000000">);</span>
</code></pre>
				<p>This license server is expecting a wrapped request, so if we try to play now, we
					will see <code>Error code 6007</code>, which means <code>LICENSE_REQUEST_FAILED</code>.  To wrap the
				license request, we must register a request filter:</p>
				<pre><code class="language-js"><span style="color: #000000">  </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">registerLicenseRequestFilter</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">(</span><span style="color: #001080">request</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// This is the raw license request generated by the Widevine CDM.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">rawLicenseRequest</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Uint8Array</span><span style="color: #000000">(</span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">body</span><span style="color: #000000">);</span>

<span style="color: #000000">    </span><span style="color: #008000">// Create the wrapped request structure.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wrapped</span><span style="color: #000000"> = {};</span>

<span style="color: #000000">    </span><span style="color: #008000">// Encode the raw license request in base64.</span>
<span style="color: #000000">    </span><span style="color: #008000">// The server we are using in this tutorial expects this field and this</span>
<span style="color: #000000">    </span><span style="color: #008000">// encoding for the raw request.</span>
<span style="color: #000000">    </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">rawLicenseRequestBase64</span><span style="color: #000000"> =</span>
<span style="color: #000000">        </span><span style="color: #795E26">btoa</span><span style="color: #000000">(</span><span style="color: #267F99">String</span><span style="color: #000000">.</span><span style="color: #001080">fromCharCode</span><span style="color: #000000">.</span><span style="color: #795E26">apply</span><span style="color: #000000">(</span><span style="color: #0000FF">null</span><span style="color: #000000">, </span><span style="color: #001080">rawLicenseRequest</span><span style="color: #000000">));</span>

<span style="color: #000000">    </span><span style="color: #008000">// Add whatever else we want to communicate to the server.</span>
<span style="color: #000000">    </span><span style="color: #008000">// None of these values are read by the server we are using in this</span>
<span style="color: #000000">    </span><span style="color: #008000">// tutorial.</span>
<span style="color: #000000">    </span><span style="color: #008000">// In practice, you would send what the server needs and the server would</span>
<span style="color: #000000">    </span><span style="color: #008000">// react to it.</span>
<span style="color: #000000">    </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">favoriteColor</span><span style="color: #000000"> = </span><span style="color: #A31515">&#039;blue&#039;</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">Beatles</span><span style="color: #000000"> = [</span><span style="color: #A31515">&#039;John&#039;</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;Paul&#039;</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;George&#039;</span><span style="color: #000000">, </span><span style="color: #A31515">&#039;Ringo&#039;</span><span style="color: #000000">];</span>
<span style="color: #000000">    </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">bestBeatleIndex</span><span style="color: #000000"> = </span><span style="color: #098658">1</span><span style="color: #000000">;  </span><span style="color: #008000">// Paul, of course.</span>
<span style="color: #000000">    </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">pEqualsNP</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;  </span><span style="color: #008000">// maybe?</span>

<span style="color: #000000">    </span><span style="color: #008000">// Encode the wrapped request as JSON.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wrappedJson</span><span style="color: #000000"> = </span><span style="color: #267F99">JSON</span><span style="color: #000000">.</span><span style="color: #795E26">stringify</span><span style="color: #000000">(</span><span style="color: #001080">wrapped</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #008000">// Convert the JSON string back into a Uint8Array to replace the request</span>
<span style="color: #000000">    </span><span style="color: #008000">// body.</span>
<span style="color: #000000">    </span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">body</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Uint8Array</span><span style="color: #000000">(</span><span style="color: #001080">wrappedJson</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">for</span><span style="color: #000000"> (</span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">i</span><span style="color: #000000"> = </span><span style="color: #098658">0</span><span style="color: #000000">; </span><span style="color: #001080">i</span><span style="color: #000000"> &lt; </span><span style="color: #001080">wrappedJson</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000">; ++</span><span style="color: #001080">i</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">body</span><span style="color: #000000">[</span><span style="color: #001080">i</span><span style="color: #000000">] = </span><span style="color: #001080">wrappedJson</span><span style="color: #000000">.</span><span style="color: #795E26">charCodeAt</span><span style="color: #000000">(</span><span style="color: #001080">i</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">  });</span>
</code></pre>
				<p>Load the page again, and the license request will succeed.</p>
				<a href="#wrapping-license-responses" id="wrapping-license-responses" style="color: inherit; text-decoration: none;">
					<h2>Wrapping License Responses</h2>
				</a>
				<p>If your license server needs to communicate complex information back to the
					application, the solution is very similar to what we just did above.  We can
					&quot;wrap&quot; the license itself in the server and &quot;unwrap&quot; it in the client.  In
				Viaccess-Orca Player, this is accomplished with a network response filter.</p>
				<p>Similar to the guideline for license requests, you may wrap the response in any
					format that can be constructed on the server and parsed by the client.  We will
					again use JSON for simplicity.  The server will send a response with a format
				that looks like this:</p>
				<pre><code class="language-json"><span style="color: #000000">{</span>
<span style="color: #000000">  </span><span style="color: #0451A5">&quot;rawLicenseBase64&quot;</span><span style="color: #000000">:</span>
<span style="color: #000000">      </span><span style="color: #A31515">&quot;VGhlIHJhdyBsaWNlbnNlIGZyb20gdGhlIGxpY2Vuc2Ugc2VydmVyIGJhY2tlbmQ=&quot;</span><span style="color: #000000">,</span>
<span style="color: #000000">  </span><span style="color: #CD3131">...</span>
<span style="color: #000000">}</span>
</code></pre>
				<p>Change the license server to:</p>
				<pre><code class="language-js"><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">licenseServer</span><span style="color: #000000"> =</span>
<span style="color: #000000">    </span><span style="color: #A31515">&#039;//cwip-shaka-proxy.appspot.com/wrapped_request_and_response&#039;</span><span style="color: #000000">;</span>
</code></pre>
				<p>This license server is sending a wrapped response, so if we try to play now, we
					will see <code>Error code 6008</code>, which means <code>LICENSE_RESPONSE_REJECTED</code>.  The
					Widevine CDM does not understand this wrapped format, so we must unwrap it first
				using a request filter:</p>
				<pre><code class="language-js"><span style="color: #000000">  </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">registerLicenseResponseFilter</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">(</span><span style="color: #001080">response</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// This is the wrapped license.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wrappedArray</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Uint8Array</span><span style="color: #000000">(</span><span style="color: #001080">response</span><span style="color: #000000">.</span><span style="color: #001080">data</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #008000">// Convert it to a string.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wrappedString</span><span style="color: #000000"> = </span><span style="color: #267F99">String</span><span style="color: #000000">.</span><span style="color: #001080">fromCharCode</span><span style="color: #000000">.</span><span style="color: #795E26">apply</span><span style="color: #000000">(</span><span style="color: #0000FF">null</span><span style="color: #000000">, </span><span style="color: #001080">wrappedArray</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #008000">// Parse the JSON string into an object.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wrapped</span><span style="color: #000000"> = </span><span style="color: #267F99">JSON</span><span style="color: #000000">.</span><span style="color: #795E26">parse</span><span style="color: #000000">(</span><span style="color: #001080">wrappedString</span><span style="color: #000000">);</span>

<span style="color: #000000">    </span><span style="color: #008000">// This is a base64-encoded version of the raw license.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">rawLicenseBase64</span><span style="color: #000000"> = </span><span style="color: #001080">wrapped</span><span style="color: #000000">.</span><span style="color: #001080">rawLicenseBase64</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">// Decode it to a string.</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">rawLicenseString</span><span style="color: #000000"> = </span><span style="color: #795E26">atob</span><span style="color: #000000">(</span><span style="color: #001080">rawLicenseBase64</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #008000">// Convert that string into a Uint8Array and replace the response data</span>
<span style="color: #000000">    </span><span style="color: #008000">// to feed it to the Widevine CDM.</span>
<span style="color: #000000">    </span><span style="color: #001080">response</span><span style="color: #000000">.</span><span style="color: #001080">data</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Uint8Array</span><span style="color: #000000">(</span><span style="color: #001080">rawLicenseString</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">for</span><span style="color: #000000"> (</span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">i</span><span style="color: #000000"> = </span><span style="color: #098658">0</span><span style="color: #000000">; </span><span style="color: #001080">i</span><span style="color: #000000"> &lt; </span><span style="color: #001080">rawLicenseString</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000">; ++</span><span style="color: #001080">i</span><span style="color: #000000">) {</span>
<span style="color: #000000">      </span><span style="color: #001080">response</span><span style="color: #000000">.</span><span style="color: #001080">data</span><span style="color: #000000">[</span><span style="color: #001080">i</span><span style="color: #000000">] = </span><span style="color: #001080">rawLicenseString</span><span style="color: #000000">.</span><span style="color: #795E26">charCodeAt</span><span style="color: #000000">(</span><span style="color: #001080">i</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>

<span style="color: #000000">    </span><span style="color: #008000">// Read additional fields from the server.</span>
<span style="color: #000000">    </span><span style="color: #008000">// The server we are using in this tutorial does not send anything useful.</span>
<span style="color: #000000">    </span><span style="color: #008000">// In practice, you could send any license metadata the client might need.</span>
<span style="color: #000000">    </span><span style="color: #008000">// Here we log what the server sent to the JavaScript console for</span>
<span style="color: #000000">    </span><span style="color: #008000">// inspection.</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">wrapped</span><span style="color: #000000">);</span>
<span style="color: #000000">  });</span>
</code></pre>
				<p>Load the page again, and the license response will be accepted by the Widevine
				CDM.  Open the JavaScript console to see what the server sent back.</p>
				<a href="#fairplay-specific-usecase" id="fairplay-specific-usecase" style="color: inherit; text-decoration: none;">
					<h2>Fairplay specific usecase</h2>
				</a>
				<p>As Fairplay does not specify any <em>default</em> format for its license exchanges, it is more than likely that the default format used by the VO Player to send and parse the DRM license exchanges will not fit your integration.</p>
				<p>For Fairplay, the VO Player expects the final license response data to be only the raw license data formated in base64.</p>
				<p>Example of request and response filters targetting VO Licenser:</p>
				<pre><code class="language-javascript">
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">fairplayVOLicenseRequestFilter</span><span style="color: #000000">(</span><span style="color: #001080">request</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// process only fairplay licenses, all others will not be touched</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">drm</span><span style="color: #000000"> == </span><span style="color: #A31515">&#039;com.apple.fps&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;fairplayVOLicenseRequestFilter START&#039;</span><span style="color: #000000">);</span>

<span style="color: #000000">        </span><span style="color: #008000">// request body is already base-64 encoded</span>
<span style="color: #000000">        </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">newBody</span><span style="color: #000000"> = </span><span style="color: #A31515">&#039;licenserequest=&#039;</span><span style="color: #000000"> + </span><span style="color: #795E26">encodeURIComponent</span><span style="color: #000000">(</span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">body</span><span style="color: #000000">);</span>

<span style="color: #000000">        </span><span style="color: #008000">// here we retrieve an additional parameter from the test page (token) to be included in the license request</span>
<span style="color: #000000">        </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">drmAddParamElem</span><span style="color: #000000"> = </span><span style="color: #001080">document</span><span style="color: #000000">.</span><span style="color: #795E26">getElementById</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;drmReqAddtionalParam&#039;</span><span style="color: #000000">).</span><span style="color: #001080">value</span><span style="color: #000000">.</span><span style="color: #795E26">trim</span><span style="color: #000000">();</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">drmAddParamElem</span><span style="color: #000000">.</span><span style="color: #001080">length</span><span style="color: #000000"> &gt; </span><span style="color: #098658">0</span><span style="color: #000000">) {</span>
<span style="color: #000000">            </span><span style="color: #001080">newBody</span><span style="color: #000000"> +=  </span><span style="color: #A31515">&#039;&amp;&#039;</span><span style="color: #000000"> + </span><span style="color: #001080">drmAddParamElem</span><span style="color: #000000">;</span>
<span style="color: #000000">        }</span>

<span style="color: #000000">        </span><span style="color: #008000">// replace request body with newly crafted one</span>
<span style="color: #000000">        </span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">body</span><span style="color: #000000"> = </span><span style="color: #001080">newBody</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #008000">// add the content-type header matching the new body format</span>
<span style="color: #000000">        </span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">headers</span><span style="color: #000000">[</span><span style="color: #A31515">&#039;content-type&#039;</span><span style="color: #000000">] = </span><span style="color: #A31515">&#039;application/x-www-form-urlencoded&#039;</span><span style="color: #000000">;</span>

<span style="color: #000000">        </span><span style="color: #001080">request</span><span style="color: #000000">.</span><span style="color: #001080">allowCrossSiteCredentials</span><span style="color: #000000"> = </span><span style="color: #0000FF">true</span><span style="color: #000000">;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">}</span>

<span style="color: #008000">// Filter used to process license responses coming for VO Licenser</span>
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">fairplayLicenseResponseFilter</span><span style="color: #000000">(</span><span style="color: #001080">response</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">keyText</span><span style="color: #000000"> = </span><span style="color: #001080">response</span><span style="color: #000000">.</span><span style="color: #001080">data</span><span style="color: #000000">.</span><span style="color: #795E26">trim</span><span style="color: #000000">();</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">substr</span><span style="color: #000000">(</span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">5</span><span style="color: #000000">) === </span><span style="color: #A31515">&#039;&lt;ckc&gt;&#039;</span><span style="color: #000000"> &amp;&amp; </span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">substr</span><span style="color: #000000">(-</span><span style="color: #098658">6</span><span style="color: #000000">) === </span><span style="color: #A31515">&#039;&lt;/ckc&gt;&#039;</span><span style="color: #000000">) {</span>
<span style="color: #000000">        </span><span style="color: #001080">keyText</span><span style="color: #000000"> = </span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">slice</span><span style="color: #000000">(</span><span style="color: #098658">5</span><span style="color: #000000">, -</span><span style="color: #098658">6</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">indexOf</span><span style="color: #000000"> (</span><span style="color: #A31515">&#039;&lt;FAIRPLAY_RESPONSE&gt;&#039;</span><span style="color: #000000">));</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000"> (</span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">indexOf</span><span style="color: #000000"> (</span><span style="color: #A31515">&#039;&lt;FAIRPLAY_RESPONSE&gt;&#039;</span><span style="color: #000000">) &gt;= </span><span style="color: #098658">0</span><span style="color: #000000">) {</span>
<span style="color: #000000">        </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">esidx</span><span style="color: #000000"> = </span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">search</span><span style="color: #000000">(</span><span style="color: #A31515">&quot;&lt;/LICENSE&gt;&quot;</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #001080">keyText</span><span style="color: #000000"> = </span><span style="color: #001080">keyText</span><span style="color: #000000">.</span><span style="color: #795E26">slice</span><span style="color: #000000">(</span><span style="color: #098658">28</span><span style="color: #000000">, </span><span style="color: #001080">esidx</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #001080">keyText</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">    </span><span style="color: #008000">// here keyText contains only the raw license data in base64</span>
<span style="color: #000000">    </span><span style="color: #001080">response</span><span style="color: #000000">.</span><span style="color: #001080">data</span><span style="color: #000000"> = </span><span style="color: #001080">keyText</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>
</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="drm-config.html">DRM configuration</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="license-wrapping.html">DRM <wbr>License <wbr>Wrapping</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="drm-persistent-license.html">DRM <wbr>Persistent <wbr>License</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>