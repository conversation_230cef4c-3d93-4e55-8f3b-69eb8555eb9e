<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>VR / Video 360° | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>VR / Video 360°</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="virtual-reality.html">VR / Video 360°</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>Support for monoscopic and stereoscopic 360° and 180° videos comes as an extension to VO Player for Web.</p>
				<a href="#inlcude-the-extention-in-the-web-page" id="inlcude-the-extention-in-the-web-page" style="color: inherit; text-decoration: none;">
					<h2>Inlcude the extention in the web page</h2>
				</a>
				<p>To make use of the extension, the dedicated library file <em>voplayer-vrext.min.js</em> must be included <strong>after</strong> the core library (<em>voplayer.min.js</em>) in the page, before creating the player object.</p>
				<pre><code class="language-html"><span style="color: #800000">&lt;!DOCTYPE</span><span style="color: #000000"> </span><span style="color: #FF0000">html</span><span style="color: #800000">&gt;</span>
<span style="color: #800000">&lt;head&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;meta</span><span style="color: #000000"> </span><span style="color: #FF0000">http-equiv</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;content-type&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">content</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;text/html; charset=UTF-8&quot;</span><span style="color: #800000">&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;title&gt;</span><span style="color: #000000">VO Player for Web (HTML5) Demo</span><span style="color: #800000">&lt;/title&gt;</span>

<span style="color: #000000">  </span><span style="color: #800000">&lt;link</span><span style="color: #000000"> </span><span style="color: #FF0000">rel</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;stylesheet&quot;</span><span style="color: #000000"> </span><span style="color: #FF0000">href</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;html5player/css/voplayer.css&quot;</span><span style="color: #800000">&gt;</span>

<span style="color: #000000">  </span><span style="color: #008000">&lt;!-- include webxr-polyfill library (see Dependency paragraph below in the tutorial) --&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&#039;https://cdn.jsdelivr.net/npm/webxr-polyfill@latest/build/webxr-polyfill.js&#039;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #000000">  </span><span style="color: #008000">&lt;!-- include the VO Player library --&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>
<span style="color: #000000">  </span><span style="color: #008000">&lt;!-- include the VR extension for VO Player (must be loaded AFTER the core library) --&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer-vrext.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>

<span style="color: #800000">&lt;/head&gt;</span>
<span style="color: #800000">&lt;body&gt;</span>
</code></pre>
				<a href="#dependency" id="dependency" style="color: inherit; text-decoration: none;">
					<h3>Dependency</h3>
				</a>
				<p>For browsers that do not support <code>navigator.xr</code> (see <a href="https://developer.mozilla.org/en-US/docs/Web/API/Navigator/xr">https://developer.mozilla.org/en-US/docs/Web/API/Navigator/xr</a> for supporting browser), you will need to add the following dependency to your web page: <code>&lt;script src=&#39;https://cdn.jsdelivr.net/npm/webxr-polyfill@latest/build/webxr-polyfill.js&#39;&gt;&lt;/script&gt;</code></p>
				<p>The example provided in the release package showcases such an integration.</p>
				<a href="#extension-use" id="extension-use" style="color: inherit; text-decoration: none;">
					<h2>Extension use</h2>
				</a>
				<p>All functions and properties related to the control of VR/360 video are available from the instance of <code>VRController</code> made exposed by the player as <code>player.vrController</code>.</p>
				<a href="#feature-availability" id="feature-availability" style="color: inherit; text-decoration: none;">
					<h3>Feature availability</h3>
				</a>
				<p>When the VR/360 video extension is not avaiable or not properly loaded, the <code>player.vrController</code> property is <code>undefined</code>.</p>
				<a href="#vr-rendering-mode" id="vr-rendering-mode" style="color: inherit; text-decoration: none;">
					<h3>VR rendering mode</h3>
				</a>
				<p>Rendering may be performed in VR headset such as Occulus Rift, Pico G2 4K or within the browser itself.</p>
				<a href="#setting-things-up" id="setting-things-up" style="color: inherit; text-decoration: none;">
					<h3>Setting things up</h3>
				</a>
				<p>Because stereoscopic videos can come in several ways, there are three properties that you shall set before calling <code>player.vrController.prepareVR()</code> :</p>
				<ul>
					<li><code>is180</code> : set to true if your video is 180. Set to false if it is 360.</li>
					<li><code>isHorizontalSplit</code> : set to true if your video is split horizontally (top-down) with the left eye at the top. Set to false if it is vertically split (left-right) with the left eye at the left.</li>
					<li><code>isMonoscopic</code> : set to true if your video isn&#39;t stereoscopic. When set to true, <code>isHorizontalSplit</code> has no effect but <code>is180</code> still does.</li>
				</ul>
				<a href="#rendering-setup" id="rendering-setup" style="color: inherit; text-decoration: none;">
					<h3>Rendering setup</h3>
				</a>
				<p>In order to render a VR content within, you will need to call <code>player.vrController.prepareVR()</code>.
				You shall call <code>player.vrController.prepareVR()</code> before loading the media.</p>
				<a href="#rendering-within-vr-headset" id="rendering-within-vr-headset" style="color: inherit; text-decoration: none;">
					<h3>Rendering within VR headset</h3>
				</a>
				<p>Switching from brower rendering to headset rendering is performed calling <code>player.vrController.enterVR()</code>.
					To exit headset rendering and get back to brower rendering you shall call  <code>player.vrController.exitVR()</code>.
				<code>player.vrController.switchVR()</code> also is available in order to toggle between browser rendering and headset rendering.</p>
				<p>Note that <code>enterVR</code>, <code>exitVR</code> and <code>switchVR</code> functions can only be called from trusted events (i.e: events that are triggered by user interaction).</p>
				<a href="#playback-control" id="playback-control" style="color: inherit; text-decoration: none;">
					<h3>Playback Control</h3>
				</a>
				<p>Simply control playback as you usually do with the player&#39;s API.</p>
				<a href="#integration-example" id="integration-example" style="color: inherit; text-decoration: none;">
					<h2>Integration example</h2>
				</a>
				<pre><code class="language-javascript">
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">onError</span><span style="color: #000000">(</span><span style="color: #001080">error</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">error</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Player error: &#039;</span><span style="color: #000000"> , </span><span style="color: #001080">error</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">startPlayback</span><span style="color: #000000">(</span><span style="color: #001080">media</span><span style="color: #000000">) {</span>

<span style="color: #000000">    </span><span style="color: #008000">// player has alreay created</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">reset</span><span style="color: #000000">().</span><span style="color: #795E26">then</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>

<span style="color: #000000">        </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Loading new media&#039;</span><span style="color: #000000">, </span><span style="color: #001080">media</span><span style="color: #000000">);</span>

<span style="color: #000000">        </span><span style="color: #008000">//apply player configuration if any</span>
<span style="color: #000000">        </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>

<span style="color: #000000">        </span><span style="color: #008000">//apply VR360 params from stream if any</span>
<span style="color: #000000">        </span><span style="color: #795E26">applyVR360Params</span><span style="color: #000000">(</span><span style="color: #001080">media</span><span style="color: #000000">);</span>

<span style="color: #000000">        </span><span style="color: #008000">// Try to load a manifest</span>
<span style="color: #000000">        </span><span style="color: #008000">// This is an asynchronous process, returning a Promise</span>
<span style="color: #000000">        </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #001080">media</span><span style="color: #000000">.</span><span style="color: #001080">url</span><span style="color: #000000">).</span><span style="color: #795E26">then</span><span style="color: #000000">(</span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>

<span style="color: #000000">            </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Player loaded &gt; start playback&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">play</span><span style="color: #000000">();</span>
<span style="color: #000000">        }).</span><span style="color: #795E26">catch</span><span style="color: #000000">(</span><span style="color: #001080">onError</span><span style="color: #000000">);  </span><span style="color: #008000">// onError is executed if the asynchronous load fails</span>
<span style="color: #000000">    });</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">applyVR360Params</span><span style="color: #000000">(</span><span style="color: #001080">stream</span><span style="color: #000000">) {</span>
<span style="color: #000000">    </span><span style="color: #008000">// only start VR/360 mode if the stream is one</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">stream</span><span style="color: #000000">.</span><span style="color: #0070C1">VR360</span><span style="color: #000000">) {</span>
<span style="color: #000000">        </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #795E26">prepareVR</span><span style="color: #000000">();</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">stream</span><span style="color: #000000">.</span><span style="color: #001080">VR_isMonoscopic</span><span style="color: #000000">) {</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">isMonoscopic</span><span style="color: #000000"> = </span><span style="color: #0000FF">true</span><span style="color: #000000">;</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">isMonoscopic</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;</span>
<span style="color: #000000">            </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">stream</span><span style="color: #000000">.</span><span style="color: #001080">VR_isHorizontalSplit</span><span style="color: #000000">) {</span>
<span style="color: #000000">                </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">isHorizontalSplit</span><span style="color: #000000"> = </span><span style="color: #0000FF">true</span><span style="color: #000000">;</span>
<span style="color: #000000">            }</span>
<span style="color: #000000">            </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">                </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">isHorizontalSplit</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;</span>
<span style="color: #000000">            }</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">if</span><span style="color: #000000">(</span><span style="color: #001080">stream</span><span style="color: #000000">.</span><span style="color: #001080">VR_is180</span><span style="color: #000000">) {</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">is180</span><span style="color: #000000"> = </span><span style="color: #0000FF">true</span><span style="color: #000000">;</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">        </span><span style="color: #AF00DB">else</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">vrController</span><span style="color: #000000">.</span><span style="color: #001080">is180</span><span style="color: #000000"> = </span><span style="color: #0000FF">false</span><span style="color: #000000">;</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">}</span>

</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>