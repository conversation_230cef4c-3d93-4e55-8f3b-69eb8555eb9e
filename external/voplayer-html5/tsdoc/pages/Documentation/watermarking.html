<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Viaccess-Orca Watermarking | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../assets/css/main.css">
	<link rel="stylesheet" href="../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../assets/js/search.json" data-base="../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Viaccess-Orca Watermarking</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../modules.html">Globals</a>
				</li>
				<li>
					<a href="basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="watermarking.html">Viaccess-Orca Watermarking</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>The VO Player for Web supports the watermarking feature as defined by Viaccess-Orca.</p>
				<a href="#watermarking-configuration" id="watermarking-configuration" style="color: inherit; text-decoration: none;">
					<h2>Watermarking configuration</h2>
				</a>
				<p>To make use of the watermarking feature, you shall configure it using the <code>player.configure()</code> API before loading the media URL.</p>
				<p>The parameters to use in the configuration object are under root key <code>vowmk</code>:</p>
				<ul>
					<li><code>url</code>: the backend URL the player will call to retrieve the watermarking information (in production environment, this URL should be a signed one)</li>
					<li><code>x-api-key</code>: the Amazon API key to use as request header when retrieving the watermarking information</li>
					<li><code>updateUrlCb</code>: a callback function the player will call to get a new backend URL after the current watermarking information has expired. This function must return a Promise that resolves with the new URL to use.</li>
				</ul>
				<p><strong>NOTE:</strong> The watermarking feature is not included in the default release version of the VO Player for Web and must be delivered separately. Please contact Viaccess-Orca for more details.</p>
				<a href="#content-id" id="content-id" style="color: inherit; text-decoration: none;">
					<h2>Content ID</h2>
				</a>
				<p>The VO Watermarking feature enables applying the mark received only on selected content.</p>
				<p>The content ID used to match the media being played to the list received from the watermarking backend is extracted form the stream&#39;s URL.</p>
				<p>The VO Player recognizes the query parameter <em>widvo</em> as the content ID to be used.</p>
				<a href="#example" id="example" style="color: inherit; text-decoration: none;">
					<h2>Example</h2>
				</a>
				<pre><code class="language-javascript">
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">vowmkBackendURL</span><span style="color: #000000"> = </span><span style="color: #A31515">&#039;https://dummy-vowmk.viaccess-orca.com/api/v1/getwmk&#039;</span><span style="color: #000000">;</span>
<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">wmkXAPIKey</span><span style="color: #000000"> = </span><span style="color: #A31515">&#039;dummy-x-api-key-for-watermarking-API&#039;</span><span style="color: #000000">;</span>
<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">wmkCount</span><span style="color: #000000"> = </span><span style="color: #098658">0</span><span style="color: #000000">;</span>

<span style="color: #008000">// this function is called each time the player needs to refresh the watermarking</span>
<span style="color: #008000">// it must return a Promise which resolves with the updated watermarking backend URL.</span>
<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">getUpdatedWMKUrl</span><span style="color: #000000">() {</span>
<span style="color: #000000">    </span><span style="color: #001080">wmkCount</span><span style="color: #000000">++;</span>
<span style="color: #000000">    </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">newUrl</span><span style="color: #000000"> = </span><span style="color: #001080">vowmkBackendURL</span><span style="color: #000000"> + </span><span style="color: #A31515">&#039;?count=&#039;</span><span style="color: #000000"> + </span><span style="color: #001080">wmkCount</span><span style="color: #000000">;</span>

<span style="color: #000000">    </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;getUpdatedWMKUrl &gt; &#039;</span><span style="color: #000000"> + </span><span style="color: #001080">newUrl</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">updatePromise</span><span style="color: #000000"> = </span><span style="color: #0000FF">new</span><span style="color: #000000"> </span><span style="color: #267F99">Promise</span><span style="color: #000000">((</span><span style="color: #001080">resolve</span><span style="color: #000000">, </span><span style="color: #001080">reject</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #795E26">setTimeout</span><span style="color: #000000">( () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> </span><span style="color: #795E26">resolve</span><span style="color: #000000">(</span><span style="color: #001080">newUrl</span><span style="color: #000000">), </span><span style="color: #098658">150</span><span style="color: #000000">);</span>
<span style="color: #000000">    });</span>
<span style="color: #000000">    </span><span style="color: #AF00DB">return</span><span style="color: #000000"> </span><span style="color: #001080">updatePromise</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">startPlaybackWithWatermarking</span><span style="color: #000000">() {</span>

<span style="color: #000000">    </span><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #0070C1">playerConfiguration</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">vowmk:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">url:</span><span style="color: #000000"> </span><span style="color: #001080">vowmkBackendURL</span><span style="color: #000000"> + </span><span style="color: #A31515">&#039;?count=0&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">updateUrlCb:</span><span style="color: #000000"> </span><span style="color: #001080">getUpdatedWMKUrl</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #A31515">&#039;x-api-key&#039;</span><span style="color: #001080">:wmkXAPIKey</span><span style="color: #000000">,</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    }</span>

<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">reset</span><span style="color: #000000">().</span><span style="color: #795E26">then</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">        </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">configure</span><span style="color: #000000">(</span><span style="color: #001080">playerConfiguration</span><span style="color: #000000">);</span>
<span style="color: #000000">        </span><span style="color: #008000">// The stream URL contains the &#039;widvo&#039; query parameter that will be used as content ID by the watermarking agent</span>
<span style="color: #000000">        </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">load</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;https://dummy-vo-domain.viaccess-orca.com/content/cid0/manifest.mpd?widvo=cid0&#039;</span><span style="color: #000000">).</span><span style="color: #795E26">then</span><span style="color: #000000">(() </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">play</span><span style="color: #000000">();</span>
<span style="color: #000000">        });</span>
<span style="color: #000000">    });</span>
<span style="color: #000000">}</span>

</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class="current pp-nav pp-page">
						<a href="watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>