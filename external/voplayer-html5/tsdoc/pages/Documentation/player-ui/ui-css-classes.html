<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Player UI default CSS | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Player UI default CSS</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../player-ui.html">Player integration with Viaccess-Orca user Interface</a>
				</li>
				<li>
					<a href="ui-css-classes.html">Player UI default CSS</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>Below is a copy of the default CSS delivered by Viaccess-Orca to style the UI of the player controls.</p>
				<pre><code class="language-css"><span style="color: #AF00DB">@font-face</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-family</span><span style="color: #000000">: </span><span style="color: #A31515">&#039;SourceSans&#039;</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">src</span><span style="color: #000000">: </span><span style="color: #795E26">url</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;../../ui/fonts/sourcesanspro.ttf&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* MAIN COLOR of the UI, used for all progress bars */</span>
<span style="color: #800000">.sqp-ui-main-bgcolor</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background</span><span style="color: #000000">: </span><span style="color: #0451A5">red</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-ui-ad-bgcolor</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background</span><span style="color: #000000">: </span><span style="color: #0451A5">yellow</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-ad-skip-button.allowed</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">60px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">0.85</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-ad-skip-button</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">54px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">5px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">190px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">30px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">5px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">0.7</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #795E26">rgb</span><span style="color: #000000">(</span><span style="color: #098658">200</span><span style="color: #000000">, </span><span style="color: #098658">200</span><span style="color: #000000">, </span><span style="color: #098658">200</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">default</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-ads-counter</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">10px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">54px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">5px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">125</span><span style="color: #000000">, </span><span style="color: #098658">0.7</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #795E26">rgb</span><span style="color: #000000">(</span><span style="color: #098658">200</span><span style="color: #000000">, </span><span style="color: #098658">200</span><span style="color: #000000">, </span><span style="color: #098658">200</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-family</span><span style="color: #000000">: </span><span style="color: #A31515">&#039;SourceSans&#039;</span><span style="color: #000000">, </span><span style="color: #0451A5">sans-serif</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">default</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-ad-skip-button-text</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #008000">/* transform: translate(0 ,-50%); */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">center</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* padding-right: 10px; */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-family</span><span style="color: #000000">: </span><span style="color: #A31515">&#039;SourceSans&#039;</span><span style="color: #000000">, </span><span style="color: #0451A5">sans-serif</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* disable selection on elements */</span>
<span style="color: #800000">.noselect</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-webkit-touch-callout</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* iOS Safari */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-webkit-user-select</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* Chrome/Safari/Opera */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-khtml-user-select</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* Konqueror */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-moz-user-select</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* Firefox */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-ms-user-select</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* Internet Explorer/Edge */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">user-select</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* Non-prefixed version, currently</span>
<span style="color: #008000">                                  not supported by any browser */</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-customer-overlay</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">50px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* generic &#039;centering&#039; class on X/Y axes */</span>
<span style="color: #800000">.sqp-center</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">transform</span><span style="color: #000000">: </span><span style="color: #795E26">translate</span><span style="color: #000000">(</span><span style="color: #098658">-50%</span><span style="color: #000000">, </span><span style="color: #098658">-50%</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-controls-box</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*  video surface filling the entirity of its parent surface */</span>
<span style="color: #800000">.sqp-video</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* overlay area on top of the video element, covering its whole surface */</span>
<span style="color: #800000">.sqp-video-overlay</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-family</span><span style="color: #000000">: </span><span style="color: #A31515">&#039;SourceSans&#039;</span><span style="color: #000000">, </span><span style="color: #0451A5">sans-serif</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-size</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#AAAAAA</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">pointer-events</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* area of the player overlay in which the notifiation of a buffering event is displayed */</span>
<span style="color: #800000">.sqp-buffering</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">60px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">60px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0.9</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#AAAAAA</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-buffering-value</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">center</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">line-height</span><span style="color: #000000">: </span><span style="color: #098658">60px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">-4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-family</span><span style="color: #000000">: </span><span style="color: #A31515">&#039;SourceSans&#039;</span><span style="color: #000000">, </span><span style="color: #0451A5">sans-serif</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* Animation keyframes for the buffering image */</span>
<span style="color: #AF00DB">@keyframes</span><span style="color: #000000"> </span><span style="color: #001080">sqp-buffering-img-anim</span><span style="color: #000000"> {</span>
<span style="color: #000000">    from {</span>
<span style="color: #000000">        </span><span style="color: #FF0000">transform</span><span style="color: #000000">: </span><span style="color: #795E26">rotate</span><span style="color: #000000">(</span><span style="color: #098658">0deg</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>

<span style="color: #000000">    to {</span>
<span style="color: #000000">        </span><span style="color: #FF0000">transform</span><span style="color: #000000">: </span><span style="color: #795E26">rotate</span><span style="color: #000000">(</span><span style="color: #098658">359deg</span><span style="color: #000000">);</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000">* Animation class for the buffering image</span>
<span style="color: #008000">* This class is automatically added to the buffering image, if any, when a buffering event occurs</span>
<span style="color: #008000">*/</span>
<span style="color: #800000">.sqp-buffering-img-anim</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">animation</span><span style="color: #000000">: sqp-buffering-img-anim </span><span style="color: #098658">2s</span><span style="color: #000000"> </span><span style="color: #0451A5">infinite</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">animation-timing-function</span><span style="color: #000000">: </span><span style="color: #0451A5">linear</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000">* buffering image</span>
<span style="color: #008000">*/</span>
<span style="color: #800000">.sqp-buffering-img</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* area containing video playback controls (by default at the bottom of the video) */</span>
<span style="color: #800000">.sqp-video-controls</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">pointer-events</span><span style="color: #000000">: </span><span style="color: #0451A5">all</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background</span><span style="color: #000000">: </span><span style="color: #795E26">linear-gradient</span><span style="color: #000000">(to </span><span style="color: #0451A5">top</span><span style="color: #000000">, </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0.65</span><span style="color: #000000">) </span><span style="color: #098658">0%</span><span style="color: #000000">, </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">, </span><span style="color: #098658">0</span><span style="color: #000000">) </span><span style="color: #098658">100%</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #008000">/* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.pointer-cursor</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* progress bar zone */</span>
<span style="color: #800000">.sqp-progressbar</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-right</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">flex</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">flex-direction</span><span style="color: #000000">: </span><span style="color: #0451A5">row</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">z-index</span><span style="color: #000000">:</span><span style="color: #098658">7</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* progress bar zone */</span>
<span style="color: #800000">.sqp-progressbar-background</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">0.5</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">flex</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-progressbar-background-not-seekable</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">80</span><span style="color: #000000">, </span><span style="color: #098658">80</span><span style="color: #000000">, </span><span style="color: #098658">80</span><span style="color: #000000">, </span><span style="color: #098658">0.8</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">0%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-progressbar-background:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">5px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">0.7</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-progressbar-background:hover</span><span style="color: #000000">&gt;</span><span style="color: #800000">.sqp-chapters</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000">* Progress bar &#039;progress&#039; zone</span>
<span style="color: #008000">* its width will be controlled by the javascript code and given in &#039;%&#039;</span>
<span style="color: #008000">*/</span>
<span style="color: #800000">.sqp-progressbar-progress</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* default width at openning, will be overriden with values in % by javascript code */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-progressbar-progress-2nd</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">yellow</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000">* playback progress bar endpoint</span>
<span style="color: #008000">* hidden by default (display: node), this element is showed only on mouse hovering,</span>
<span style="color: #008000">* using the animation classes below</span>
<span style="color: #008000">*/</span>
<span style="color: #800000">.sqp-progressbar-progress-endpoint</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">-3px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">-6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-progressbar-progress-live-endpoint</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">-6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">-15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">#CCCCCC</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#000000</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-size</span><span style="color: #000000">: </span><span style="color: #098658">13px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">3px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">z-index</span><span style="color: #000000">: </span><span style="color: #098658">7</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* animation for the &#039;bullet&#039; at the end of the progress bar (makes it appear) */</span>
<span style="color: #AF00DB">@keyframes</span><span style="color: #000000"> </span><span style="color: #001080">endpoint-anim</span><span style="color: #000000"> {</span>
<span style="color: #000000">    from {</span>
<span style="color: #000000">        </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    }</span>

<span style="color: #000000">    to {</span>
<span style="color: #000000">        </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">-3px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">        </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">-6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* activation of the animation for the progress bar endpoint on hovering of the progres bar itself */</span>
<span style="color: #800000">.sqp-progressbar-background:hover</span><span style="color: #000000">&gt;</span><span style="color: #800000">.sqp-progressbar-progress-play</span><span style="color: #000000">&gt;</span><span style="color: #800000">.sqp-progressbar-progress-endpoint</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-webkit-animation-fill-mode</span><span style="color: #000000">: </span><span style="color: #0451A5">forwards</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">animation-fill-mode</span><span style="color: #000000">: </span><span style="color: #0451A5">forwards</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">animation-name</span><span style="color: #000000">: endpoint-anim;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">animation-duration</span><span style="color: #000000">: </span><span style="color: #098658">0.05s</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-program-pure-live</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-right</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-program-pure-live-label</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">red</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-size</span><span style="color: #000000">: </span><span style="color: #098658">13px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">3px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">z-index</span><span style="color: #000000">: </span><span style="color: #098658">7</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-chapters</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-chapter</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background</span><span style="color: #000000">: </span><span style="color: #0451A5">red</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">-8px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-top</span><span style="color: #000000">: </span><span style="color: #098658">-6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-webkit-transform</span><span style="color: #000000">: </span><span style="color: #795E26">scaleX</span><span style="color: #000000">(</span><span style="color: #098658">0.7</span><span style="color: #000000">) </span><span style="color: #795E26">rotate</span><span style="color: #000000">(</span><span style="color: #098658">45deg</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-controls-toolbar</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #795E26">calc</span><span style="color: #000000">(</span><span style="color: #098658">100%</span><span style="color: #000000"> - </span><span style="color: #098658">8px</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* Live Button that appears when playing a Live+TS or (Event+KnowDuration) media */</span>
<span style="color: #800000">.sqp-chapter-live-button</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">#CCCCCC</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#000000</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/*position: absolute;*/</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-top</span><span style="color: #000000">: </span><span style="color: #098658">-7px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/* margin-left: 10px; */</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-size</span><span style="color: #000000">: </span><span style="color: #098658">13px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">3px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">z-index</span><span style="color: #000000">: </span><span style="color: #098658">7</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/*left: 100%;*/</span>
<span style="color: #000000">    </span><span style="color: #FF0000">float</span><span style="color: #000000">: </span><span style="color: #0451A5">right</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-right</span><span style="color: #000000">: </span><span style="color: #098658">-6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #008000">/*fixing issue on Safari with Chinese character*/</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: -webkit-max-content;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-chapter-live-button:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#FFFFFF</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">-webkit-transform</span><span style="color: #000000">: </span><span style="color: #795E26">scale</span><span style="color: #000000">(</span><span style="color: #098658">1.1</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* generic class for all buttons in video controller UI */</span>
<span style="color: #800000">.sqp-button</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">50px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">32px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">transparent</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#AAAAAA</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* hover color for all buttons */</span>
<span style="color: #800000">.sqp-button:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#FFFFFF</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-quality-btn</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* icon size for all UI buttons */</span>
<span style="color: #800000">.sqp-button</span><span style="color: #000000"> </span><span style="color: #800000">.icon</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">24px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">24px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* color for disabled button */</span>
<span style="color: #800000">.sqp-button:disabled</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">gray</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* color for inactive button */</span>
<span style="color: #800000">.sqp-button-inactive</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">#7b7b7b</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* remove dotted outline on buttons */</span>
<span style="color: #800000">button.sqp-button</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">outline</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* remove dotted outline on buttons (Firefox) */</span>
<span style="color: #800000">button.sqp-button::-moz-focus-inner</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-right</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">float</span><span style="color: #000000">: </span><span style="color: #0451A5">right</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-player-controls-text</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">line-height</span><span style="color: #000000">: </span><span style="color: #098658">28px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">10px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-right</span><span style="color: #000000">: </span><span style="color: #098658">10px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-player-controls-times-zone</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">default</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-player-controls-times-zone</span><span style="color: #000000"> </span><span style="color: #800000">div</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* class applied to time indications in the UI */</span>
<span style="color: #800000">.sqp-time</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">center</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">top</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-volume-btn</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* specificity for the icon in the volume button */</span>
<span style="color: #800000">.sqp-volume-btn</span><span style="color: #000000"> </span><span style="color: #800000">.icon</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* volume bar */</span>
<span style="color: #800000">.sqp-volume-bar</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inline-block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">120px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">28px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin-left</span><span style="color: #000000">: </span><span style="color: #098658">-4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">overflow-x</span><span style="color: #000000">: </span><span style="color: #0451A5">hidden</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* background of the volume bar */</span>
<span style="color: #800000">.sqp-volume-bar-bg</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">11px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">0.5</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-volume-bar-bg:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">255</span><span style="color: #000000">, </span><span style="color: #098658">0.7</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/* volume &#039;progress&#039; on top the background (width is controlled by javascript and given in %) */</span>
<span style="color: #800000">.sqp-volume-bar-level</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">min-width</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-volume-bar-level-endpoint</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">inherit</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">-4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">12px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000">-- Classes related to the &#039;Option&#039; menu (Qualities, Audio and subtitles tracks) --</span>
<span style="color: #008000">*/</span>
<span style="color: #800000">.sqp-optmenu</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">0.9</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">10px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">54px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">overflow</span><span style="color: #000000">: </span><span style="color: #0451A5">hidden</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">pointer-events</span><span style="color: #000000">: </span><span style="color: #0451A5">all</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">z-index</span><span style="color: #000000">:</span><span style="color: #098658">7</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-optmenu-1st</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">max-width</span><span style="color: #000000">: </span><span style="color: #098658">340px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-item</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-row</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">middle</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-title</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">block</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-bottom</span><span style="color: #000000">: </span><span style="color: #098658">1px</span><span style="color: #000000"> </span><span style="color: #0451A5">solid</span><span style="color: #000000"> </span><span style="color: #0451A5">#8a8a8a</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">min-width</span><span style="color: #000000">: </span><span style="color: #098658">150px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-title-2nd</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">0px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-title-back</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">18px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">18px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">10px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-item</span><span style="color: #000000"> </span><span style="color: #800000">div</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">middle</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-name</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">middle</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">60px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-value</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">right</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-optmenu-2nd</span><span style="color: #000000"> </span><span style="color: #800000">.sqp-opt-list-container</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">max-height</span><span style="color: #000000">: </span><span style="color: #098658">300px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">overflow-y</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-items-list</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">overflow-y</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-value-pick:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">0.9</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-value-pick</span><span style="color: #000000"> </span><span style="color: #800000">.pick-arrow</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">16px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">middle</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-value-2nd</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">right</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-right</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-bottom</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.opt-item-select</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">table-cell</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">14px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">20px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">vertical-align</span><span style="color: #000000">: </span><span style="color: #0451A5">middle</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-left</span><span style="color: #000000">: </span><span style="color: #098658">6px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding-top</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-quality-selected</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-opt-select:hover</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">70</span><span style="color: #000000">, </span><span style="color: #098658">0.9</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">pointer</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #008000">/*</span>
<span style="color: #008000"> -- Classes related to the &#039;Tooltip&#039; (hover UI above the playback progress bar) --</span>
<span style="color: #008000">*/</span>
<span style="color: #008000">/* tooltip area (its position and content is driven by javascript) */</span>
<span style="color: #800000">.sqp-tooltip</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-color</span><span style="color: #000000">: </span><span style="color: #795E26">rgba</span><span style="color: #000000">(</span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">30</span><span style="color: #000000">, </span><span style="color: #098658">0.9</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">font-size</span><span style="color: #000000">: </span><span style="color: #098658">10pt</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">center</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">54px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">padding</span><span style="color: #000000">: </span><span style="color: #098658">4px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">overflow</span><span style="color: #000000">: </span><span style="color: #0451A5">hidden</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">border-radius</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-tooltip-thumbnail</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #FF0000">max-height</span><span style="color: #000000">: </span><span style="color: #098658">100px</span><span style="color: #000000">;</span>
<span style="color: #000000">  </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">150px</span><span style="color: #000000">;</span>
<span style="color: #000000">  </span><span style="color: #FF0000">margin-bottom</span><span style="color: #000000">: </span><span style="color: #098658">2px</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-thumbnail-canvas</span><span style="color: #000000"> {</span>
<span style="color: #000000">  </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">  </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-banner-img</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">100%</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-banner-a</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">left</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">bottom</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-banner-close</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">absolute</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">right</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">0</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">height</span><span style="color: #000000">: </span><span style="color: #098658">15px</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-image</span><span style="color: #000000">: </span><span style="color: #795E26">url</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;../../ui/images/closeAdBannerButton.png&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-size</span><span style="color: #000000">: </span><span style="color: #0451A5">contain</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-repeat</span><span style="color: #000000">: </span><span style="color: #0451A5">no-repeat</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background-position-x</span><span style="color: #000000">: </span><span style="color: #0451A5">right</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.importantDisplayNone</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">display</span><span style="color: #000000">: </span><span style="color: #0451A5">none</span><span style="color: #000000"> </span><span style="color: #0000FF">!important</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.importantCursorDefault</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">cursor</span><span style="color: #000000">: </span><span style="color: #0451A5">default</span><span style="color: #000000"> </span><span style="color: #0000FF">!important</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #800000">.sqp-section-overlay</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #FF0000">position</span><span style="color: #000000">: </span><span style="color: #0451A5">relative</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">top</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">width</span><span style="color: #000000">: </span><span style="color: #098658">50%</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">margin</span><span style="color: #000000">: </span><span style="color: #0451A5">auto</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">text-align</span><span style="color: #000000">: </span><span style="color: #0451A5">center</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">color</span><span style="color: #000000">: </span><span style="color: #0451A5">black</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #FF0000">background</span><span style="color: #000000">: </span><span style="color: #0451A5">white</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

</code></pre>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="playback-ui.html">Viaccess-<wbr><wbr>Orca UI integration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="progressbar-control.html">Control of <wbr>Progress <wbr>Bar</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="ui-css-classes.html">Player UI default CSS</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>