<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Viaccess-Orca UI integration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Viaccess-Orca UI integration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../player-ui.html">Player integration with Viaccess-Orca user Interface</a>
				</li>
				<li>
					<a href="playback-ui.html">Viaccess-Orca UI integration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<p>Viaccess-Orca provides a UI controller (UI + Javascript control logic) to facilitate the creation of a turn-key playback experience.</p>
				<a href="#package-description" id="package-description" style="color: inherit; text-decoration: none;">
					<h2>Package description</h2>
				</a>
				<p>The controller comes as a set of resources:</p>
				<ul>
					<li>an additional Javascript file <em>voplayer-ui.min.js</em></li>
					<li>a CSS styling sheet <em>voplayer-playback-ui.css</em></li>
					<li>a collection of icons in SVG and PNG formats <em>images/*.svg|png</em></li>
				</ul>
				<a href="#integration" id="integration" style="color: inherit; text-decoration: none;">
					<h2>Integration</h2>
				</a>
				<p>To use the UI controller module:</p>
				<ol>
					<li>the Javascript file <em>voplayer-ui.min.js</em> must be added in the webpage&#39;s header <strong>after</strong> the player module <em>voplayer.min.js</em></li>
					<li>the css file <em>voplayer-playback-ui.css</em> must be added in the webpage&#39;s header</li>
					<li>the images resources must be available in their <em>images/</em> folder, on the server, at the same level as the webpage</li>
				</ol>
				<p>The creation of a player including the control UI is then done using function <a href="../../../modules/UIController.html#createPlayerWithControls">voplayer.createPlayerWithControls()</a>.</p>
				<p>Note: you can&#39;t create 2 players instances with UI (it means that you can&#39;t call &#39;player.createPlayerWithControls&#39; API twice ).</p>
				<p>The player license must be given as parameter <em>license</em> in the configuration object given to the creation API. You must also give a <em>base</em> path in which the UI resources are stored relatively to the page. In the following example, all Viaccess-Orca player related files are stored under the folder html5player.</p>
				<pre><code class="language-html"><span style="color: #800000">&lt;!DOCTYPE</span><span style="color: #000000"> </span><span style="color: #FF0000">html</span><span style="color: #800000">&gt;</span>
<span style="color: #800000">&lt;html&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;head&gt;</span>
<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- Viaccess-Orca Player compiled library: --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>
<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- Viaccess-Orca Playback UI controls logic --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;html5player/js/voplayer-ui.min.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>
<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- Your application source: --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;script</span><span style="color: #000000FF"> </span><span style="color: #FF0000">src</span><span style="color: #000000FF">=</span><span style="color: #0000FF">&quot;myapp.js&quot;</span><span style="color: #800000">&gt;&lt;/script&gt;</span>
<span style="color: #000000">    [...]</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;/head&gt;</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;body&gt;</span>
<span style="color: #000000">    [...]</span>
<span style="color: #000000">    </span><span style="color: #008000">&lt;!-- div that will contain the video, its original content will be replaced --&gt;</span>
<span style="color: #000000">    </span><span style="color: #800000">&lt;div</span><span style="color: #000000"> </span><span style="color: #FF0000">id</span><span style="color: #000000">=</span><span style="color: #0000FF">&quot;videoContext&quot;</span><span style="color: #800000">&gt;</span><span style="color: #000000">The video will be displayed here</span><span style="color: #800000">&lt;/div&gt;</span>
<span style="color: #000000">    [...]</span>
<span style="color: #000000">  </span><span style="color: #800000">&lt;/body&gt;</span>
<span style="color: #800000">&lt;/html&gt;</span>
</code></pre>
				<pre><code class="language-js">
<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">uiConfiguration</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    </span><span style="color: #001080">license:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&lt;YOUR_LICENSE_HERE&gt;&#039;</span><span style="color: #000000">,</span>
<span style="color: #000000">    </span><span style="color: #001080">base:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;html5player&#039;</span>
<span style="color: #000000">    ...</span>
<span style="color: #000000">  };</span>
<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">player</span><span style="color: #000000"> = </span><span style="color: #001080">voplayer</span><span style="color: #000000">.</span><span style="color: #795E26">createPlayerWithControls</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;videoContext&#039;</span><span style="color: #000000">, </span><span style="color: #001080">uiConfiguration</span><span style="color: #000000">);</span>

<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;error&#039;</span><span style="color: #000000">, </span><span style="color: #001080">onError</span><span style="color: #000000">);</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #795E26">on</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;ready&#039;</span><span style="color: #000000">, </span><span style="color: #0000FF">function</span><span style="color: #000000">() {</span>
<span style="color: #000000">  </span><span style="color: #001080">console</span><span style="color: #000000">.</span><span style="color: #795E26">log</span><span style="color: #000000">(</span><span style="color: #A31515">&#039;Player Controls loaded and ready&#039;</span><span style="color: #000000">);</span>
<span style="color: #000000">  </span><span style="color: #008000">// at this point, the UI and controls are loaded in the page and a playback can start</span>
<span style="color: #000000">});</span>
</code></pre>
				<p><strong>Important:</strong> the Controller module requires jQuery to be available to work properly.
					If not instructed otherwise, the module will look for a jQuery instance available in the page, if any.
					A dedicated version of jQuery can also be given in the UI configuration object (see below).
				If jQuery is not available (neither in the page and nor in configuration), the UI setup will throw an error.</p>
				<a href="#controls-and-ui-customization" id="controls-and-ui-customization" style="color: inherit; text-decoration: none;">
					<h2>Controls and UI customization</h2>
				</a>
				<a href="#use-of-a-specific-jquery-version" id="use-of-a-specific-jquery-version" style="color: inherit; text-decoration: none;">
					<h3>Use of a specific jQuery version</h3>
				</a>
				<p>The UI controller module requires the presence of jQuery to work properly. By default, the module will look for a version of jQuery already loaded in the page (via global variable <em>jQuery</em>).</p>
				<p>It is possible to configure the module to use its own, dedicated version of jQuery in isolation from the rest of the page.
				To do so, set the <em>PlaybackUIConfiguration.jQuery</em> string property to the URL of the jQuery script to load and use in the module (as it would be filled-in in a direct &lt;script&gt; tag). This jQuery script will be loaded in complete isolation from the rest of the page, using <em>jQuery.noConflict(true);</em>.</p>
				<a href="#ui-texts-customization" id="ui-texts-customization" style="color: inherit; text-decoration: none;">
					<h3>UI texts customization</h3>
				</a>
				<p>The default UI injected by the controller uses as little text literals as possible and prefers icons whenever possible.
				The few text literals left can be localized using the Object <em>PlaybackUIConfiguration.localization</em>, which takes the following parameters:</p>
				<ul>
					<li><em>qualityTxt</em>: text used in the option menu for quality selection (defaults to &quot;Quality&quot;)</li>
					<li><em>audioTxt</em>: text used in option menu for audio track selection (defaults to &quot;Audio&quot;)</li>
					<li><em>subtitleTxt</em>: text used in option menu for subtitle track selection (defaults to &quot;Subtitles / CC&quot;)</li>
					<li><em>liveBtnText</em>: text used in the &#39;Live&#39; button</li>
					<li><em>offTrackChoice</em>: text used in subtitle track selection menu to indicate no track is selected for playback</li>
				</ul>
				<a href="#other-ui-parameters" id="other-ui-parameters" style="color: inherit; text-decoration: none;">
					<h3>Other UI parameters</h3>
				</a>
				<ul>
					<li><p><em>togglePlayPauseOnClick</em>:  to enable/disable the play/pause  on mouse click</p>
					</li>
					<li><p><em>toggleFullscreenOnDblClick</em>:  to enable/disable the switch to full screen mode on mouse double click</p>
					</li>
					<li><p><em>useQualityOnlyButton</em>:  to transform  the &quot;option&quot; button  to video qualities list only when no audio tracks or subtitles choices are available</p>
					</li>
					<li><p><em>controlsHiddingDelay</em>: delay before the UI starts hiding on mouse pointer stills/leaves the video area , it is configurable with two parameters:</p>
						<ul>
							<li><em>mouseIn</em>:  delay for the UI to start hiding while the mouse pointer is still within the video area but not over any control</li>
							<li><em>mouseOut</em>: delay before the UI to start hiding when the mouse pointer leaves the video area</li>
						</ul>
					</li>
					<li><p><em>customerOverlay</em>:  div element placed on top of the video that can contain custom elements (at discretion for the integrator). This element can follow the rest of the UI&#39;s visibility.
						The content is treated as a &quot;black box&quot; and is injected in the DOM model as-is; no processing of any kind is performed.It is configurable with  two parameters:</p>
						<ul>
							<li><em>htmlContent</em>:  HTML content to be used inside the overlay (injected as innerHTML)</li>
							<li><em>display</em>:  display options are:<ul>
									<li><em>alwaysOn</em>: always show the htmlContent</li>
									<li><em>hidden</em>:  always hide the htmlContent</li>
									<li><em>default</em>: follow the UI controls;show the content if UI controls are shown, hide it  otherwise.</li>
								</ul>
							</li>
						</ul>
					</li>
				</ul>
				<a href="#option-menu-lists-customization" id="option-menu-lists-customization" style="color: inherit; text-decoration: none;">
					<h3>Option menu lists customization</h3>
				</a>
				<p>The UI controller comes with defaults &quot;formatters&quot; to create textual content for qualities, audio tracks and subtitles tracks to be displayed in the option menus.
				These formatters can be overridden as follow:</p>
				<a href="#formater-for-qualities-in-menu" id="formater-for-qualities-in-menu" style="color: inherit; text-decoration: none;">
					<h4>Formater for qualities in menu</h4>
				</a>
				<p>Define <em>PlaybackUIConfiguration.qualityLevelFormater</em> as a function:</p>
				<pre><code><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">formatQualityLevelForDisplay</span><span style="color: #000000">(</span><span style="color: #001080">level</span><span style="color: #000000">, </span><span style="color: #008000">// quality level with same format as Quality</span>
<span style="color: #000000">    </span><span style="color: #001080">forPrimaryDisplay</span><span style="color: #000000">, </span><span style="color: #008000">// true when the output is intended for display in the main option menu, false for display in the secondary list of qualities</span>
<span style="color: #000000">    </span><span style="color: #001080">abrEnabled</span><span style="color: #000000"> </span><span style="color: #008000">// true when the ABR module is enabled in the player, false otherwise</span>
<span style="color: #000000">    ) {</span>
<span style="color: #000000">  </span><span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">formatedQuality</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    </span><span style="color: #001080">text:</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;&#039;</span><span style="color: #000000">, </span><span style="color: #008000">// text that will be displayed (either in the main option menu [forPrimaryDisplay = true] or in the list of available qualities to choose from [forPrimaryDisplay = false])</span>
<span style="color: #000000">    </span><span style="color: #001080">isHD:</span><span style="color: #000000"> </span><span style="color: #0000FF">false</span><span style="color: #000000"> </span><span style="color: #008000">// boolean specifying if this particular quality level is considered &#039;HD&#039;</span>
<span style="color: #000000">  };</span>
<span style="color: #000000">  </span><span style="color: #008000">//TODO: implement logic to format the quality text</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">return</span><span style="color: #000000"> </span><span style="color: #001080">formatedQuality</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    ...,</span>
<span style="color: #000000">    </span><span style="color: #001080">qualityLevelFormater:</span><span style="color: #000000"> </span><span style="color: #001080">formatQualityLevelForDisplay</span><span style="color: #000000">,</span>
<span style="color: #000000">    ...</span>
<span style="color: #000000">}</span>
</code></pre>
				<p>Note: level is a <a href="../../../interfaces/Types.Quality.html">Quality</a> object</p>
				<a href="#formater-for-audio-tracks-in-menu" id="formater-for-audio-tracks-in-menu" style="color: inherit; text-decoration: none;">
					<h4>Formater for audio tracks in menu</h4>
				</a>
				<p>Define <em>PlaybackUIConfiguration.audioTrackDisplayFormater</em> as a function:</p>
				<pre><code><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">formatAudioTrackForDisplay</span><span style="color: #000000">(</span><span style="color: #001080">track</span><span style="color: #000000"> </span><span style="color: #008000">/* AudioTrack to be formated */</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #008000">//TODO: implement</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">return</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;formated_audio_track_name&#039;</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    ...,</span>
<span style="color: #000000">    </span><span style="color: #001080">audioTrackDisplayFormater:</span><span style="color: #000000"> </span><span style="color: #001080">formatAudioTrackForDisplay</span><span style="color: #000000">,</span>
<span style="color: #000000">    ...</span>
<span style="color: #000000">}</span>
</code></pre>
				<p>Note: track is an <a href="../../../interfaces/Types.AudioTrack.html">AudioTrack</a> object</p>
				<a href="#formater-for-subtitle-tracks-in-menu" id="formater-for-subtitle-tracks-in-menu" style="color: inherit; text-decoration: none;">
					<h4>Formater for subtitle tracks in menu</h4>
				</a>
				<p>Define <em>PlaybackUIConfiguration.textTrackDisplayFormater</em> as a function:</p>
				<pre><code><span style="color: #0000FF">function</span><span style="color: #000000"> </span><span style="color: #795E26">formatTextTrackForDisplay</span><span style="color: #000000">(</span><span style="color: #001080">track</span><span style="color: #000000"> </span><span style="color: #008000">/* TextTrack to be formated  */</span><span style="color: #000000">) {</span>
<span style="color: #000000">  </span><span style="color: #008000">//TODO: implement</span>
<span style="color: #000000">  </span><span style="color: #AF00DB">return</span><span style="color: #000000"> </span><span style="color: #A31515">&#039;formated_text_track_name&#039;</span><span style="color: #000000">;</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">var</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">    ...,</span>
<span style="color: #000000">    </span><span style="color: #001080">textTrackDisplayFormater:</span><span style="color: #000000"> </span><span style="color: #001080">formatTextTrackForDisplay</span><span style="color: #000000">,</span>
<span style="color: #000000">    ...</span>
<span style="color: #000000">}</span>
</code></pre>
				<p>Note: track is a <a href="../../../interfaces/Types.TextTrack.html">TextTrack</a> object</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="playback-ui.html">Viaccess-<wbr><wbr>Orca UI integration</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="progressbar-control.html">Control of <wbr>Progress <wbr>Bar</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="ui-css-classes.html">Player UI default CSS</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>