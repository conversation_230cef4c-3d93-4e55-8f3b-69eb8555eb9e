<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Control of Progress Bar | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../../../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../../../assets/css/main.css">
	<link rel="stylesheet" href="../../../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../../../assets/js/search.json" data-base="../../..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../../../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Control of Progress Bar</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../../../modules.html">Globals</a>
				</li>
				<li>
					<a href="../basic-usage.html">Documentation</a>
				</li>
				<li>
					<a href="../player-ui.html">Player integration with Viaccess-Orca user Interface</a>
				</li>
				<li>
					<a href="progressbar-control.html">Control of Progress Bar</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<div class="tsd-panel tsd-typography">
				<a href="#preamble" id="preamble" style="color: inherit; text-decoration: none;">
					<h2>Preamble</h2>
				</a>
				<p>This tutorial is only applicable when the player is created with the UI provided by Viaccess-Orca via the method: <code>createPlayerWithControls</code>.</p>
				<a href="#introduction" id="introduction" style="color: inherit; text-decoration: none;">
					<h2>Introduction</h2>
				</a>
				<p>It is possible to control the UI of the progress bar to enable different use cases: from the simplest one : Video On Demand to more complex one: Switching between StartOver and Live URLs while interacting with the progress bar. All these use cases are enabled via the configuration of the UI and are depicted in this section.</p>
				<a href="#elements-of-the-progress-bar" id="elements-of-the-progress-bar" style="color: inherit; text-decoration: none;">
					<h2>Elements of the progress bar</h2>
				</a>
				<p><img src="./Elements.png" alt="Progress bar UI elements"></p>
				<p>The progress bar is composed of three sections:</p>
				<ul>
					<li>The Non Seekable left part</li>
					<li>The Seekable part</li>
					<li>The Non Seekable right part</li>
				</ul>
				<p>The Seekable part is composed of the following elements:</p>
				<ul>
					<li>The progress section</li>
					<li>The LIVE indicator / Button</li>
					<li>The time tooltip</li>
				</ul>
				<p>Next to the volume controls are provided two time indicators:</p>
				<ul>
					<li>The playback time</li>
					<li>The Media Duration or program End Time</li>
				</ul>
				<a href="#non-seekable-part-of-the-progress-bar" id="non-seekable-part-of-the-progress-bar" style="color: inherit; text-decoration: none;">
					<h3>Non seekable part of the progress bar</h3>
				</a>
				<p>Non seekable part of the progress bar are only visible when External Program startTime and endTime are provided to the UI controller via a new UI configuration.</p>
				<a href="#seekable-part-of-the-progress-bar" id="seekable-part-of-the-progress-bar" style="color: inherit; text-decoration: none;">
					<h3>Seekable part of the progress bar</h3>
				</a>
				<p>The seekable part of the progress bar is always visible, except in some LIVE use cases where the UI controller is informed that the TimeShift Window of the stream is too small to provide a meaningful seekable experience to the end user.</p>
				<a href="#live-indicator--button" id="live-indicator--button" style="color: inherit; text-decoration: none;">
					<h3>LIVE indicator / Button</h3>
				</a>
				<p>The LIVE indicator is made visible on LIVE and StartOver use case (when the program is not ended for the later). In case where the LIVE stream is considered as non seekable (TimeShift Window of the stream is too small), the LIVE indicator will be placed at the right side of the volume control in place of the timing information.</p>
				<p>LIVE indicator is changed to RED color when playback is considered to be Live (i.e. between <strong>now</strong> and <strong>now</strong> - 20 seconds).</p>
				<a href="#time-tooltip" id="time-tooltip" style="color: inherit; text-decoration: none;">
					<h3>Time tooltip</h3>
				</a>
				<p>The time tooltip is visible all along the seekable progress bar and indicates a time position where you can seek. The tooltip time indication may be relative to the stream or relative to the wall clock time if this is option is set to the UI controller via a new UI configuration.</p>
				<a href="#playback-time" id="playback-time" style="color: inherit; text-decoration: none;">
					<h3>Playback Time</h3>
				</a>
				<p>Playback time indicates the current time of playback. The playback time indication may be relative to the stream or relative to the wall clock time if this is option is set to the UI controller via a new UI configuration</p>
				<a href="#media-duration-or-program-end-time" id="media-duration-or-program-end-time" style="color: inherit; text-decoration: none;">
					<h3>Media Duration or Program end time</h3>
				</a>
				<p>Depending of the use case, the time indicator at the right side of the playback time may indicate either the Media Duration (e.g. VOD), TimeShift Windows Size (e.g. LIVE or StartOver) or the Program end time (only if this was provided to the UI controller).</p>
				<a href="#configuration-of-the-progress-bar" id="configuration-of-the-progress-bar" style="color: inherit; text-decoration: none;">
					<h2>Configuration of the progress bar</h2>
				</a>
				<p>Progress bar UI may be configured by providing a new configuration to the UI controller. Configuration may be changed during stream playback.</p>
				<a href="#access-to-controller-and-api-to-update-the-ui" id="access-to-controller-and-api-to-update-the-ui" style="color: inherit; text-decoration: none;">
					<h3>Access to controller and API to update the UI</h3>
				</a>
				<p>Controller is accessed from the player the following way:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">uiController</span><span style="color: #000000"> = </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">;</span>
</code></pre>
				<p>Providing a new config to the UI Controller shall be performed the following way:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = { ... };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<p>The <code>updateConfiguration</code> may be called any time as soon as the player is created with its UI.</p>
				<a href="#configuration-elements" id="configuration-elements" style="color: inherit; text-decoration: none;">
					<h3>Configuration elements</h3>
				</a>
				<p>The following elements of the UI controller configuration are used to configure the progress bar:</p>
				<ul>
					<li><code>displayRelativePositionOnLive</code></li>
					<li><code>minimumTimeShiftWindow</code></li>
					<li><code>externalProgramInfo</code></li>
					<li><code>onEndProgramReached</code></li>
					<li><code>onStartOver</code></li>
					<li><code>onBackToLive</code></li>
				</ul>
				<a href="#displayrelativepositiononlive" id="displayrelativepositiononlive" style="color: inherit; text-decoration: none;">
					<h4>displayRelativePositionOnLive</h4>
				</a>
				<p>This is a boolean to indicate if the timing indication shall be displayed relative to the wall clock time (true) or to the stream (false).
				Applicable to Catchup, Live and StartOver. Not effective if external program information is provided.</p>
				<a href="#minimumtimeshiftwindow" id="minimumtimeshiftwindow" style="color: inherit; text-decoration: none;">
					<h4>minimumTimeShiftWindow</h4>
				</a>
				<p>Time in seconds to indicate the minimal time below which the content is not considered as seekable. Applicable to Live stream only.</p>
				<a href="#externalprograminfo" id="externalprograminfo" style="color: inherit; text-decoration: none;">
					<h4>externalProgramInfo</h4>
				</a>
				<p>Object composed of <code>startTime</code> and <code>endTime</code> properties indicating the start and the end of the program.
				The <code>startTime</code> and <code>endTime</code> shall be provided in <a href="https://en.wikipedia.org/wiki/ISO_8601">ISO format</a> (example: 2020-09-16T13:25:56Z, 2020-09-16T13:25Z is also acceptable)</p>
				<p><code>startTime</code> and <code>endTime</code> may be expressed in Local Time Zone (no explicit timezone indication).</p>
				<p>Note: If your service is spread over several time zones, it is required to set <code>startTime</code>and <code>endTime</code> in <strong>fully qualified ISO format, including UTC offset indication</strong>.</p>
				<p>Applicable to Catchup, Live and StartOver use cases.</p>
				<a href="#onendprogramreached" id="onendprogramreached" style="color: inherit; text-decoration: none;">
					<h4>onEndProgramReached</h4>
				</a>
				<p>Callback to update the <code>externalProgramInfo</code> when <strong>now</strong> as reached <code>endTime</code></p>
				<a href="#onstartover" id="onstartover" style="color: inherit; text-decoration: none;">
					<h4>onStartOver</h4>
				</a>
				<p>Callback to switch from a Live URL to a StartOver URL.
				Applicable to Combined Live and StartOver use case.</p>
				<a href="#onbacktolive" id="onbacktolive" style="color: inherit; text-decoration: none;">
					<h4>onBackToLive</h4>
				</a>
				<p>Callback to switch from a StartOver URL to a Live URL.
				Applicable to Combined Live and StartOver use case.</p>
				<a href="#use-cases-description" id="use-cases-description" style="color: inherit; text-decoration: none;">
					<h2>Use cases description</h2>
				</a>
				<a href="#video-on-demand" id="video-on-demand" style="color: inherit; text-decoration: none;">
					<h3>Video On Demand</h3>
				</a>
				<p>Video On demand (VOD) is the most basic use case for the progress bar where there is no specific configuration provided to the UI. In that case:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part represents the duration of the VOD content</li>
					<li>The LIVE indicator / Button is not visible</li>
					<li>The time tooltip varies from 0 to the VOD content duration indicated in HH:MM:SS</li>
					<li>The playback time indicates the current time of playback in HH:MM:SS</li>
					<li>The Media Duration indicates the duration of the VOD content in HH:MM:SS</li>
				</ul>
				<p><img src="./VOD.png" alt="VOD use case"></p>
				<a href="#catchup-with-external-program-information" id="catchup-with-external-program-information" style="color: inherit; text-decoration: none;">
					<h3>Catchup with external program information</h3>
				</a>
				<p>A Catchup use case is similar to a VOD use case. Integrator may however want to indicate external program information relative to the Catchup program.
					Note that in that case only the <code>startTime</code> will be taken into account, end time being computed from <code>startTime</code> and program duration retrieved from the stream.
				In that case:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part represents the duration of the VOD content</li>
					<li>The LIVE indicator / Button is not visible</li>
					<li>The time tooltip varies from <code>startTime</code> to (<code>startTime</code> + VOD content duration) indicated in HH:MM:SS</li>
					<li>The playback time indicates the current time of playback in HH:MM:SS relative to <code>startTime</code></li>
					<li>The Media Duration indicates the duration of the VOD content in HH:MM:SS</li>
				</ul>
				<p><img src="./Catchup-External-PI.png" alt="Catchup with external Program Information"></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #008000">// your code to retrieve program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#live-use-case" id="live-use-case" style="color: inherit; text-decoration: none;">
					<h3>Live use case</h3>
				</a>
				<a href="#non-seekable-live-with-no-external-program-information" id="non-seekable-live-with-no-external-program-information" style="color: inherit; text-decoration: none;">
					<h4>Non Seekable Live with no external program information</h4>
				</a>
				<p>In some Live cases, the width of the stream time shift window available from the server may be so small that it is not relevant to provide to the end user the ability to seek within this time shift window.
				Integrator may do so providing in the configuration the time below which the content is not considered as seekable.</p>
				<p>In that case:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part is not visible</li>
					<li>The LIVE indicator is visible and placed at the right side of the volume controls</li>
					<li>The time tooltip is not visible</li>
					<li>The playback time is not visible</li>
					<li>The Media Duration is not visible</li>
				</ul>
				<p><img src="./LIVE-no-timeshift.png" alt=""></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">minimumTimeShiftWindow:</span><span style="color: #000000"> ... </span><span style="color: #008000">// value in seconds</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#non-seekable-live-with-external-program-information" id="non-seekable-live-with-external-program-information" style="color: inherit; text-decoration: none;">
					<h4>Non Seekable Live with external program information</h4>
				</a>
				<p>Eventhough the Live content is not considered as seekable, integrator may indicate the start and end time of the program and have the Live indicator placed on the progress bar relative to the start and end time of the program.</p>
				<p>In that case:</p>
				<ul>
					<li>The Non Seekable parts are visible</li>
					<li>The Seekable part is not visible</li>
					<li>The LIVE indicator is visible and placed on the progress bar relative to the <code>startTime</code> and <code>endTime</code> of the program</li>
					<li>The time tooltip is not visible</li>
					<li>The playback time is visible and represents the Live time</li>
					<li>The Media Duration is visible and represents the <code>endTime</code> of the program</li>
				</ul>
				<p><img src="./LIVE-no-timeshift-but-external-PI.png" alt=""></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #008000">// your code to retrieve program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">minimumTimeShiftWindow:</span><span style="color: #000000"> ... </span><span style="color: #008000">// value in seconds</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<p>Note that current playback position is always visible within the progress bar, therefore if <code>startTime</code> or <code>endTime</code> are not properly set, the non seekable part of the progress bar will be ajusted accordingly.</p>
				<a href="#seekable-live-nominal" id="seekable-live-nominal" style="color: inherit; text-decoration: none;">
					<h4>Seekable Live nominal</h4>
				</a>
				<p>In case where a Live content is considered as seekable, the nominal use case (no specific configuration provided) will be as follows:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part is visible and represents the duration of the live timeshift window</li>
					<li>The LIVE indicator is visible and placed at the right end side of the progress bar</li>
					<li>The time tooltip is visible and varies from 0 to the duration of the live timeshift window</li>
					<li>The playback time is visible and represents playback time within the range [ 0 , duration of the live timeshift window]</li>
					<li>The Media Duration is visible and represents the duration of the live timeshift window</li>
				</ul>
				<p><img src="./Live-timeshift.png" alt=""></p>
				<a href="#seekable-live-with-relative-position-to-wall-clock-time" id="seekable-live-with-relative-position-to-wall-clock-time" style="color: inherit; text-decoration: none;">
					<h4>Seekable Live with relative position to wall clock time</h4>
				</a>
				<p>For the nominal case it is possible to have time displayed relative to wallclock time.
				In that case:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part is visible and represents the duration of the live timeshift window</li>
					<li>The LIVE indicator is visible and placed at the right end side of the progress bar</li>
					<li>The time tooltip is visible and varies from (<strong>now</strong> - the duration of the live timeshift window) to <strong>now</strong></li>
					<li>The playback time is visible and represents playback time within the range [ (<strong>now</strong> - the duration of the live timeshift window) , <strong>now</strong>]</li>
					<li>The Media Duration is visible and represents the <strong>now</strong> time</li>
				</ul>
				<p><img src="./Live-timeshift-wallclock.png" alt=""></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">displayRelativePositionOnLive:</span><span style="color: #000000"> </span><span style="color: #0000FF">true</span><span style="color: #000000"> </span><span style="color: #008000">// value in seconds</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#seekable-live-with-external-program-information" id="seekable-live-with-external-program-information" style="color: inherit; text-decoration: none;">
					<h4>Seekable Live with external program information</h4>
				</a>
				<p>It is also possible to provide external timing program information:</p>
				<p>In that case:</p>
				<ul>
					<li>The Non Seekable left part and right part may be visible (depending of the live position, the current playback position, the time shift window size and the external timing information provided for the program)</li>
					<li>The Seekable part is visible and represents the either the duration of the live timeshift window or the duration between program <code>startTime</code> and <strong>now</strong></li>
					<li>The LIVE indicator is visible and placed on the progress bar relatively to program <code>startTime</code> and program <code>endTime</code></li>
					<li>The time tooltip is visible and varies at maximum from (<strong>now</strong> - the duration of the live timeshift window) to <strong>now</strong></li>
					<li>The playback time is visible and represents playback time within the maximum range [ (<strong>now</strong> - the duration of the live timeshift window) , <strong>now</strong>]</li>
					<li>The Media Duration is visible and represents the <strong>now</strong> time</li>
				</ul>
				<p><img src="./Live-timeshift-externalPI.png" alt=""></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #008000">// your code to retrieve program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<p>Note that current playback position and LIVE position are always visible within the progress bar, therefore if <code>startTime</code> or <code>endTime</code> are not properly set, the non seekable part of the progress bar will be ajusted accordingly.</p>
				<a href="#startover-use-case" id="startover-use-case" style="color: inherit; text-decoration: none;">
					<h3>StartOver use case</h3>
				</a>
				<p>StartOver stream corresponds to a use case where</p>
				<ul>
					<li>the stream has a start date</li>
					<li>the time shift window is growing with time</li>
					<li>the stream has eventually an end date</li>
				</ul>
				<a href="#startover-nominal-case" id="startover-nominal-case" style="color: inherit; text-decoration: none;">
					<h4>StartOver Nominal case</h4>
				</a>
				<p>In the nominal case where no specific configuration is provided the UI will behave as follows:</p>
				<ul>
					<li>The Non Seekable parts are not visible</li>
					<li>The Seekable part is visible and represents the duration of the growing timeshift window</li>
					<li>The LIVE indicator is visible and placed at the right end side of the progress bar. It will disappear in case the StartOver stream has an end time and that end time is reached</li>
					<li>The time tooltip is visible and varies from 0 to the duration of the growing timeshift window</li>
					<li>The playback time is visible and represents playback time within the range [ 0 , duration of the timeshift window]</li>
					<li>The Media Duration is visible and represents the duration of the growing timeshift window until eventually the end time of the StartOver stream is reached</li>
				</ul>
				<p><img src="./STOV.png" alt=""></p>
				<a href="#startover-with-external-program-information" id="startover-with-external-program-information" style="color: inherit; text-decoration: none;">
					<h4>StartOver with external program information</h4>
				</a>
				<p>External program infomation may be provided. If provided the <code>startTime</code> and <code>endTime</code> shall be aligned with the start time and end time of the StartOver stream. If not the progress bar UI may not behave as expected.
				In that case:</p>
				<ul>
					<li>The Non Seekable parts are visible</li>
					<li>The Seekable part is visible and represents the duration of the growing timeshift window</li>
					<li>The LIVE indicator is visible and placed at the right end side of the seekable progress bar. It will disappear in case the StartOver stream has an end time and that end time is reached</li>
					<li>The time tooltip is visible and varies from <code>startTime</code> to (<code>startTime</code> + the duration of the growing timeshift window)</li>
					<li>The playback time is visible and represents playback time within the range [ <code>startTime</code> , (<code>startTime</code> + the duration of the growing timeshift window)]</li>
					<li>The Media Duration is visible and represents the duration of the growing timeshift window until eventually the end time of the StartOver stream is reached</li>
				</ul>
				<p><img src="./STOV-external-PI.png" alt=""></p>
				<p>Configuration will be performed as follows:</p>
				<pre><code class="language-javascript"><span style="color: #008000">// your code to retrieve program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        }</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#updating-external-program-timing-information" id="updating-external-program-timing-information" style="color: inherit; text-decoration: none;">
					<h3>Updating External Program Timing information</h3>
				</a>
				<p>For Live use cases, when initial external program information is provided and when Live time reaches program <code>endTime</code> it is desired to update the external Program information with the time of the next program.
				To do so the integrator may provide via the configuration the definition of the <code>onEndProgramReached</code> callback.</p>
				<p>This callback shall be provided as follows via the configuration:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">endProgramReachedCallback</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {};</span>

<span style="color: #000000">    </span><span style="color: #008000">// your code to retrieve program information from the backend for the next program</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">nextProgramStartTime</span><span style="color: #000000"> = ...;</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">nextProgramEndTime</span><span style="color: #000000"> = ...;</span>

<span style="color: #000000">    </span><span style="color: #001080">configs</span><span style="color: #000000">.</span><span style="color: #001080">externalProgramInfo</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">nextProgramStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">nextProgramEndTime</span>
<span style="color: #000000">    }</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #008000">// your code to retrieve initial program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        },</span>
<span style="color: #000000">        </span><span style="color: #001080">onEndProgramReached:endProgramReachedCallback</span>
<span style="color: #000000">    };</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<a href="#combining-live-url-and-startover-url" id="combining-live-url-and-startover-url" style="color: inherit; text-decoration: none;">
					<h3>Combining Live URL and StartOver URL</h3>
				</a>
				<p>A more advanced use case but widely used one is the combination of manifest URLs for Live stream (with small time shift window) and StartOver stream (with large time shift window).
				This allows the service to set very fast servers (with SSD and fast memory) for Live stream where user demand is high and lower server configuration for StartOver stream where user demand is lower.</p>
				<p>The switch between Live URL and StartOver URL (and vice versa) may be triggered via the UI itself with the configuration of two callbacks: <code>onStartOver</code> and <code>onBackToLive</code>.</p>
				<p><code>onStartOver</code> callback will be trigered if the Live stream is playing and the user either Pause the stream of jump in the past within the progress bar.
				<code>onBackToLive</code> callback will be trigered if the StartOver stream is playing and the user clicks back on the Live button.</p>
				<p>Only one callback at a time shall be provided via the configuration, the other one shall be let to <code>undefined</code>
				These callbacks shall be provided as follows via the configuration:</p>
				<pre><code class="language-javascript"><span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">onStartOver</span><span style="color: #000000"> = (</span><span style="color: #001080">startTime</span><span style="color: #000000">) </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {};</span>

<span style="color: #000000">    </span><span style="color: #008000">//Your code to retrieve the StartOver URL from the backend</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">startOverStreamUrl</span><span style="color: #000000"> = ... ;</span>

<span style="color: #000000">    </span><span style="color: #008000">//Your function to load the media calling player.reset, player.configure, player.load</span>
<span style="color: #000000">    </span><span style="color: #795E26">loadMedia</span><span style="color: #000000">({</span>
<span style="color: #000000">        </span><span style="color: #001080">url:</span><span style="color: #000000"> </span><span style="color: #001080">startOverStreamUrl</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">startTime</span><span style="color: #000000">,</span>
<span style="color: #000000">    });</span>

<span style="color: #000000">    </span><span style="color: #008000">//!!! Important you shall update the config with the onBackToLive defined callback and let onStartOver undefined</span>
<span style="color: #000000">    </span><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">onStartOver</span><span style="color: #000000"> = </span><span style="color: #0000FF">undefined</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">onBackToLive</span><span style="color: #000000"> = </span><span style="color: #001080">onBackToLive</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #0000FF">const</span><span style="color: #000000"> </span><span style="color: #795E26">onBackToLive</span><span style="color: #000000"> = () </span><span style="color: #0000FF">=&gt;</span><span style="color: #000000"> {</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {};</span>

<span style="color: #000000">    </span><span style="color: #008000">//Your code to retrieve the Live URL from the backend</span>
<span style="color: #000000">    </span><span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">liveStreamUrl</span><span style="color: #000000"> = ... ;</span>

<span style="color: #000000">    </span><span style="color: #008000">//Your function to load the media calling player.reset, player.configure, player.load</span>
<span style="color: #000000">    </span><span style="color: #795E26">loadMedia</span><span style="color: #000000">({</span>
<span style="color: #000000">        </span><span style="color: #001080">url:</span><span style="color: #000000"> </span><span style="color: #001080">liveStreamUrl</span><span style="color: #000000">,</span>
<span style="color: #000000">    });</span>

<span style="color: #000000">    </span><span style="color: #008000">//!!! Important you shall update the config with the onStartOver defined callback and let onBackToLive undefined</span>
<span style="color: #000000">    </span><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">onBackToLive</span><span style="color: #000000"> = </span><span style="color: #0000FF">undefined</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #001080">config</span><span style="color: #000000">.</span><span style="color: #001080">onStartOver</span><span style="color: #000000"> = </span><span style="color: #001080">onStartOver</span><span style="color: #000000">;</span>
<span style="color: #000000">    </span><span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
<span style="color: #000000">}</span>

<span style="color: #008000">// your code to retrieve initial program information from the backend</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T17:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span><span style="color: #000000"> = ... </span><span style="color: #008000">// example &#039;2020-08-31T18:00:00&#039;</span>
<span style="color: #0000FF">let</span><span style="color: #000000"> </span><span style="color: #001080">config</span><span style="color: #000000"> = {</span>
<span style="color: #000000">        </span><span style="color: #001080">externalProgramInfo:</span><span style="color: #000000"> {</span>
<span style="color: #000000">            </span><span style="color: #001080">startTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalStartTime</span><span style="color: #000000">,</span>
<span style="color: #000000">            </span><span style="color: #001080">endTime:</span><span style="color: #000000"> </span><span style="color: #001080">externalEndTime</span>
<span style="color: #000000">        },</span>

<span style="color: #000000">        </span><span style="color: #008000">//!!!! If the first time you wish to start on StartOver URL</span>
<span style="color: #000000">        </span><span style="color: #001080">onStartOver:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">onBackToLive:</span><span style="color: #000000"> </span><span style="color: #001080">onBackToLive</span><span style="color: #000000">,</span>

<span style="color: #000000">        </span><span style="color: #008000">//!!!! If the first time you wish to start on Live URL</span>
<span style="color: #000000">        </span><span style="color: #001080">onStartOver:</span><span style="color: #000000"> </span><span style="color: #001080">onStartOver</span><span style="color: #000000">,</span>
<span style="color: #000000">        </span><span style="color: #001080">onBackToLive:</span><span style="color: #000000"> </span><span style="color: #0000FF">undefined</span><span style="color: #000000">,</span>
<span style="color: #000000">};</span>
<span style="color: #001080">player</span><span style="color: #000000">.</span><span style="color: #001080">uiController</span><span style="color: #000000">.</span><span style="color: #795E26">updateConfiguration</span><span style="color: #000000">(</span><span style="color: #001080">config</span><span style="color: #000000">);</span>
</code></pre>
				<p>For better experience the configuration shall include the definition of the <code>onEndProgramReached</code> callback</p>
			</div>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent pp-active">
						<a href="../player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="playback-ui.html">Viaccess-<wbr><wbr>Orca UI integration</a>
					</li>
					<li class="current pp-nav pp-page pp-child">
						<a href="progressbar-control.html">Control of <wbr>Progress <wbr>Bar</a>
					</li>
					<li class=" pp-nav pp-page pp-child">
						<a href="ui-css-classes.html">Player UI default CSS</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class="label pp-nav pp-group">
						<span>API</span>
					</li>
					<li class=" ">
						<a href="../../../modules.html">Modules</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Errors.html">Errors</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Events.html">Events</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/HandleExternalThumbnails.html">Handle<wbr>External<wbr>Thumbnails</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewController.html">Multi<wbr>View<wbr>Controller</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewTypes.html">Multi<wbr>View<wbr>Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/MultiViewUIController.html">Multi<wbr>ViewUIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Player.html">Player</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerBPKExtended.html">PlayerBPKExtended</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/PlayerPlatforms.html">Player<wbr>Platforms</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/Types.html">Types</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/UIController.html">UIController</a>
					</li>
					<li class=" tsd-kind-module">
						<a href="../../../modules/VRController.html">VRController</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../../../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../../../assets/js/search.json"><' + '/script>');</script>
</body>
</html>