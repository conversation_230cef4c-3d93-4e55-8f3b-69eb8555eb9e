<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>AdvertisementConfiguration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface AdvertisementConfiguration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.AdvertisementConfiguration.html">AdvertisementConfiguration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p>Interface for advertisment configuration</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">AdvertisementConfiguration</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#adCallbackForTunnelSkipping" class="tsd-kind-icon">ad<wbr>Callback<wbr>For<wbr>Tunnel<wbr>Skipping</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#adHttpCustomHeaders" class="tsd-kind-icon">ad<wbr>Http<wbr>Custom<wbr>Headers</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#adRequestTimeout" class="tsd-kind-icon">ad<wbr>Request<wbr>Timeout</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#disableTunnelIfAdUnavailable" class="tsd-kind-icon">disable<wbr>Tunnel<wbr>IfAd<wbr>Unavailable</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#enableVmapUpToPoliciesForStartAt" class="tsd-kind-icon">enable<wbr>Vmap<wbr>UpTo<wbr>Policies<wbr>For<wbr>Start<wbr>At</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#loadVastWithVmap" class="tsd-kind-icon">load<wbr>Vast<wbr>With<wbr>Vmap</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#mainPlayerReloadCallback" class="tsd-kind-icon">main<wbr>Player<wbr>Reload<wbr>Callback</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#mainPlayerResumeCallback" class="tsd-kind-icon">main<wbr>Player<wbr>Resume<wbr>Callback</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#maxAdBitrate" class="tsd-kind-icon">max<wbr>AdBitrate</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#maxAdRequestParsingTime" class="tsd-kind-icon">max<wbr>AdRequest<wbr>Parsing<wbr>Time</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#maxAdResolutionHeight" class="tsd-kind-icon">max<wbr>AdResolution<wbr>Height</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#nonLinearClosable" class="tsd-kind-icon">non<wbr>Linear<wbr>Closable</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#nonLinearDurationOverride" class="tsd-kind-icon">non<wbr>Linear<wbr>Duration<wbr>Override</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#seekToAllowed" class="tsd-kind-icon">seek<wbr>ToAllowed</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#sendInfoEvent" class="tsd-kind-icon">send<wbr>Info<wbr>Event</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#useMainPlayerVideoElement" class="tsd-kind-icon">use<wbr>Main<wbr>Player<wbr>Video<wbr>Element</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vastReportLastProgressEvent" class="tsd-kind-icon">vast<wbr>Report<wbr>Last<wbr>Progress<wbr>Event</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vastTimeout" class="tsd-kind-icon">vast<wbr>Timeout</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vastV20SkipAllowed" class="tsd-kind-icon">vast<wbr>V20<wbr>Skip<wbr>Allowed</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vmapSeekBackwardPlayUpTo" class="tsd-kind-icon">vmap<wbr>Seek<wbr>Backward<wbr>Play<wbr>UpTo</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vmapSeekForwardPlayUpTo" class="tsd-kind-icon">vmap<wbr>Seek<wbr>Forward<wbr>Play<wbr>UpTo</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vmapSkipOffsetOverride" class="tsd-kind-icon">vmap<wbr>Skip<wbr>Offset<wbr>Override</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvertisementConfiguration.html#vmapTimeout" class="tsd-kind-icon">vmap<wbr>Timeout</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="adCallbackForTunnelSkipping" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ad<wbr>Callback<wbr>For<wbr>Tunnel<wbr>Skipping</h3>
					<div class="tsd-signature tsd-kind-icon">ad<wbr>Callback<wbr>For<wbr>Tunnel<wbr>Skipping<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>adBreaksInfo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-interface">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>adBreaksInfo<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>Configures the VO Player to wait for a response from the application before proceeding with the ad tunnel playback.
												This can be used to control whether an ad tunnel should be skipped when preloaded.</p>
											</div>
											<p>When the VO Player calls the callback adCallbackForTunnelSkipping, the application should respond
											by calling the skipAdTunnel method to indicate whether the ad tunnel should be skipped or played.</p>
										</div>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>adBreaksInfo: <span class="tsd-signature-symbol">{}</span></h5>
												<ul class="tsd-parameters">
													<li class="tsd-parameter-index-signature">
														<h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]: </span><a href="Types.AdvertisementAdBreakInfo.html" class="tsd-signature-type">AdvertisementAdBreakInfo</a></h5>
													</li>
												</ul>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="adHttpCustomHeaders" class="tsd-anchor"></a>
					<h3>ad<wbr>Http<wbr>Custom<wbr>Headers</h3>
					<div class="tsd-signature tsd-kind-icon">ad<wbr>Http<wbr>Custom<wbr>Headers<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Provides an array of strings representing the HTTP custom headers to be set when making an advertisement call.
								If not provided, no custom headers will be set.
								Example value: { &quot;headerKey1&quot;: &quot;headerValue1&quot;, &quot;headerKey2&quot;: &quot;headerValue2&quot; }
							Default value: {}</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="adRequestTimeout" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ad<wbr>Request<wbr>Timeout</h3>
					<div class="tsd-signature tsd-kind-icon">ad<wbr>Request<wbr>Timeout<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Timeout value in seconds for any agnostic ad calls (default 5s)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="disableTunnelIfAdUnavailable" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> disable<wbr>Tunnel<wbr>IfAd<wbr>Unavailable</h3>
					<div class="tsd-signature tsd-kind-icon">disable<wbr>Tunnel<wbr>IfAd<wbr>Unavailable<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>This API indicates to VO Player to discard all the tunnel if an ad is unavailable in a tunnel
							Default value: false</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="enableVmapUpToPoliciesForStartAt" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr>Vmap<wbr>UpTo<wbr>Policies<wbr>For<wbr>Start<wbr>At</h3>
					<div class="tsd-signature tsd-kind-icon">enable<wbr>Vmap<wbr>UpTo<wbr>Policies<wbr>For<wbr>Start<wbr>At<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Boolean indicating if Mid roll advertisements before start position can be played when the argument is set to true.
							By default, the value is false and only preroll will be played if present in VMAP or MAST response</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="loadVastWithVmap" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> load<wbr>Vast<wbr>With<wbr>Vmap</h3>
					<div class="tsd-signature tsd-kind-icon">load<wbr>Vast<wbr>With<wbr>Vmap<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>By default when parsing a MAST or VMAP response, all included VAST will be loaded to build the varions tunnels expected during the playback of the content
								This might be problematic when a content will include a lot of ad breaks with a lot of advertisement in each ad breaks.
								Consequently you may set the boolean <code>loadVastWithVmap</code> to false so that the VAST are requested at the time when the adBreak occurs.
							Default value: true</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mainPlayerReloadCallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> main<wbr>Player<wbr>Reload<wbr>Callback</h3>
					<div class="tsd-signature tsd-kind-icon">main<wbr>Player<wbr>Reload<wbr>Callback<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>manifestUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, configuration<span class="tsd-signature-symbol">: </span><a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-interface">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>manifestUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, configuration<span class="tsd-signature-symbol">: </span><a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>Callback to the application to reload the content after a postroll advertisment.
													Only applicable in combination with useMainPlayerVideoElement set to true.
													If not provided the player will reload the main content based on the url and configuration previously provided.
													If provided the application will be in charge to reload the main content.
												Use case: it is in particular required when manifest url or license url depends on token that may expire.</p>
											</div>
										</div>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>manifestUrl: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>configuration: <a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mainPlayerResumeCallback" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> main<wbr>Player<wbr>Resume<wbr>Callback</h3>
					<div class="tsd-signature tsd-kind-icon">main<wbr>Player<wbr>Resume<wbr>Callback<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>manifestUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, configuration<span class="tsd-signature-symbol">: </span><a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a>, resumeTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter-signature">
								<ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-interface">
									<li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>manifestUrl<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, configuration<span class="tsd-signature-symbol">: </span><a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a>, resumeTime<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></li>
								</ul>
								<ul class="tsd-descriptions">
									<li class="tsd-description">
										<div class="tsd-comment tsd-typography">
											<div class="lead">
												<p>Callback to the application to resume the playback of the main content after the playback of an advertisement.
													Only applicable in combination with useMainPlayerVideoElement set to true.
													If not provided the player will resume the main content based on the url and configuration previously provided.
													If provided the application will be in charge to resume the main content.
												Use case: it is in particular required when manifest url or license url depends on token that may expire.</p>
											</div>
										</div>
										<h4 class="tsd-parameters-title">Parameters</h4>
										<ul class="tsd-parameters">
											<li>
												<h5>manifestUrl: <span class="tsd-signature-type">string</span></h5>
											</li>
											<li>
												<h5>configuration: <a href="Types.PlayerConfiguration.html" class="tsd-signature-type">PlayerConfiguration</a></h5>
											</li>
											<li>
												<h5>resumeTime: <span class="tsd-signature-type">number</span></h5>
											</li>
										</ul>
										<h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></h4>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="maxAdBitrate" class="tsd-anchor"></a>
					<h3>max<wbr>AdBitrate</h3>
					<div class="tsd-signature tsd-kind-icon">max<wbr>AdBitrate<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Defines the maximum value in kbps to limit an ad bitrate. If not provided, there will be no limitation on the ad bitrate.
							Default value: -1</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="maxAdRequestParsingTime" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> max<wbr>AdRequest<wbr>Parsing<wbr>Time</h3>
					<div class="tsd-signature tsd-kind-icon">max<wbr>AdRequest<wbr>Parsing<wbr>Time<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Specifies the maximum time, in milliseconds, allowed for parsing an ad request.
								If the ad request parsing time exceeds this limit, the process will be interrupted,
								triggering an internal &#39;reset&#39; event to halt the <code>loadAdRequest</code> function and avoid unnecessary delays.
								Setting this parameter allows finer control over ad-loading performance,
							ensuring a quick timeout when ad request parsing takes longer than expected.</p>
						</div>
						<p>By default, this parameter is undefined, meaning there is no timeout restriction on ad request parsing.</p>
						<p>Example:
						To set a 100 ms maximum parsing time, use <code>maxAdRequestParsingTime: 100</code>.</p>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="maxAdResolutionHeight" class="tsd-anchor"></a>
					<h3>max<wbr>AdResolution<wbr>Height</h3>
					<div class="tsd-signature tsd-kind-icon">max<wbr>AdResolution<wbr>Height<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Defines the maximum value in pixels to limit an ad resolution. If not provided, there will be no limitation on the ad resolution.
							Default value: -1</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="nonLinearClosable" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> non<wbr>Linear<wbr>Closable</h3>
					<div class="tsd-signature tsd-kind-icon">non<wbr>Linear<wbr>Closable<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Indicates whether the non-linear advertisement is closable by the user.
							Default value: false</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="nonLinearDurationOverride" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> non<wbr>Linear<wbr>Duration<wbr>Override</h3>
					<div class="tsd-signature tsd-kind-icon">non<wbr>Linear<wbr>Duration<wbr>Override<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Defines the duration of all non-linear ads that are received via a VAST request.
							Default value: -1, in this case the duration of the non-linear ad is the one provided in the VAST response.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="seekToAllowed" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> seek<wbr>ToAllowed</h3>
					<div class="tsd-signature tsd-kind-icon">seek<wbr>ToAllowed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configures whether seeking during advertisement playback is allowed.
								By default, seeking during advertisements is forbidden both via the UI and the API.
								When seeking is attempted during an ad with seeking disabled, a warning event is triggered with the error code ADS_LINEAR_SEEK_FORBIDDEN.
								This method allows you to change this behavior by specifying whether seeking should be permitted during ads.
							Default value: false</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sendInfoEvent" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> send<wbr>Info<wbr>Event</h3>
					<div class="tsd-signature tsd-kind-icon">send<wbr>Info<wbr>Event<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Specifies if event infoEvent should be sent or not</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="useMainPlayerVideoElement" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> use<wbr>Main<wbr>Player<wbr>Video<wbr>Element</h3>
					<div class="tsd-signature tsd-kind-icon">use<wbr>Main<wbr>Player<wbr>Video<wbr>Element<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Indicate if the video element provided to the main content player shall be used also for the advertisment player
								Use case: low end platforms where only one video element could be instantiated (e.g. some SmartTvs or USB Dongles).
								Impact: less smooth transitions between main content and advertisment.
							Default value: false.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vastReportLastProgressEvent" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vast<wbr>Report<wbr>Last<wbr>Progress<wbr>Event</h3>
					<div class="tsd-signature tsd-kind-icon">vast<wbr>Report<wbr>Last<wbr>Progress<wbr>Event<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>This API indicates if the SDK will or not report only the last VAST progress tracking event.
							Default value: false</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vastTimeout" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vast<wbr>Timeout</h3>
					<div class="tsd-signature tsd-kind-icon">vast<wbr>Timeout<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Timeout value in seconds for any VAST calls (default 5s)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vastV20SkipAllowed" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vast<wbr>V20<wbr>Skip<wbr>Allowed</h3>
					<div class="tsd-signature tsd-kind-icon">vast<wbr>V20<wbr>Skip<wbr>Allowed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>This API indicates to VO Player to allow or forbid skipping of VAST v2.0 ads
							Default value: true</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vmapSeekBackwardPlayUpTo" class="tsd-anchor"></a>
					<h3>vmap<wbr>Seek<wbr>Backward<wbr>Play<wbr>UpTo</h3>
					<div class="tsd-signature tsd-kind-icon">vmap<wbr>Seek<wbr>Backward<wbr>Play<wbr>UpTo<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>vmapSeekBackwardPlayUpTo: Configures the maximum number of ads to replay when seeking backward in the content.
							int vmapSeekBackwardPlayUpTo; // integer varying from -1 (all ads will be replayed) to N defined by the application/integrator. playback position remains the one scheduled by the vmap. default value 0 (ads already played are not replayed)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vmapSeekForwardPlayUpTo" class="tsd-anchor"></a>
					<h3>vmap<wbr>Seek<wbr>Forward<wbr>Play<wbr>UpTo</h3>
					<div class="tsd-signature tsd-kind-icon">vmap<wbr>Seek<wbr>Forward<wbr>Play<wbr>UpTo<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configuration option defined by the application/integrator to control the number of ads played when seeking forward in the content.</p>
						</div>
						<p>Values:</p>
						<ul>
							<li><code>-1</code> : All scheduled ads will be played (default behavior).</li>
							<li><code>0</code>  : No ads will be played after the seek position.</li>
							<li><code>N</code>  : Only the first <code>N</code> ads after the seek position will be played.</li>
						</ul>
						<p>The playback position will start at the seek position, and all ads to be played are stacked.</p>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vmapSkipOffsetOverride" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vmap<wbr>Skip<wbr>Offset<wbr>Override</h3>
					<div class="tsd-signature tsd-kind-icon">vmap<wbr>Skip<wbr>Offset<wbr>Override<span class="tsd-signature-symbol">:</span> <a href="Types.VmapSkipOffset.html" class="tsd-signature-type">VmapSkipOffset</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configuration for VMAP skip offset overriding.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vmapTimeout" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vmap<wbr>Timeout</h3>
					<div class="tsd-signature tsd-kind-icon">vmap<wbr>Timeout<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Timeout value in seconds for any VMAP calls (default 5s)</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-interface tsd-parent-kind-module">
						<a href="Types.AdvertisementConfiguration.html" class="tsd-kind-icon">Advertisement<wbr>Configuration</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#adCallbackForTunnelSkipping" class="tsd-kind-icon">ad<wbr>Callback<wbr>For<wbr>Tunnel<wbr>Skipping</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#adHttpCustomHeaders" class="tsd-kind-icon">ad<wbr>Http<wbr>Custom<wbr>Headers</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#adRequestTimeout" class="tsd-kind-icon">ad<wbr>Request<wbr>Timeout</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#disableTunnelIfAdUnavailable" class="tsd-kind-icon">disable<wbr>Tunnel<wbr>IfAd<wbr>Unavailable</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#enableVmapUpToPoliciesForStartAt" class="tsd-kind-icon">enable<wbr>Vmap<wbr>UpTo<wbr>Policies<wbr>For<wbr>Start<wbr>At</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#loadVastWithVmap" class="tsd-kind-icon">load<wbr>Vast<wbr>With<wbr>Vmap</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#mainPlayerReloadCallback" class="tsd-kind-icon">main<wbr>Player<wbr>Reload<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#mainPlayerResumeCallback" class="tsd-kind-icon">main<wbr>Player<wbr>Resume<wbr>Callback</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#maxAdBitrate" class="tsd-kind-icon">max<wbr>AdBitrate</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#maxAdRequestParsingTime" class="tsd-kind-icon">max<wbr>AdRequest<wbr>Parsing<wbr>Time</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#maxAdResolutionHeight" class="tsd-kind-icon">max<wbr>AdResolution<wbr>Height</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#nonLinearClosable" class="tsd-kind-icon">non<wbr>Linear<wbr>Closable</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#nonLinearDurationOverride" class="tsd-kind-icon">non<wbr>Linear<wbr>Duration<wbr>Override</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#seekToAllowed" class="tsd-kind-icon">seek<wbr>ToAllowed</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#sendInfoEvent" class="tsd-kind-icon">send<wbr>Info<wbr>Event</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#useMainPlayerVideoElement" class="tsd-kind-icon">use<wbr>Main<wbr>Player<wbr>Video<wbr>Element</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vastReportLastProgressEvent" class="tsd-kind-icon">vast<wbr>Report<wbr>Last<wbr>Progress<wbr>Event</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vastTimeout" class="tsd-kind-icon">vast<wbr>Timeout</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vastV20SkipAllowed" class="tsd-kind-icon">vast<wbr>V20<wbr>Skip<wbr>Allowed</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vmapSeekBackwardPlayUpTo" class="tsd-kind-icon">vmap<wbr>Seek<wbr>Backward<wbr>Play<wbr>UpTo</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vmapSeekForwardPlayUpTo" class="tsd-kind-icon">vmap<wbr>Seek<wbr>Forward<wbr>Play<wbr>UpTo</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vmapSkipOffsetOverride" class="tsd-kind-icon">vmap<wbr>Skip<wbr>Offset<wbr>Override</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvertisementConfiguration.html#vmapTimeout" class="tsd-kind-icon">vmap<wbr>Timeout</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>