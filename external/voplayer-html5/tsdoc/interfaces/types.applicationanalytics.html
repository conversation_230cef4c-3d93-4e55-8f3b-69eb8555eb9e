<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ApplicationAnalytics | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface ApplicationAnalytics</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.ApplicationAnalytics.html">ApplicationAnalytics</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p>Interface depicting some application analytics set by the application</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">ApplicationAnalytics</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#mdaApplicationLanguage" class="tsd-kind-icon">mda<wbr>Application<wbr>Language</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#mdaApplicationName" class="tsd-kind-icon">mda<wbr>Application<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#mdaApplicationVersion" class="tsd-kind-icon">mda<wbr>Application<wbr>Version</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#mdaDeviceOperator" class="tsd-kind-icon">mda<wbr>Device<wbr>Operator</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesCdnSessionId" class="tsd-kind-icon">ses<wbr>Cdn<wbr>Session<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentBdcastStart" class="tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Start</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentBdcastStop" class="tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Stop</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentEpisodeNb" class="tsd-kind-icon">ses<wbr>Content<wbr>Episode<wbr>Nb</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentGenre" class="tsd-kind-icon">ses<wbr>Content<wbr>Genre</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentId" class="tsd-kind-icon">ses<wbr>Content<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentLanguage" class="tsd-kind-icon">ses<wbr>Content<wbr>Language</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentName" class="tsd-kind-icon">ses<wbr>Content<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentParentalRating" class="tsd-kind-icon">ses<wbr>Content<wbr>Parental<wbr>Rating</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentProgramId" class="tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentProgramName" class="tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Name</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentProvider" class="tsd-kind-icon">ses<wbr>Content<wbr>Provider</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentSeasonNb" class="tsd-kind-icon">ses<wbr>Content<wbr>Season<wbr>Nb</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesContentType" class="tsd-kind-icon">ses<wbr>Content<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesPlaybackStartupTime" class="tsd-kind-icon">ses<wbr>Playback<wbr>Startup<wbr>Time</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionAccountId" class="tsd-kind-icon">ses<wbr>Session<wbr>Account<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionProfileType" class="tsd-kind-icon">ses<wbr>Session<wbr>Profile<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionSerialId" class="tsd-kind-icon">ses<wbr>Session<wbr>Serial<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionSubscriptionType" class="tsd-kind-icon">ses<wbr>Session<wbr>Subscription<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionType" class="tsd-kind-icon">ses<wbr>Session<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.ApplicationAnalytics.html#sesSessionUserId" class="tsd-kind-icon">ses<wbr>Session<wbr>User<wbr>Id</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mdaApplicationLanguage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> mda<wbr>Application<wbr>Language</h3>
					<div class="tsd-signature tsd-kind-icon">mda<wbr>Application<wbr>Language<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Language used by the Application set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mdaApplicationName" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> mda<wbr>Application<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">mda<wbr>Application<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Name of the Application set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mdaApplicationVersion" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> mda<wbr>Application<wbr>Version</h3>
					<div class="tsd-signature tsd-kind-icon">mda<wbr>Application<wbr>Version<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Version of the Application set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="mdaDeviceOperator" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> mda<wbr>Device<wbr>Operator</h3>
					<div class="tsd-signature tsd-kind-icon">mda<wbr>Device<wbr>Operator<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Name of the operator set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesCdnSessionId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Cdn<wbr>Session<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Cdn<wbr>Session<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>CDN ID set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentBdcastStart" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Bdcast<wbr>Start</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Start<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentBdcastStop" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Bdcast<wbr>Stop</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Stop<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentEpisodeNb" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Episode<wbr>Nb</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Episode<wbr>Nb<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Episode Number with a Season of a Series</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentGenre" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Genre</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Genre<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Genres associated to content, if several available should be concatenated and separated with coma</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>ID of the content or channel whatched set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentLanguage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Language</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Language<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Latest playback audio language set by the end user</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentName" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Name of the content or channel watched set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentParentalRating" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Parental<wbr>Rating</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Parental<wbr>Rating<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>String representing the parental rating (PR) of the content</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentProgramId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Program<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Id of the program watched</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentProgramName" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Program<wbr>Name</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Name of the program watched</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentProvider" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Provider</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Provider<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>String representing the provider of the content</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentSeasonNb" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Season<wbr>Nb</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Season<wbr>Nb<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Season Number for a Series</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesContentType" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Content<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Content<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Type of content</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesPlaybackStartupTime" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Playback<wbr>Startup<wbr>Time</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Playback<wbr>Startup<wbr>Time<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The duration in milliseconds between the user input to start a playback and the first frame displayed callback</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionAccountId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>Account<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>Account<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Account/Household ID set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionProfileType" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>Profile<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>Profile<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>User profile type set by the application (eg.: classic, kid...)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionSerialId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>Serial<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>Serial<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The serial number id of the device set by the application</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionSubscriptionType" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>Subscription<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>Subscription<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Type or tier of subscription associated to account</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionType" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Type of the session set by the application (eg.: catchup, npvr, catchup-ad, live, VoD)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sesSessionUserId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ses<wbr>Session<wbr>User<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">ses<wbr>Session<wbr>User<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>User profile id set by the application (to uniquely identify a user)</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-interface tsd-parent-kind-module">
						<a href="Types.ApplicationAnalytics.html" class="tsd-kind-icon">Application<wbr>Analytics</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#mdaApplicationLanguage" class="tsd-kind-icon">mda<wbr>Application<wbr>Language</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#mdaApplicationName" class="tsd-kind-icon">mda<wbr>Application<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#mdaApplicationVersion" class="tsd-kind-icon">mda<wbr>Application<wbr>Version</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#mdaDeviceOperator" class="tsd-kind-icon">mda<wbr>Device<wbr>Operator</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesCdnSessionId" class="tsd-kind-icon">ses<wbr>Cdn<wbr>Session<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentBdcastStart" class="tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Start</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentBdcastStop" class="tsd-kind-icon">ses<wbr>Content<wbr>Bdcast<wbr>Stop</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentEpisodeNb" class="tsd-kind-icon">ses<wbr>Content<wbr>Episode<wbr>Nb</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentGenre" class="tsd-kind-icon">ses<wbr>Content<wbr>Genre</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentId" class="tsd-kind-icon">ses<wbr>Content<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentLanguage" class="tsd-kind-icon">ses<wbr>Content<wbr>Language</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentName" class="tsd-kind-icon">ses<wbr>Content<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentParentalRating" class="tsd-kind-icon">ses<wbr>Content<wbr>Parental<wbr>Rating</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentProgramId" class="tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentProgramName" class="tsd-kind-icon">ses<wbr>Content<wbr>Program<wbr>Name</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentProvider" class="tsd-kind-icon">ses<wbr>Content<wbr>Provider</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentSeasonNb" class="tsd-kind-icon">ses<wbr>Content<wbr>Season<wbr>Nb</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesContentType" class="tsd-kind-icon">ses<wbr>Content<wbr>Type</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesPlaybackStartupTime" class="tsd-kind-icon">ses<wbr>Playback<wbr>Startup<wbr>Time</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionAccountId" class="tsd-kind-icon">ses<wbr>Session<wbr>Account<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionProfileType" class="tsd-kind-icon">ses<wbr>Session<wbr>Profile<wbr>Type</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionSerialId" class="tsd-kind-icon">ses<wbr>Session<wbr>Serial<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionSubscriptionType" class="tsd-kind-icon">ses<wbr>Session<wbr>Subscription<wbr>Type</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionType" class="tsd-kind-icon">ses<wbr>Session<wbr>Type</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.ApplicationAnalytics.html#sesSessionUserId" class="tsd-kind-icon">ses<wbr>Session<wbr>User<wbr>Id</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>