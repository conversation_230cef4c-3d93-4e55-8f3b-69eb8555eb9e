<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>InternalVmapBreak | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface InternalVmapBreak</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.InternalVmapBreak.html">InternalVmapBreak</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">InternalVmapBreak</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#adSource" class="tsd-kind-icon">ad<wbr>Source</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#adSources" class="tsd-kind-icon">ad<wbr>Sources</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#breakId" class="tsd-kind-icon">break<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#breakType" class="tsd-kind-icon">break<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#extensions" class="tsd-kind-icon">extensions</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#repeatAfter" class="tsd-kind-icon">repeat<wbr>After</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#timeOffset" class="tsd-kind-icon">time<wbr>Offset</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#trackingEvents" class="tsd-kind-icon">tracking<wbr>Events</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.InternalVmapBreak.html#vastData" class="tsd-kind-icon">vast<wbr>Data</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="adSource" class="tsd-anchor"></a>
					<h3>ad<wbr>Source</h3>
					<div class="tsd-signature tsd-kind-icon">ad<wbr>Source<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="adSources" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ad<wbr>Sources</h3>
					<div class="tsd-signature tsd-kind-icon">ad<wbr>Sources<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="breakId" class="tsd-anchor"></a>
					<h3>break<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">break<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="breakType" class="tsd-anchor"></a>
					<h3>break<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">break<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="extensions" class="tsd-anchor"></a>
					<h3>extensions</h3>
					<div class="tsd-signature tsd-kind-icon">extensions<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="repeatAfter" class="tsd-anchor"></a>
					<h3>repeat<wbr>After</h3>
					<div class="tsd-signature tsd-kind-icon">repeat<wbr>After<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="timeOffset" class="tsd-anchor"></a>
					<h3>time<wbr>Offset</h3>
					<div class="tsd-signature tsd-kind-icon">time<wbr>Offset<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="trackingEvents" class="tsd-anchor"></a>
					<h3>tracking<wbr>Events</h3>
					<div class="tsd-signature tsd-kind-icon">tracking<wbr>Events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vastData" class="tsd-anchor"></a>
					<h3>vast<wbr>Data</h3>
					<div class="tsd-signature tsd-kind-icon">vast<wbr>Data<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-interface tsd-parent-kind-module">
						<a href="Types.InternalVmapBreak.html" class="tsd-kind-icon">Internal<wbr>Vmap<wbr>Break</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#adSource" class="tsd-kind-icon">ad<wbr>Source</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#adSources" class="tsd-kind-icon">ad<wbr>Sources</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#breakId" class="tsd-kind-icon">break<wbr>Id</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#breakType" class="tsd-kind-icon">break<wbr>Type</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#extensions" class="tsd-kind-icon">extensions</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#repeatAfter" class="tsd-kind-icon">repeat<wbr>After</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#timeOffset" class="tsd-kind-icon">time<wbr>Offset</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#trackingEvents" class="tsd-kind-icon">tracking<wbr>Events</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.InternalVmapBreak.html#vastData" class="tsd-kind-icon">vast<wbr>Data</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>