<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>AdvancedDrmConfiguration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface AdvancedDrmConfiguration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.AdvancedDrmConfiguration.html">AdvancedDrmConfiguration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p>Interface for Advanced DRM configuration</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">AdvancedDrmConfiguration</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#audioRobustness" class="tsd-kind-icon">audio<wbr>Robustness</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#distinctiveIdentifierRequired" class="tsd-kind-icon">distinctive<wbr>Identifier<wbr>Required</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#httpRequestHeaders" class="tsd-kind-icon">http<wbr>Request<wbr>Headers</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#persistentStateRequired" class="tsd-kind-icon">persistent<wbr>State<wbr>Required</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#serverCertificate" class="tsd-kind-icon">server<wbr>Certificate</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#serverCertificateUrl" class="tsd-kind-icon">server<wbr>Certificate<wbr>Url</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#serverURL" class="tsd-kind-icon">serverURL</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#sessionIds" class="tsd-kind-icon">session<wbr>Ids</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.AdvancedDrmConfiguration.html#videoRobustness" class="tsd-kind-icon">video<wbr>Robustness</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="audioRobustness" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> audio<wbr>Robustness</h3>
					<div class="tsd-signature tsd-kind-icon">audio<wbr>Robustness<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A key-system-specific string that specifies a required security level for audio. If the security level requested is not available on the platform, playback will fail. Defaults to &#39;&#39;, i.e., no specific robustness required.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="distinctiveIdentifierRequired" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> distinctive<wbr>Identifier<wbr>Required</h3>
					<div class="tsd-signature tsd-kind-icon">distinctive<wbr>Identifier<wbr>Required<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>True if the application requires the key system to support distinctive identifiers.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="httpRequestHeaders" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> http<wbr>Request<wbr>Headers</h3>
					<div class="tsd-signature tsd-kind-icon">http<wbr>Request<wbr>Headers<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>X-AxDRM-Message<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>x-dt-custom-data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>httpRequestHeaders contain header to pass for encrypted video request x-dt-custom-data</p>
						</div>
					</div>
					<div class="tsd-type-declaration">
						<h4>Type declaration</h4>
						<ul class="tsd-parameters">
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> X-<wbr><wbr>AxDRM-<wbr><wbr>Message<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
							<li class="tsd-parameter">
								<h5><span class="tsd-flag ts-flagOptional">Optional</span> x-<wbr>dt-<wbr>custom-<wbr>data<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5>
							</li>
						</ul>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="persistentStateRequired" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> persistent<wbr>State<wbr>Required</h3>
					<div class="tsd-signature tsd-kind-icon">persistent<wbr>State<wbr>Required<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>True if the application requires the key system to support persistent state, e.g., for persistent license storage.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="serverCertificate" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> server<wbr>Certificate</h3>
					<div class="tsd-signature tsd-kind-icon">server<wbr>Certificate<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Uint8Array</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A key-system-specific server certificate used to encrypt license requests. Its use is optional and is meant as an optimization to avoid a round-trip to request a certificate.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="serverCertificateUrl" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> server<wbr>Certificate<wbr>Url</h3>
					<div class="tsd-signature tsd-kind-icon">server<wbr>Certificate<wbr>Url<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The URL to download a key-system-specific server certificate used to encrypt license requests. Its use is optional.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="serverURL" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> serverURL</h3>
					<div class="tsd-signature tsd-kind-icon">serverURL<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>license server url</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="sessionIds" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> session<wbr>Ids</h3>
					<div class="tsd-signature tsd-kind-icon">session<wbr>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>IDs of persistent sessions to be loaded</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="videoRobustness" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> video<wbr>Robustness</h3>
					<div class="tsd-signature tsd-kind-icon">video<wbr>Robustness<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A key-system-specific string that specifies a required security level for video. If the security level requested is not available on the platform, playback will fail. Defaults to &#39;&#39;, i.e., no specific robustness required.</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-interface tsd-parent-kind-module">
						<a href="Types.AdvancedDrmConfiguration.html" class="tsd-kind-icon">Advanced<wbr>Drm<wbr>Configuration</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#audioRobustness" class="tsd-kind-icon">audio<wbr>Robustness</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#distinctiveIdentifierRequired" class="tsd-kind-icon">distinctive<wbr>Identifier<wbr>Required</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#httpRequestHeaders" class="tsd-kind-icon">http<wbr>Request<wbr>Headers</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#persistentStateRequired" class="tsd-kind-icon">persistent<wbr>State<wbr>Required</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#serverCertificate" class="tsd-kind-icon">server<wbr>Certificate</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#serverCertificateUrl" class="tsd-kind-icon">server<wbr>Certificate<wbr>Url</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#serverURL" class="tsd-kind-icon">serverURL</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#sessionIds" class="tsd-kind-icon">session<wbr>Ids</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.AdvancedDrmConfiguration.html#videoRobustness" class="tsd-kind-icon">video<wbr>Robustness</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>