<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>PlayerConfiguration | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface PlayerConfiguration</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.PlayerConfiguration.html">PlayerConfiguration</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-comment">
				<div class="tsd-comment tsd-typography">
					<div class="lead">
						<p>Interface depicting player configuration parameters</p>
					</div>
				</div>
			</section>
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="target">PlayerConfiguration</span>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#abr" class="tsd-kind-icon">abr</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#ads" class="tsd-kind-icon">ads</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#applicationAnalytics" class="tsd-kind-icon">application<wbr>Analytics</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#autostart" class="tsd-kind-icon">autostart</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#dashConfiguration" class="tsd-kind-icon">dash<wbr>Configuration</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#drm" class="tsd-kind-icon">drm</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#enableSmartLibAdvertisement" class="tsd-kind-icon">enable<wbr>Smart<wbr>Lib<wbr>Advertisement</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#events" class="tsd-kind-icon">events</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#externalThumbnails" class="tsd-kind-icon">external<wbr>Thumbnails</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#forceAppleNative" class="tsd-kind-icon">force<wbr>Apple<wbr>Native</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#hlsConfiguration" class="tsd-kind-icon">hls<wbr>Configuration</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#iosPlaysInline" class="tsd-kind-icon">ios<wbr>Plays<wbr>Inline</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#isEventAutomatic" class="tsd-kind-icon">is<wbr>Event<wbr>Automatic</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#isEventStream" class="tsd-kind-icon">is<wbr>Event<wbr>Stream</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#preferredAudioKind" class="tsd-kind-icon">preferred<wbr>Audio<wbr>Kind</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#preferredAudioLanguage" class="tsd-kind-icon">preferred<wbr>Audio<wbr>Language</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#preferredTextKind" class="tsd-kind-icon">preferred<wbr>Text<wbr>Kind</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#preferredTextLanguage" class="tsd-kind-icon">preferred<wbr>Text<wbr>Language</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#streaming" class="tsd-kind-icon">streaming</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#subtitles" class="tsd-kind-icon">subtitles</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#tizenNativeConfiguration" class="tsd-kind-icon">tizen<wbr>Native<wbr>Configuration</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#vowmk" class="tsd-kind-icon">vowmk</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="Types.PlayerConfiguration.html#webosNativeConfiguration" class="tsd-kind-icon">webos<wbr>Native<wbr>Configuration</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="abr" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> abr</h3>
					<div class="tsd-signature tsd-kind-icon">abr<span class="tsd-signature-symbol">:</span> <a href="Types.AbrConfiguration.html" class="tsd-signature-type">AbrConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>ABR configuration and settings</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="ads" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ads</h3>
					<div class="tsd-signature tsd-kind-icon">ads<span class="tsd-signature-symbol">:</span> <a href="Types.AdvertisementConfiguration.html" class="tsd-signature-type">AdvertisementConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Advertisement configuration</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="applicationAnalytics" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> application<wbr>Analytics</h3>
					<div class="tsd-signature tsd-kind-icon">application<wbr>Analytics<span class="tsd-signature-symbol">:</span> <a href="Types.ApplicationAnalytics.html" class="tsd-signature-type">ApplicationAnalytics</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>anayltics set by the application and send with PSR</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="autostart" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> autostart</h3>
					<div class="tsd-signature tsd-kind-icon">autostart<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>true if the player should start automatically</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="dashConfiguration" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> dash<wbr>Configuration</h3>
					<div class="tsd-signature tsd-kind-icon">dash<wbr>Configuration<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Object holding specific configuration for DASH core engine</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="drm" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> drm</h3>
					<div class="tsd-signature tsd-kind-icon">drm<span class="tsd-signature-symbol">:</span> <a href="Types.DrmConfiguration.html" class="tsd-signature-type">DrmConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>DRM configuraton and settings (valid only for DASH streams)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="enableSmartLibAdvertisement" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr>Smart<wbr>Lib<wbr>Advertisement</h3>
					<div class="tsd-signature tsd-kind-icon">enable<wbr>Smart<wbr>Lib<wbr>Advertisement<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Specifies the configuration for the Broadpeak SmartLib advertisement support. If set to &#39;true&#39;, the player will enable the advertisement support with Broadpeak SmartLib.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="events" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> events</h3>
					<div class="tsd-signature tsd-kind-icon">events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>schemeId to be supported. array of schemeID and callback</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="externalThumbnails" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> external<wbr>Thumbnails</h3>
					<div class="tsd-signature tsd-kind-icon">external<wbr>Thumbnails<span class="tsd-signature-symbol">:</span> <a href="Types.ExternalThumbnails.html" class="tsd-signature-type">ExternalThumbnails</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Object holding specific configuration for the extenal Thumbnails implementation</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="forceAppleNative" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> force<wbr>Apple<wbr>Native</h3>
					<div class="tsd-signature tsd-kind-icon">force<wbr>Apple<wbr>Native<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Specifies if an HLS clear content (non protected by Fairplay DRM) shall be loaded with Native Player</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="hlsConfiguration" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> hls<wbr>Configuration</h3>
					<div class="tsd-signature tsd-kind-icon">hls<wbr>Configuration<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Record</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Object holding specific configuration for HLS core engine (hls.js configuration type, not applied in Safari)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="iosPlaysInline" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> ios<wbr>Plays<wbr>Inline</h3>
					<div class="tsd-signature tsd-kind-icon">ios<wbr>Plays<wbr>Inline<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Specifies if the video willl be played inline on Safari iOS (when false, video will start in fullscreen), defaults to false</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="isEventAutomatic" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr>Event<wbr>Automatic</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Event<wbr>Automatic<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Determines if the stream type (Event/StartOver) is identified automatically. If &#39;true&#39;, the player computes it internally using &#39;seekRange().start&#39; as an offset to set the start time to 0. On DASH streams, at least one manifest update is required for detection. If &#39;false&#39; the integrator can specify this via &#39;isEventStream&#39;.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="isEventStream" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr>Event<wbr>Stream</h3>
					<div class="tsd-signature tsd-kind-icon">is<wbr>Event<wbr>Stream<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Indicates if the stream is an Event/StartOver stream. Considered only if &#39;isEventAutomatic&#39; is &#39;false&#39;. If &#39;true&#39; &#39;seekRange().start&#39; is used as the offset to set the start time to 0.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="preferredAudioKind" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> preferred<wbr>Audio<wbr>Kind</h3>
					<div class="tsd-signature tsd-kind-icon">preferred<wbr>Audio<wbr>Kind<span class="tsd-signature-symbol">:</span> <a href="Types.PreferredTrackKind.html" class="tsd-signature-type">PreferredTrackKind</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>preferredAudioKind</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="preferredAudioLanguage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> preferred<wbr>Audio<wbr>Language</h3>
					<div class="tsd-signature tsd-kind-icon">preferred<wbr>Audio<wbr>Language<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>preferredAudioLanguage</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="preferredTextKind" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> preferred<wbr>Text<wbr>Kind</h3>
					<div class="tsd-signature tsd-kind-icon">preferred<wbr>Text<wbr>Kind<span class="tsd-signature-symbol">:</span> <a href="Types.PreferredTrackKind.html" class="tsd-signature-type">PreferredTrackKind</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>preferredTextKind</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="preferredTextLanguage" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> preferred<wbr>Text<wbr>Language</h3>
					<div class="tsd-signature tsd-kind-icon">preferred<wbr>Text<wbr>Language<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>preferredTextLanguage</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="streaming" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> streaming</h3>
					<div class="tsd-signature tsd-kind-icon">streaming<span class="tsd-signature-symbol">:</span> <a href="Types.StreamingConfiguration.html" class="tsd-signature-type">StreamingConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Streaming configuration</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="subtitles" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> subtitles</h3>
					<div class="tsd-signature tsd-kind-icon">subtitles<span class="tsd-signature-symbol">:</span> <a href="Types.SubtitleConfiguration.html" class="tsd-signature-type">SubtitleConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Subtitles configuration (for HLS only)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="tizenNativeConfiguration" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> tizen<wbr>Native<wbr>Configuration</h3>
					<div class="tsd-signature tsd-kind-icon">tizen<wbr>Native<wbr>Configuration<span class="tsd-signature-symbol">:</span> <a href="Types.TizenNativeConfiguration.html" class="tsd-signature-type">TizenNativeConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configuration specific for Tizen native implementation</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="vowmk" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> vowmk</h3>
					<div class="tsd-signature tsd-kind-icon">vowmk<span class="tsd-signature-symbol">:</span> <a href="Types.VOWatermarkingConfiguration.html" class="tsd-signature-type">VOWatermarkingConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configuration specific for Viaccess-Orca watermarking feature</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="webosNativeConfiguration" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> webos<wbr>Native<wbr>Configuration</h3>
					<div class="tsd-signature tsd-kind-icon">webos<wbr>Native<wbr>Configuration<span class="tsd-signature-symbol">:</span> <a href="Types.WebosNativeConfiguration.html" class="tsd-signature-type">WebosNativeConfiguration</a></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Configuration specific for WebOS native implementation</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-interface tsd-parent-kind-module">
						<a href="Types.PlayerConfiguration.html" class="tsd-kind-icon">Player<wbr>Configuration</a>
						<ul>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#abr" class="tsd-kind-icon">abr</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#ads" class="tsd-kind-icon">ads</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#applicationAnalytics" class="tsd-kind-icon">application<wbr>Analytics</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#autostart" class="tsd-kind-icon">autostart</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#dashConfiguration" class="tsd-kind-icon">dash<wbr>Configuration</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#drm" class="tsd-kind-icon">drm</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#enableSmartLibAdvertisement" class="tsd-kind-icon">enable<wbr>Smart<wbr>Lib<wbr>Advertisement</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#events" class="tsd-kind-icon">events</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#externalThumbnails" class="tsd-kind-icon">external<wbr>Thumbnails</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#forceAppleNative" class="tsd-kind-icon">force<wbr>Apple<wbr>Native</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#hlsConfiguration" class="tsd-kind-icon">hls<wbr>Configuration</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#iosPlaysInline" class="tsd-kind-icon">ios<wbr>Plays<wbr>Inline</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#isEventAutomatic" class="tsd-kind-icon">is<wbr>Event<wbr>Automatic</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#isEventStream" class="tsd-kind-icon">is<wbr>Event<wbr>Stream</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#preferredAudioKind" class="tsd-kind-icon">preferred<wbr>Audio<wbr>Kind</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#preferredAudioLanguage" class="tsd-kind-icon">preferred<wbr>Audio<wbr>Language</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#preferredTextKind" class="tsd-kind-icon">preferred<wbr>Text<wbr>Kind</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#preferredTextLanguage" class="tsd-kind-icon">preferred<wbr>Text<wbr>Language</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#streaming" class="tsd-kind-icon">streaming</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#subtitles" class="tsd-kind-icon">subtitles</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#tizenNativeConfiguration" class="tsd-kind-icon">tizen<wbr>Native<wbr>Configuration</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#vowmk" class="tsd-kind-icon">vowmk</a>
							</li>
							<li class=" tsd-kind-property tsd-parent-kind-interface">
								<a href="Types.PlayerConfiguration.html#webosNativeConfiguration" class="tsd-kind-icon">webos<wbr>Native<wbr>Configuration</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>