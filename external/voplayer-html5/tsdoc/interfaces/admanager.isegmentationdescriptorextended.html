<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ISegmentationDescriptorExtended | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Interface ISegmentationDescriptorExtended</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/AdManager.html">AdManager</a>
				</li>
				<li>
					<a href="AdManager.ISegmentationDescriptorExtended.html">ISegmentationDescriptorExtended</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel tsd-hierarchy">
				<h3>Hierarchy</h3>
				<ul class="tsd-hierarchy">
					<li>
						<span class="tsd-signature-type">ISegmentationDescriptor</span>
						<ul class="tsd-hierarchy">
							<li>
								<span class="target">ISegmentationDescriptorExtended</span>
							</li>
						</ul>
					</li>
				</ul>
			</section>
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Properties</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#archiveAllowedFlag" class="tsd-kind-icon">archive<wbr>Allowed<wbr>Flag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#componentCount" class="tsd-kind-icon">component<wbr>Count</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#deliveryNotRestrictedFlag" class="tsd-kind-icon">delivery<wbr>Not<wbr>Restricted<wbr>Flag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#descriptorLength" class="tsd-kind-icon">descriptor<wbr>Length</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#deviceResctrictions" class="tsd-kind-icon">device<wbr>Resctrictions</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#indentifier" class="tsd-kind-icon">indentifier</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#noRegionalBlackoutFlag" class="tsd-kind-icon">no<wbr>Regional<wbr>Blackout<wbr>Flag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="AdManager.ISegmentationDescriptorExtended.html#private_bytes" class="tsd-kind-icon">private_<wbr>bytes</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#programSegmentationFlag" class="tsd-kind-icon">program<wbr>Segmentation<wbr>Flag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentNum" class="tsd-kind-icon">segment<wbr>Num</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationDuration" class="tsd-kind-icon">segmentation<wbr>Duration</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationDurationFlag" class="tsd-kind-icon">segmentation<wbr>Duration<wbr>Flag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationEventCancelIndicator" class="tsd-kind-icon">segmentation<wbr>Event<wbr>Cancel<wbr>Indicator</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationEventId" class="tsd-kind-icon">segmentation<wbr>Event<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationTypeId" class="tsd-kind-icon">segmentation<wbr>Type<wbr>Id</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationUpid" class="tsd-kind-icon">segmentation<wbr>Upid</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationUpidContent" class="tsd-kind-icon">segmentation<wbr>Upid<wbr>Content</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationUpidLength" class="tsd-kind-icon">segmentation<wbr>Upid<wbr>Length</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentationUpidType" class="tsd-kind-icon">segmentation<wbr>Upid<wbr>Type</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#segmentsExpected" class="tsd-kind-icon">segments<wbr>Expected</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#spliceDescriptorTag" class="tsd-kind-icon">splice<wbr>Descriptor<wbr>Tag</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#subSegmentNum" class="tsd-kind-icon">sub<wbr>Segment<wbr>Num</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#subSegmentsExpected" class="tsd-kind-icon">sub<wbr>Segments<wbr>Expected</a></li>
								<li class="tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external"><a href="AdManager.ISegmentationDescriptorExtended.html#webDeliveryAllowedFlag" class="tsd-kind-icon">web<wbr>Delivery<wbr>Allowed<wbr>Flag</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Properties</h2>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="archiveAllowedFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> archive<wbr>Allowed<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">archive<wbr>Allowed<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.archiveAllowedFlag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="componentCount" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> component<wbr>Count</h3>
					<div class="tsd-signature tsd-kind-icon">component<wbr>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.componentCount</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="deliveryNotRestrictedFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> delivery<wbr>Not<wbr>Restricted<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">delivery<wbr>Not<wbr>Restricted<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.deliveryNotRestrictedFlag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="descriptorLength" class="tsd-anchor"></a>
					<h3>descriptor<wbr>Length</h3>
					<div class="tsd-signature tsd-kind-icon">descriptor<wbr>Length<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.descriptorLength</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="deviceResctrictions" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> device<wbr>Resctrictions</h3>
					<div class="tsd-signature tsd-kind-icon">device<wbr>Resctrictions<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">SegmentationMessage</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.deviceResctrictions</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="indentifier" class="tsd-anchor"></a>
					<h3>indentifier</h3>
					<div class="tsd-signature tsd-kind-icon">indentifier<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.indentifier</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="noRegionalBlackoutFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> no<wbr>Regional<wbr>Blackout<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">no<wbr>Regional<wbr>Blackout<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.noRegionalBlackoutFlag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="private_bytes" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> private_<wbr>bytes</h3>
					<div class="tsd-signature tsd-kind-icon">private_<wbr>bytes<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="programSegmentationFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> program<wbr>Segmentation<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">program<wbr>Segmentation<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.programSegmentationFlag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentNum" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segment<wbr>Num</h3>
					<div class="tsd-signature tsd-kind-icon">segment<wbr>Num<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentNum</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationDuration" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Duration</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Duration<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationDuration</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationDurationFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Duration<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Duration<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationDurationFlag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationEventCancelIndicator" class="tsd-anchor"></a>
					<h3>segmentation<wbr>Event<wbr>Cancel<wbr>Indicator</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Event<wbr>Cancel<wbr>Indicator<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationEventCancelIndicator</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationEventId" class="tsd-anchor"></a>
					<h3>segmentation<wbr>Event<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Event<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationEventId</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationTypeId" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Type<wbr>Id</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Type<wbr>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">SegmentationTypeId</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationTypeId</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationUpid" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Upid</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Upid<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Uint8Array</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationUpid</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface">
					<a name="segmentationUpidContent" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Upid<wbr>Content</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Upid<wbr>Content<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationUpidLength" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Upid<wbr>Length</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Upid<wbr>Length<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationUpidLength</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentationUpidType" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segmentation<wbr>Upid<wbr>Type</h3>
					<div class="tsd-signature tsd-kind-icon">segmentation<wbr>Upid<wbr>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">SegmentationUpidType</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentationUpidType</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="segmentsExpected" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> segments<wbr>Expected</h3>
					<div class="tsd-signature tsd-kind-icon">segments<wbr>Expected<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.segmentsExpected</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="spliceDescriptorTag" class="tsd-anchor"></a>
					<h3>splice<wbr>Descriptor<wbr>Tag</h3>
					<div class="tsd-signature tsd-kind-icon">splice<wbr>Descriptor<wbr>Tag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">SpliceDescriptorTag</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.spliceDescriptorTag</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="subSegmentNum" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> sub<wbr>Segment<wbr>Num</h3>
					<div class="tsd-signature tsd-kind-icon">sub<wbr>Segment<wbr>Num<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.subSegmentNum</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="subSegmentsExpected" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> sub<wbr>Segments<wbr>Expected</h3>
					<div class="tsd-signature tsd-kind-icon">sub<wbr>Segments<wbr>Expected<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.subSegmentsExpected</p>
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface tsd-is-inherited tsd-is-external">
					<a name="webDeliveryAllowedFlag" class="tsd-anchor"></a>
					<h3><span class="tsd-flag ts-flagOptional">Optional</span> web<wbr>Delivery<wbr>Allowed<wbr>Flag</h3>
					<div class="tsd-signature tsd-kind-icon">web<wbr>Delivery<wbr>Allowed<wbr>Flag<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div>
					<aside class="tsd-sources">
						<p>Inherited from ISegmentationDescriptor.webDeliveryAllowedFlag</p>
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/AdManager.html">Ad<wbr>Manager</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
				<li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>