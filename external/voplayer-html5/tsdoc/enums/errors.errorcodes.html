<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ErrorCodes | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Enumeration ErrorCodes</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Errors.html">Errors</a>
				</li>
				<li>
					<a href="Errors.ErrorCodes.html">ErrorCodes</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_AD_REQUEST_INTERRUPTED" class="tsd-kind-icon">ADS_<wbr>AD_<wbr>REQUEST_<wbr>INTERRUPTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_LINEAR_SEEK_FORBIDDEN" class="tsd-kind-icon">ADS_<wbr>LINEAR_<wbr>SEEK_<wbr>FORBIDDEN</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR" class="tsd-kind-icon">ADS_<wbr>MAST_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_MAST_PARSING_ERROR" class="tsd-kind-icon">ADS_<wbr>MAST_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_PLAYER_EXTENSION_MISSING" class="tsd-kind-icon">ADS_<wbr>PLAYER_<wbr>EXTENSION_<wbr>MISSING</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_PLAYER_LICENSE_NO_SUPPORT" class="tsd-kind-icon">ADS_<wbr>PLAYER_<wbr>LICENSE_<wbr>NO_<wbr>SUPPORT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_REQUEST_FAILED" class="tsd-kind-icon">ADS_<wbr>REQUEST_<wbr>FAILED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_AD_TYPE_NOT_EXPECTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>AD_<wbr>TYPE_<wbr>NOT_<wbr>EXPECTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_COMPANION_CREATIVE_DIMENSION_TOO_LARGE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>COMPANION_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_COMPANION_RESSOURCE_TYPE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>COMPANION_<wbr>RESSOURCE_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_DURATION_NOT_EXPECTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>DURATION_<wbr>NOT_<wbr>EXPECTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_EMPTY_RESPONSE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>EMPTY_<wbr>RESPONSE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_FILE_NOT_FOUND" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>FILE_<wbr>NOT_<wbr>FOUND</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_GENERAL_COMPANIONSADS_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>COMPANIONSADS_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_GENERAL_LINEAR_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>LINEAR_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_GENERAL_NONLINEAR_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>NONLINEAR_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_GENERAL_VPAID_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>VPAID_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_GENERAL_WRAPPER_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>WRAPPER_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_LINEARITY_NOT_EXPECTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>LINEARITY_<wbr>NOT_<wbr>EXPECTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_MEDIAFILE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>MEDIAFILE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_NONLINEAR_CREATIVE_DIMENSION_TOO_LARGE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_NONLINEAR_RESSOURCE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>RESSOURCE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_NO_RESPONSE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>NO_<wbr>RESPONSE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_PROBLEM_DISPLAYING_MEDIAFILE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>PROBLEM_<wbr>DISPLAYING_<wbr>MEDIAFILE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_REPONSE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>REPONSE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_SCHEMA_VALIDATION_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>SCHEMA_<wbr>VALIDATION_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_SIZE_NOT_EXPECTED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>SIZE_<wbr>NOT_<wbr>EXPECTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_TIMEOUT_MEDIAFILE_URI" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>MEDIAFILE_<wbr>URI</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_TIMEOUT_VAST_URI" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>VAST_<wbr>URI</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_TOO_MANY_WRAPPER_RESPONSES_RECEIVED" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>TOO_<wbr>MANY_<wbr>WRAPPER_<wbr>RESPONSES_<wbr>RECEIVED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_UNABLE_TO_DISPLAY_COMPANION" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>DISPLAY_<wbr>COMPANION</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_UNABLE_TO_FETCH_COMPANION_RESSOURCE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>COMPANION_<wbr>RESSOURCE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_UNABLE_TO_FETCH_RESSOURCE" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>RESSOURCE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_UNDEFINED_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNDEFINED_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VAST_XML_PARSING_ERROR" class="tsd-kind-icon">ADS_<wbr>VAST_<wbr>XML_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_ADBREAK_TYPE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>ADBREAK_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_AD_DOCUMENT_RETRIEVAL_TIMEOUT" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>TIMEOUT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_AD_TEMPLATE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>TEMPLATE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_GENERAL_AD_RESPONSE_DOCUMENT_ERROR" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>GENERAL_<wbr>AD_<wbr>RESPONSE_<wbr>DOCUMENT_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_MAST_EMPTY_RESPONSE" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>MAST_<wbr>EMPTY_<wbr>RESPONSE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_PARSING_ERROR" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_RESPONSE_NOT_SUPPORTED" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>RESPONSE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ADS_VMAP_SCHEMA_ERROR" class="tsd-kind-icon">ADS_<wbr>VMAP_<wbr>SCHEMA_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#API_DEPRECATED" class="tsd-kind-icon">API_<wbr>DEPRECATED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#API_NOT_SUPPORTED" class="tsd-kind-icon">API_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#BAD_ENCODING" class="tsd-kind-icon">BAD_<wbr>ENCODING</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#BAD_HTTP_STATUS" class="tsd-kind-icon">BAD_<wbr>HTTP_<wbr>STATUS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#BUFFER_READ_OUT_OF_BOUNDS" class="tsd-kind-icon">BUFFER_<wbr>READ_<wbr>OUT_<wbr>OF_<wbr>BOUNDS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#CANNOT_ADD_EXTERNAL_TEXT_TO_LIVE_STREAM" class="tsd-kind-icon">CANNOT_<wbr>ADD_<wbr>EXTERNAL_<wbr>TEXT_<wbr>TO_<wbr>LIVE_<wbr>STREAM</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#CONTENT_UNSUPPORTED_BY_BROWSER" class="tsd-kind-icon">CONTENT_<wbr>UNSUPPORTED_<wbr>BY_<wbr>BROWSER</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_CONFLICTING_KEY_IDS" class="tsd-kind-icon">DASH_<wbr>CONFLICTING_<wbr>KEY_<wbr>IDS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_DUPLICATE_REPRESENTATION_ID" class="tsd-kind-icon">DASH_<wbr>DUPLICATE_<wbr>REPRESENTATION_<wbr>ID</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_EMPTY_ADAPTATION_SET" class="tsd-kind-icon">DASH_<wbr>EMPTY_<wbr>ADAPTATION_<wbr>SET</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_EMPTY_PERIOD" class="tsd-kind-icon">DASH_<wbr>EMPTY_<wbr>PERIOD</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_INVALID_XML" class="tsd-kind-icon">DASH_<wbr>INVALID_<wbr>XML</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_MULTIPLE_KEY_IDS_NOT_SUPPORTED" class="tsd-kind-icon">DASH_<wbr>MULTIPLE_<wbr>KEY_<wbr>IDS_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_NO_COMMON_KEY_SYSTEM" class="tsd-kind-icon">DASH_<wbr>NO_<wbr>COMMON_<wbr>KEY_<wbr>SYSTEM</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_NO_SEGMENT_INFO" class="tsd-kind-icon">DASH_<wbr>NO_<wbr>SEGMENT_<wbr>INFO</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_PSSH_BAD_ENCODING" class="tsd-kind-icon">DASH_<wbr>PSSH_<wbr>BAD_<wbr>ENCODING</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_UNSUPPORTED_CONTAINER" class="tsd-kind-icon">DASH_<wbr>UNSUPPORTED_<wbr>CONTAINER</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DASH_XLINK_DEPTH_LIMIT" class="tsd-kind-icon">DASH_<wbr>XLINK_<wbr>DEPTH_<wbr>LIMIT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DATA_UPDATE_FAILURE" class="tsd-kind-icon">DATA_<wbr>UPDATE_<wbr>FAILURE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#DRM_ERROR" class="tsd-kind-icon">DRM_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#ENCRYPTED_CONTENT_WITHOUT_DRM_INFO" class="tsd-kind-icon">ENCRYPTED_<wbr>CONTENT_<wbr>WITHOUT_<wbr>DRM_<wbr>INFO</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#EXCEPTION_ERROR" class="tsd-kind-icon">EXCEPTION_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#EXPIRED" class="tsd-kind-icon">EXPIRED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FAILED_TO_ATTACH_TO_VIDEO" class="tsd-kind-icon">FAILED_<wbr>TO_<wbr>ATTACH_<wbr>TO_<wbr>VIDEO</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FAILED_TO_CREATE_CDM" class="tsd-kind-icon">FAILED_<wbr>TO_<wbr>CREATE_<wbr>CDM</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FAILED_TO_CREATE_SESSION" class="tsd-kind-icon">FAILED_<wbr>TO_<wbr>CREATE_<wbr>SESSION</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FAILED_TO_GENERATE_LICENSE_REQUEST" class="tsd-kind-icon">FAILED_<wbr>TO_<wbr>GENERATE_<wbr>LICENSE_<wbr>REQUEST</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FMA_BAD_ARGUMENTS" class="tsd-kind-icon">FMA_<wbr>BAD_<wbr>ARGUMENTS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FMA_NOT_INITILIZED" class="tsd-kind-icon">FMA_<wbr>NOT_<wbr>INITILIZED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#FMA_OTHER_ERROR" class="tsd-kind-icon">FMA_<wbr>OTHER_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#HLS_FRAG_DECRYPT_ERROR" class="tsd-kind-icon">HLS_<wbr>FRAG_<wbr>DECRYPT_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#HLS_FRAG_PARSING_ERROR" class="tsd-kind-icon">HLS_<wbr>FRAG_<wbr>PARSING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#HTTP_ERROR" class="tsd-kind-icon">HTTP_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#IMG_ERROR_GENERIC" class="tsd-kind-icon">IMG_<wbr>ERROR_<wbr>GENERIC</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#IMG_ERROR_NO_DURATION" class="tsd-kind-icon">IMG_<wbr>ERROR_<wbr>NO_<wbr>DURATION</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INIT_DATA_TRANSFORM_ERROR" class="tsd-kind-icon">INIT_<wbr>DATA_<wbr>TRANSFORM_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INTERNAL_ERROR" class="tsd-kind-icon">INTERNAL_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_MP4_TTML" class="tsd-kind-icon">INVALID_<wbr>MP4_<wbr>TTML</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_MP4_VTT" class="tsd-kind-icon">INVALID_<wbr>MP4_<wbr>VTT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_OPERATION" class="tsd-kind-icon">INVALID_<wbr>OPERATION</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_PLAYER_LICENSE" class="tsd-kind-icon">INVALID_<wbr>PLAYER_<wbr>LICENSE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_SERVER_CERTIFICATE" class="tsd-kind-icon">INVALID_<wbr>SERVER_<wbr>CERTIFICATE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_STATE" class="tsd-kind-icon">INVALID_<wbr>STATE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_STREAMS_CHOSEN" class="tsd-kind-icon">INVALID_<wbr>STREAMS_<wbr>CHOSEN</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_TEXT_CUE" class="tsd-kind-icon">INVALID_<wbr>TEXT_<wbr>CUE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_TEXT_HEADER" class="tsd-kind-icon">INVALID_<wbr>TEXT_<wbr>HEADER</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#INVALID_XML" class="tsd-kind-icon">INVALID_<wbr>XML</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#JS_INTEGER_OVERFLOW" class="tsd-kind-icon">JS_<wbr>INTEGER_<wbr>OVERFLOW</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#KEY_MESSAGE_ERROR" class="tsd-kind-icon">KEY_<wbr>MESSAGE_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#LICENSE_REQUEST_FAILED" class="tsd-kind-icon">LICENSE_<wbr>REQUEST_<wbr>FAILED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#LICENSE_RESPONSE_REJECTED" class="tsd-kind-icon">LICENSE_<wbr>RESPONSE_<wbr>REJECTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#LOAD_INTERRUPTED" class="tsd-kind-icon">LOAD_<wbr>INTERRUPTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MALFORMED_DATA_URI" class="tsd-kind-icon">MALFORMED_<wbr>DATA_<wbr>URI</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MALFORMED_TEST_URI" class="tsd-kind-icon">MALFORMED_<wbr>TEST_<wbr>URI</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MANIFEST_PARSING_FAILURE" class="tsd-kind-icon">MANIFEST_<wbr>PARSING_<wbr>FAILURE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MEDIA_SOURCE_OPERATION_FAILED" class="tsd-kind-icon">MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>FAILED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MEDIA_SOURCE_OPERATION_THREW" class="tsd-kind-icon">MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>THREW</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MP4_SIDX_INVALID_TIMESCALE" class="tsd-kind-icon">MP4_<wbr>SIDX_<wbr>INVALID_<wbr>TIMESCALE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MP4_SIDX_TYPE_NOT_SUPPORTED" class="tsd-kind-icon">MP4_<wbr>SIDX_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#MP4_SIDX_WRONG_BOX_TYPE" class="tsd-kind-icon">MP4_<wbr>SIDX_<wbr>WRONG_<wbr>BOX_<wbr>TYPE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#NO_ERROR_CODE" class="tsd-kind-icon">NO_<wbr>ERROR_<wbr>CODE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#NO_LICENSE_SERVER_GIVEN" class="tsd-kind-icon">NO_<wbr>LICENSE_<wbr>SERVER_<wbr>GIVEN</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#NO_PERIODS" class="tsd-kind-icon">NO_<wbr>PERIODS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#NO_RECOGNIZED_KEY_SYSTEMS" class="tsd-kind-icon">NO_<wbr>RECOGNIZED_<wbr>KEY_<wbr>SYSTEMS</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#NO_VIDEO_ELEMENT" class="tsd-kind-icon">NO_<wbr>VIDEO_<wbr>ELEMENT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#OFFLINE_SESSION_REMOVED" class="tsd-kind-icon">OFFLINE_<wbr>SESSION_<wbr>REMOVED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#OPERATION_ABORTED" class="tsd-kind-icon">OPERATION_<wbr>ABORTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#PLAY_PROMISE_INTERRUPTED" class="tsd-kind-icon">PLAY_<wbr>PROMISE_<wbr>INTERRUPTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#QUOTA_EXCEEDED_ERROR" class="tsd-kind-icon">QUOTA_<wbr>EXCEEDED_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#REQUESTED_KEY_SYSTEM_CONFIG_UNAVAILABLE" class="tsd-kind-icon">REQUESTED_<wbr>KEY_<wbr>SYSTEM_<wbr>CONFIG_<wbr>UNAVAILABLE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#REQUEST_FILTER_ERROR" class="tsd-kind-icon">REQUEST_<wbr>FILTER_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#RESPONSE_FILTER_ERROR" class="tsd-kind-icon">RESPONSE_<wbr>FILTER_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#RESTRICTIONS_CANNOT_BE_MET" class="tsd-kind-icon">RESTRICTIONS_<wbr>CANNOT_<wbr>BE_<wbr>MET</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#SERVER_CERTIFICATE_REQUIRED" class="tsd-kind-icon">SERVER_<wbr>CERTIFICATE_<wbr>REQUIRED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#STATUS_CHANGED_EXPIRED" class="tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>EXPIRED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#STATUS_CHANGED_INTERNAL_ERROR" class="tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>INTERNAL_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#STATUS_CHANGED_OUTPUT_RESTRICTED" class="tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>OUTPUT_<wbr>RESTRICTED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#TIMEOUT" class="tsd-kind-icon">TIMEOUT</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#TRANSMUXING_FAILED" class="tsd-kind-icon">TRANSMUXING_<wbr>FAILED</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNABLE_TO_DETECT_ENCODING" class="tsd-kind-icon">UNABLE_<wbr>TO_<wbr>DETECT_<wbr>ENCODING</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNABLE_TO_EXTRACT_CUE_START_TIME" class="tsd-kind-icon">UNABLE_<wbr>TO_<wbr>EXTRACT_<wbr>CUE_<wbr>START_<wbr>TIME</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNABLE_TO_GUESS_MANIFEST_TYPE" class="tsd-kind-icon">UNABLE_<wbr>TO_<wbr>GUESS_<wbr>MANIFEST_<wbr>TYPE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNEXPECTED_TEST_REQUEST" class="tsd-kind-icon">UNEXPECTED_<wbr>TEST_<wbr>REQUEST</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNMAPPED_ERROR" class="tsd-kind-icon">UNMAPPED_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNPLAYABLE_PERIOD" class="tsd-kind-icon">UNPLAYABLE_<wbr>PERIOD</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#UNSUPPORTED_SCHEME" class="tsd-kind-icon">UNSUPPORTED_<wbr>SCHEME</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#VIDEO_ERROR" class="tsd-kind-icon">VIDEO_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#WIDEVINE_CERTIFICATE_LOADING_ERROR" class="tsd-kind-icon">WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>ERROR</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCodes.html#WIDEVINE_CERTIFICATE_LOADING_TIMEOUT" class="tsd-kind-icon">WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>TIMEOUT</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_AD_REQUEST_INTERRUPTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>AD_<wbr>REQUEST_<wbr>INTERRUPTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>AD_<wbr>REQUEST_<wbr>INTERRUPTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error issued when an ad request is interrupted due to exceeding <code>maxAdRequestParsingTime</code>.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_LINEAR_SEEK_FORBIDDEN" class="tsd-anchor"></a>
					<h3>ADS_<wbr>LINEAR_<wbr>SEEK_<wbr>FORBIDDEN</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>LINEAR_<wbr>SEEK_<wbr>FORBIDDEN<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20005</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad linear seek forbiddem</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_MAST_AD_EXTRACTION_OR_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>MAST_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>MAST_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21106</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad MAST response document extraction or parsing error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_MAST_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>MAST_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>MAST_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21100</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>MAST Parsing error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_PLAYER_EXTENSION_MISSING" class="tsd-anchor"></a>
					<h3>ADS_<wbr>PLAYER_<wbr>EXTENSION_<wbr>MISSING</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>PLAYER_<wbr>EXTENSION_<wbr>MISSING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning when the Ads extension library is missing</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_PLAYER_LICENSE_NO_SUPPORT" class="tsd-anchor"></a>
					<h3>ADS_<wbr>PLAYER_<wbr>LICENSE_<wbr>NO_<wbr>SUPPORT</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>PLAYER_<wbr>LICENSE_<wbr>NO_<wbr>SUPPORT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning when the Player License does not have the Ads feature enabled</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_REQUEST_FAILED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>REQUEST_<wbr>FAILED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>REQUEST_<wbr>FAILED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error issued when advertisment media asset could not be retrieved.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_AD_TYPE_NOT_EXPECTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>AD_<wbr>TYPE_<wbr>NOT_<wbr>EXPECTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>AD_<wbr>TYPE_<wbr>NOT_<wbr>EXPECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20200</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Trafficking error. Video player received an Ad type that it was not expecting and/or cannot  display.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_COMPANION_CREATIVE_DIMENSION_TOO_LARGE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>COMPANION_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>COMPANION_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20601</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Unable to display Companion because creative dimensions do not fit within Companion display area (i.e., no available space).</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_COMPANION_RESSOURCE_TYPE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>COMPANION_<wbr>RESSOURCE_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>COMPANION_<wbr>RESSOURCE_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20604</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Couldn’t find Companion resource with supported type.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_DURATION_NOT_EXPECTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>DURATION_<wbr>NOT_<wbr>EXPECTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>DURATION_<wbr>NOT_<wbr>EXPECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20202</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Video player expecting different duration.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_EMPTY_RESPONSE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>EMPTY_<wbr>RESPONSE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>EMPTY_<wbr>RESPONSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error issued when VAST ad response is empty or its body is empty.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_FILE_NOT_FOUND" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>FILE_<wbr>NOT_<wbr>FOUND</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>FILE_<wbr>NOT_<wbr>FOUND<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20401</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>File not found. Unable to find Linear/MediaFile from URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_GENERAL_COMPANIONSADS_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>GENERAL_<wbr>COMPANIONSADS_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>COMPANIONSADS_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20600</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General CompanionAds error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_GENERAL_LINEAR_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>GENERAL_<wbr>LINEAR_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>LINEAR_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20400</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General Linear error. Video player is unable to display the Linear Ad.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_GENERAL_NONLINEAR_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>GENERAL_<wbr>NONLINEAR_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>NONLINEAR_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20500</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General NonLinearAds error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_GENERAL_VPAID_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>GENERAL_<wbr>VPAID_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>VPAID_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20901</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General VPAID error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_GENERAL_WRAPPER_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>GENERAL_<wbr>WRAPPER_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>GENERAL_<wbr>WRAPPER_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20300</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General Wrapper error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_LINEARITY_NOT_EXPECTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>LINEARITY_<wbr>NOT_<wbr>EXPECTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>LINEARITY_<wbr>NOT_<wbr>EXPECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20201</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Video player expecting different linearity</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_MEDIAFILE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>MEDIAFILE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>MEDIAFILE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20403</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Couldn’t find MediaFile that is supported by this video player, based on the attributes of the MediaFile element</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_NONLINEAR_CREATIVE_DIMENSION_TOO_LARGE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>CREATIVE_<wbr>DIMENSION_<wbr>TOO_<wbr>LARGE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20501</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Unable to display NonLinear Ad because creative dimensions do not align with creative display area (i.e. creative dimension too large).</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_NONLINEAR_RESSOURCE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>RESSOURCE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>NONLINEAR_<wbr>RESSOURCE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20503</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Couldn’t find NonLinear resource with supported type.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_NO_RESPONSE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>NO_<wbr>RESPONSE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>NO_<wbr>RESPONSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20303</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>No Ads VAST response after one or more Wrappers.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_PROBLEM_DISPLAYING_MEDIAFILE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>PROBLEM_<wbr>DISPLAYING_<wbr>MEDIAFILE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>PROBLEM_<wbr>DISPLAYING_<wbr>MEDIAFILE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20405</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Problem displaying MediaFile. Video player found a MediaFile with supported type but couldn’t display it. MediaFile may include: unsupported codecs, different MIME type than MediaFile@type, unsupported delivery method, etc.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_REPONSE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>REPONSE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>REPONSE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20102</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VAST version of response not supported.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_SCHEMA_VALIDATION_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>SCHEMA_<wbr>VALIDATION_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>SCHEMA_<wbr>VALIDATION_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20101</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VAST schema validation error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_SIZE_NOT_EXPECTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>SIZE_<wbr>NOT_<wbr>EXPECTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>SIZE_<wbr>NOT_<wbr>EXPECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20203</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Video player expecting different size.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_TIMEOUT_MEDIAFILE_URI" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>MEDIAFILE_<wbr>URI</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>MEDIAFILE_<wbr>URI<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20402</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Timeout of MediaFile URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_TIMEOUT_VAST_URI" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>VAST_<wbr>URI</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>TIMEOUT_<wbr>VAST_<wbr>URI<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20301</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Timeout of VAST URI provided in Wrapper element, or of VAST URI provided in a subsequent  Wrapper element. (URI was either unavailable or reached a timeout as defined by the video player.)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_TOO_MANY_WRAPPER_RESPONSES_RECEIVED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>TOO_<wbr>MANY_<wbr>WRAPPER_<wbr>RESPONSES_<wbr>RECEIVED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>TOO_<wbr>MANY_<wbr>WRAPPER_<wbr>RESPONSES_<wbr>RECEIVED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20302</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Wrapper limit reached, as defined by the video player. Too many Wrapper responses have been received with no InLine response.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_UNABLE_TO_DISPLAY_COMPANION" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>DISPLAY_<wbr>COMPANION</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>DISPLAY_<wbr>COMPANION<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20602</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Unable to display Required Companion.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_UNABLE_TO_FETCH_COMPANION_RESSOURCE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>COMPANION_<wbr>RESSOURCE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>COMPANION_<wbr>RESSOURCE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20603</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Unable to fetch CompanionAds/Companion resource.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_UNABLE_TO_FETCH_RESSOURCE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>RESSOURCE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNABLE_<wbr>TO_<wbr>FETCH_<wbr>RESSOURCE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20502</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Unable to fetch NonLinearAds/NonLinear resource.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_UNDEFINED_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>UNDEFINED_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>UNDEFINED_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20900</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Undefined Error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VAST_XML_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VAST_<wbr>XML_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VAST_<wbr>XML_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20100</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VAST XML parsing error according to VAST 3.0 specifications.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_ADBREAK_TYPE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>ADBREAK_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>ADBREAK_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>AdBreak type not supported</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_AD_DOCUMENT_RETRIEVAL_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21008</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad response document retrieval error (e.g., Http server responded with error code)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_AD_DOCUMENT_RETRIEVAL_TIMEOUT" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>TIMEOUT</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>DOCUMENT_<wbr>RETRIEVAL_<wbr>TIMEOUT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21007</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad response document retrieval timeout</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_AD_EXTRACTION_OR_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>EXTRACTION_<wbr>OR_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad response document extraction or parsing error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_AD_TEMPLATE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>AD_<wbr>TEMPLATE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>AD_<wbr>TEMPLATE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21005</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Ad response template type not supported</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_GENERAL_AD_RESPONSE_DOCUMENT_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>GENERAL_<wbr>AD_<wbr>RESPONSE_<wbr>DOCUMENT_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>GENERAL_<wbr>AD_<wbr>RESPONSE_<wbr>DOCUMENT_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>General ad response document error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_MAST_EMPTY_RESPONSE" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>MAST_<wbr>EMPTY_<wbr>RESPONSE</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>MAST_<wbr>EMPTY_<wbr>RESPONSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error issued when VMAP or MAST ad response is empty or its body is empty.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VMAP parsing error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_RESPONSE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>RESPONSE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>RESPONSE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VMAP version of response not supported</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADS_VMAP_SCHEMA_ERROR" class="tsd-anchor"></a>
					<h3>ADS_<wbr>VMAP_<wbr>SCHEMA_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">ADS_<wbr>VMAP_<wbr>SCHEMA_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 21000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>VMAP schema error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="API_DEPRECATED" class="tsd-anchor"></a>
					<h3>API_<wbr>DEPRECATED</h3>
					<div class="tsd-signature tsd-kind-icon">API_<wbr>DEPRECATED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 9000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A waring or an error related to API dpreciation</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="API_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>API_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">API_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 9001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A waring or an error related to API not supported</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="BAD_ENCODING" class="tsd-anchor"></a>
					<h3>BAD_<wbr>ENCODING</h3>
					<div class="tsd-signature tsd-kind-icon">BAD_<wbr>ENCODING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The response data contains invalid Unicode character encoding.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="BAD_HTTP_STATUS" class="tsd-anchor"></a>
					<h3>BAD_<wbr>HTTP_<wbr>STATUS</h3>
					<div class="tsd-signature tsd-kind-icon">BAD_<wbr>HTTP_<wbr>STATUS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An HTTP network request returned an HTTP status that indicated a failure.<br/>
							The <code>data</code> object returned within the error will provide information on the type of error and information to potentially restart a session.</p>
							<ul>
								<li> `data.type` : the type of content that failed to download, either 'MANIFEST' or 'CONTENT'</li>
								<li> `data.uri` : string representing the uri where the error occurred.</li>
								<li> `data.status` : HTTTP error code.</li>
								<li> `data.currentTime` : time of playback when the error occurred, can be used to reload the manifest and resume at the time of playback.</li>
								<li> `data.seekRange` : seekRange of playback when the error occurred.</li>
								<li> `data.audioTrackId` : id of the selected audio track when the error occurred. May be used when reloading the manifest and resume with the proper audio track.</li>
								<li> `data.textTrackId` : id of the selected text track when the error occurred. May be used when reloading the manifest and resume with the proper text track.<:li>
							</ul>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="BUFFER_READ_OUT_OF_BOUNDS" class="tsd-anchor"></a>
					<h3>BUFFER_<wbr>READ_<wbr>OUT_<wbr>OF_<wbr>BOUNDS</h3>
					<div class="tsd-signature tsd-kind-icon">BUFFER_<wbr>READ_<wbr>OUT_<wbr>OF_<wbr>BOUNDS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Some component tried to read past the end of a buffer.  The segment index,
							init segment, or PSSH may be malformed.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="CANNOT_ADD_EXTERNAL_TEXT_TO_LIVE_STREAM" class="tsd-anchor"></a>
					<h3>CANNOT_<wbr>ADD_<wbr>EXTERNAL_<wbr>TEXT_<wbr>TO_<wbr>LIVE_<wbr>STREAM</h3>
					<div class="tsd-signature tsd-kind-icon">CANNOT_<wbr>ADD_<wbr>EXTERNAL_<wbr>TEXT_<wbr>TO_<wbr>LIVE_<wbr>STREAM<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4033</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="CONTENT_UNSUPPORTED_BY_BROWSER" class="tsd-anchor"></a>
					<h3>CONTENT_<wbr>UNSUPPORTED_<wbr>BY_<wbr>BROWSER</h3>
					<div class="tsd-signature tsd-kind-icon">CONTENT_<wbr>UNSUPPORTED_<wbr>BY_<wbr>BROWSER<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4032</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_CONFLICTING_KEY_IDS" class="tsd-anchor"></a>
					<h3>DASH_<wbr>CONFLICTING_<wbr>KEY_<wbr>IDS</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>CONFLICTING_<wbr>KEY_<wbr>IDS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4010</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest specifies conflicting key IDs.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_DUPLICATE_REPRESENTATION_ID" class="tsd-anchor"></a>
					<h3>DASH_<wbr>DUPLICATE_<wbr>REPRESENTATION_<wbr>ID</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>DUPLICATE_<wbr>REPRESENTATION_<wbr>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4018</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A Representation has an id that is the same as another Representation in
								the same Period.  This makes manifest updates impossible since we cannot
							map the updated Representation to the old one.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_EMPTY_ADAPTATION_SET" class="tsd-anchor"></a>
					<h3>DASH_<wbr>EMPTY_<wbr>ADAPTATION_<wbr>SET</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>EMPTY_<wbr>ADAPTATION_<wbr>SET<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest contained an AdaptationSet with no Representations.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_EMPTY_PERIOD" class="tsd-anchor"></a>
					<h3>DASH_<wbr>EMPTY_<wbr>PERIOD</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>EMPTY_<wbr>PERIOD<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest contained an Period with no AdaptationSets.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_INVALID_XML" class="tsd-anchor"></a>
					<h3>DASH_<wbr>INVALID_<wbr>XML</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>INVALID_<wbr>XML<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest contained invalid XML markup.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_MULTIPLE_KEY_IDS_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>DASH_<wbr>MULTIPLE_<wbr>KEY_<wbr>IDS_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>MULTIPLE_<wbr>KEY_<wbr>IDS_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4009</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Having multiple key IDs per Representation is not supported.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_NO_COMMON_KEY_SYSTEM" class="tsd-anchor"></a>
					<h3>DASH_<wbr>NO_<wbr>COMMON_<wbr>KEY_<wbr>SYSTEM</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>NO_<wbr>COMMON_<wbr>KEY_<wbr>SYSTEM<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4008</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>There is an AdaptationSet whose Representations do not have any common
							key-systems.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_NO_SEGMENT_INFO" class="tsd-anchor"></a>
					<h3>DASH_<wbr>NO_<wbr>SEGMENT_<wbr>INFO</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>NO_<wbr>SEGMENT_<wbr>INFO<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest contained a Representation with insufficient segment
							information.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_PSSH_BAD_ENCODING" class="tsd-anchor"></a>
					<h3>DASH_<wbr>PSSH_<wbr>BAD_<wbr>ENCODING</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>PSSH_<wbr>BAD_<wbr>ENCODING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4007</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The embedded PSSH data has invalid encoding.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_UNSUPPORTED_CONTAINER" class="tsd-anchor"></a>
					<h3>DASH_<wbr>UNSUPPORTED_<wbr>CONTAINER</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>UNSUPPORTED_<wbr>CONTAINER<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The DASH Manifest contained an unsupported container format.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DASH_XLINK_DEPTH_LIMIT" class="tsd-anchor"></a>
					<h3>DASH_<wbr>XLINK_<wbr>DEPTH_<wbr>LIMIT</h3>
					<div class="tsd-signature tsd-kind-icon">DASH_<wbr>XLINK_<wbr>DEPTH_<wbr>LIMIT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4028</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DATA_UPDATE_FAILURE" class="tsd-anchor"></a>
					<h3>DATA_<wbr>UPDATE_<wbr>FAILURE</h3>
					<div class="tsd-signature tsd-kind-icon">DATA_<wbr>UPDATE_<wbr>FAILURE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4015</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error was raised while updating some data.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DRM_ERROR" class="tsd-anchor"></a>
					<h3>DRM_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">DRM_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6055</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ENCRYPTED_CONTENT_WITHOUT_DRM_INFO" class="tsd-anchor"></a>
					<h3>ENCRYPTED_<wbr>CONTENT_<wbr>WITHOUT_<wbr>DRM_<wbr>INFO</h3>
					<div class="tsd-signature tsd-kind-icon">ENCRYPTED_<wbr>CONTENT_<wbr>WITHOUT_<wbr>DRM_<wbr>INFO<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6010</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The manifest does not specify any DRM info, but the content is encrypted.
							Either the manifest or the manifest parser are broken.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="EXCEPTION_ERROR" class="tsd-anchor"></a>
					<h3>EXCEPTION_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">EXCEPTION_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 10001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error related to an exception raised by the core player.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="EXPIRED" class="tsd-anchor"></a>
					<h3>EXPIRED</h3>
					<div class="tsd-signature tsd-kind-icon">EXPIRED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6014</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The license has expired.  This is triggered when playback is stalled on a
								&#39;waitingforkeys&#39; event and there are any expired keys in the key status map
							of any active session.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FAILED_TO_ATTACH_TO_VIDEO" class="tsd-anchor"></a>
					<h3>FAILED_<wbr>TO_<wbr>ATTACH_<wbr>TO_<wbr>VIDEO</h3>
					<div class="tsd-signature tsd-kind-icon">FAILED_<wbr>TO_<wbr>ATTACH_<wbr>TO_<wbr>VIDEO<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The browser found one of the requested key systems and created an instance
								of the CDM, but it failed to attach the CDM to the video for some unknown
								reason.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FAILED_TO_CREATE_CDM" class="tsd-anchor"></a>
					<h3>FAILED_<wbr>TO_<wbr>CREATE_<wbr>CDM</h3>
					<div class="tsd-signature tsd-kind-icon">FAILED_<wbr>TO_<wbr>CREATE_<wbr>CDM<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The browser found one of the requested key systems, but it failed to
								create an instance of the CDM for some unknown reason.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FAILED_TO_CREATE_SESSION" class="tsd-anchor"></a>
					<h3>FAILED_<wbr>TO_<wbr>CREATE_<wbr>SESSION</h3>
					<div class="tsd-signature tsd-kind-icon">FAILED_<wbr>TO_<wbr>CREATE_<wbr>SESSION<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6005</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The CDM refused to create a session for some unknown reason.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FAILED_TO_GENERATE_LICENSE_REQUEST" class="tsd-anchor"></a>
					<h3>FAILED_<wbr>TO_<wbr>GENERATE_<wbr>LICENSE_<wbr>REQUEST</h3>
					<div class="tsd-signature tsd-kind-icon">FAILED_<wbr>TO_<wbr>GENERATE_<wbr>LICENSE_<wbr>REQUEST<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The CDM was unable to generate a license request for the init data it was
								given.  The init data may be malformed or in an unsupported format.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FMA_BAD_ARGUMENTS" class="tsd-anchor"></a>
					<h3>FMA_<wbr>BAD_<wbr>ARGUMENTS</h3>
					<div class="tsd-signature tsd-kind-icon">FMA_<wbr>BAD_<wbr>ARGUMENTS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 12002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning when we try to call an FMA API with wrong parameters</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FMA_NOT_INITILIZED" class="tsd-anchor"></a>
					<h3>FMA_<wbr>NOT_<wbr>INITILIZED</h3>
					<div class="tsd-signature tsd-kind-icon">FMA_<wbr>NOT_<wbr>INITILIZED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 12001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning when we try to use the FMA before being initialized</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FMA_OTHER_ERROR" class="tsd-anchor"></a>
					<h3>FMA_<wbr>OTHER_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">FMA_<wbr>OTHER_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 12000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning when we try to use the FMA before being initialized</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HLS_FRAG_DECRYPT_ERROR" class="tsd-anchor"></a>
					<h3>HLS_<wbr>FRAG_<wbr>DECRYPT_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">HLS_<wbr>FRAG_<wbr>DECRYPT_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3019</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HLS_FRAG_PARSING_ERROR" class="tsd-anchor"></a>
					<h3>HLS_<wbr>FRAG_<wbr>PARSING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">HLS_<wbr>FRAG_<wbr>PARSING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3020</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HTTP_ERROR" class="tsd-anchor"></a>
					<h3>HTTP_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">HTTP_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1002</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An HTTP network request failed with an error, but not from the server.
							<br> error.data[0] is the URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="IMG_ERROR_GENERIC" class="tsd-anchor"></a>
					<h3>IMG_<wbr>ERROR_<wbr>GENERIC</h3>
					<div class="tsd-signature tsd-kind-icon">IMG_<wbr>ERROR_<wbr>GENERIC<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3022</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Generic Image error</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="IMG_ERROR_NO_DURATION" class="tsd-anchor"></a>
					<h3>IMG_<wbr>ERROR_<wbr>NO_<wbr>DURATION</h3>
					<div class="tsd-signature tsd-kind-icon">IMG_<wbr>ERROR_<wbr>NO_<wbr>DURATION<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3021</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Error raised when trying to play an image but no duration was provided for its playback</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INIT_DATA_TRANSFORM_ERROR" class="tsd-anchor"></a>
					<h3>INIT_<wbr>DATA_<wbr>TRANSFORM_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">INIT_<wbr>DATA_<wbr>TRANSFORM_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6016</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INTERNAL_ERROR" class="tsd-anchor"></a>
					<h3>INTERNAL_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">INTERNAL_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 8000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A non documented internal error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_MP4_TTML" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>MP4_<wbr>TTML</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>MP4_<wbr>TTML<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2007</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>MP4 segment does not contain TTML.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_MP4_VTT" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>MP4_<wbr>VTT</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>MP4_<wbr>VTT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2008</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>MP4 segment does not contain VTT.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_OPERATION" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>OPERATION</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>OPERATION<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7003</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_PLAYER_LICENSE" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>PLAYER_<wbr>LICENSE</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>PLAYER_<wbr>LICENSE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7005</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_SERVER_CERTIFICATE" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>SERVER_<wbr>CERTIFICATE</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>SERVER_<wbr>CERTIFICATE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The CDM rejected the server certificate supplied by the application.
								The certificate may be malformed or in an unsupported format.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_STATE" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>STATE</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>STATE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7004</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_STREAMS_CHOSEN" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>STREAMS_<wbr>CHOSEN</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>STREAMS_<wbr>CHOSEN<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 5005</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_TEXT_CUE" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>TEXT_<wbr>CUE</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>TEXT_<wbr>CUE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The text parser failed to parse a text stream due to an invalid cue.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_TEXT_HEADER" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>TEXT_<wbr>HEADER</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>TEXT_<wbr>HEADER<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The text parser failed to parse a text stream due to an invalid header.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INVALID_XML" class="tsd-anchor"></a>
					<h3>INVALID_<wbr>XML</h3>
					<div class="tsd-signature tsd-kind-icon">INVALID_<wbr>XML<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2005</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The XML parser failed to parse an xml stream, or the XML lacks mandatory
							elements for TTML.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="JS_INTEGER_OVERFLOW" class="tsd-anchor"></a>
					<h3>JS_<wbr>INTEGER_<wbr>OVERFLOW</h3>
					<div class="tsd-signature tsd-kind-icon">JS_<wbr>INTEGER_<wbr>OVERFLOW<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Some component tried to parse an integer that was too large to fit in a
								JavaScript number without rounding error.  JavaScript can only natively
							represent integers up to 53 bits.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="KEY_MESSAGE_ERROR" class="tsd-anchor"></a>
					<h3>KEY_<wbr>MESSAGE_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">KEY_<wbr>MESSAGE_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6017</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error was issued by the key message system.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="LICENSE_REQUEST_FAILED" class="tsd-anchor"></a>
					<h3>LICENSE_<wbr>REQUEST_<wbr>FAILED</h3>
					<div class="tsd-signature tsd-kind-icon">LICENSE_<wbr>REQUEST_<wbr>FAILED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6007</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The license request failed.  This could be a timeout, a network failure, or
								a rejection by the server.
							<br> error.data[0] is a shaka.util.Error from the networking engine.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="LICENSE_RESPONSE_REJECTED" class="tsd-anchor"></a>
					<h3>LICENSE_<wbr>RESPONSE_<wbr>REJECTED</h3>
					<div class="tsd-signature tsd-kind-icon">LICENSE_<wbr>RESPONSE_<wbr>REJECTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6008</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The license response was rejected by the CDM.  The server&#39;s response may be
								invalid or malformed for this CDM.
							<br> error.data[0] is an error message string from the browser.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="LOAD_INTERRUPTED" class="tsd-anchor"></a>
					<h3>LOAD_<wbr>INTERRUPTED</h3>
					<div class="tsd-signature tsd-kind-icon">LOAD_<wbr>INTERRUPTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7000</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MALFORMED_DATA_URI" class="tsd-anchor"></a>
					<h3>MALFORMED_<wbr>DATA_<wbr>URI</h3>
					<div class="tsd-signature tsd-kind-icon">MALFORMED_<wbr>DATA_<wbr>URI<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A network request was made with a malformed data URI.
							<br> error.data[0] is the URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MALFORMED_TEST_URI" class="tsd-anchor"></a>
					<h3>MALFORMED_<wbr>TEST_<wbr>URI</h3>
					<div class="tsd-signature tsd-kind-icon">MALFORMED_<wbr>TEST_<wbr>URI<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1008</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MANIFEST_PARSING_FAILURE" class="tsd-anchor"></a>
					<h3>MANIFEST_<wbr>PARSING_<wbr>FAILURE</h3>
					<div class="tsd-signature tsd-kind-icon">MANIFEST_<wbr>PARSING_<wbr>FAILURE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4013</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>There was an error while parsing the manifest.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MEDIA_SOURCE_OPERATION_FAILED" class="tsd-anchor"></a>
					<h3>MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>FAILED</h3>
					<div class="tsd-signature tsd-kind-icon">MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>FAILED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3014</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A MediaSource operation failed.
							<br> error.data[0] is a MediaError code from the video element.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MEDIA_SOURCE_OPERATION_THREW" class="tsd-anchor"></a>
					<h3>MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>THREW</h3>
					<div class="tsd-signature tsd-kind-icon">MEDIA_<wbr>SOURCE_<wbr>OPERATION_<wbr>THREW<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3015</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A MediaSource operation threw an exception.
							<br> error.data[0] is the exception that was thrown.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MP4_SIDX_INVALID_TIMESCALE" class="tsd-anchor"></a>
					<h3>MP4_<wbr>SIDX_<wbr>INVALID_<wbr>TIMESCALE</h3>
					<div class="tsd-signature tsd-kind-icon">MP4_<wbr>SIDX_<wbr>INVALID_<wbr>TIMESCALE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3005</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The MP4 SIDX parser encountered an invalid timescale.
							The segment index data may be corrupt.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MP4_SIDX_TYPE_NOT_SUPPORTED" class="tsd-anchor"></a>
					<h3>MP4_<wbr>SIDX_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED</h3>
					<div class="tsd-signature tsd-kind-icon">MP4_<wbr>SIDX_<wbr>TYPE_<wbr>NOT_<wbr>SUPPORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The MP4 SIDX parser encountered a type of SIDX that is not supported.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MP4_SIDX_WRONG_BOX_TYPE" class="tsd-anchor"></a>
					<h3>MP4_<wbr>SIDX_<wbr>WRONG_<wbr>BOX_<wbr>TYPE</h3>
					<div class="tsd-signature tsd-kind-icon">MP4_<wbr>SIDX_<wbr>WRONG_<wbr>BOX_<wbr>TYPE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3004</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The MP4 SIDX parser found the wrong box type.
							Either the segment index range is incorrect or the data is corrupt.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NO_ERROR_CODE" class="tsd-anchor"></a>
					<h3>NO_<wbr>ERROR_<wbr>CODE</h3>
					<div class="tsd-signature tsd-kind-icon">NO_<wbr>ERROR_<wbr>CODE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 0</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Some error raised do not have a specific error code, in that case a code 0 will be issued.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NO_LICENSE_SERVER_GIVEN" class="tsd-anchor"></a>
					<h3>NO_<wbr>LICENSE_<wbr>SERVER_<wbr>GIVEN</h3>
					<div class="tsd-signature tsd-kind-icon">NO_<wbr>LICENSE_<wbr>SERVER_<wbr>GIVEN<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6012</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>No license server was given for the key system signaled by the manifest.
							A license server URI is required for every key system.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NO_PERIODS" class="tsd-anchor"></a>
					<h3>NO_<wbr>PERIODS</h3>
					<div class="tsd-signature tsd-kind-icon">NO_<wbr>PERIODS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4014</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>No valid periods were found in the manifest.  Please check that your
							manifest is correct and free of typos.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NO_RECOGNIZED_KEY_SYSTEMS" class="tsd-anchor"></a>
					<h3>NO_<wbr>RECOGNIZED_<wbr>KEY_<wbr>SYSTEMS</h3>
					<div class="tsd-signature tsd-kind-icon">NO_<wbr>RECOGNIZED_<wbr>KEY_<wbr>SYSTEMS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The manifest indicated protected content, but the manifest parser was
							unable to determine what key systems should be used.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NO_VIDEO_ELEMENT" class="tsd-anchor"></a>
					<h3>NO_<wbr>VIDEO_<wbr>ELEMENT</h3>
					<div class="tsd-signature tsd-kind-icon">NO_<wbr>VIDEO_<wbr>ELEMENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7002</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="OFFLINE_SESSION_REMOVED" class="tsd-anchor"></a>
					<h3>OFFLINE_<wbr>SESSION_<wbr>REMOVED</h3>
					<div class="tsd-signature tsd-kind-icon">OFFLINE_<wbr>SESSION_<wbr>REMOVED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6013</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A required offline session was removed.  The content is not playable.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="OPERATION_ABORTED" class="tsd-anchor"></a>
					<h3>OPERATION_<wbr>ABORTED</h3>
					<div class="tsd-signature tsd-kind-icon">OPERATION_<wbr>ABORTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7001</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="PLAY_PROMISE_INTERRUPTED" class="tsd-anchor"></a>
					<h3>PLAY_<wbr>PROMISE_<wbr>INTERRUPTED</h3>
					<div class="tsd-signature tsd-kind-icon">PLAY_<wbr>PROMISE_<wbr>INTERRUPTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A warning issued when the play promise is rejected.
							This can occur due to:</p>
							<ol>
								<li>The play request being interrupted (e.g., by a pause or a new load request).</li>
								<li>The browser blocking autoplay (e.g., due to autoplay policy restrictions).</li>
							</ol>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="QUOTA_EXCEEDED_ERROR" class="tsd-anchor"></a>
					<h3>QUOTA_<wbr>EXCEEDED_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">QUOTA_<wbr>EXCEEDED_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3017</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A MediaSource operation threw QuotaExceededError and recovery failed. The
								content cannot be played correctly because the segments are too large for
								the browser/platform. This may occur when attempting to play very high
								quality, very high bitrate content on low-end devices.
							<br> error.data[0] is the type of content which caused the error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="REQUESTED_KEY_SYSTEM_CONFIG_UNAVAILABLE" class="tsd-anchor"></a>
					<h3>REQUESTED_<wbr>KEY_<wbr>SYSTEM_<wbr>CONFIG_<wbr>UNAVAILABLE</h3>
					<div class="tsd-signature tsd-kind-icon">REQUESTED_<wbr>KEY_<wbr>SYSTEM_<wbr>CONFIG_<wbr>UNAVAILABLE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6001</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>None of the requested key system configurations are available.  This may
							happen under the following conditions:</p>
							<ul>
								<li> The key system is not supported.
									<li> The key system does not support the features requested (e.g. persistent state).
										<li> A user prompt was shown and the user denied access.
											<li> The key system is not available from unsecure contexts. (ie. requires HTTPS) See https://goo.gl/EEhZqT.
							</ul>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="REQUEST_FILTER_ERROR" class="tsd-anchor"></a>
					<h3>REQUEST_<wbr>FILTER_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">REQUEST_<wbr>FILTER_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1006</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A request filter threw an error.
							<br> error.data[0] is the original error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="RESPONSE_FILTER_ERROR" class="tsd-anchor"></a>
					<h3>RESPONSE_<wbr>FILTER_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">RESPONSE_<wbr>FILTER_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1007</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A response filter threw an error.
							<br> error.data[0] is the original error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="RESTRICTIONS_CANNOT_BE_MET" class="tsd-anchor"></a>
					<h3>RESTRICTIONS_<wbr>CANNOT_<wbr>BE_<wbr>MET</h3>
					<div class="tsd-signature tsd-kind-icon">RESTRICTIONS_<wbr>CANNOT_<wbr>BE_<wbr>MET<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4012</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>There exist some streams that could be decoded, but restrictions imposed
								by the application or the key system prevent us from playing.  This may
							happen under the following conditions:</p>
							<ul>
								<li>The application has given restrictions to the Player that restrict
									at least one content type completely (e.g. no playable audio).
									<li>The key system has imposed output restrictions that cannot be met
										(such as HDCP) and there are no unrestricted alternatives.
							</ul>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="SERVER_CERTIFICATE_REQUIRED" class="tsd-anchor"></a>
					<h3>SERVER_<wbr>CERTIFICATE_<wbr>REQUIRED</h3>
					<div class="tsd-signature tsd-kind-icon">SERVER_<wbr>CERTIFICATE_<wbr>REQUIRED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6015</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error indicating that a server certificate is required to properly perform a license request.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="STATUS_CHANGED_EXPIRED" class="tsd-anchor"></a>
					<h3>STATUS_<wbr>CHANGED_<wbr>EXPIRED</h3>
					<div class="tsd-signature tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>EXPIRED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6011</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error was raised when the DRM status expired.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="STATUS_CHANGED_INTERNAL_ERROR" class="tsd-anchor"></a>
					<h3>STATUS_<wbr>CHANGED_<wbr>INTERNAL_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>INTERNAL_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6019</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error was raised when the key is not currently usable for decryption because of an error in the CDM</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="STATUS_CHANGED_OUTPUT_RESTRICTED" class="tsd-anchor"></a>
					<h3>STATUS_<wbr>CHANGED_<wbr>OUTPUT_<wbr>RESTRICTED</h3>
					<div class="tsd-signature tsd-kind-icon">STATUS_<wbr>CHANGED_<wbr>OUTPUT_<wbr>RESTRICTED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6018</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error was raised when the video output security does not meet the DRM restrictions requirements (e.g. HDCP security level)</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="TIMEOUT" class="tsd-anchor"></a>
					<h3>TIMEOUT</h3>
					<div class="tsd-signature tsd-kind-icon">TIMEOUT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A network request timed out.
							<br> error.data[0] is the URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="TRANSMUXING_FAILED" class="tsd-anchor"></a>
					<h3>TRANSMUXING_<wbr>FAILED</h3>
					<div class="tsd-signature tsd-kind-icon">TRANSMUXING_<wbr>FAILED<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3018</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNABLE_TO_DETECT_ENCODING" class="tsd-anchor"></a>
					<h3>UNABLE_<wbr>TO_<wbr>DETECT_<wbr>ENCODING</h3>
					<div class="tsd-signature tsd-kind-icon">UNABLE_<wbr>TO_<wbr>DETECT_<wbr>ENCODING<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2003</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Was unable to detect the encoding of the response text.  Suggest adding
							byte-order-markings to the response data.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNABLE_TO_EXTRACT_CUE_START_TIME" class="tsd-anchor"></a>
					<h3>UNABLE_<wbr>TO_<wbr>EXTRACT_<wbr>CUE_<wbr>START_<wbr>TIME</h3>
					<div class="tsd-signature tsd-kind-icon">UNABLE_<wbr>TO_<wbr>EXTRACT_<wbr>CUE_<wbr>START_<wbr>TIME<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2009</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNABLE_TO_GUESS_MANIFEST_TYPE" class="tsd-anchor"></a>
					<h3>UNABLE_<wbr>TO_<wbr>GUESS_<wbr>MANIFEST_<wbr>TYPE</h3>
					<div class="tsd-signature tsd-kind-icon">UNABLE_<wbr>TO_<wbr>GUESS_<wbr>MANIFEST_<wbr>TYPE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The Player was unable to guess the manifest type based on file extension
								or MIME type.  To fix, try one of the following:
								<br><ul>
									<li>Rename the manifest so that the URI ends in a well-known extension.
										<li>Configure the server to send a recognizable Content-Type header.
							<li>Configure the server to accept a HEAD request for the manifest.</p>
							</ul>
							<br> error.data[0] is the manifest URI.
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNEXPECTED_TEST_REQUEST" class="tsd-anchor"></a>
					<h3>UNEXPECTED_<wbr>TEST_<wbr>REQUEST</h3>
					<div class="tsd-signature tsd-kind-icon">UNEXPECTED_<wbr>TEST_<wbr>REQUEST<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1009</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNMAPPED_ERROR" class="tsd-anchor"></a>
					<h3>UNMAPPED_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">UNMAPPED_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 10000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error emited by a core player which is not mapped to a high level player error.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNPLAYABLE_PERIOD" class="tsd-anchor"></a>
					<h3>UNPLAYABLE_<wbr>PERIOD</h3>
					<div class="tsd-signature tsd-kind-icon">UNPLAYABLE_<wbr>PERIOD<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4011</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The manifest contains a period with no playable streams.
								Either the period was originally empty, or the streams within cannot be
							played on this browser or platform.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNSUPPORTED_SCHEME" class="tsd-anchor"></a>
					<h3>UNSUPPORTED_<wbr>SCHEME</h3>
					<div class="tsd-signature tsd-kind-icon">UNSUPPORTED_<wbr>SCHEME<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1000</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>A network request was made using an unsupported URI scheme.
							<br> error.data[0] is the URI.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="VIDEO_ERROR" class="tsd-anchor"></a>
					<h3>VIDEO_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">VIDEO_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3016</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>The video element reported an error.
							The <code>data</code> object returned within the error provide information on video element error.</p>
							<ul>
								<li> `data.mesage` : the video element error message</li>
								<li> `data.code` : the video element error code</li>
								<li> `data.extCode` : the video element error extended code (Microsoft extendend error code available on IE)</li>
							</ul>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="WIDEVINE_CERTIFICATE_LOADING_ERROR" class="tsd-anchor"></a>
					<h3>WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>ERROR</h3>
					<div class="tsd-signature tsd-kind-icon">WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>ERROR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6021</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error raised when an error occur while loading the Widevine certificate</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="WIDEVINE_CERTIFICATE_LOADING_TIMEOUT" class="tsd-anchor"></a>
					<h3>WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>TIMEOUT</h3>
					<div class="tsd-signature tsd-kind-icon">WIDEVINE_<wbr>CERTIFICATE_<wbr>LOADING_<wbr>TIMEOUT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6020</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>An error raised when a timemout occurs while loading the Widevine certificate</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Errors.html">Errors</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>