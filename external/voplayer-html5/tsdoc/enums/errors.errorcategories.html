<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>ErrorCategories | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Enumeration ErrorCategories</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Errors.html">Errors</a>
				</li>
				<li>
					<a href="Errors.ErrorCategories.html">ErrorCategories</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#ADVERTISEMENT_CATEGORY" class="tsd-kind-icon">ADVERTISEMENT_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#API_CATEGORY" class="tsd-kind-icon">API_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#DRM_CATEGORY" class="tsd-kind-icon">DRM_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#FMA_CATEGORY" class="tsd-kind-icon">FMA_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#INTERNAL_CATEGORY" class="tsd-kind-icon">INTERNAL_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#MANIFEST_CATEGORY" class="tsd-kind-icon">MANIFEST_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#MEDIA_CATEGORY" class="tsd-kind-icon">MEDIA_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#NETWORK_CATEGORY" class="tsd-kind-icon">NETWORK_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#PLAYER_CATEGORY" class="tsd-kind-icon">PLAYER_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#STREAMING_CATEGORY" class="tsd-kind-icon">STREAMING_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#TEXT_CATEGORY" class="tsd-kind-icon">TEXT_<wbr>CATEGORY</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Errors.ErrorCategories.html#UNMAPPED_CATEGORY" class="tsd-kind-icon">UNMAPPED_<wbr>CATEGORY</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="ADVERTISEMENT_CATEGORY" class="tsd-anchor"></a>
					<h3>ADVERTISEMENT_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">ADVERTISEMENT_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 20</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors triggered from Advertisement module</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="API_CATEGORY" class="tsd-anchor"></a>
					<h3>API_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">API_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 9</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors triggered on API change, used to trigger depreciation of API</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DRM_CATEGORY" class="tsd-anchor"></a>
					<h3>DRM_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">DRM_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 6</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors related to DRM.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="FMA_CATEGORY" class="tsd-anchor"></a>
					<h3>FMA_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">FMA_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 12</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors triggered from FMA module</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="INTERNAL_CATEGORY" class="tsd-anchor"></a>
					<h3>INTERNAL_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">INTERNAL_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 8</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors triggered internally, in principle should not be raised to the integrator</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MANIFEST_CATEGORY" class="tsd-anchor"></a>
					<h3>MANIFEST_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">MANIFEST_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 4</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors parsing the Manifest.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="MEDIA_CATEGORY" class="tsd-anchor"></a>
					<h3>MEDIA_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">MEDIA_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 3</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors parsing or processing audio or video streams.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="NETWORK_CATEGORY" class="tsd-anchor"></a>
					<h3>NETWORK_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">NETWORK_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 1</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors from the network stack.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="PLAYER_CATEGORY" class="tsd-anchor"></a>
					<h3>PLAYER_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">PLAYER_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 7</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Miscellaneous errors from the player.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="STREAMING_CATEGORY" class="tsd-anchor"></a>
					<h3>STREAMING_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">STREAMING_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 5</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors related to streaming.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="TEXT_CATEGORY" class="tsd-anchor"></a>
					<h3>TEXT_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">TEXT_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 2</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors parsing text streams.</p>
						</div>
					</div>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="UNMAPPED_CATEGORY" class="tsd-anchor"></a>
					<h3>UNMAPPED_<wbr>CATEGORY</h3>
					<div class="tsd-signature tsd-kind-icon">UNMAPPED_<wbr>CATEGORY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = 10</span></div>
					<aside class="tsd-sources">
					</aside>
					<div class="tsd-comment tsd-typography">
						<div class="lead">
							<p>Errors triggered with no specific mapping category</p>
						</div>
					</div>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Errors.html">Errors</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>