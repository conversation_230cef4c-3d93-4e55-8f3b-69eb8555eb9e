<!doctype html>
<html class="default no-js">
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>VideoCodecs | VO Player - HTML5</title>
	<meta name="description" content="Documentation for VO Player - HTML5">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="../assets/css/main.css">
	<link rel="stylesheet" href="../assets/css/pages.css">
</head>
<body>
<header>
	<div class="tsd-page-toolbar">
		<div class="container">
			<div class="table-wrap">
				<div class="table-cell" id="tsd-search" data-index="../assets/js/search.json" data-base="..">
					<div class="field">
						<label for="tsd-search-field" class="tsd-widget search no-caption">Search</label>
						<input id="tsd-search-field" type="text" />
					</div>
					<ul class="results">
						<li class="state loading">Preparing search index...</li>
						<li class="state failure">The search index is not available</li>
					</ul>
					<a href="../index.html" class="title">VO Player - HTML5</a>
				</div>
				<div class="table-cell" id="tsd-widgets">
					<div id="tsd-filter">
						<a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a>
						<div class="tsd-filter-group">
							<div class="tsd-select" id="tsd-filter-visibility">
								<span class="tsd-select-label">All</span>
								<ul class="tsd-select-list">
									<li data-value="public">Public</li>
									<li data-value="protected">Public/Protected</li>
									<li data-value="private" class="selected">All</li>
								</ul>
							</div>
							<input type="checkbox" id="tsd-filter-inherited" checked />
							<label class="tsd-widget" for="tsd-filter-inherited">Inherited</label>
							<input type="checkbox" id="tsd-filter-externals" checked />
							<label class="tsd-widget" for="tsd-filter-externals">Externals</label>
							<input type="checkbox" id="tsd-filter-only-exported" />
							<label class="tsd-widget" for="tsd-filter-only-exported">Only exported</label>
						</div>
					</div>
					<a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a>
				</div>
			</div>
		</div>
	</div>
	<div class="tsd-page-title">
		<div class="container">
			<div style="position:relative;height:35px">
				<div style="position:absolute;bottom:0px">
					<h1>Enumeration VideoCodecs</h1>
				</div>
				<img style="position:absolute;bottom:0px;right:0px" src="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png" class="hs-image-widget " style="width:109px;border-width:0px;border:0px;" width="109" alt="logo-transparent-sml.png" title="logo-transparent-sml.png" srcset="https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=55&amp;name=logo-transparent-sml.png 55w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=109&amp;name=logo-transparent-sml.png 109w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=164&amp;name=logo-transparent-sml.png 164w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=218&amp;name=logo-transparent-sml.png 218w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=273&amp;name=logo-transparent-sml.png 273w, https://www.viaccess-orca.com/hs-fs/hub/330957/file-398013684-png/logo-transparent-sml.png?width=327&amp;name=logo-transparent-sml.png 327w" sizes="(max-width: 109px) 100vw, 109px">
			</div>
			<ul style="padding-top:10px" class="tsd-breadcrumb">
				<li>
					<a href="../modules.html">Globals</a>
				</li>
				<li>
					<a href="../modules/Types.html">Types</a>
				</li>
				<li>
					<a href="Types.VideoCodecs.html">VideoCodecs</a>
				</li>
			</ul>
		</div>
	</div>
</header>
<div class="container container-main">
	<div class="row">
		<div class="col-8 col-content">
			<section class="tsd-panel-group tsd-index-group">
				<h2>Index</h2>
				<section class="tsd-panel tsd-index-panel">
					<div class="tsd-index-content">
						<section class="tsd-index-section ">
							<h3>Enumeration members</h3>
							<ul class="tsd-index-list">
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1" class="tsd-kind-icon">AVC1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_42001E" class="tsd-kind-icon">AVC1_<wbr>42001E</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_42001F" class="tsd-kind-icon">AVC1_<wbr>42001F</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_42801F" class="tsd-kind-icon">AVC1_<wbr>42801F</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_4D001E" class="tsd-kind-icon">AVC1_<wbr>4<wbr>D001E</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_4D001F" class="tsd-kind-icon">AVC1_<wbr>4<wbr>D001F</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_640029" class="tsd-kind-icon">AVC1_<wbr>640029</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_66_13" class="tsd-kind-icon">AVC1_<wbr>66_<wbr>13</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_66_30" class="tsd-kind-icon">AVC1_<wbr>66_<wbr>30</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_77_30" class="tsd-kind-icon">AVC1_<wbr>77_<wbr>30</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC1_Main" class="tsd-kind-icon">AVC1_<wbr><wbr>Main</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#AVC3_42E01E" class="tsd-kind-icon">AVC3_<wbr>42<wbr>E01E</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile4_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVH1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile4_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVHE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile5_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVH1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile5_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVHE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile8_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVH1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile8_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVHE</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile9_DVA1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVA1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#DolbyVision_Profile9_DVAV" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVAV</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#H264_High" class="tsd-kind-icon">H264_<wbr><wbr>High</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#HEVC_HEV1" class="tsd-kind-icon">HEVC_<wbr>HEV1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#HEVC_HVC1" class="tsd-kind-icon">HEVC_<wbr>HVC1</a></li>
								<li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="Types.VideoCodecs.html#HVC1_1_6_L93_90" class="tsd-kind-icon">HVC1_<wbr>1_6_<wbr><wbr>L93_<wbr>90</a></li>
							</ul>
						</section>
					</div>
				</section>
			</section>
			<section class="tsd-panel-group tsd-member-group ">
				<h2>Enumeration members</h2>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1" class="tsd-anchor"></a>
					<h3>AVC1</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.42E01E\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_42001E" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>42001E</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>42001E<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.42001e\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_42001F" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>42001F</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>42001F<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.42001f\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_42801F" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>42801F</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>42801F<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.42801f\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_4D001E" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>4<wbr>D001E</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>4<wbr>D001E<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.4d001e\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_4D001F" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>4<wbr>D001F</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>4<wbr>D001F<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.4d001f\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_640029" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>640029</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>640029<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.640029\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_66_13" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>66_<wbr>13</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>66_<wbr>13<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.66.13\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_66_30" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>66_<wbr>30</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>66_<wbr>30<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.66.30\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_77_30" class="tsd-anchor"></a>
					<h3>AVC1_<wbr>77_<wbr>30</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr>77_<wbr>30<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.77.30\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC1_Main" class="tsd-anchor"></a>
					<h3>AVC1_<wbr><wbr>Main</h3>
					<div class="tsd-signature tsd-kind-icon">AVC1_<wbr><wbr>Main<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.4D401E\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="AVC3_42E01E" class="tsd-anchor"></a>
					<h3>AVC3_<wbr>42<wbr>E01E</h3>
					<div class="tsd-signature tsd-kind-icon">AVC3_<wbr>42<wbr>E01E<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc3.42E01E\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile4_DVH1" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVH1</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVH1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvh1.04.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile4_DVHE" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVHE</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVHE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvhe.04.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile5_DVH1" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVH1</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVH1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvh1.05.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile5_DVHE" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVHE</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVHE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvhe.05.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile8_DVH1" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVH1</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVH1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvh1.08.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile8_DVHE" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVHE</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVHE<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvhe.08.07\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile9_DVA1" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVA1</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVA1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dva1.09.05\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="DolbyVision_Profile9_DVAV" class="tsd-anchor"></a>
					<h3>Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVAV</h3>
					<div class="tsd-signature tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVAV<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;dvav.09.05\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="H264_High" class="tsd-anchor"></a>
					<h3>H264_<wbr><wbr>High</h3>
					<div class="tsd-signature tsd-kind-icon">H264_<wbr><wbr>High<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;avc1.64001F\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HEVC_HEV1" class="tsd-anchor"></a>
					<h3>HEVC_<wbr>HEV1</h3>
					<div class="tsd-signature tsd-kind-icon">HEVC_<wbr>HEV1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;hev1.1.6.L93.B0\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HEVC_HVC1" class="tsd-anchor"></a>
					<h3>HEVC_<wbr>HVC1</h3>
					<div class="tsd-signature tsd-kind-icon">HEVC_<wbr>HVC1<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;hvc1.1.6.L93.B0\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
				<section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum">
					<a name="HVC1_1_6_L93_90" class="tsd-anchor"></a>
					<h3>HVC1_<wbr>1_6_<wbr><wbr>L93_<wbr>90</h3>
					<div class="tsd-signature tsd-kind-icon">HVC1_<wbr>1_6_<wbr><wbr>L93_<wbr>90<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol"> = &quot;video/mp4; codecs&#x3D;\&quot;hvc1.1.6.L93.90\&quot;&quot;</span></div>
					<aside class="tsd-sources">
					</aside>
				</section>
			</section>
		</div>
		<div class="col-4 col-menu menu-sticky-wrap menu-highlight">
			<nav class="tsd-navigation primary">
				<ul>
					<li class="label pp-nav pp-group">
						<span>Documentation</span>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/basic-usage.html">Basic player integration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-ui.html">Player integration with <wbr>Viaccess-<wbr><wbr>Orca user <wbr>Interface</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/player-configuration.html">Player configuration</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/playback-drm.html">Playback of DRM protected streams</a>
					</li>
					<li class=" pp-nav pp-page pp-parent">
						<a href="../pages/Documentation/quality-and-tracks.html">Tracks and <wbr>Video <wbr>Quality management</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/vmap-vast-how-to.html">Advertisement with VMAP and VAST</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/virtual-reality.html">VR / <wbr>Video 360°</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smart-lib.html">Braodpeak <wbr>Smartlib pre-<wbr><wbr>Integration</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/smarttv-support.html">Support for <wbr>SmartTVs</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/watermarking.html">Viaccess-<wbr><wbr>Orca <wbr>Watermarking</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/logging-mechanism.html">Logging mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/buffer-sensitivity.html">Adaptive <wbr>Bitrate <wbr>Control</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/download-error-handling.html">Download error robustness mechanism</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/npm-package.html">Import player library as local npm package</a>
					</li>
					<li class=" pp-nav pp-page">
						<a href="../pages/Documentation/fma.html">Front <wbr>Monitoring <wbr>Agent (FMA)</a>
					</li>
					<li class=" ">
						<a href="../modules.html">Modules</a>
					</li>
					<li class="current tsd-kind-module">
						<a href="../modules/Types.html">Types</a>
					</li>
				</ul>
			</nav>
			<nav class="tsd-navigation secondary menu-sticky">
				<ul class="before-current">
				</ul>
				<ul class="current">
					<li class="current tsd-kind-enum tsd-parent-kind-module">
						<a href="Types.VideoCodecs.html" class="tsd-kind-icon">Video<wbr>Codecs</a>
						<ul>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1" class="tsd-kind-icon">AVC1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_42001E" class="tsd-kind-icon">AVC1_<wbr>42001E</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_42001F" class="tsd-kind-icon">AVC1_<wbr>42001F</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_42801F" class="tsd-kind-icon">AVC1_<wbr>42801F</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_4D001E" class="tsd-kind-icon">AVC1_<wbr>4<wbr>D001E</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_4D001F" class="tsd-kind-icon">AVC1_<wbr>4<wbr>D001F</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_640029" class="tsd-kind-icon">AVC1_<wbr>640029</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_66_13" class="tsd-kind-icon">AVC1_<wbr>66_<wbr>13</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_66_30" class="tsd-kind-icon">AVC1_<wbr>66_<wbr>30</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_77_30" class="tsd-kind-icon">AVC1_<wbr>77_<wbr>30</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC1_Main" class="tsd-kind-icon">AVC1_<wbr><wbr>Main</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#AVC3_42E01E" class="tsd-kind-icon">AVC3_<wbr>42<wbr>E01E</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile4_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVH1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile4_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile4_<wbr>DVHE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile5_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVH1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile5_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile5_<wbr>DVHE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile8_DVH1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVH1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile8_DVHE" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile8_<wbr>DVHE</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile9_DVA1" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVA1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#DolbyVision_Profile9_DVAV" class="tsd-kind-icon">Dolby<wbr>Vision_<wbr><wbr>Profile9_<wbr>DVAV</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#H264_High" class="tsd-kind-icon">H264_<wbr><wbr>High</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#HEVC_HEV1" class="tsd-kind-icon">HEVC_<wbr>HEV1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#HEVC_HVC1" class="tsd-kind-icon">HEVC_<wbr>HVC1</a>
							</li>
							<li class=" tsd-kind-enum-member tsd-parent-kind-enum">
								<a href="Types.VideoCodecs.html#HVC1_1_6_L93_90" class="tsd-kind-icon">HVC1_<wbr>1_6_<wbr><wbr>L93_<wbr>90</a>
							</li>
						</ul>
					</li>
				</ul>
				<ul class="after-current">
				</ul>
			</nav>
		</div>
	</div>
</div>
<footer>
	<div class="container">
		<h2>Legend</h2>
		<div class="tsd-legend-group">
			<ul class="tsd-legend">
				<li class="tsd-kind-function"><span class="tsd-kind-icon">Function</span></li>
				<li class="tsd-kind-type-alias"><span class="tsd-kind-icon">Type alias</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-enum"><span class="tsd-kind-icon">Enumeration</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-interface"><span class="tsd-kind-icon">Interface</span></li>
			</ul>
			<ul class="tsd-legend">
				<li class="tsd-kind-class"><span class="tsd-kind-icon">Class</span></li>
			</ul>
		</div>
	</div>
</footer>
<div class="overlay"></div>
<script src="../assets/js/main.js"></script>
<script>if (location.protocol == 'file:') document.write('<script src="../assets/js/search.json"><' + '/script>');</script>
</body>
</html>