e2e chrome:
  image:
    name: $E2E_TESTS_IMAGE
  stage: post-tests
  tags:
    - docker
  script:
    - source ./ci-scripts/get-feature-folder-name.sh
    - git clone --branch master https://gitlab-ci-token:${CI_JOB_TOKEN}@git.opl.tools/rumiuk/atvapps/orange-tv-go-web-automatic-tests.git
    - cd orange-tv-go-web-automatic-tests
    - chmod +x ci-scripts/url-change.sh
    - ci-scripts/url-change.sh
    - yarn install --frozen-lockfile
    - CYPRESS_FEATURE_PATH=${FEATURE_FOLDER_NAME} yarn e2e:feature:run:chrome
  allow_failure: true
  artifacts:
    when: always
    paths:
      - orange-tv-go-web-automatic-tests/cypress/videos/**/*.mp4
      - orange-tv-go-web-automatic-tests/cypress/screenshots/**/*.png
      - orange-tv-go-web-automatic-tests/cypress/reports/html/index.html
    expire_in: 1 day
  dependencies:
    - feature deploy
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always