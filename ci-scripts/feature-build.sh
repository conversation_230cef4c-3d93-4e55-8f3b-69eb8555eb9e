#!/bin/bash
folder=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
folder=${folder#*feature/}
folder=${folder#*fix/}
folder=${folder#*task/}

folder=`echo $folder | cut -d'_' -f 1`
echo ---
echo feature folder name: /$folder/
echo ---
sed -i '1 a "homepage": "/'$folder'",' package.json
#cat package.json
sed -i "s/'\//'\/$folder\//" src/routes/routes-map.ts
#sed -i "s/ROUTE: '\/"$folder"\//ROUTE: '\//" src/internalComponents/constants/routeConfig.js
#cat src/routes/routes-map.ts
