feature build:
  needs: ["get default tech config"]
  only:
    - merge_requests
  tags:
    - docker
  stage: build
  script:
    - sh ./ci-scripts/feature-build.sh
    - yarn install --cache-folder .yarn
    - yarn lint
    - yarn build:feature
  artifacts:
    paths:
      - ./dist/*
    expire_in: 1 day

feature deploy:
  tags:
    - tvgo-dev-deploy
  stage: deploy
  dependencies:
    - feature build
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "production"
      when: on_success
  script:
    - sh ./ci-scripts/feature-deploy.sh
    