pre build:
  needs: ["get default tech config"]
  only:
    - master
  tags:
    - docker
  stage: build
  script:
    - yarn install --cache-folder .yarn
    - yarn lint
    - yarn build:preprod
  artifacts:
    paths:
      - ./dist/*
    expire_in: 1 day

pre publish:
  stage: publish
  only:
    - master
  tags:
    - docker
  dependencies:
    - pre build  
  script:
    - tar -czf web2-pre-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz ./dist*
    - |
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
        --upload-file web2-pre-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz \
        "$CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/pre/${APP_VERSION}/web2-pre-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz"
