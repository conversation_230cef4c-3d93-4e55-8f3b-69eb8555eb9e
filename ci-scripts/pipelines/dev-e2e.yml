develop-e2e build:
  needs: ["get default tech config"]
  only:
   - develop
  tags:
    - docker
  stage: build

  script:
    - yarn install --cache-folder .yarn
    - yarn lint
    - yarn build:dev-e2e
  artifacts:
    paths:
      - ./dist/*
    expire_in: 1 day

develop deploy-e2e:
  only:
    - develop
  tags:
    - tvgo-dev-deploy
  stage: deploy
  dependencies:
    - develop-e2e build
  script:
    - find /var/www/html/dev/* -maxdepth 0 -name 'YPT*' -prune -o -exec rm -rf '{}' ';'
    - cp -avr ./dist/* /var/www/html/dev
    