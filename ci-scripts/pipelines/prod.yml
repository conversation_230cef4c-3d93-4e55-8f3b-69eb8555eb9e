prod build:
  needs: ["get default tech config"]
  only:
    - production
  tags:
    - docker
  stage: build
  script:
    - yarn install --cache-folder .yarn
    - yarn lint
    - yarn build:prod
  artifacts:
    paths:
      - ./dist/*
    expire_in: 1 day

prod publish:
  stage: publish
  only:
    - production
  tags:
    - docker
  dependencies:
    - prod build  
  script:
    - tar -czf web2-prod-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz ./dist*
    - |
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
        --upload-file web2-prod-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz \
        "$CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/prod/${APP_VERSION}/web2-prod-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz"
