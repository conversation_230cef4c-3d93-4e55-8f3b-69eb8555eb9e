develop build:
  needs: ["get default tech config"]
  only:
   - develop
  tags:
    - docker
  stage: build
  script:
    - yarn install --cache-folder .yarn
    - yarn lint
    - yarn build:dev
    - yarn build:storybook
  artifacts:
    paths:
      - ./dist/*
      - ./storybook-static/*
    expire_in: 1 day

develop deploy:
  only:
    - develop
  tags:
    - tvgo-dev-deploy
  stage: deploy
  dependencies:
    - develop build
  script:
    - find /var/www/html/dev2/* -maxdepth 0 -name 'YPT*' -prune -o -exec rm -rf '{}' ';'
    - cp -avr ./dist/* /var/www/html/dev2
    - rm -rf /var/www/html/storybook/*
    - cp -avr ./storybook-static/* /var/www/html/storybook

develop publish:
  stage: publish
  only:
    - develop
  tags:
    - docker
  dependencies:
    - develop build  
  script:
    - tar -czf web2-dev-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz ./dist*
    - |
      curl --header "JOB-TOKEN: $CI_JOB_TOKEN" \
        --upload-file web2-dev-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz \
        "$CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/dev/${APP_VERSION}/web2-dev-${APP_VERSION}-${CI_PIPELINE_IID}.tar.gz"
