#!/bin/bash

echo ---
echo deleting feature folders older than $FEATURE_MAX_DAYS
find /var/www/html/dev2/* -type d -maxdepth 0 -name 'YPT*' -ctime +$FEATURE_MAX_DAYS -exec rm -rf {} \;
echo ---

folder=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME
folder=${folder#*feature/}
folder=${folder#*fix/}
folder=${folder#*task/}

folder=`echo $folder | cut -d'_' -f 1`

echo ---
echo feature folder name: /$folder/
echo ---

mkdir -p /var/www/html/dev2/$folder
rm -rf /var/www/html/dev2/$folder/*
cp -avr ./dist/* /var/www/html/dev2/$folder
