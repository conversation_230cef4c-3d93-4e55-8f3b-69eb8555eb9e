#!/bin/bash
# File created to pass yarn audit in case of unable to resolve vulnerabilities

handle_audit_failure() {
  echo -e "\nSecurity vulnerabilities were found that were not ignored.\n"
  echo -e "Check to see if these vulnerabilities apply to production\nand/or if they have fixes available. If they do not have\nfixes and they do not apply to production, you may ignore them.\n"
  echo -e "To ignore these vulnerabilities, run:\n"
  echo -e "yarn audit --json | grep auditAdvisory > yarn-audit-known-issues\n"
  echo "and commit the yarn-audit-known-issues file."
}

# Run yarn audit and capture the result
yarn audit || resultCode=$?

# If vulnerabilities are found, check if they are known issues
if [ "$resultCode" != 0 ]; then
  if [ -f yarn-audit-known-issues ]; then
    currentResult=$(yarn audit --json | grep auditAdvisory)

    if diff -q <(echo "$currentResult") yarn-audit-known-issues > /dev/null 2>&1; then
      echo
      echo "Ignoring known vulnerabilities."
      exit 0
    fi
  fi

  handle_audit_failure
  exit "$resultCode"
fi