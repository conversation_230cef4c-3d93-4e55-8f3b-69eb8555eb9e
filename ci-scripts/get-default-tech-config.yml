get default tech config:
  only:
    - develop
    - master
    - production
    - merge_requests
  tags:
    - docker
  stage: build
  script:
    - curl -o src/services/config/defaultTechConfig.json -H "Accept:application/json;ver=2" "https://tvgo.orange.pl/gpapi2/core/appConfigs?deviceCat=otg&group=web&types=tech_configurations&revisions=1"
    - yarn prettier ./src/services/config/defaultTechConfig.json
  artifacts:
    paths:
      - ./src/services/config/defaultTechConfig.json
